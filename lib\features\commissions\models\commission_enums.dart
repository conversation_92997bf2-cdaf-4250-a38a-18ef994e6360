import 'package:flutter/material.dart';

/// Commission type enumeration
enum CommissionType {
  directSale,
  referralBonus,
  levelBonus,
  performanceBonus,
  teamBonus,
  leadershipBonus,
  achievementBonus,
}

extension CommissionTypeExtension on CommissionType {
  String get displayName {
    switch (this) {
      case CommissionType.directSale:
        return 'Direct Sale';
      case CommissionType.referralBonus:
        return 'Referral Bonus';
      case CommissionType.levelBonus:
        return 'Level Bonus';
      case CommissionType.performanceBonus:
        return 'Performance Bonus';
      case CommissionType.teamBonus:
        return 'Team Bonus';
      case CommissionType.leadershipBonus:
        return 'Leadership Bonus';
      case CommissionType.achievementBonus:
        return 'Achievement Bonus';
    }
  }

  String get value {
    switch (this) {
      case CommissionType.directSale:
        return 'direct_sale';
      case CommissionType.referralBonus:
        return 'referral_bonus';
      case CommissionType.levelBonus:
        return 'level_bonus';
      case CommissionType.performanceBonus:
        return 'performance_bonus';
      case CommissionType.teamBonus:
        return 'team_bonus';
      case CommissionType.leadershipBonus:
        return 'leadership_bonus';
      case CommissionType.achievementBonus:
        return 'achievement_bonus';
    }
  }

  IconData get icon {
    switch (this) {
      case CommissionType.directSale:
        return Icons.sell;
      case CommissionType.referralBonus:
        return Icons.person_add;
      case CommissionType.levelBonus:
        return Icons.stairs;
      case CommissionType.performanceBonus:
        return Icons.trending_up;
      case CommissionType.teamBonus:
        return Icons.group;
      case CommissionType.leadershipBonus:
        return Icons.emoji_events;
      case CommissionType.achievementBonus:
        return Icons.star;
    }
  }

  Color get color {
    switch (this) {
      case CommissionType.directSale:
        return const Color(0xFF4CAF50);
      case CommissionType.referralBonus:
        return const Color(0xFF2196F3);
      case CommissionType.levelBonus:
        return const Color(0xFF9C27B0);
      case CommissionType.performanceBonus:
        return const Color(0xFFFF9800);
      case CommissionType.teamBonus:
        return const Color(0xFF607D8B);
      case CommissionType.leadershipBonus:
        return const Color(0xFFFFD700);
      case CommissionType.achievementBonus:
        return const Color(0xFFE91E63);
    }
  }

  String get description {
    switch (this) {
      case CommissionType.directSale:
        return 'Commission from direct property sales';
      case CommissionType.referralBonus:
        return 'Bonus for referring new agents';
      case CommissionType.levelBonus:
        return 'Bonus based on downline level performance';
      case CommissionType.performanceBonus:
        return 'Bonus for achieving performance targets';
      case CommissionType.teamBonus:
        return 'Bonus based on team performance';
      case CommissionType.leadershipBonus:
        return 'Bonus for leadership achievements';
      case CommissionType.achievementBonus:
        return 'Bonus for special achievements';
    }
  }
}

/// Commission status enumeration
enum CommissionStatus {
  pending,
  approved,
  paid,
  rejected,
  onHold,
}

extension CommissionStatusExtension on CommissionStatus {
  String get displayName {
    switch (this) {
      case CommissionStatus.pending:
        return 'Pending';
      case CommissionStatus.approved:
        return 'Approved';
      case CommissionStatus.paid:
        return 'Paid';
      case CommissionStatus.rejected:
        return 'Rejected';
      case CommissionStatus.onHold:
        return 'On Hold';
    }
  }

  String get value {
    switch (this) {
      case CommissionStatus.pending:
        return 'pending';
      case CommissionStatus.approved:
        return 'approved';
      case CommissionStatus.paid:
        return 'paid';
      case CommissionStatus.rejected:
        return 'rejected';
      case CommissionStatus.onHold:
        return 'on_hold';
    }
  }

  Color get color {
    switch (this) {
      case CommissionStatus.pending:
        return const Color(0xFFFF9800);
      case CommissionStatus.approved:
        return const Color(0xFF4CAF50);
      case CommissionStatus.paid:
        return const Color(0xFF2196F3);
      case CommissionStatus.rejected:
        return const Color(0xFFF44336);
      case CommissionStatus.onHold:
        return const Color(0xFF9E9E9E);
    }
  }

  IconData get icon {
    switch (this) {
      case CommissionStatus.pending:
        return Icons.schedule;
      case CommissionStatus.approved:
        return Icons.check_circle;
      case CommissionStatus.paid:
        return Icons.payment;
      case CommissionStatus.rejected:
        return Icons.cancel;
      case CommissionStatus.onHold:
        return Icons.pause_circle;
    }
  }
}

/// Commission calculation period
enum CommissionPeriod {
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
}

extension CommissionPeriodExtension on CommissionPeriod {
  String get displayName {
    switch (this) {
      case CommissionPeriod.daily:
        return 'Daily';
      case CommissionPeriod.weekly:
        return 'Weekly';
      case CommissionPeriod.monthly:
        return 'Monthly';
      case CommissionPeriod.quarterly:
        return 'Quarterly';
      case CommissionPeriod.yearly:
        return 'Yearly';
    }
  }

  Duration get duration {
    switch (this) {
      case CommissionPeriod.daily:
        return const Duration(days: 1);
      case CommissionPeriod.weekly:
        return const Duration(days: 7);
      case CommissionPeriod.monthly:
        return const Duration(days: 30);
      case CommissionPeriod.quarterly:
        return const Duration(days: 90);
      case CommissionPeriod.yearly:
        return const Duration(days: 365);
    }
  }
}

/// Commission tier levels
enum CommissionTier {
  bronze,
  silver,
  gold,
  platinum,
  diamond,
}

extension CommissionTierExtension on CommissionTier {
  String get displayName {
    switch (this) {
      case CommissionTier.bronze:
        return 'Bronze';
      case CommissionTier.silver:
        return 'Silver';
      case CommissionTier.gold:
        return 'Gold';
      case CommissionTier.platinum:
        return 'Platinum';
      case CommissionTier.diamond:
        return 'Diamond';
    }
  }

  Color get color {
    switch (this) {
      case CommissionTier.bronze:
        return const Color(0xFFCD7F32);
      case CommissionTier.silver:
        return const Color(0xFFC0C0C0);
      case CommissionTier.gold:
        return const Color(0xFFFFD700);
      case CommissionTier.platinum:
        return const Color(0xFFE5E4E2);
      case CommissionTier.diamond:
        return const Color(0xFFB9F2FF);
    }
  }

  double get multiplier {
    switch (this) {
      case CommissionTier.bronze:
        return 1.0;
      case CommissionTier.silver:
        return 1.2;
      case CommissionTier.gold:
        return 1.5;
      case CommissionTier.platinum:
        return 1.8;
      case CommissionTier.diamond:
        return 2.0;
    }
  }

  double get minimumSales {
    switch (this) {
      case CommissionTier.bronze:
        return 0;
      case CommissionTier.silver:
        return 1000000; // 10L
      case CommissionTier.gold:
        return 2500000; // 25L
      case CommissionTier.platinum:
        return 5000000; // 50L
      case CommissionTier.diamond:
        return 10000000; // 1Cr
    }
  }

  IconData get icon {
    switch (this) {
      case CommissionTier.bronze:
        return Icons.workspace_premium;
      case CommissionTier.silver:
        return Icons.military_tech;
      case CommissionTier.gold:
        return Icons.emoji_events;
      case CommissionTier.platinum:
        return Icons.diamond;
      case CommissionTier.diamond:
        return Icons.auto_awesome;
    }
  }
}
