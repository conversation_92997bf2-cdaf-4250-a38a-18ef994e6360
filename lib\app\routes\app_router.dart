import 'package:flutter/material.dart';
import 'package:go_router/go_router.dart';
import 'package:rama_realty_mlm/features/auth/presentation/pages/login_page.dart';
import 'package:rama_realty_mlm/features/auth/presentation/pages/profile_page.dart';
import 'package:rama_realty_mlm/features/auth/presentation/pages/register_page.dart';
import 'package:rama_realty_mlm/features/commissions/presentation/pages/commissions_page.dart';
import 'package:rama_realty_mlm/features/dashboard/presentation/pages/dashboard_page.dart';
import 'package:rama_realty_mlm/features/properties/presentation/pages/properties_page.dart';
import 'package:rama_realty_mlm/features/stars/presentation/pages/stars_page.dart';

final GlobalKey<NavigatorState> _rootNavigatorKey = GlobalKey<NavigatorState>(
  debugLabel: 'root',
);
final GlobalKey<NavigatorState> _shellNavigatorKey = GlobalKey<NavigatorState>(
  debugLabel: 'shell',
);

final router = GoRouter(
  navigatorKey: _rootNavigatorKey,
  initialLocation: '/login',
  routes: [
    GoRoute(path: '/login', builder: (context, state) => const LoginPage()),
    GoRoute(
      path: '/register',
      builder: (context, state) => const RegisterPage(),
    ),
    ShellRoute(
      navigatorKey: _shellNavigatorKey,
      builder: (context, state, child) {
        return DashboardPage(child: child);
      },
      routes: [
        GoRoute(
          path: '/dashboard',
          builder: (context, state) => const DashboardHomeTab(),
        ),
        GoRoute(
          path: '/properties',
          builder: (context, state) => const PropertiesPage(),
        ),
        GoRoute(
          path: '/commissions',
          builder: (context, state) => const CommissionsPage(),
        ),
        GoRoute(path: '/stars', builder: (context, state) => const StarsPage()),
        GoRoute(
          path: '/profile',
          builder: (context, state) => const ProfilePage(),
        ),
      ],
    ),
  ],
);
