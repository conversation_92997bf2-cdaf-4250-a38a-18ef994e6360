import 'package:cloud_firestore/cloud_firestore.dart';

/// Star model for tracking individual star awards
class StarModel {
  final String id;
  final String agentId;
  final String agentName;
  final String transactionId;
  final String? propertyId;
  final String? propertyTitle;
  final int count; // Number of stars awarded (usually 1)
  final String type; // 'sale', 'upline_bonus', 'first_sale_bonus'
  final String source; // 'direct_sale', 'upline_level_1', 'upline_level_2', etc.
  final DateTime earnedAt;
  final String? notes;
  final Map<String, dynamic>? additionalData;

  const StarModel({
    required this.id,
    required this.agentId,
    required this.agentName,
    required this.transactionId,
    this.propertyId,
    this.propertyTitle,
    this.count = 1,
    required this.type,
    required this.source,
    required this.earnedAt,
    this.notes,
    this.additionalData,
  });

  /// Create StarModel from Firestore document
  factory StarModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return StarModel(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      transactionId: data['transactionId'] ?? '',
      propertyId: data['propertyId'],
      propertyTitle: data['propertyTitle'],
      count: data['count'] ?? 1,
      type: data['type'] ?? 'sale',
      source: data['source'] ?? 'direct_sale',
      earnedAt: (data['earnedAt'] as Timestamp).toDate(),
      notes: data['notes'],
      additionalData: data['additionalData'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'agentId': agentId,
      'agentName': agentName,
      'transactionId': transactionId,
      'propertyId': propertyId,
      'propertyTitle': propertyTitle,
      'count': count,
      'type': type,
      'source': source,
      'earnedAt': Timestamp.fromDate(earnedAt),
      'notes': notes,
      'additionalData': additionalData,
    };
  }

  /// Create a copy with updated fields
  StarModel copyWith({
    String? id,
    String? agentId,
    String? agentName,
    String? transactionId,
    String? propertyId,
    String? propertyTitle,
    int? count,
    String? type,
    String? source,
    DateTime? earnedAt,
    String? notes,
    Map<String, dynamic>? additionalData,
  }) {
    return StarModel(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      transactionId: transactionId ?? this.transactionId,
      propertyId: propertyId ?? this.propertyId,
      propertyTitle: propertyTitle ?? this.propertyTitle,
      count: count ?? this.count,
      type: type ?? this.type,
      source: source ?? this.source,
      earnedAt: earnedAt ?? this.earnedAt,
      notes: notes ?? this.notes,
      additionalData: additionalData ?? this.additionalData,
    );
  }

  /// Get star type description
  String get typeDescription {
    switch (type) {
      case 'sale': return 'Direct Sale';
      case 'upline_bonus': return 'Upline Bonus';
      case 'first_sale_bonus': return 'First Sale Bonus';
      case 'milestone_bonus': return 'Milestone Bonus';
      default: return type;
    }
  }

  /// Get star source description
  String get sourceDescription {
    switch (source) {
      case 'direct_sale': return 'Direct Sale';
      case 'upline_level_1': return 'Level 1 Upline';
      case 'upline_level_2': return 'Level 2 Upline';
      case 'first_downline_sale': return 'First Downline Sale';
      case 'milestone_achievement': return 'Milestone Achievement';
      default: return source;
    }
  }

  @override
  String toString() {
    return 'StarModel(id: $id, agent: $agentName, count: $count, type: $type, source: $source)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StarModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Star bonus model for tracking 12-star bonuses
class StarBonusModel {
  final String id;
  final String agentId;
  final String agentName;
  final int starCount; // Star count when bonus was awarded (12, 24, 36, etc.)
  final double bonusAmount; // Bonus amount in INR
  final String bonusType; // 'monetary', 'recognition', 'gift', etc.
  final String status; // 'pending', 'awarded', 'cancelled'
  final DateTime eligibleAt; // When agent became eligible
  final DateTime? awardedAt; // When bonus was actually awarded
  final String? awardedBy; // Admin who awarded the bonus
  final String? notes;
  final Map<String, dynamic>? bonusDetails;

  const StarBonusModel({
    required this.id,
    required this.agentId,
    required this.agentName,
    required this.starCount,
    required this.bonusAmount,
    this.bonusType = 'monetary',
    this.status = 'pending',
    required this.eligibleAt,
    this.awardedAt,
    this.awardedBy,
    this.notes,
    this.bonusDetails,
  });

  /// Create StarBonusModel from Firestore document
  factory StarBonusModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return StarBonusModel(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      starCount: data['starCount'] ?? 12,
      bonusAmount: (data['bonusAmount'] ?? 0.0).toDouble(),
      bonusType: data['bonusType'] ?? 'monetary',
      status: data['status'] ?? 'pending',
      eligibleAt: (data['eligibleAt'] as Timestamp).toDate(),
      awardedAt: data['awardedAt'] != null ? (data['awardedAt'] as Timestamp).toDate() : null,
      awardedBy: data['awardedBy'],
      notes: data['notes'],
      bonusDetails: data['bonusDetails'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'agentId': agentId,
      'agentName': agentName,
      'starCount': starCount,
      'bonusAmount': bonusAmount,
      'bonusType': bonusType,
      'status': status,
      'eligibleAt': Timestamp.fromDate(eligibleAt),
      'awardedAt': awardedAt != null ? Timestamp.fromDate(awardedAt!) : null,
      'awardedBy': awardedBy,
      'notes': notes,
      'bonusDetails': bonusDetails,
    };
  }

  /// Create a copy with updated fields
  StarBonusModel copyWith({
    String? id,
    String? agentId,
    String? agentName,
    int? starCount,
    double? bonusAmount,
    String? bonusType,
    String? status,
    DateTime? eligibleAt,
    DateTime? awardedAt,
    String? awardedBy,
    String? notes,
    Map<String, dynamic>? bonusDetails,
  }) {
    return StarBonusModel(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      starCount: starCount ?? this.starCount,
      bonusAmount: bonusAmount ?? this.bonusAmount,
      bonusType: bonusType ?? this.bonusType,
      status: status ?? this.status,
      eligibleAt: eligibleAt ?? this.eligibleAt,
      awardedAt: awardedAt ?? this.awardedAt,
      awardedBy: awardedBy ?? this.awardedBy,
      notes: notes ?? this.notes,
      bonusDetails: bonusDetails ?? this.bonusDetails,
    );
  }

  /// Get formatted bonus amount
  String get formattedBonusAmount {
    if (bonusAmount >= 10000000) {
      return '₹${(bonusAmount / 10000000).toStringAsFixed(2)} Cr';
    } else if (bonusAmount >= 100000) {
      return '₹${(bonusAmount / 100000).toStringAsFixed(2)} L';
    } else if (bonusAmount >= 1000) {
      return '₹${(bonusAmount / 1000).toStringAsFixed(2)} K';
    } else {
      return '₹${bonusAmount.toStringAsFixed(0)}';
    }
  }

  /// Check if bonus is awarded
  bool get isAwarded => status == 'awarded';

  /// Check if bonus is pending
  bool get isPending => status == 'pending';

  /// Check if bonus is cancelled
  bool get isCancelled => status == 'cancelled';

  @override
  String toString() {
    return 'StarBonusModel(id: $id, agent: $agentName, stars: $starCount, amount: $formattedBonusAmount, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StarBonusModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Star statistics model
class StarStatsModel {
  final int totalStars;
  final int directSaleStars;
  final int uplineBonusStars;
  final int firstSaleBonusStars;
  final int currentStreak;
  final int longestStreak;
  final DateTime? lastStarEarned;
  final double progressToNextBonus;
  final int starsToNextBonus;
  final List<StarBonusModel> bonusHistory;
  final Map<String, int> starsByMonth;
  final Map<String, int> starsByType;

  const StarStatsModel({
    this.totalStars = 0,
    this.directSaleStars = 0,
    this.uplineBonusStars = 0,
    this.firstSaleBonusStars = 0,
    this.currentStreak = 0,
    this.longestStreak = 0,
    this.lastStarEarned,
    this.progressToNextBonus = 0.0,
    this.starsToNextBonus = 12,
    this.bonusHistory = const [],
    this.starsByMonth = const {},
    this.starsByType = const {},
  });

  /// Calculate progress percentage to next bonus
  double get progressPercentage {
    if (starsToNextBonus <= 0) return 100.0;
    final currentProgress = 12 - starsToNextBonus;
    return (currentProgress / 12) * 100;
  }

  /// Check if eligible for next bonus
  bool get isEligibleForBonus => starsToNextBonus <= 0;

  /// Get next bonus milestone
  int get nextBonusMilestone {
    final completedBonuses = (totalStars / 12).floor();
    return (completedBonuses + 1) * 12;
  }

  @override
  String toString() {
    return 'StarStatsModel(total: $totalStars, toNext: $starsToNextBonus, progress: ${progressPercentage.toStringAsFixed(1)}%)';
  }
}
