import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../../../core/services/auth_service.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../../onboarding/presentation/pages/onboarding_page.dart';
import '../../../dashboard/presentation/pages/dashboard_page.dart';
import '../pages/login_page.dart';

/// Authentication wrapper that handles routing based on auth and onboarding status
class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return StreamBuilder<User?>(
      stream: AuthService.authStateChanges,
      builder: (context, snapshot) {
        // Loading state
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Scaffold(
            body: LoadingWidget(message: 'Checking authentication...'),
          );
        }

        // Not authenticated - show login
        if (!snapshot.hasData || snapshot.data == null) {
          return const LoginPage();
        }

        // Authenticated - check onboarding status
        final user = snapshot.data!;
        return FutureBuilder<bool>(
          future: AuthService.needsOnboarding(user.uid),
          builder: (context, onboardingSnapshot) {
            // Loading onboarding check
            if (onboardingSnapshot.connectionState == ConnectionState.waiting) {
              return const Scaffold(
                body: LoadingWidget(message: 'Loading your profile...'),
              );
            }

            // Error checking onboarding - default to onboarding
            if (onboardingSnapshot.hasError) {
              return const OnboardingPage();
            }

            // Needs onboarding
            if (onboardingSnapshot.data == true) {
              return const OnboardingPage();
            }

            // Onboarding complete - show dashboard
            return const DashboardPage(child: DashboardHomeTab());
          },
        );
      },
    );
  }
}

/// Provider for authentication state
final authStateProvider = StreamProvider<User?>((ref) {
  return AuthService.authStateChanges;
});

/// Provider for onboarding status
final onboardingStatusProvider = FutureProvider.family<bool, String>((
  ref,
  userId,
) async {
  return await AuthService.needsOnboarding(userId);
});

/// Combined auth and onboarding state provider
final authFlowStateProvider = Provider<AuthFlowState>((ref) {
  final authState = ref.watch(authStateProvider);

  return authState.when(
    data: (user) {
      if (user == null) {
        return const AuthFlowState.unauthenticated();
      }

      final onboardingState = ref.watch(onboardingStatusProvider(user.uid));
      return onboardingState.when(
        data: (needsOnboarding) {
          if (needsOnboarding) {
            return AuthFlowState.needsOnboarding(user);
          }
          return AuthFlowState.authenticated(user);
        },
        loading: () => AuthFlowState.loading(user),
        error: (error, stack) => AuthFlowState.needsOnboarding(user),
      );
    },
    loading: () => const AuthFlowState.loading(null),
    error: (error, stack) => const AuthFlowState.unauthenticated(),
  );
});

/// Authentication flow state
class AuthFlowState {
  final AuthFlowStatus status;
  final User? user;
  final String? error;

  const AuthFlowState._({required this.status, this.user, this.error});

  const AuthFlowState.unauthenticated()
    : this._(status: AuthFlowStatus.unauthenticated);

  const AuthFlowState.loading(User? user)
    : this._(status: AuthFlowStatus.loading, user: user);

  const AuthFlowState.needsOnboarding(User user)
    : this._(status: AuthFlowStatus.needsOnboarding, user: user);

  const AuthFlowState.authenticated(User user)
    : this._(status: AuthFlowStatus.authenticated, user: user);

  const AuthFlowState.error(String error)
    : this._(status: AuthFlowStatus.error, error: error);
}

/// Authentication flow status enum
enum AuthFlowStatus {
  unauthenticated,
  loading,
  needsOnboarding,
  authenticated,
  error,
}

/// Enhanced auth wrapper using providers
class EnhancedAuthWrapper extends ConsumerWidget {
  const EnhancedAuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final authFlowState = ref.watch(authFlowStateProvider);

    switch (authFlowState.status) {
      case AuthFlowStatus.unauthenticated:
        return const LoginPage();

      case AuthFlowStatus.loading:
        return Scaffold(
          body: LoadingWidget(
            message: authFlowState.user == null
                ? 'Checking authentication...'
                : 'Loading your profile...',
          ),
        );

      case AuthFlowStatus.needsOnboarding:
        return const OnboardingPage();

      case AuthFlowStatus.authenticated:
        return const DashboardPage(child: DashboardHomeTab());

      case AuthFlowStatus.error:
        return Scaffold(
          body: Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                const Icon(Icons.error_outline, size: 64, color: Colors.red),
                const SizedBox(height: 16),
                Text(
                  'Authentication Error',
                  style: Theme.of(context).textTheme.headlineSmall,
                ),
                const SizedBox(height: 8),
                Text(
                  authFlowState.error ?? 'An unknown error occurred',
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 24),
                ElevatedButton(
                  onPressed: () {
                    ref.invalidate(authFlowStateProvider);
                  },
                  child: const Text('Retry'),
                ),
              ],
            ),
          ),
        );
    }
  }
}
