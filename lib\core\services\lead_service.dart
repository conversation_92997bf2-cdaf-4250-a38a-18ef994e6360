import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/lead_model.dart';
import '../models/agent_favorite_model.dart';
import '../constants/app_constants.dart';

/// Service for managing leads and agent-property interactions
class LeadService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Create a new lead
  static Future<LeadResult> createLead(LeadModel lead) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.leadsCollection)
          .add(lead.toFirestore());

      final createdLead = lead.copyWith(id: docRef.id);
      
      // Update with the generated ID
      await docRef.update({'id': docRef.id});

      // Update agent portfolio metrics
      await _updateAgentPortfolioMetrics(lead.agentId, lead.propertyId, 'lead_created');

      return LeadResult.success(createdLead);
    } catch (e) {
      print('Error creating lead: $e');
      return LeadResult.failure('Failed to create lead: ${e.toString()}');
    }
  }

  /// Get leads by agent
  static Future<List<LeadModel>> getLeadsByAgent(String agentId, {
    String? status,
    String? priority,
    int? limit,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.leadsCollection)
          .where('agentId', isEqualTo: agentId)
          .orderBy('updatedAt', descending: true);

      if (status != null) {
        query = query.where('status', isEqualTo: status);
      }

      if (priority != null) {
        query = query.where('priority', isEqualTo: priority);
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => LeadModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting leads by agent: $e');
      return [];
    }
  }

  /// Get leads by property
  static Future<List<LeadModel>> getLeadsByProperty(String propertyId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.leadsCollection)
          .where('propertyId', isEqualTo: propertyId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => LeadModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting leads by property: $e');
      return [];
    }
  }

  /// Update lead status
  static Future<LeadResult> updateLeadStatus(String leadId, String status, {
    String? notes,
    DateTime? followUpDate,
  }) async {
    try {
      final updateData = {
        'status': status,
        'updatedAt': Timestamp.now(),
      };

      if (notes != null) {
        updateData['notes'] = notes;
      }

      if (followUpDate != null) {
        updateData['followUpDate'] = Timestamp.fromDate(followUpDate);
      }

      if (status == 'contacted') {
        updateData['lastContactedAt'] = Timestamp.now();
      }

      await _firestore
          .collection(AppConstants.leadsCollection)
          .doc(leadId)
          .update(updateData);

      return LeadResult.success(null);
    } catch (e) {
      print('Error updating lead status: $e');
      return LeadResult.failure('Failed to update lead: ${e.toString()}');
    }
  }

  /// Add interaction to lead
  static Future<LeadResult> addLeadInteraction(LeadInteractionModel interaction) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.leadInteractionsCollection)
          .add(interaction.toFirestore());

      // Update lead with interaction ID
      await _firestore
          .collection(AppConstants.leadsCollection)
          .doc(interaction.leadId)
          .update({
        'interactions': FieldValue.arrayUnion([docRef.id]),
        'lastContactedAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return LeadResult.success(null);
    } catch (e) {
      print('Error adding lead interaction: $e');
      return LeadResult.failure('Failed to add interaction: ${e.toString()}');
    }
  }

  /// Get lead interactions
  static Future<List<LeadInteractionModel>> getLeadInteractions(String leadId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.leadInteractionsCollection)
          .where('leadId', isEqualTo: leadId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => LeadInteractionModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting lead interactions: $e');
      return [];
    }
  }

  /// Add property to agent favorites
  static Future<LeadResult> addToFavorites(AgentFavoriteModel favorite) async {
    try {
      // Check if already in favorites
      final existingQuery = await _firestore
          .collection(AppConstants.agentFavoritesCollection)
          .where('agentId', isEqualTo: favorite.agentId)
          .where('propertyId', isEqualTo: favorite.propertyId)
          .get();

      if (existingQuery.docs.isNotEmpty) {
        return LeadResult.failure('Property already in favorites');
      }

      await _firestore
          .collection(AppConstants.agentFavoritesCollection)
          .add(favorite.toFirestore());

      return LeadResult.success(null);
    } catch (e) {
      print('Error adding to favorites: $e');
      return LeadResult.failure('Failed to add to favorites: ${e.toString()}');
    }
  }

  /// Remove property from agent favorites
  static Future<LeadResult> removeFromFavorites(String agentId, String propertyId) async {
    try {
      final query = await _firestore
          .collection(AppConstants.agentFavoritesCollection)
          .where('agentId', isEqualTo: agentId)
          .where('propertyId', isEqualTo: propertyId)
          .get();

      for (final doc in query.docs) {
        await doc.reference.delete();
      }

      return LeadResult.success(null);
    } catch (e) {
      print('Error removing from favorites: $e');
      return LeadResult.failure('Failed to remove from favorites: ${e.toString()}');
    }
  }

  /// Get agent favorites
  static Future<List<AgentFavoriteModel>> getAgentFavorites(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.agentFavoritesCollection)
          .where('agentId', isEqualTo: agentId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => AgentFavoriteModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting agent favorites: $e');
      return [];
    }
  }

  /// Check if property is in agent favorites
  static Future<bool> isPropertyInFavorites(String agentId, String propertyId) async {
    try {
      final query = await _firestore
          .collection(AppConstants.agentFavoritesCollection)
          .where('agentId', isEqualTo: agentId)
          .where('propertyId', isEqualTo: propertyId)
          .limit(1)
          .get();

      return query.docs.isNotEmpty;
    } catch (e) {
      print('Error checking favorites: $e');
      return false;
    }
  }

  /// Get agent property portfolio
  static Future<List<AgentPropertyPortfolioModel>> getAgentPortfolio(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.agentPortfolioCollection)
          .where('agentId', isEqualTo: agentId)
          .orderBy('assignedAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => AgentPropertyPortfolioModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting agent portfolio: $e');
      return [];
    }
  }

  /// Record property sharing
  static Future<LeadResult> recordPropertySharing(PropertySharingModel sharing) async {
    try {
      await _firestore
          .collection(AppConstants.propertySharingCollection)
          .add(sharing.toFirestore());

      // Update agent portfolio metrics
      await _updateAgentPortfolioMetrics(sharing.agentId, sharing.propertyId, 'shared');

      return LeadResult.success(null);
    } catch (e) {
      print('Error recording property sharing: $e');
      return LeadResult.failure('Failed to record sharing: ${e.toString()}');
    }
  }

  /// Get property sharing analytics
  static Future<List<PropertySharingModel>> getPropertySharingAnalytics(
    String agentId, {
    String? propertyId,
    DateTime? startDate,
    DateTime? endDate,
  }) async {
    try {
      Query query = _firestore
          .collection(AppConstants.propertySharingCollection)
          .where('agentId', isEqualTo: agentId);

      if (propertyId != null) {
        query = query.where('propertyId', isEqualTo: propertyId);
      }

      if (startDate != null) {
        query = query.where('sharedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate));
      }

      if (endDate != null) {
        query = query.where('sharedAt', isLessThanOrEqualTo: Timestamp.fromDate(endDate));
      }

      final snapshot = await query.orderBy('sharedAt', descending: true).get();
      return snapshot.docs.map((doc) => PropertySharingModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting sharing analytics: $e');
      return [];
    }
  }

  /// Update agent portfolio metrics
  static Future<void> _updateAgentPortfolioMetrics(
    String agentId, 
    String propertyId, 
    String action,
  ) async {
    try {
      final portfolioQuery = await _firestore
          .collection(AppConstants.agentPortfolioCollection)
          .where('agentId', isEqualTo: agentId)
          .where('propertyId', isEqualTo: propertyId)
          .limit(1)
          .get();

      if (portfolioQuery.docs.isNotEmpty) {
        final doc = portfolioQuery.docs.first;
        final updateData = <String, dynamic>{
          'lastActivityAt': Timestamp.now(),
        };

        switch (action) {
          case 'lead_created':
            updateData['leadsCount'] = FieldValue.increment(1);
            updateData['status'] = 'leads_generated';
            break;
          case 'shared':
            updateData['sharesCount'] = FieldValue.increment(1);
            if (doc.data()['status'] == 'assigned') {
              updateData['status'] = 'promoting';
            }
            break;
          case 'viewed':
            updateData['viewsCount'] = FieldValue.increment(1);
            break;
        }

        await doc.reference.update(updateData);
      }
    } catch (e) {
      print('Error updating portfolio metrics: $e');
    }
  }

  /// Get lead statistics for agent
  static Future<Map<String, dynamic>> getLeadStatistics(String agentId) async {
    try {
      final leads = await getLeadsByAgent(agentId);
      
      final stats = <String, dynamic>{
        'totalLeads': leads.length,
        'newLeads': leads.where((l) => l.status == 'new').length,
        'contactedLeads': leads.where((l) => l.status == 'contacted').length,
        'interestedLeads': leads.where((l) => l.status == 'interested').length,
        'convertedLeads': leads.where((l) => l.status == 'converted').length,
        'hotLeads': leads.where((l) => l.isHotLead).length,
        'followUpNeeded': leads.where((l) => l.needsFollowUp).length,
        'conversionRate': leads.isNotEmpty 
            ? (leads.where((l) => l.status == 'converted').length / leads.length) * 100 
            : 0.0,
      };

      return stats;
    } catch (e) {
      print('Error getting lead statistics: $e');
      return {};
    }
  }
}

/// Result class for lead operations
class LeadResult {
  final bool isSuccess;
  final String message;
  final LeadModel? lead;

  const LeadResult._({
    required this.isSuccess,
    required this.message,
    this.lead,
  });

  factory LeadResult.success(LeadModel? lead) {
    return LeadResult._(
      isSuccess: true,
      message: 'Operation completed successfully',
      lead: lead,
    );
  }

  factory LeadResult.failure(String message) {
    return LeadResult._(
      isSuccess: false,
      message: message,
    );
  }
}
