import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/models/network_node.dart';
import '../../../../core/services/mlm_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

/// Network tree provider for current user
final networkTreeProvider = FutureProvider.autoDispose<NetworkNode?>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return null;
  
  return await MLMService.getUserNetworkTree(currentUser.id);
});

/// Direct downlines provider
final directDownlinesProvider = FutureProvider.autoDispose<List<UserModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  return await MLMService.getDirectDownlines(currentUser.id);
});

/// Upline chain provider
final uplineChainProvider = FutureProvider.autoDispose<List<UserModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  return await MLMService.getUserUplineChain(currentUser.id);
});

/// Network performance provider
final networkPerformanceProvider = FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return {};
  
  return await MLMService.getNetworkPerformance(currentUser.id);
});

/// Top performers provider
final topPerformersProvider = FutureProvider.autoDispose.family<List<UserModel>, TopPerformersParams>(
  (ref, params) async {
    final currentUser = ref.watch(currentUserProvider);
    if (currentUser == null) return [];
    
    return await MLMService.getTopPerformers(
      currentUser.id,
      limit: params.limit,
      sortBy: params.sortBy,
    );
  },
);

/// Recent activities provider
final recentActivitiesProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  return await MLMService.getRecentActivities(currentUser.id);
});

/// Network search provider
final networkSearchProvider = StateNotifierProvider<NetworkSearchNotifier, NetworkSearchState>((ref) {
  return NetworkSearchNotifier(ref);
});

/// Network search state
class NetworkSearchState {
  final String query;
  final List<UserModel> results;
  final bool isLoading;
  final String? error;

  const NetworkSearchState({
    this.query = '',
    this.results = const [],
    this.isLoading = false,
    this.error,
  });

  NetworkSearchState copyWith({
    String? query,
    List<UserModel>? results,
    bool? isLoading,
    String? error,
  }) {
    return NetworkSearchState(
      query: query ?? this.query,
      results: results ?? this.results,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Network search notifier
class NetworkSearchNotifier extends StateNotifier<NetworkSearchState> {
  final Ref ref;

  NetworkSearchNotifier(this.ref) : super(const NetworkSearchState());

  /// Search network users
  Future<void> searchUsers(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(query: '', results: [], error: null);
      return;
    }

    state = state.copyWith(query: query, isLoading: true, error: null);

    try {
      final currentUser = ref.read(currentUserProvider);
      if (currentUser == null) {
        state = state.copyWith(
          isLoading: false,
          error: 'User not authenticated',
        );
        return;
      }

      final results = await MLMService.searchNetworkUsers(currentUser.id, query);
      state = state.copyWith(
        results: results,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Search failed: ${e.toString()}',
      );
    }
  }

  /// Clear search
  void clearSearch() {
    state = const NetworkSearchState();
  }
}

/// Network view mode provider
final networkViewModeProvider = StateProvider<NetworkViewMode>((ref) {
  return NetworkViewMode.tree;
});

/// Network view modes
enum NetworkViewMode {
  tree,
  list,
  grid,
  chart,
}

/// Network filter provider
final networkFilterProvider = StateNotifierProvider<NetworkFilterNotifier, NetworkFilterState>((ref) {
  return NetworkFilterNotifier();
});

/// Network filter state
class NetworkFilterState {
  final int? selectedLevel;
  final bool showActiveOnly;
  final String sortBy;
  final bool sortAscending;

  const NetworkFilterState({
    this.selectedLevel,
    this.showActiveOnly = true,
    this.sortBy = 'name',
    this.sortAscending = true,
  });

  NetworkFilterState copyWith({
    int? selectedLevel,
    bool? showActiveOnly,
    String? sortBy,
    bool? sortAscending,
  }) {
    return NetworkFilterState(
      selectedLevel: selectedLevel ?? this.selectedLevel,
      showActiveOnly: showActiveOnly ?? this.showActiveOnly,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

/// Network filter notifier
class NetworkFilterNotifier extends StateNotifier<NetworkFilterState> {
  NetworkFilterNotifier() : super(const NetworkFilterState());

  void setLevel(int? level) {
    state = state.copyWith(selectedLevel: level);
  }

  void setShowActiveOnly(bool showActiveOnly) {
    state = state.copyWith(showActiveOnly: showActiveOnly);
  }

  void setSortBy(String sortBy) {
    state = state.copyWith(sortBy: sortBy);
  }

  void setSortAscending(bool ascending) {
    state = state.copyWith(sortAscending: ascending);
  }

  void resetFilters() {
    state = const NetworkFilterState();
  }
}

/// Filtered network data provider
final filteredNetworkDataProvider = FutureProvider.autoDispose<List<UserModel>>((ref) async {
  final networkTree = await ref.watch(networkTreeProvider.future);
  final filter = ref.watch(networkFilterProvider);
  
  if (networkTree == null) return [];

  // Get all nodes from the tree
  final allNodes = networkTree.getDescendants();
  var users = allNodes.map((node) => node.user).toList();

  // Apply filters
  if (filter.selectedLevel != null) {
    users = users.where((user) => user.level == filter.selectedLevel).toList();
  }

  if (filter.showActiveOnly) {
    users = users.where((user) => user.isActive).toList();
  }

  // Apply sorting
  users.sort((a, b) {
    int comparison;
    switch (filter.sortBy) {
      case 'level':
        comparison = a.level.compareTo(b.level);
        break;
      case 'stars':
        comparison = a.totalStars.compareTo(b.totalStars);
        break;
      case 'commissions':
        comparison = a.totalCommissions.compareTo(b.totalCommissions);
        break;
      case 'joinDate':
        comparison = a.createdAt.compareTo(b.createdAt);
        break;
      case 'name':
      default:
        comparison = a.name.compareTo(b.name);
        break;
    }
    return filter.sortAscending ? comparison : -comparison;
  });

  return users;
});

/// Parameters for top performers
class TopPerformersParams {
  final int limit;
  final String sortBy;

  const TopPerformersParams({
    this.limit = 10,
    this.sortBy = 'totalStars',
  });

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TopPerformersParams &&
        other.limit == limit &&
        other.sortBy == sortBy;
  }

  @override
  int get hashCode => Object.hash(limit, sortBy);
}

/// Network statistics provider
final networkStatsProvider = FutureProvider.autoDispose<NetworkStats?>((ref) async {
  final networkTree = await ref.watch(networkTreeProvider.future);
  return networkTree?.stats;
});

/// Network levels provider (for dropdown filters)
final networkLevelsProvider = FutureProvider.autoDispose<List<int>>((ref) async {
  final networkTree = await ref.watch(networkTreeProvider.future);
  if (networkTree == null) return [];

  final levels = <int>{};
  final allNodes = networkTree.getDescendants();
  
  for (final node in allNodes) {
    levels.add(node.user.level);
  }

  final levelList = levels.toList()..sort();
  return levelList;
});
