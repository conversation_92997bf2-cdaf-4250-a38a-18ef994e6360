import 'package:flutter/material.dart';

import '../../../../shared/themes/app_theme.dart';

/// Search bar for filtering agents in referral management
class ReferralSearchBar extends StatefulWidget {
  final String searchQuery;
  final Function(String) onSearchChanged;

  const ReferralSearchBar({
    super.key,
    required this.searchQuery,
    required this.onSearchChanged,
  });

  @override
  State<ReferralSearchBar> createState() => _ReferralSearchBarState();
}

class _ReferralSearchBarState extends State<ReferralSearchBar> {
  late TextEditingController _controller;
  late FocusNode _focusNode;

  @override
  void initState() {
    super.initState();
    _controller = TextEditingController(text: widget.searchQuery);
    _focusNode = FocusNode();
  }

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: AppTheme.createCardDecoration(),
      child: Text<PERSON>ield(
        controller: _controller,
        focusNode: _focusNode,
        style: TextStyle(
          color: AppTheme.darkPrimaryText,
          fontSize: 16,
        ),
        decoration: InputDecoration(
          hintText: 'Search agents by name, email, or referral code...',
          hintStyle: TextStyle(
            color: AppTheme.darkHintText,
            fontSize: 16,
          ),
          prefixIcon: Container(
            margin: const EdgeInsets.all(12),
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(8),
            ),
            child: const Icon(
              Icons.search,
              color: Colors.white,
              size: 20,
            ),
          ),
          suffixIcon: widget.searchQuery.isNotEmpty
              ? IconButton(
                  onPressed: () {
                    _controller.clear();
                    widget.onSearchChanged('');
                  },
                  icon: Icon(
                    Icons.clear,
                    color: AppTheme.darkSecondaryText,
                  ),
                )
              : null,
          border: InputBorder.none,
          contentPadding: const EdgeInsets.symmetric(
            horizontal: 16,
            vertical: 16,
          ),
        ),
        onChanged: widget.onSearchChanged,
      ),
    );
  }
}
