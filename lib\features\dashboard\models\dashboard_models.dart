import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../commissions/models/commission_enums.dart';

/// Agent dashboard data model
class AgentDashboardData {
  final String agentId;
  final String agentName;
  final String agentEmail;
  final CommissionTier currentTier;
  final DashboardMetrics metrics;
  final List<DashboardGoal> goals;
  final List<RecentActivity> recentActivities;
  final List<DashboardNotification> notifications;
  final TeamOverview teamOverview;
  final PerformanceInsights insights;
  final Map<String, dynamic> preferences;

  const AgentDashboardData({
    required this.agentId,
    required this.agentName,
    required this.agentEmail,
    required this.currentTier,
    required this.metrics,
    required this.goals,
    required this.recentActivities,
    required this.notifications,
    required this.teamOverview,
    required this.insights,
    this.preferences = const {},
  });

  /// Copy with modifications
  AgentDashboardData copyWith({
    String? agentId,
    String? agentName,
    String? agentEmail,
    CommissionTier? currentTier,
    DashboardMetrics? metrics,
    List<DashboardGoal>? goals,
    List<RecentActivity>? recentActivities,
    List<DashboardNotification>? notifications,
    TeamOverview? teamOverview,
    PerformanceInsights? insights,
    Map<String, dynamic>? preferences,
  }) {
    return AgentDashboardData(
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      agentEmail: agentEmail ?? this.agentEmail,
      currentTier: currentTier ?? this.currentTier,
      metrics: metrics ?? this.metrics,
      goals: goals ?? this.goals,
      recentActivities: recentActivities ?? this.recentActivities,
      notifications: notifications ?? this.notifications,
      teamOverview: teamOverview ?? this.teamOverview,
      insights: insights ?? this.insights,
      preferences: preferences ?? this.preferences,
    );
  }
}

/// Dashboard metrics
class DashboardMetrics {
  final double totalSales;
  final double totalCommissions;
  final double monthlyTarget;
  final double monthlyProgress;
  final int propertiesSold;
  final int teamSize;
  final double conversionRate;
  final double growthRate;
  final Map<String, double> monthlyData;

  const DashboardMetrics({
    required this.totalSales,
    required this.totalCommissions,
    required this.monthlyTarget,
    required this.monthlyProgress,
    required this.propertiesSold,
    required this.teamSize,
    required this.conversionRate,
    required this.growthRate,
    this.monthlyData = const {},
  });

  /// Get target completion percentage
  double get targetCompletionPercentage {
    if (monthlyTarget <= 0) return 0;
    return (monthlyProgress / monthlyTarget * 100).clamp(0, 100);
  }

  /// Get formatted monthly target
  String get formattedMonthlyTarget {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(monthlyTarget);
  }

  /// Get formatted monthly progress
  String get formattedMonthlyProgress {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(monthlyProgress);
  }

  /// Get formatted total sales
  String get formattedTotalSales {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(totalSales);
  }

  /// Get formatted total commissions
  String get formattedTotalCommissions {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(totalCommissions);
  }
}

/// Dashboard goal
class DashboardGoal {
  final String id;
  final String title;
  final String description;
  final GoalType type;
  final double targetValue;
  final double currentValue;
  final DateTime deadline;
  final GoalStatus status;
  final String? reward;
  final DateTime createdAt;

  const DashboardGoal({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.targetValue,
    required this.currentValue,
    required this.deadline,
    required this.status,
    this.reward,
    required this.createdAt,
  });

  /// Get progress percentage
  double get progressPercentage {
    if (targetValue <= 0) return 0;
    return (currentValue / targetValue * 100).clamp(0, 100);
  }

  /// Get days remaining
  int get daysRemaining {
    return deadline.difference(DateTime.now()).inDays;
  }

  /// Check if goal is overdue
  bool get isOverdue {
    return DateTime.now().isAfter(deadline) && status != GoalStatus.completed;
  }

  /// Get progress color
  Color get progressColor {
    if (status == GoalStatus.completed) return const Color(0xFF4CAF50);
    if (isOverdue) return const Color(0xFFF44336);
    if (progressPercentage >= 80) return const Color(0xFF4CAF50);
    if (progressPercentage >= 50) return const Color(0xFFFF9800);
    return const Color(0xFF2196F3);
  }
}

/// Goal types
enum GoalType {
  sales,
  commissions,
  recruitment,
  properties,
  custom,
}

extension GoalTypeExtension on GoalType {
  String get displayName {
    switch (this) {
      case GoalType.sales:
        return 'Sales Target';
      case GoalType.commissions:
        return 'Commission Goal';
      case GoalType.recruitment:
        return 'Recruitment Goal';
      case GoalType.properties:
        return 'Properties Goal';
      case GoalType.custom:
        return 'Custom Goal';
    }
  }

  IconData get icon {
    switch (this) {
      case GoalType.sales:
        return Icons.trending_up;
      case GoalType.commissions:
        return Icons.account_balance_wallet;
      case GoalType.recruitment:
        return Icons.person_add;
      case GoalType.properties:
        return Icons.home;
      case GoalType.custom:
        return Icons.flag;
    }
  }
}

/// Goal status
enum GoalStatus {
  active,
  completed,
  paused,
  cancelled,
}

extension GoalStatusExtension on GoalStatus {
  String get displayName {
    switch (this) {
      case GoalStatus.active:
        return 'Active';
      case GoalStatus.completed:
        return 'Completed';
      case GoalStatus.paused:
        return 'Paused';
      case GoalStatus.cancelled:
        return 'Cancelled';
    }
  }

  Color get color {
    switch (this) {
      case GoalStatus.active:
        return const Color(0xFF2196F3);
      case GoalStatus.completed:
        return const Color(0xFF4CAF50);
      case GoalStatus.paused:
        return const Color(0xFFFF9800);
      case GoalStatus.cancelled:
        return const Color(0xFF9E9E9E);
    }
  }
}

/// Recent activity
class RecentActivity {
  final String id;
  final ActivityType type;
  final String title;
  final String description;
  final DateTime timestamp;
  final Map<String, dynamic> metadata;

  const RecentActivity({
    required this.id,
    required this.type,
    required this.title,
    required this.description,
    required this.timestamp,
    this.metadata = const {},
  });

  /// Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Activity types
enum ActivityType {
  propertySold,
  commissionEarned,
  agentReferred,
  goalAchieved,
  tierUpgraded,
  teamGrowth,
  training,
  meeting,
}

extension ActivityTypeExtension on ActivityType {
  String get displayName {
    switch (this) {
      case ActivityType.propertySold:
        return 'Property Sold';
      case ActivityType.commissionEarned:
        return 'Commission Earned';
      case ActivityType.agentReferred:
        return 'Agent Referred';
      case ActivityType.goalAchieved:
        return 'Goal Achieved';
      case ActivityType.tierUpgraded:
        return 'Tier Upgraded';
      case ActivityType.teamGrowth:
        return 'Team Growth';
      case ActivityType.training:
        return 'Training';
      case ActivityType.meeting:
        return 'Meeting';
    }
  }

  IconData get icon {
    switch (this) {
      case ActivityType.propertySold:
        return Icons.home_filled;
      case ActivityType.commissionEarned:
        return Icons.account_balance_wallet;
      case ActivityType.agentReferred:
        return Icons.person_add;
      case ActivityType.goalAchieved:
        return Icons.emoji_events;
      case ActivityType.tierUpgraded:
        return Icons.upgrade;
      case ActivityType.teamGrowth:
        return Icons.group_add;
      case ActivityType.training:
        return Icons.school;
      case ActivityType.meeting:
        return Icons.meeting_room;
    }
  }

  Color get color {
    switch (this) {
      case ActivityType.propertySold:
        return const Color(0xFF4CAF50);
      case ActivityType.commissionEarned:
        return const Color(0xFFFF9800);
      case ActivityType.agentReferred:
        return const Color(0xFF2196F3);
      case ActivityType.goalAchieved:
        return const Color(0xFFFFD700);
      case ActivityType.tierUpgraded:
        return const Color(0xFF9C27B0);
      case ActivityType.teamGrowth:
        return const Color(0xFF607D8B);
      case ActivityType.training:
        return const Color(0xFF795548);
      case ActivityType.meeting:
        return const Color(0xFF3F51B5);
    }
  }
}

/// Dashboard notification
class DashboardNotification {
  final String id;
  final NotificationType type;
  final String title;
  final String message;
  final DateTime timestamp;
  final bool isRead;
  final NotificationPriority priority;
  final Map<String, dynamic> actionData;

  const DashboardNotification({
    required this.id,
    required this.type,
    required this.title,
    required this.message,
    required this.timestamp,
    this.isRead = false,
    this.priority = NotificationPriority.normal,
    this.actionData = const {},
  });

  /// Get time ago string
  String get timeAgo {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays}d ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours}h ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes}m ago';
    } else {
      return 'Just now';
    }
  }
}

/// Notification types
enum NotificationType {
  commission,
  goal,
  team,
  system,
  promotion,
  reminder,
}

extension NotificationTypeExtension on NotificationType {
  IconData get icon {
    switch (this) {
      case NotificationType.commission:
        return Icons.account_balance_wallet;
      case NotificationType.goal:
        return Icons.flag;
      case NotificationType.team:
        return Icons.group;
      case NotificationType.system:
        return Icons.info;
      case NotificationType.promotion:
        return Icons.campaign;
      case NotificationType.reminder:
        return Icons.alarm;
    }
  }

  Color get color {
    switch (this) {
      case NotificationType.commission:
        return const Color(0xFF4CAF50);
      case NotificationType.goal:
        return const Color(0xFF2196F3);
      case NotificationType.team:
        return const Color(0xFF9C27B0);
      case NotificationType.system:
        return const Color(0xFF607D8B);
      case NotificationType.promotion:
        return const Color(0xFFFF9800);
      case NotificationType.reminder:
        return const Color(0xFFF44336);
    }
  }
}

/// Notification priority
enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}

/// Team overview
class TeamOverview {
  final int totalMembers;
  final int activeMembers;
  final int newMembersThisMonth;
  final double teamSales;
  final double teamCommissions;
  final List<TeamMember> topPerformers;

  const TeamOverview({
    required this.totalMembers,
    required this.activeMembers,
    required this.newMembersThisMonth,
    required this.teamSales,
    required this.teamCommissions,
    required this.topPerformers,
  });

  /// Get team activity rate
  double get activityRate {
    if (totalMembers == 0) return 0;
    return (activeMembers / totalMembers) * 100;
  }
}

/// Team member
class TeamMember {
  final String id;
  final String name;
  final String? profileImageUrl;
  final double sales;
  final CommissionTier tier;
  final int level;

  const TeamMember({
    required this.id,
    required this.name,
    this.profileImageUrl,
    required this.sales,
    required this.tier,
    required this.level,
  });
}

/// Performance insights
class PerformanceInsights {
  final List<Insight> insights;
  final List<Recommendation> recommendations;
  final PerformanceTrend trend;

  const PerformanceInsights({
    required this.insights,
    required this.recommendations,
    required this.trend,
  });
}

/// Individual insight
class Insight {
  final String title;
  final String description;
  final InsightType type;
  final double impact;

  const Insight({
    required this.title,
    required this.description,
    required this.type,
    required this.impact,
  });
}

/// Insight types
enum InsightType {
  positive,
  negative,
  neutral,
  opportunity,
}

/// Recommendation
class Recommendation {
  final String title;
  final String description;
  final RecommendationType type;
  final int priority;

  const Recommendation({
    required this.title,
    required this.description,
    required this.type,
    required this.priority,
  });
}

/// Recommendation types
enum RecommendationType {
  training,
  networking,
  marketing,
  strategy,
  goal,
}

/// Performance trend
enum PerformanceTrend {
  improving,
  stable,
  declining,
}

extension PerformanceTrendExtension on PerformanceTrend {
  String get displayName {
    switch (this) {
      case PerformanceTrend.improving:
        return 'Improving';
      case PerformanceTrend.stable:
        return 'Stable';
      case PerformanceTrend.declining:
        return 'Declining';
    }
  }

  IconData get icon {
    switch (this) {
      case PerformanceTrend.improving:
        return Icons.trending_up;
      case PerformanceTrend.stable:
        return Icons.trending_flat;
      case PerformanceTrend.declining:
        return Icons.trending_down;
    }
  }

  Color get color {
    switch (this) {
      case PerformanceTrend.improving:
        return const Color(0xFF4CAF50);
      case PerformanceTrend.stable:
        return const Color(0xFF2196F3);
      case PerformanceTrend.declining:
        return const Color(0xFFF44336);
    }
  }
}
