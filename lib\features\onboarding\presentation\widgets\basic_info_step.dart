import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../models/onboarding_data.dart';
import '../../providers/onboarding_provider.dart';

/// Basic information collection step in onboarding
class BasicInfoStep extends ConsumerStatefulWidget {
  const BasicInfoStep({super.key});

  @override
  ConsumerState<BasicInfoStep> createState() => _BasicInfoStepState();
}

class _BasicInfoStepState extends ConsumerState<BasicInfoStep> {
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _referralCodeController = TextEditingController();
  final _nameFocusNode = FocusNode();
  final _phoneFocusNode = FocusNode();
  final _referralCodeFocusNode = FocusNode();

  @override
  void initState() {
    super.initState();
    
    // Initialize controllers with existing data
    final data = ref.read(onboardingProvider).data;
    _nameController.text = data.name ?? '';
    _phoneController.text = data.phoneNumber ?? '';
    _referralCodeController.text = data.referralCode ?? '';
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _referralCodeController.dispose();
    _nameFocusNode.dispose();
    _phoneFocusNode.dispose();
    _referralCodeFocusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingProvider);
    final data = onboardingState.data;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Text(
            'Basic Information',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Please provide your basic details to set up your agent profile.',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 16,
            ),
          ),
          const SizedBox(height: 32),

          // Name Field
          _buildInputField(
            label: 'Full Name',
            hint: 'Enter your full name',
            controller: _nameController,
            focusNode: _nameFocusNode,
            icon: Icons.person,
            isRequired: true,
            errorText: data.nameError,
            isValid: data.isNameValid,
            onChanged: (value) {
              ref.read(onboardingProvider.notifier).updateName(value);
            },
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _phoneFocusNode.requestFocus(),
          ),

          const SizedBox(height: 24),

          // Phone Number Field
          _buildInputField(
            label: 'Phone Number',
            hint: '+91 98765 43210',
            controller: _phoneController,
            focusNode: _phoneFocusNode,
            icon: Icons.phone,
            isRequired: true,
            errorText: data.phoneError,
            isValid: data.isPhoneValid,
            keyboardType: TextInputType.phone,
            onChanged: (value) {
              ref.read(onboardingProvider.notifier).updatePhoneNumber(value);
            },
            textInputAction: TextInputAction.next,
            onSubmitted: (_) => _referralCodeFocusNode.requestFocus(),
          ),

          const SizedBox(height: 24),

          // Referral Code Field
          _buildReferralCodeField(context, onboardingState),

          const SizedBox(height: 32),

          // Info Card
          _buildInfoCard(),
        ],
      ),
    );
  }

  /// Build input field with validation
  Widget _buildInputField({
    required String label,
    required String hint,
    required TextEditingController controller,
    required FocusNode focusNode,
    required IconData icon,
    required bool isRequired,
    required Function(String) onChanged,
    String? errorText,
    bool isValid = false,
    TextInputType? keyboardType,
    TextInputAction? textInputAction,
    Function(String)? onSubmitted,
  }) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              label,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            if (isRequired) ...[
              const SizedBox(width: 4),
              Text(
                '*',
                style: TextStyle(
                  color: AppTheme.errorColor,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ],
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: AppTheme.createCardDecoration(),
          child: TextFormField(
            controller: controller,
            focusNode: focusNode,
            keyboardType: keyboardType,
            textInputAction: textInputAction,
            onChanged: onChanged,
            onFieldSubmitted: onSubmitted,
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 16,
            ),
            decoration: InputDecoration(
              hintText: hint,
              hintStyle: TextStyle(
                color: AppTheme.darkHintText,
                fontSize: 16,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  icon,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              suffixIcon: isValid
                  ? Icon(
                      Icons.check_circle,
                      color: AppTheme.successColor,
                    )
                  : null,
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              errorText: errorText,
              errorStyle: TextStyle(
                color: AppTheme.errorColor,
                fontSize: 12,
              ),
            ),
          ),
        ),
      ],
    );
  }

  /// Build referral code field with verification
  Widget _buildReferralCodeField(BuildContext context, OnboardingState state) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Row(
          children: [
            Text(
              'Referral Code',
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
            const SizedBox(width: 8),
            Text(
              '(Optional)',
              style: TextStyle(
                color: AppTheme.darkSecondaryText,
                fontSize: 14,
                fontStyle: FontStyle.italic,
              ),
            ),
          ],
        ),
        const SizedBox(height: 8),
        Container(
          decoration: AppTheme.createCardDecoration(),
          child: TextFormField(
            controller: _referralCodeController,
            focusNode: _referralCodeFocusNode,
            textCapitalization: TextCapitalization.characters,
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 16,
            ),
            decoration: InputDecoration(
              hintText: 'Enter referral code (optional)',
              hintStyle: TextStyle(
                color: AppTheme.darkHintText,
                fontSize: 16,
              ),
              prefixIcon: Container(
                margin: const EdgeInsets.all(12),
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.secondaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: const Icon(
                  Icons.group_add,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              suffixIcon: _buildReferralCodeSuffix(state),
              border: InputBorder.none,
              contentPadding: const EdgeInsets.symmetric(
                horizontal: 16,
                vertical: 16,
              ),
              errorText: state.data.referralCodeError,
              errorStyle: TextStyle(
                color: AppTheme.errorColor,
                fontSize: 12,
              ),
            ),
            onChanged: (value) {
              ref.read(onboardingProvider.notifier).updateReferralCode(value);
            },
          ),
        ),
        
        // Referral Code Owner Info
        if (state.referralCodeOwnerName != null) ...[
          const SizedBox(height: 8),
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: AppTheme.successColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(8),
              border: Border.all(
                color: AppTheme.successColor.withValues(alpha: 0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.check_circle,
                  color: AppTheme.successColor,
                  size: 20,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Referral by: ${state.referralCodeOwnerName}',
                    style: TextStyle(
                      color: AppTheme.successColor,
                      fontSize: 14,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ],
    );
  }

  /// Build referral code suffix icon
  Widget? _buildReferralCodeSuffix(OnboardingState state) {
    if (state.isReferralCodeVerifying) {
      return Container(
        padding: const EdgeInsets.all(12),
        child: SizedBox(
          width: 20,
          height: 20,
          child: CircularProgressIndicator(
            strokeWidth: 2,
            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.primaryColor),
          ),
        ),
      );
    }

    if (state.data.isReferralCodeValid && state.referralCodeOwnerName != null) {
      return Icon(
        Icons.check_circle,
        color: AppTheme.successColor,
      );
    }

    return null;
  }

  /// Build info card
  Widget _buildInfoCard() {
    return GradientWidgets.gradientCard(
      gradient: AppTheme.secondaryGradient,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                Icons.info_outline,
                color: Colors.white,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Important Information',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildInfoItem(
            '• Your name and phone number are required for account verification',
          ),
          _buildInfoItem(
            '• Referral code is optional - if not provided, you\'ll be assigned to admin',
          ),
          _buildInfoItem(
            '• All information can be updated later from your profile',
          ),
          _buildInfoItem(
            '• Your phone number will be used for important notifications',
          ),
        ],
      ),
    );
  }

  /// Build info item
  Widget _buildInfoItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Text(
        text,
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.9),
          fontSize: 14,
          height: 1.4,
        ),
      ),
    );
  }
}
