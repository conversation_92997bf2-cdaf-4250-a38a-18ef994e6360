import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'test_data_service.dart';

/// Service to create Firebase Authentication users for test data
class FirebaseAuthSetupService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Create Firebase Authentication users for all test agents
  static Future<void> createTestUsers() async {
    try {
      print('🔐 Creating Firebase Authentication users for test data...');

      final users = await TestDataService.getUsers();
      final successfulUsers = <String>[];
      final failedUsers = <String>[];

      for (final user in users) {
        try {
          final email = user['email'] as String;
          final password = 'agent123'; // Standard password for all test users

          print('Creating user: $email');

          // Create user in Firebase Authentication
          final userCredential = await _auth.createUserWithEmailAndPassword(
            email: email,
            password: password,
          );

          if (userCredential.user != null) {
            // Update user profile
            await userCredential.user!.updateDisplayName(
              user['name'] as String,
            );

            // Create user document in Firestore with proper timestamp handling
            final now = Timestamp.now();
            await _firestore
                .collection('users')
                .doc(userCredential.user!.uid)
                .set({
                  'id': user['id'],
                  'name': user['name'],
                  'email': user['email'],
                  'role': user['role'],
                  'level': user['level'],
                  'uplineId': user['uplineId'],
                  'phone': user['phone'],
                  'address': user['address'],
                  'joinedAt':
                      user['joinedAt'], // This should be a string from test data
                  'isActive': user['isActive'],
                  'totalStars': user['totalStars'],
                  'totalCommissions': user['totalCommissions'],
                  'profileImageUrl': user['profileImageUrl'],
                  'createdAt': now,
                  'updatedAt': now,
                });

            successfulUsers.add(email);
            print('✅ Created user: $email');
          }
        } catch (e) {
          failedUsers.add(user['email'] as String);
          print('❌ Failed to create user ${user['email']}: $e');

          // If user already exists, that's okay
          if (e.toString().contains('email-already-in-use')) {
            print('   (User already exists - this is okay)');
            successfulUsers.add(user['email'] as String);
          }
        }
      }

      print('\n📊 Firebase Authentication Setup Results:');
      print('✅ Successful: ${successfulUsers.length} users');
      print('❌ Failed: ${failedUsers.length} users');

      if (failedUsers.isNotEmpty) {
        print('Failed users: ${failedUsers.join(', ')}');
      }
    } catch (e) {
      print('❌ Error setting up Firebase Authentication: $e');
      rethrow;
    }
  }

  /// Delete all test users from Firebase Authentication
  static Future<void> deleteTestUsers() async {
    try {
      print('🗑️ Deleting test users from Firebase Authentication...');

      final users = await TestDataService.getUsers();
      int deletedCount = 0;

      for (final user in users) {
        try {
          final email = user['email'] as String;

          // Sign in as the user first (required to delete)
          try {
            final userCredential = await _auth.signInWithEmailAndPassword(
              email: email,
              password: 'agent123',
            );

            if (userCredential.user != null) {
              // Delete user document from Firestore
              await _firestore
                  .collection('users')
                  .doc(userCredential.user!.uid)
                  .delete();

              // Delete the user account
              await userCredential.user!.delete();
              deletedCount++;
              print('✅ Deleted user: $email');
            }
          } catch (e) {
            print('❌ Failed to delete user $email: $e');
          }
        } catch (e) {
          print('❌ Error processing user ${user['email']}: $e');
        }
      }

      print(
        '🗑️ Deleted $deletedCount test users from Firebase Authentication',
      );
    } catch (e) {
      print('❌ Error deleting test users: $e');
      rethrow;
    }
  }

  /// Check if test users exist in Firebase Authentication
  static Future<Map<String, bool>> checkTestUsersExist() async {
    final results = <String, bool>{};

    try {
      final users = await TestDataService.getUsers();

      for (final user in users) {
        final email = user['email'] as String;
        try {
          // Try to sign in to check if user exists
          await _auth.signInWithEmailAndPassword(
            email: email,
            password: 'agent123',
          );
          results[email] = true;
        } catch (e) {
          results[email] = false;
        }
      }
    } catch (e) {
      print('Error checking test users: $e');
    }

    return results;
  }

  /// Create a specific test user
  static Future<bool> createSingleTestUser(
    String email,
    String name,
    String role,
  ) async {
    try {
      final userCredential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: 'agent123',
      );

      if (userCredential.user != null) {
        await userCredential.user!.updateDisplayName(name);

        final now = Timestamp.now();
        await _firestore.collection('users').doc(userCredential.user!.uid).set({
          'name': name,
          'email': email,
          'role': role,
          'isActive': true,
          'level': role == 'regional_manager'
              ? 1
              : role == 'team_leader'
              ? 2
              : role == 'senior_agent'
              ? 3
              : 4,
          'totalStars': 0,
          'totalCommissions': 0.0,
          'joinedAt': DateTime.now().toIso8601String(),
          'createdAt': now,
          'updatedAt': now,
        });

        print('✅ Created single test user: $email');
        return true;
      }
    } catch (e) {
      print('❌ Failed to create user $email: $e');
    }

    return false;
  }

  /// Quick setup for essential test users
  static Future<void> createEssentialTestUsers() async {
    print('🚀 Creating essential test users...');

    final essentialUsers = [
      {
        'email': '<EMAIL>',
        'name': 'Rajesh Kumar',
        'role': 'regional_manager',
      },
      {
        'email': '<EMAIL>',
        'name': 'Amit Singh',
        'role': 'team_leader',
      },
      {
        'email': '<EMAIL>',
        'name': 'Senior Agent 1',
        'role': 'senior_agent',
      },
      {
        'email': '<EMAIL>',
        'name': 'Junior Agent 1',
        'role': 'junior_agent',
      },
    ];

    for (final user in essentialUsers) {
      await createSingleTestUser(user['email']!, user['name']!, user['role']!);
    }

    print('✅ Essential test users created!');
  }
}
