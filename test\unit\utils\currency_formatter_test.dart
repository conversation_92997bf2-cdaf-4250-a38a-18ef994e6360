import 'package:flutter_test/flutter_test.dart';
import 'package:rama_realty_mlm/core/utils/currency_formatter.dart';

void main() {
  group('CurrencyFormatter Tests', () {
    group('Indian Rupee Formatting', () {
      test('should format amounts in crores correctly', () {
        // Test cases for crore formatting
        expect(CurrencyFormatter.formatAmount(10000000), '₹1.0 Cr');
        expect(CurrencyFormatter.formatAmount(15000000), '₹1.5 Cr');
        expect(CurrencyFormatter.formatAmount(25000000), '₹2.5 Cr');
        expect(CurrencyFormatter.formatAmount(100000000), '₹10.0 Cr');
        expect(CurrencyFormatter.formatAmount(125000000), '₹12.5 Cr');
        expect(CurrencyFormatter.formatAmount(999999999), '₹100.0 Cr');
      });

      test('should format amounts in lakhs correctly', () {
        // Test cases for lakh formatting
        expect(CurrencyFormatter.formatAmount(100000), '₹1.0 L');
        expect(CurrencyFormatter.formatAmount(150000), '₹1.5 L');
        expect(CurrencyFormatter.formatAmount(250000), '₹2.5 L');
        expect(CurrencyFormatter.formatAmount(1000000), '₹10.0 L');
        expect(CurrencyFormatter.formatAmount(2500000), '₹25.0 L');
        expect(CurrencyFormatter.formatAmount(9999999), '₹100.0 L');
      });

      test('should format amounts in thousands correctly', () {
        // Test cases for thousand formatting
        expect(CurrencyFormatter.formatAmount(1000), '₹1.0 K');
        expect(CurrencyFormatter.formatAmount(1500), '₹1.5 K');
        expect(CurrencyFormatter.formatAmount(2500), '₹2.5 K');
        expect(CurrencyFormatter.formatAmount(10000), '₹10.0 K');
        expect(CurrencyFormatter.formatAmount(25000), '₹25.0 K');
        expect(CurrencyFormatter.formatAmount(99999), '₹100.0 K');
      });

      test('should format small amounts in rupees correctly', () {
        // Test cases for direct rupee formatting
        expect(CurrencyFormatter.formatAmount(0), '₹0');
        expect(CurrencyFormatter.formatAmount(1), '₹1');
        expect(CurrencyFormatter.formatAmount(50), '₹50');
        expect(CurrencyFormatter.formatAmount(100), '₹100');
        expect(CurrencyFormatter.formatAmount(500), '₹500');
        expect(CurrencyFormatter.formatAmount(999), '₹999');
      });

      test('should handle decimal amounts correctly', () {
        // Test cases for decimal amounts
        expect(CurrencyFormatter.formatAmount(1500.50), '₹1.5 K');
        expect(CurrencyFormatter.formatAmount(150000.75), '₹1.5 L');
        expect(CurrencyFormatter.formatAmount(15000000.25), '₹1.5 Cr');
        expect(CurrencyFormatter.formatAmount(999.99), '₹1000');
      });

      test('should handle negative amounts correctly', () {
        // Test cases for negative amounts
        expect(CurrencyFormatter.formatAmount(-1000), '-₹1.0 K');
        expect(CurrencyFormatter.formatAmount(-100000), '-₹1.0 L');
        expect(CurrencyFormatter.formatAmount(-10000000), '-₹1.0 Cr');
        expect(CurrencyFormatter.formatAmount(-500), '-₹500');
      });

      test('should handle very large amounts correctly', () {
        // Test cases for very large amounts
        expect(CurrencyFormatter.formatAmount(1000000000), '₹100.0 Cr');
        expect(CurrencyFormatter.formatAmount(10000000000), '₹1000.0 Cr');
        expect(CurrencyFormatter.formatAmount(100000000000), '₹10000.0 Cr');
      });
    });

    group('Compact Formatting', () {
      test('should format amounts compactly', () {
        expect(CurrencyFormatter.formatAmountCompact(1500), '₹1.5K');
        expect(CurrencyFormatter.formatAmountCompact(150000), '₹1.5L');
        expect(CurrencyFormatter.formatAmountCompact(15000000), '₹1.5Cr');
        expect(CurrencyFormatter.formatAmountCompact(500), '₹500');
      });
    });

    group('Detailed Formatting', () {
      test('should format amounts with detailed breakdown', () {
        expect(CurrencyFormatter.formatAmountDetailed(12345678), '₹1,23,45,678');
        expect(CurrencyFormatter.formatAmountDetailed(1000000), '₹10,00,000');
        expect(CurrencyFormatter.formatAmountDetailed(100000), '₹1,00,000');
        expect(CurrencyFormatter.formatAmountDetailed(10000), '₹10,000');
        expect(CurrencyFormatter.formatAmountDetailed(1000), '₹1,000');
        expect(CurrencyFormatter.formatAmountDetailed(100), '₹100');
      });
    });

    group('Commission Formatting', () {
      test('should format commission amounts correctly', () {
        // Test commission-specific formatting
        expect(CurrencyFormatter.formatCommission(50000), '₹50.0 K');
        expect(CurrencyFormatter.formatCommission(125000), '₹1.3 L');
        expect(CurrencyFormatter.formatCommission(2500000), '₹25.0 L');
        expect(CurrencyFormatter.formatCommission(5000), '₹5.0 K');
      });

      test('should format commission percentages correctly', () {
        expect(CurrencyFormatter.formatCommissionRate(0.05), '5.0%');
        expect(CurrencyFormatter.formatCommissionRate(0.02), '2.0%');
        expect(CurrencyFormatter.formatCommissionRate(0.01), '1.0%');
        expect(CurrencyFormatter.formatCommissionRate(0.005), '0.5%');
        expect(CurrencyFormatter.formatCommissionRate(0.002), '0.2%');
      });
    });

    group('Property Price Formatting', () {
      test('should format property prices correctly', () {
        // Typical property price ranges in India
        expect(CurrencyFormatter.formatPropertyPrice(2500000), '₹25.0 L');
        expect(CurrencyFormatter.formatPropertyPrice(5000000), '₹50.0 L');
        expect(CurrencyFormatter.formatPropertyPrice(7500000), '₹75.0 L');
        expect(CurrencyFormatter.formatPropertyPrice(10000000), '₹1.0 Cr');
        expect(CurrencyFormatter.formatPropertyPrice(25000000), '₹2.5 Cr');
        expect(CurrencyFormatter.formatPropertyPrice(50000000), '₹5.0 Cr');
      });

      test('should format property price ranges correctly', () {
        expect(
          CurrencyFormatter.formatPriceRange(2500000, 5000000),
          '₹25.0 L - ₹50.0 L',
        );
        expect(
          CurrencyFormatter.formatPriceRange(5000000, 10000000),
          '₹50.0 L - ₹1.0 Cr',
        );
        expect(
          CurrencyFormatter.formatPriceRange(10000000, 25000000),
          '₹1.0 Cr - ₹2.5 Cr',
        );
      });

      test('should handle invalid price ranges correctly', () {
        // When min > max, should swap them
        expect(
          CurrencyFormatter.formatPriceRange(5000000, 2500000),
          '₹25.0 L - ₹50.0 L',
        );
        
        // When min == max
        expect(
          CurrencyFormatter.formatPriceRange(2500000, 2500000),
          '₹25.0 L',
        );
      });
    });

    group('Budget Formatting', () {
      test('should format budget ranges correctly', () {
        expect(
          CurrencyFormatter.formatBudgetRange(2500000, 5000000),
          '₹25.0 L - ₹50.0 L',
        );
        expect(
          CurrencyFormatter.formatBudgetRange(null, 5000000),
          'Up to ₹50.0 L',
        );
        expect(
          CurrencyFormatter.formatBudgetRange(2500000, null),
          'Above ₹25.0 L',
        );
        expect(
          CurrencyFormatter.formatBudgetRange(null, null),
          'Budget not specified',
        );
      });
    });

    group('Star Bonus Formatting', () {
      test('should format star bonus amounts correctly', () {
        expect(CurrencyFormatter.formatStarBonus(50000), '₹50.0 K');
        expect(CurrencyFormatter.formatStarBonus(100000), '₹1.0 L');
        expect(CurrencyFormatter.formatStarBonus(250000), '₹2.5 L');
      });
    });

    group('Performance Metrics', () {
      test('should format performance metrics correctly', () {
        expect(CurrencyFormatter.formatPerformanceMetric(0.234), '23.4%');
        expect(CurrencyFormatter.formatPerformanceMetric(0.876), '87.6%');
        expect(CurrencyFormatter.formatPerformanceMetric(1.0), '100.0%');
        expect(CurrencyFormatter.formatPerformanceMetric(0.0), '0.0%');
      });
    });

    group('Edge Cases', () {
      test('should handle zero amounts correctly', () {
        expect(CurrencyFormatter.formatAmount(0), '₹0');
        expect(CurrencyFormatter.formatAmountCompact(0), '₹0');
        expect(CurrencyFormatter.formatAmountDetailed(0), '₹0');
      });

      test('should handle very small decimal amounts correctly', () {
        expect(CurrencyFormatter.formatAmount(0.01), '₹0');
        expect(CurrencyFormatter.formatAmount(0.99), '₹1');
        expect(CurrencyFormatter.formatAmount(1.01), '₹1');
      });

      test('should handle infinity and NaN correctly', () {
        expect(CurrencyFormatter.formatAmount(double.infinity), '₹∞');
        expect(CurrencyFormatter.formatAmount(double.negativeInfinity), '-₹∞');
        expect(CurrencyFormatter.formatAmount(double.nan), '₹--');
      });

      test('should handle very large numbers correctly', () {
        expect(CurrencyFormatter.formatAmount(1e15), '₹100000000.0 Cr');
        expect(CurrencyFormatter.formatAmount(1e20), '₹10000000000000.0 Cr');
      });
    });

    group('Localization Support', () {
      test('should support different decimal precision', () {
        expect(CurrencyFormatter.formatAmountWithPrecision(1500000, 0), '₹2 L');
        expect(CurrencyFormatter.formatAmountWithPrecision(1500000, 1), '₹1.5 L');
        expect(CurrencyFormatter.formatAmountWithPrecision(1500000, 2), '₹1.50 L');
      });

      test('should support custom currency symbols', () {
        expect(
          CurrencyFormatter.formatAmountWithSymbol(1500000, 'Rs.'),
          'Rs.1.5 L',
        );
        expect(
          CurrencyFormatter.formatAmountWithSymbol(1500000, 'INR '),
          'INR 1.5 L',
        );
      });
    });
  });
}
