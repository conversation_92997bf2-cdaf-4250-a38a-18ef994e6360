import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../../core/providers/app_providers.dart' as core_providers;
import '../../shared/themes/app_theme.dart';
import '../../shared/widgets/main_navigation.dart';
import '../../features/auth/presentation/providers/auth_providers.dart';
import '../../features/auth/domain/auth_state.dart';


import '../../features/auth/presentation/pages/login_page.dart';
import '../../features/auth/presentation/pages/agent_onboarding_page.dart';
import '../../features/landing/presentation/pages/landing_page.dart';

/// Authentication wrapper that handles app routing based on auth state
class AuthWrapper extends ConsumerWidget {
  const AuthWrapper({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    // Check our custom auth state first
    final authState = ref.watch(authStateProvider);

    // Debug: Print current auth state
    if (kDebugMode) {
      print('AuthWrapper: Current auth state: ${authState.runtimeType}');
    }

    // If authenticated through our demo system, show main app
    if (authState is AuthAuthenticated) {
      if (kDebugMode) {
        print('AuthWrapper: User authenticated, showing main app');
      }
      return const MainNavigationWidget();
    }

    // If not authenticated, show landing page
    if (authState is AuthUnauthenticated) {
      if (kDebugMode) {
        print('AuthWrapper: User not authenticated, showing landing page');
      }
      return const LandingPage();
    }

    // If loading, show loading screen
    if (authState is AuthLoading) {
      if (kDebugMode) {
        print('AuthWrapper: Auth loading, showing loading screen');
      }
      return const LoadingScreen();
    }

    // If error, show landing page with error
    if (authState is AuthError) {
      if (kDebugMode) {
        print('AuthWrapper: Auth error: ${authState.message}');
      }
      return const LandingPage();
    }

    // Default: show landing page
    if (kDebugMode) {
      print('AuthWrapper: Default case, showing landing page');
    }
    return const LandingPage();
  }
}

/// Splash screen shown during initial loading
class SplashScreen extends StatelessWidget {
  const SplashScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Container(
        decoration: BoxDecoration(gradient: AppTheme.primaryGradient),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Icon(
                  Icons.home_work,
                  size: 64,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 32),

              // App Name
              const Text(
                'Rama Samriddhi',
                style: TextStyle(
                  color: Color(0xFFFF4500), // Bright Bold Orange
                  fontSize: 32,
                  fontWeight: FontWeight.w900, // Extra bold
                  letterSpacing: 1.0,
                ),
              ),

              const SizedBox(height: 8),

              // Tagline
              Text(
                'Building Networks, Creating Success',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 16,
                ),
              ),

              const SizedBox(height: 48),

              // Loading Indicator
              SizedBox(
                width: 40,
                height: 40,
                child: CircularProgressIndicator(
                  valueColor: AlwaysStoppedAnimation<Color>(
                    Colors.white.withValues(alpha: 0.8),
                  ),
                  strokeWidth: 3,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Loading screen shown during data loading
class LoadingScreen extends StatelessWidget {
  const LoadingScreen({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 60,
              height: 60,
              child: CircularProgressIndicator(
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
                strokeWidth: 4,
              ),
            ),

            const SizedBox(height: 24),

            Text(
              'Loading your data...',
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 18,
                fontWeight: FontWeight.w500,
              ),
            ),

            const SizedBox(height: 8),

            Text(
              'Please wait while we set up your dashboard',
              style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 14),
            ),
          ],
        ),
      ),
    );
  }
}

/// Error screen shown when there's an authentication error
class ErrorScreen extends StatelessWidget {
  final String error;

  const ErrorScreen({super.key, required this.error});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Center(
        child: Padding(
          padding: const EdgeInsets.all(24),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(Icons.error_outline, size: 80, color: AppTheme.errorColor),

              const SizedBox(height: 24),

              Text(
                'Something went wrong',
                style: TextStyle(
                  color: AppTheme.darkPrimaryText,
                  fontSize: 24,
                  fontWeight: FontWeight.bold,
                ),
              ),

              const SizedBox(height: 16),

              Text(
                error,
                style: TextStyle(
                  color: AppTheme.darkSecondaryText,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              ElevatedButton(
                onPressed: () {
                  // Restart the app or navigate to login
                  Navigator.of(context).pushAndRemoveUntil(
                    MaterialPageRoute(
                      builder: (context) => const AuthWrapper(),
                    ),
                    (route) => false,
                  );
                },
                style: ElevatedButton.styleFrom(
                  backgroundColor: AppTheme.primaryColor,
                  foregroundColor: Colors.white,
                  padding: const EdgeInsets.symmetric(
                    horizontal: 32,
                    vertical: 16,
                  ),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text(
                  'Try Again',
                  style: TextStyle(fontSize: 16, fontWeight: FontWeight.w600),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Onboarding page for new users
class OnboardingPage extends StatelessWidget {
  final User firebaseUser;

  const OnboardingPage({super.key, required this.firebaseUser});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Container(
        decoration: BoxDecoration(gradient: AppTheme.primaryGradient),
        child: SafeArea(
          child: Padding(
            padding: const EdgeInsets.all(24),
            child: Column(
              children: [
                const Spacer(),

                // Welcome Message
                const Icon(Icons.waving_hand, size: 80, color: Colors.white),

                const SizedBox(height: 24),

                Text(
                  'Welcome to Rama Samriddhi!',
                  style: const TextStyle(
                    color: Color(0xFFFF4500), // Bright Bold Orange
                    fontSize: 32,
                    fontWeight: FontWeight.w900, // Extra bold
                    letterSpacing: 1.0,
                  ),
                  textAlign: TextAlign.center,
                ),

                const SizedBox(height: 16),

                Text(
                  'Let\'s complete your profile to get started with your real estate journey.',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 16,
                  ),
                  textAlign: TextAlign.center,
                ),

                const Spacer(),

                // Complete Profile Button
                SizedBox(
                  width: double.infinity,
                  child: ElevatedButton(
                    onPressed: () {
                      // Navigate to profile completion
                      _navigateToProfileCompletion(context);
                    },
                    style: ElevatedButton.styleFrom(
                      backgroundColor: Colors.white,
                      foregroundColor: AppTheme.primaryColor,
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: const Text(
                      'Complete Profile',
                      style: TextStyle(
                        fontSize: 18,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ),

                const SizedBox(height: 16),

                // Skip for Now Button
                TextButton(
                  onPressed: () {
                    // Skip onboarding and go to main app
                    _skipOnboarding(context);
                  },
                  child: Text(
                    'Skip for now',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 16,
                    ),
                  ),
                ),

                const SizedBox(height: 24),
              ],
            ),
          ),
        ),
      ),
    );
  }

  void _navigateToProfileCompletion(BuildContext context) {
    // Navigate to profile completion page
    // For now, we'll just show a dialog
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.darkCard,
        title: Text(
          'Profile Completion',
          style: TextStyle(color: AppTheme.darkPrimaryText),
        ),
        content: Text(
          'Profile completion will be available soon.',
          style: TextStyle(color: AppTheme.darkSecondaryText),
        ),
        actions: [
          TextButton(
            onPressed: () {
              Navigator.of(context).pop();
              _skipOnboarding(context);
            },
            child: Text('OK', style: TextStyle(color: AppTheme.primaryColor)),
          ),
        ],
      ),
    );
  }

  void _skipOnboarding(BuildContext context) {
    // Navigate to main app
    Navigator.of(context).pushAndRemoveUntil(
      MaterialPageRoute(builder: (context) => const MainNavigationWidget()),
      (route) => false,
    );
  }
}
