import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../../core/services/test_data_service.dart';

/// Floating action button for test data management
class TestDataFAB extends ConsumerStatefulWidget {
  const TestDataFAB({super.key});

  @override
  ConsumerState<TestDataFAB> createState() => _TestDataFABState();
}

class _TestDataFABState extends ConsumerState<TestDataFAB> {
  bool _isLoading = false;
  bool _isDataLoaded = false;

  @override
  void initState() {
    super.initState();
    _checkDataStatus();
  }

  Future<void> _checkDataStatus() async {
    final isLoaded = await TestDataService.isTestDataLoaded();
    if (mounted) {
      setState(() {
        _isDataLoaded = isLoaded;
      });
    }
  }

  Future<void> _loadTestData() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final success = await TestDataService.loadTestData();
      if (success) {
        await _checkDataStatus();
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('✅ Test data loaded successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('❌ Failed to load test data'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('❌ Error: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }

  void _showTestDataDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.darkSurface,
        title: Row(
          children: [
            Icon(
              Icons.data_usage,
              color: AppTheme.primaryColor,
            ),
            const SizedBox(width: 12),
            const Text(
              'Test Data Manager',
              style: TextStyle(color: Colors.white),
            ),
          ],
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              _isDataLoaded 
                  ? 'Test data is currently loaded in the app.'
                  : 'No test data found. Load comprehensive MLM test data?',
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
              ),
            ),
            const SizedBox(height: 16),
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: AppTheme.warningColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.warningColor.withOpacity(0.3),
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    'Test Data Includes:',
                    style: TextStyle(
                      color: AppTheme.warningColor,
                      fontWeight: FontWeight.bold,
                      fontSize: 12,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    '• 43 MLM users (5-level hierarchy)\n'
                    '• 50 property listings\n'
                    '• 30 transactions with commissions\n'
                    '• 200+ star achievements\n'
                    '• 100 customer leads',
                    style: TextStyle(
                      color: AppTheme.warningColor,
                      fontSize: 11,
                      height: 1.4,
                    ),
                  ),
                ],
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Cancel'),
          ),
          if (!_isDataLoaded)
            ElevatedButton(
              onPressed: _isLoading ? null : () {
                Navigator.pop(context);
                _loadTestData();
              },
              style: ElevatedButton.styleFrom(
                backgroundColor: AppTheme.primaryColor,
              ),
              child: _isLoading
                  ? const SizedBox(
                      width: 16,
                      height: 16,
                      child: CircularProgressIndicator(strokeWidth: 2),
                    )
                  : const Text('Load Test Data'),
            ),
          if (_isDataLoaded)
            OutlinedButton(
              onPressed: () async {
                Navigator.pop(context);
                await TestDataService.clearTestData();
                await _checkDataStatus();
                if (mounted) {
                  ScaffoldMessenger.of(context).showSnackBar(
                    const SnackBar(
                      content: Text('🗑️ Test data cleared!'),
                      backgroundColor: Colors.orange,
                    ),
                  );
                }
              },
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppTheme.errorColor),
                foregroundColor: AppTheme.errorColor,
              ),
              child: const Text('Clear Data'),
            ),
        ],
      ),
    );
  }

  @override
  Widget build(BuildContext context) {
    // Only show in debug mode
    if (!kDebugMode) {
      return const SizedBox.shrink();
    }

    return FloatingActionButton.extended(
      onPressed: _showTestDataDialog,
      backgroundColor: _isDataLoaded ? Colors.green : AppTheme.primaryColor,
      icon: Icon(
        _isDataLoaded ? Icons.check_circle : Icons.data_usage,
        color: Colors.white,
      ),
      label: Text(
        _isDataLoaded ? 'Data Loaded' : 'Test Data',
        style: const TextStyle(color: Colors.white),
      ),
    );
  }
}
