import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:rama_realty_mlm/main.dart' as app;

void main() {
  final binding = IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('App Performance Tests', () {
    testWidgets('app startup performance', (WidgetTester tester) async {
      // Measure app startup time
      final stopwatch = Stopwatch()..start();
      
      app.main();
      await tester.pumpAndSettle();
      
      stopwatch.stop();
      final startupTime = stopwatch.elapsedMilliseconds;
      
      print('App startup time: ${startupTime}ms');
      
      // App should start within 3 seconds
      expect(startupTime, lessThan(3000));
      
      // Verify app is loaded
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('login page rendering performance', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Measure login page rendering
      final stopwatch = Stopwatch()..start();
      
      // Navigate to login if not already there
      if (find.text('Welcome Back').evaluate().isEmpty) {
        await tester.tap(find.text('Login'));
        await tester.pumpAndSettle();
      }
      
      stopwatch.stop();
      final renderTime = stopwatch.elapsedMilliseconds;
      
      print('Login page render time: ${renderTime}ms');
      
      // Page should render within 1 second
      expect(renderTime, lessThan(1000));
      
      // Verify login page elements are present
      expect(find.text('Welcome Back'), findsOneWidget);
      expect(find.byType(TextField), findsAtLeastNWidgets(2));
    });

    testWidgets('property list scrolling performance', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login first (assuming test user exists)
      await _performLogin(tester);

      // Navigate to properties page
      await _navigateToProperties(tester);

      // Measure scrolling performance
      final stopwatch = Stopwatch()..start();
      
      // Perform scroll operations
      for (int i = 0; i < 10; i++) {
        await tester.drag(find.byType(ListView), const Offset(0, -300));
        await tester.pump();
      }
      
      await tester.pumpAndSettle();
      stopwatch.stop();
      
      final scrollTime = stopwatch.elapsedMilliseconds;
      print('Property list scroll time (10 scrolls): ${scrollTime}ms');
      
      // Scrolling should be smooth (less than 2 seconds for 10 scrolls)
      expect(scrollTime, lessThan(2000));
    });

    testWidgets('dashboard loading performance', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login first
      await _performLogin(tester);

      // Measure dashboard loading time
      final stopwatch = Stopwatch()..start();
      
      // Navigate to dashboard
      if (find.text('Dashboard').evaluate().isNotEmpty) {
        await tester.tap(find.text('Dashboard'));
      } else if (find.byIcon(Icons.dashboard).evaluate().isNotEmpty) {
        await tester.tap(find.byIcon(Icons.dashboard));
      }
      
      await tester.pumpAndSettle();
      stopwatch.stop();
      
      final loadTime = stopwatch.elapsedMilliseconds;
      print('Dashboard load time: ${loadTime}ms');
      
      // Dashboard should load within 2 seconds
      expect(loadTime, lessThan(2000));
    });

    testWidgets('form input responsiveness', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Navigate to registration form
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Measure form input responsiveness
      final stopwatch = Stopwatch()..start();
      
      // Fill form fields rapidly
      await tester.enterText(find.byKey(const Key('name_field')), 'Performance Test User');
      await tester.pump();
      
      await tester.enterText(find.byKey(const Key('email_field')), '<EMAIL>');
      await tester.pump();
      
      await tester.enterText(find.byKey(const Key('phone_field')), '+************');
      await tester.pump();
      
      await tester.enterText(find.byKey(const Key('password_field')), 'password123');
      await tester.pump();
      
      await tester.enterText(find.byKey(const Key('confirm_password_field')), 'password123');
      await tester.pump();
      
      stopwatch.stop();
      final inputTime = stopwatch.elapsedMilliseconds;
      
      print('Form input time: ${inputTime}ms');
      
      // Form inputs should be responsive (less than 500ms)
      expect(inputTime, lessThan(500));
    });

    testWidgets('image loading performance', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login and navigate to properties
      await _performLogin(tester);
      await _navigateToProperties(tester);

      // Measure image loading time
      final stopwatch = Stopwatch()..start();
      
      // Wait for images to load
      await tester.pumpAndSettle(const Duration(seconds: 5));
      
      stopwatch.stop();
      final imageLoadTime = stopwatch.elapsedMilliseconds;
      
      print('Image loading time: ${imageLoadTime}ms');
      
      // Images should load within 5 seconds
      expect(imageLoadTime, lessThan(5000));
    });

    testWidgets('navigation performance', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login first
      await _performLogin(tester);

      // Test navigation between different pages
      final navigationTimes = <String, int>{};
      
      // Test navigation to different sections
      final navigationTests = [
        ('Dashboard', Icons.dashboard),
        ('Properties', Icons.home_work),
        ('Profile', Icons.person),
        ('Settings', Icons.settings),
      ];

      for (final test in navigationTests) {
        final stopwatch = Stopwatch()..start();
        
        // Navigate to section
        if (find.text(test.$1).evaluate().isNotEmpty) {
          await tester.tap(find.text(test.$1));
        } else if (find.byIcon(test.$2).evaluate().isNotEmpty) {
          await tester.tap(find.byIcon(test.$2));
        }
        
        await tester.pumpAndSettle();
        stopwatch.stop();
        
        navigationTimes[test.$1] = stopwatch.elapsedMilliseconds;
        print('${test.$1} navigation time: ${stopwatch.elapsedMilliseconds}ms');
      }

      // All navigation should be under 1.5 seconds
      for (final time in navigationTimes.values) {
        expect(time, lessThan(1500));
      }
    });

    testWidgets('memory usage during property browsing', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login and navigate to properties
      await _performLogin(tester);
      await _navigateToProperties(tester);

      // Simulate browsing multiple properties
      for (int i = 0; i < 20; i++) {
        // Scroll to load more properties
        await tester.drag(find.byType(ListView), const Offset(0, -200));
        await tester.pump();
        
        // Tap on a property if available
        final propertyCards = find.byType(Card);
        if (propertyCards.evaluate().isNotEmpty) {
          await tester.tap(propertyCards.first);
          await tester.pumpAndSettle();
          
          // Go back
          if (find.byIcon(Icons.arrow_back).evaluate().isNotEmpty) {
            await tester.tap(find.byIcon(Icons.arrow_back));
            await tester.pumpAndSettle();
          }
        }
      }

      // Test should complete without memory issues
      expect(find.byType(MaterialApp), findsOneWidget);
    });

    testWidgets('search performance', (WidgetTester tester) async {
      app.main();
      await tester.pumpAndSettle();

      // Login and navigate to properties
      await _performLogin(tester);
      await _navigateToProperties(tester);

      // Find search field
      final searchField = find.byIcon(Icons.search);
      if (searchField.evaluate().isNotEmpty) {
        await tester.tap(searchField);
        await tester.pumpAndSettle();

        // Measure search performance
        final stopwatch = Stopwatch()..start();
        
        await tester.enterText(find.byType(TextField).last, 'Mumbai');
        await tester.pumpAndSettle();
        
        stopwatch.stop();
        final searchTime = stopwatch.elapsedMilliseconds;
        
        print('Search time: ${searchTime}ms');
        
        // Search should be responsive (under 1 second)
        expect(searchTime, lessThan(1000));
      }
    });

    testWidgets('commission calculation performance', (WidgetTester tester) async {
      // Test commission calculation performance with large numbers
      final stopwatch = Stopwatch()..start();
      
      // Simulate multiple commission calculations
      for (int i = 0; i < 1000; i++) {
        final saleAmount = 1000000.0 + (i * 100000);
        
        // Calculate commissions for all levels
        for (int level = 0; level < 5; level++) {
          final commission = _calculateCommission(saleAmount, level);
          expect(commission, greaterThanOrEqualTo(0));
        }
      }
      
      stopwatch.stop();
      final calculationTime = stopwatch.elapsedMilliseconds;
      
      print('Commission calculation time (5000 calculations): ${calculationTime}ms');
      
      // Calculations should be fast (under 100ms for 5000 calculations)
      expect(calculationTime, lessThan(100));
    });
  });
}

// Helper functions
Future<void> _performLogin(WidgetTester tester) async {
  // Check if already logged in
  if (find.text('Dashboard').evaluate().isNotEmpty ||
      find.byIcon(Icons.dashboard).evaluate().isNotEmpty) {
    return;
  }

  // Perform login
  if (find.text('Welcome Back').evaluate().isNotEmpty) {
    await tester.enterText(find.byKey(const Key('login_email_field')), '<EMAIL>');
    await tester.enterText(find.byKey(const Key('login_password_field')), 'admin123');
    await tester.tap(find.byKey(const Key('login_button')));
    await tester.pumpAndSettle(const Duration(seconds: 3));
  }
}

Future<void> _navigateToProperties(WidgetTester tester) async {
  // Navigate to properties page
  if (find.text('Properties').evaluate().isNotEmpty) {
    await tester.tap(find.text('Properties'));
  } else if (find.byIcon(Icons.home_work).evaluate().isNotEmpty) {
    await tester.tap(find.byIcon(Icons.home_work));
  } else {
    // Open drawer if needed
    if (find.byIcon(Icons.menu).evaluate().isNotEmpty) {
      await tester.tap(find.byIcon(Icons.menu));
      await tester.pumpAndSettle();
      
      if (find.text('Properties').evaluate().isNotEmpty) {
        await tester.tap(find.text('Properties'));
      }
    }
  }
  
  await tester.pumpAndSettle();
}

// Mock commission calculation for performance testing
double _calculateCommission(double saleAmount, int level) {
  const rates = [0.05, 0.02, 0.01, 0.005, 0.002];
  if (level >= 0 && level < rates.length) {
    return saleAmount * rates[level];
  }
  return 0.0;
}
