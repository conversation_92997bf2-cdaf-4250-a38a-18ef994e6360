import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../commissions/models/commission_enums.dart';
import '../../models/network_models.dart';
import 'network_node_widget.dart';

/// Interactive network tree visualization widget
class InteractiveNetworkTree extends ConsumerStatefulWidget {
  final NetworkNode rootNode;
  final NetworkVisualizationConfig config;
  final Function(NetworkNode)? onNodeTap;
  final Function(NetworkNode)? onNodeLongPress;
  final bool enableZoom;
  final bool enablePan;

  const InteractiveNetworkTree({
    super.key,
    required this.rootNode,
    this.config = const NetworkVisualizationConfig(),
    this.onNodeTap,
    this.onNodeLongPress,
    this.enableZoom = true,
    this.enablePan = true,
  });

  @override
  ConsumerState<InteractiveNetworkTree> createState() =>
      _InteractiveNetworkTreeState();
}

class _InteractiveNetworkTreeState extends ConsumerState<InteractiveNetworkTree>
    with TickerProviderStateMixin {
  late TransformationController _transformationController;
  late AnimationController _animationController;
  NetworkNode? _selectedNode;
  NetworkNode? _hoveredNode;
  final Map<String, GlobalKey> _nodeKeys = {};

  @override
  void initState() {
    super.initState();
    _transformationController = TransformationController();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 300),
      vsync: this,
    );
    _generateNodeKeys(widget.rootNode);
  }

  @override
  void dispose() {
    _transformationController.dispose();
    _animationController.dispose();
    super.dispose();
  }

  void _generateNodeKeys(NetworkNode node) {
    _nodeKeys[node.id] = GlobalKey();
    for (final child in node.children) {
      _generateNodeKeys(child);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Controls
        _buildControls(),

        // Network Tree
        Expanded(
          child: Container(
            decoration: BoxDecoration(
              color: AppTheme.darkSurface,
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.darkBorder.withValues(alpha: 0.3),
              ),
            ),
            child: ClipRRect(
              borderRadius: BorderRadius.circular(16),
              child: InteractiveViewer(
                transformationController: _transformationController,
                boundaryMargin: const EdgeInsets.all(100),
                minScale: 0.1,
                maxScale: 3.0,
                constrained: false,
                child: SizedBox(
                  width: _calculateTreeWidth(),
                  height: _calculateTreeHeight(),
                  child: CustomPaint(
                    painter: NetworkConnectionsPainter(
                      rootNode: widget.rootNode,
                      config: widget.config,
                      nodeKeys: _nodeKeys,
                      selectedNode: _selectedNode,
                    ),
                    child: _buildTreeLayout(),
                  ),
                ),
              ),
            ),
          ),
        ),

        // Selected Node Info
        if (_selectedNode != null) _buildSelectedNodeInfo(),
      ],
    );
  }

  /// Build control buttons
  Widget _buildControls() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Row(
        children: [
          // Zoom Controls
          _buildControlButton(
            icon: Icons.zoom_in,
            onPressed: _zoomIn,
            tooltip: 'Zoom In',
          ),
          const SizedBox(width: 8),
          _buildControlButton(
            icon: Icons.zoom_out,
            onPressed: _zoomOut,
            tooltip: 'Zoom Out',
          ),
          const SizedBox(width: 8),
          _buildControlButton(
            icon: Icons.center_focus_strong,
            onPressed: _resetZoom,
            tooltip: 'Reset View',
          ),

          const Spacer(),

          // Layout Controls
          _buildControlButton(
            icon: Icons.unfold_more,
            onPressed: _expandAll,
            tooltip: 'Expand All',
          ),
          const SizedBox(width: 8),
          _buildControlButton(
            icon: Icons.unfold_less,
            onPressed: _collapseAll,
            tooltip: 'Collapse All',
          ),
        ],
      ),
    );
  }

  /// Build control button
  Widget _buildControlButton({
    required IconData icon,
    required VoidCallback onPressed,
    required String tooltip,
  }) {
    return Tooltip(
      message: tooltip,
      child: Container(
        decoration: BoxDecoration(
          color: AppTheme.darkCard,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.darkBorder.withValues(alpha: 0.3)),
        ),
        child: IconButton(
          onPressed: onPressed,
          icon: Icon(icon, color: AppTheme.darkPrimaryText),
          iconSize: 20,
        ),
      ),
    );
  }

  /// Build tree layout
  Widget _buildTreeLayout() {
    return Stack(children: _buildNodeWidgets(widget.rootNode, 0, 0));
  }

  /// Build node widgets recursively
  List<Widget> _buildNodeWidgets(NetworkNode node, double x, double y) {
    final widgets = <Widget>[];

    // Add current node
    widgets.add(
      Positioned(
        left: x,
        top: y,
        child: NetworkNodeWidget(
          key: _nodeKeys[node.id],
          node: node,
          config: widget.config,
          isSelected: _selectedNode?.id == node.id,
          isHovered: _hoveredNode?.id == node.id,
          onTap: () => _onNodeTap(node),
          onLongPress: () => _onNodeLongPress(node),
          onHover: (isHovered) => _onNodeHover(node, isHovered),
        ),
      ),
    );

    // Add children
    if (node.children.isNotEmpty) {
      final childrenWidth =
          (node.children.length - 1) * widget.config.nodeSpacing;
      final startX = x - childrenWidth / 2;
      final childY = y + widget.config.levelSpacing;

      for (int i = 0; i < node.children.length; i++) {
        final child = node.children[i];
        final childX = startX + (i * widget.config.nodeSpacing);

        widgets.addAll(_buildNodeWidgets(child, childX, childY));
      }
    }

    return widgets;
  }

  /// Calculate tree width
  double _calculateTreeWidth() {
    final maxWidth = _getMaxWidthAtAnyLevel(widget.rootNode, 0);
    return maxWidth + 200; // Add padding
  }

  /// Calculate tree height
  double _calculateTreeHeight() {
    final maxDepth = _getMaxDepth(widget.rootNode);
    return (maxDepth * widget.config.levelSpacing) +
        widget.config.nodeSize +
        200;
  }

  /// Get maximum width at any level
  double _getMaxWidthAtAnyLevel(NetworkNode node, int level) {
    final currentLevelNodes = _getNodesAtLevel(widget.rootNode, level);
    final currentWidth = currentLevelNodes.length * widget.config.nodeSpacing;

    double maxWidth = currentWidth;

    for (final child in node.children) {
      final childMaxWidth = _getMaxWidthAtAnyLevel(child, level + 1);
      maxWidth = maxWidth > childMaxWidth ? maxWidth : childMaxWidth;
    }

    return maxWidth;
  }

  /// Get nodes at specific level
  List<NetworkNode> _getNodesAtLevel(NetworkNode root, int targetLevel) {
    if (targetLevel == 0) return [root];

    final nodes = <NetworkNode>[];
    for (final child in root.children) {
      nodes.addAll(_getNodesAtLevel(child, targetLevel - 1));
    }
    return nodes;
  }

  /// Get maximum depth of tree
  int _getMaxDepth(NetworkNode node) {
    if (node.children.isEmpty) return 0;

    int maxChildDepth = 0;
    for (final child in node.children) {
      final childDepth = _getMaxDepth(child);
      maxChildDepth = maxChildDepth > childDepth ? maxChildDepth : childDepth;
    }

    return maxChildDepth + 1;
  }

  /// Handle node tap
  void _onNodeTap(NetworkNode node) {
    setState(() {
      _selectedNode = node;
    });
    widget.onNodeTap?.call(node);
  }

  /// Handle node long press
  void _onNodeLongPress(NetworkNode node) {
    widget.onNodeLongPress?.call(node);
  }

  /// Handle node hover
  void _onNodeHover(NetworkNode node, bool isHovered) {
    setState(() {
      _hoveredNode = isHovered ? node : null;
    });
  }

  /// Zoom in
  void _zoomIn() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    if (currentScale < 3.0) {
      _transformationController.value = Matrix4.identity()
        ..scale(currentScale * 1.2);
    }
  }

  /// Zoom out
  void _zoomOut() {
    final currentScale = _transformationController.value.getMaxScaleOnAxis();
    if (currentScale > 0.1) {
      _transformationController.value = Matrix4.identity()
        ..scale(currentScale * 0.8);
    }
  }

  /// Reset zoom
  void _resetZoom() {
    _transformationController.value = Matrix4.identity();
  }

  /// Expand all nodes
  void _expandAll() {
    // Implementation would involve state management for expanded/collapsed nodes
    // For now, this is a placeholder
  }

  /// Collapse all nodes
  void _collapseAll() {
    // Implementation would involve state management for expanded/collapsed nodes
    // For now, this is a placeholder
  }

  /// Build selected node info panel
  Widget _buildSelectedNodeInfo() {
    final node = _selectedNode!;

    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(16),
      decoration: AppTheme.createCardDecoration(),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              CircleAvatar(
                radius: 20,
                backgroundColor: node.tier.color,
                child: Text(
                  node.initials,
                  style: const TextStyle(
                    color: Colors.white,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      node.name,
                      style: TextStyle(
                        color: AppTheme.darkPrimaryText,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                    Text(
                      node.email,
                      style: TextStyle(
                        color: AppTheme.darkSecondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                decoration: BoxDecoration(
                  color: node.tier.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  node.tier.displayName,
                  style: TextStyle(
                    color: node.tier.color,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(child: _buildInfoItem('Sales', node.formattedSales)),
              Expanded(
                child: _buildInfoItem('Commissions', node.formattedCommissions),
              ),
              Expanded(
                child: _buildInfoItem(
                  'Team Size',
                  '${node.totalNetworkSize - 1}',
                ),
              ),
              Expanded(child: _buildInfoItem('Level', '${node.level}')),
            ],
          ),
        ],
      ),
    );
  }

  /// Build info item
  Widget _buildInfoItem(String label, String value) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          label,
          style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 10),
        ),
        Text(
          value,
          style: TextStyle(
            color: AppTheme.darkPrimaryText,
            fontSize: 12,
            fontWeight: FontWeight.w600,
          ),
        ),
      ],
    );
  }
}

/// Custom painter for drawing connections between nodes
class NetworkConnectionsPainter extends CustomPainter {
  final NetworkNode rootNode;
  final NetworkVisualizationConfig config;
  final Map<String, GlobalKey> nodeKeys;
  final NetworkNode? selectedNode;

  NetworkConnectionsPainter({
    required this.rootNode,
    required this.config,
    required this.nodeKeys,
    this.selectedNode,
  });

  @override
  void paint(Canvas canvas, Size size) {
    final paint = Paint()
      ..color = AppTheme.darkBorder
      ..strokeWidth = 2
      ..style = PaintingStyle.stroke;

    final selectedPaint = Paint()
      ..color = AppTheme.primaryColor
      ..strokeWidth = 3
      ..style = PaintingStyle.stroke;

    _drawConnections(canvas, rootNode, paint, selectedPaint);
  }

  void _drawConnections(
    Canvas canvas,
    NetworkNode node,
    Paint paint,
    Paint selectedPaint,
  ) {
    final nodeKey = nodeKeys[node.id];
    if (nodeKey?.currentContext == null) return;

    final nodeBox = nodeKey!.currentContext!.findRenderObject() as RenderBox?;
    if (nodeBox == null) return;

    final nodePosition = nodeBox.localToGlobal(Offset.zero);
    final nodeCenter = Offset(
      nodePosition.dx + config.nodeSize / 2,
      nodePosition.dy + config.nodeSize / 2,
    );

    for (final child in node.children) {
      final childKey = nodeKeys[child.id];
      if (childKey?.currentContext == null) continue;

      final childBox =
          childKey!.currentContext!.findRenderObject() as RenderBox?;
      if (childBox == null) continue;

      final childPosition = childBox.localToGlobal(Offset.zero);
      final childCenter = Offset(
        childPosition.dx + config.nodeSize / 2,
        childPosition.dy + config.nodeSize / 2,
      );

      // Choose paint based on selection
      final connectionPaint =
          (selectedNode?.id == node.id || selectedNode?.id == child.id)
          ? selectedPaint
          : paint;

      // Draw connection line
      canvas.drawLine(nodeCenter, childCenter, connectionPaint);

      // Recursively draw child connections
      _drawConnections(canvas, child, paint, selectedPaint);
    }
  }

  @override
  bool shouldRepaint(NetworkConnectionsPainter oldDelegate) {
    return oldDelegate.selectedNode?.id != selectedNode?.id;
  }
}
