import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/agent_interaction_providers.dart';
import '../widgets/lead_widgets.dart';
import '../../../../core/models/lead_model.dart';

/// Agent leads management page
class AgentLeadsPage extends ConsumerStatefulWidget {
  const AgentLeadsPage({super.key});

  @override
  ConsumerState<AgentLeadsPage> createState() => _AgentLeadsPageState();
}

class _AgentLeadsPageState extends ConsumerState<AgentLeadsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final leadStats = ref.watch(leadStatisticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('My Leads'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.analytics),
            onPressed: () => _showAnalyticsDialog(context),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.inbox),
                  const SizedBox(width: 8),
                  leadStats.when(
                    data: (stats) => Text('All (${stats['totalLeads'] ?? 0})'),
                    loading: () => const Text('All'),
                    error: (_, __) => const Text('All'),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.new_releases),
                  const SizedBox(width: 8),
                  leadStats.when(
                    data: (stats) => Text('New (${stats['newLeads'] ?? 0})'),
                    loading: () => const Text('New'),
                    error: (_, __) => const Text('New'),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.local_fire_department),
                  const SizedBox(width: 8),
                  leadStats.when(
                    data: (stats) => Text('Hot (${stats['hotLeads'] ?? 0})'),
                    loading: () => const Text('Hot'),
                    error: (_, __) => const Text('Hot'),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.schedule),
                  const SizedBox(width: 8),
                  leadStats.when(
                    data: (stats) =>
                        Text('Follow-up (${stats['followUpNeeded'] ?? 0})'),
                    loading: () => const Text('Follow-up'),
                    error: (_, __) => const Text('Follow-up'),
                  ),
                ],
              ),
            ),
            Tab(
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(Icons.check_circle),
                  const SizedBox(width: 8),
                  leadStats.when(
                    data: (stats) =>
                        Text('Converted (${stats['convertedLeads'] ?? 0})'),
                    loading: () => const Text('Converted'),
                    error: (_, __) => const Text('Converted'),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildAllLeadsTab(),
          _buildNewLeadsTab(),
          _buildHotLeadsTab(),
          _buildFollowUpLeadsTab(),
          _buildConvertedLeadsTab(),
        ],
      ),
      floatingActionButton: FloatingActionButton(
        onPressed: () => _showCreateLeadDialog(context),
        tooltip: 'Add New Lead',
        child: const Icon(Icons.add),
      ),
    );
  }

  Widget _buildAllLeadsTab() {
    final leadsAsync = ref.watch(filteredLeadsProvider);

    return _buildLeadsList(leadsAsync);
  }

  Widget _buildNewLeadsTab() {
    final leadsAsync = ref.watch(agentLeadsByStatusProvider('new'));

    return _buildLeadsList(leadsAsync);
  }

  Widget _buildHotLeadsTab() {
    final leadsAsync = ref.watch(hotLeadsProvider);

    return _buildLeadsList(leadsAsync);
  }

  Widget _buildFollowUpLeadsTab() {
    final leadsAsync = ref.watch(followUpNeededLeadsProvider);

    return _buildLeadsList(leadsAsync);
  }

  Widget _buildConvertedLeadsTab() {
    final leadsAsync = ref.watch(agentLeadsByStatusProvider('converted'));

    return _buildLeadsList(leadsAsync);
  }

  Widget _buildLeadsList(AsyncValue<List<LeadModel>> leadsAsync) {
    return leadsAsync.when(
      data: (leads) {
        if (leads.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.people_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No leads found'),
                Text('Leads will appear here when customers show interest'),
              ],
            ),
          );
        }

        return RefreshIndicator(
          onRefresh: () async {
            ref.invalidate(filteredLeadsProvider);
            ref.invalidate(leadStatisticsProvider);
          },
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(vertical: 8),
            itemCount: leads.length,
            itemBuilder: (context, index) {
              final lead = leads[index];
              return LeadCard(
                lead: lead,
                onTap: () => _showLeadDetails(context, lead),
                onStatusUpdate: () {
                  ref.invalidate(filteredLeadsProvider);
                  ref.invalidate(leadStatisticsProvider);
                },
              );
            },
          ),
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading leads: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(filteredLeadsProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  void _showLeadDetails(BuildContext context, LeadModel lead) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 16),

              Text(
                'Lead Details',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),

              const SizedBox(height: 16),

              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      _buildDetailRow(
                        'Customer',
                        lead.customerName,
                        Icons.person,
                      ),
                      _buildDetailRow('Phone', lead.customerPhone, Icons.phone),
                      _buildDetailRow('Email', lead.customerEmail, Icons.email),
                      _buildDetailRow(
                        'Property',
                        lead.propertyTitle,
                        Icons.home_work,
                      ),
                      _buildDetailRow(
                        'Status',
                        lead.statusDisplayName,
                        Icons.info,
                      ),
                      _buildDetailRow(
                        'Priority',
                        lead.priorityDisplayName,
                        Icons.priority_high,
                      ),
                      _buildDetailRow(
                        'Source',
                        lead.sourceDisplayName,
                        Icons.source,
                      ),
                      _buildDetailRow(
                        'Budget',
                        lead.formattedBudgetRange,
                        Icons.currency_rupee,
                      ),
                      if (lead.notes != null && lead.notes!.isNotEmpty)
                        _buildDetailRow('Notes', lead.notes!, Icons.note),
                      _buildDetailRow(
                        'Created',
                        _formatDate(lead.createdAt),
                        Icons.calendar_today,
                      ),
                      if (lead.lastContactedAt != null)
                        _buildDetailRow(
                          'Last Contact',
                          _formatDate(lead.lastContactedAt!),
                          Icons.access_time,
                        ),
                      if (lead.followUpDate != null)
                        _buildDetailRow(
                          'Follow-up Date',
                          _formatDate(lead.followUpDate!),
                          Icons.schedule,
                        ),
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                    color: Colors.grey,
                  ),
                ),
                const SizedBox(height: 2),
                Text(value, style: const TextStyle(fontSize: 14)),
              ],
            ),
          ),
        ],
      ),
    );
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year} ${date.hour}:${date.minute.toString().padLeft(2, '0')}';
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Leads'),
        content: const Text('Lead filtering options coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showAnalyticsDialog(BuildContext context) {
    final leadStats = ref.read(leadStatisticsProvider);

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Lead Analytics'),
        content: leadStats.when(
          data: (stats) => Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              _buildStatRow('Total Leads', '${stats['totalLeads'] ?? 0}'),
              _buildStatRow(
                'Conversion Rate',
                '${(stats['conversionRate'] ?? 0.0).toStringAsFixed(1)}%',
              ),
              _buildStatRow('Hot Leads', '${stats['hotLeads'] ?? 0}'),
              _buildStatRow(
                'Follow-up Needed',
                '${stats['followUpNeeded'] ?? 0}',
              ),
            ],
          ),
          loading: () => const CircularProgressIndicator(),
          error: (_, __) => const Text('Error loading analytics'),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildStatRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        mainAxisAlignment: MainAxisAlignment.spaceBetween,
        children: [
          Text(label),
          Text(value, style: const TextStyle(fontWeight: FontWeight.bold)),
        ],
      ),
    );
  }

  void _showCreateLeadDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Create New Lead'),
        content: const Text('Lead creation form coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }
}
