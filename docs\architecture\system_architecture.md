# Rama Realty MLM - System Architecture

## Overview
This document outlines the system architecture for the Rama Realty Multi-Level Marketing (MLM) application, designed for real estate marketing through a hierarchical agent network.

## Technology Stack

### Frontend
- **Framework**: Flutter 3.32.5
- **State Management**: Riverpod 2.6.1
- **Navigation**: GoRouter 14.6.2
- **UI Components**: Material Design 3

### Backend
- **Platform**: Firebase
- **Authentication**: Firebase Auth
- **Database**: Cloud Firestore (NoSQL)
- **Storage**: Firebase Storage
- **Functions**: Cloud Functions (Node.js)
- **Hosting**: Firebase Hosting
- **Analytics**: Firebase Analytics
- **Messaging**: Firebase Cloud Messaging (FCM)

### Development Tools
- **IDE**: VS Code / Android Studio
- **Version Control**: Git
- **CI/CD**: Firebase CLI
- **Testing**: Flutter Test Framework

## Architecture Patterns

### Clean Architecture
The application follows Clean Architecture principles with clear separation of concerns:

```
lib/
├── app/                    # Application layer
│   ├── app.dart           # Main app configuration
│   └── routes/            # Navigation configuration
├── core/                  # Core utilities and shared code
│   ├── constants/         # Application constants
│   ├── models/           # Data models
│   ├── services/         # Core services
│   └── utils/            # Utility functions
├── features/             # Feature modules
│   ├── auth/             # Authentication feature
│   ├── dashboard/        # Dashboard feature
│   ├── mlm/              # MLM network feature
│   ├── properties/       # Property management
│   ├── commissions/      # Commission tracking
│   ├── stars/            # Star reward system
│   └── admin/            # Admin panel
└── shared/               # Shared UI components
    ├── widgets/          # Reusable widgets
    ├── themes/           # App theming
    └── extensions/       # Dart extensions
```

### Feature Structure
Each feature follows a consistent structure:

```
feature/
├── data/                 # Data layer
│   ├── datasources/      # Remote/local data sources
│   ├── models/           # Data models
│   └── repositories/     # Repository implementations
├── domain/               # Business logic layer
│   ├── entities/         # Business entities
│   ├── repositories/     # Repository interfaces
│   └── usecases/         # Business use cases
└── presentation/         # Presentation layer
    ├── pages/            # UI pages
    ├── widgets/          # Feature-specific widgets
    └── providers/        # State management
```

## Database Schema

### Collections

#### 1. users
```json
{
  "id": "string",
  "email": "string",
  "name": "string",
  "phoneNumber": "string",
  "role": "agent|admin|super_admin",
  "profileImageUrl": "string?",
  "uplineId": "string?",
  "downlineIds": ["string"],
  "level": "number",
  "totalCommissions": "number",
  "totalStars": "number",
  "totalBonuses": "number",
  "isActive": "boolean",
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "additionalInfo": "object?"
}
```

#### 2. properties
```json
{
  "id": "string",
  "title": "string",
  "description": "string",
  "type": "residential|commercial|land|industrial|agricultural",
  "status": "for_sale|for_rent|sold|rented",
  "price": "number",
  "currency": "string",
  "location": "string",
  "city": "string",
  "state": "string",
  "pincode": "string",
  "latitude": "number?",
  "longitude": "number?",
  "areaSquareFeet": "number?",
  "bedrooms": "number?",
  "bathrooms": "number?",
  "amenities": ["string"],
  "imageUrls": ["string"],
  "videoUrl": "string?",
  "ownerId": "string",
  "assignedAgentId": "string?",
  "isApproved": "boolean",
  "isFeatured": "boolean",
  "createdAt": "timestamp",
  "updatedAt": "timestamp",
  "additionalDetails": "object?"
}
```

#### 3. mlm_network
```json
{
  "id": "string",
  "userId": "string",
  "uplineId": "string?",
  "level": "number",
  "joinedAt": "timestamp",
  "isActive": "boolean"
}
```

#### 4. transactions
```json
{
  "id": "string",
  "propertyId": "string",
  "agentId": "string",
  "buyerId": "string?",
  "type": "sale|rental",
  "amount": "number",
  "commissionRate": "number",
  "status": "pending|completed|cancelled",
  "completedAt": "timestamp?",
  "createdAt": "timestamp"
}
```

#### 5. commissions
```json
{
  "id": "string",
  "transactionId": "string",
  "agentId": "string",
  "amount": "number",
  "level": "number",
  "rate": "number",
  "status": "pending|paid|cancelled",
  "paidAt": "timestamp?",
  "createdAt": "timestamp"
}
```

#### 6. stars
```json
{
  "id": "string",
  "agentId": "string",
  "transactionId": "string",
  "count": "number",
  "type": "sale|upline_bonus",
  "earnedAt": "timestamp"
}
```

#### 7. notifications
```json
{
  "id": "string",
  "userId": "string",
  "type": "new_property|commission|star|bonus|network",
  "title": "string",
  "message": "string",
  "data": "object?",
  "isRead": "boolean",
  "createdAt": "timestamp"
}
```

#### 8. admin_config
```json
{
  "id": "string",
  "type": "commission_rates|star_bonus|general",
  "config": "object",
  "updatedBy": "string",
  "updatedAt": "timestamp"
}
```

## Security Model

### Authentication
- Firebase Authentication with email/password
- JWT tokens for API access
- Role-based access control (RBAC)

### Authorization
- Firestore security rules
- User role validation
- Resource-level permissions

### Data Protection
- HTTPS encryption in transit
- Firestore encryption at rest
- Input validation and sanitization
- File upload restrictions

## Scalability Considerations

### Performance Optimization
- Lazy loading of data
- Image caching and optimization
- Pagination for large datasets
- Efficient Firestore queries with indexing

### Horizontal Scaling
- Firebase auto-scaling
- CDN for static assets
- Optimized database queries
- Caching strategies

### Monitoring
- Firebase Analytics
- Performance monitoring
- Error tracking
- User behavior analytics

## Development Workflow

### Environment Setup
1. Flutter SDK installation
2. Firebase project configuration
3. Development environment setup
4. Testing framework setup

### Deployment Pipeline
1. Code review and testing
2. Firebase deployment
3. Production monitoring
4. Performance optimization

## Next Steps
1. Complete Task 2: User Authentication System
2. Implement Task 3: MLM Hierarchy Structure
3. Develop Task 4: Property Management System
4. Continue with remaining tasks as planned
