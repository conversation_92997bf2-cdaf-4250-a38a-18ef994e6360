import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

/// Report configuration model
class ReportConfig {
  final String id;
  final String name;
  final String description;
  final ReportType type;
  final ReportPeriod period;
  final DateTime startDate;
  final DateTime endDate;
  final List<String> metrics;
  final List<ReportFilter> filters;
  final ReportFormat format;
  final bool includeCharts;
  final bool includeComparisons;
  final String? agentId;
  final DateTime createdAt;

  const ReportConfig({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.period,
    required this.startDate,
    required this.endDate,
    required this.metrics,
    this.filters = const [],
    this.format = ReportFormat.pdf,
    this.includeCharts = true,
    this.includeComparisons = false,
    this.agentId,
    required this.createdAt,
  });

  /// Copy with modifications
  ReportConfig copyWith({
    String? id,
    String? name,
    String? description,
    ReportType? type,
    ReportPeriod? period,
    DateTime? startDate,
    DateTime? endDate,
    List<String>? metrics,
    List<ReportFilter>? filters,
    ReportFormat? format,
    bool? includeCharts,
    bool? includeComparisons,
    String? agentId,
    DateTime? createdAt,
  }) {
    return ReportConfig(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      type: type ?? this.type,
      period: period ?? this.period,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      metrics: metrics ?? this.metrics,
      filters: filters ?? this.filters,
      format: format ?? this.format,
      includeCharts: includeCharts ?? this.includeCharts,
      includeComparisons: includeComparisons ?? this.includeComparisons,
      agentId: agentId ?? this.agentId,
      createdAt: createdAt ?? this.createdAt,
    );
  }

  /// Get formatted date range
  String get formattedDateRange {
    final formatter = DateFormat('MMM dd, yyyy');
    return '${formatter.format(startDate)} - ${formatter.format(endDate)}';
  }
}

/// Report types
enum ReportType {
  sales,
  commissions,
  team,
  network,
  performance,
  financial,
  custom,
}

extension ReportTypeExtension on ReportType {
  String get displayName {
    switch (this) {
      case ReportType.sales:
        return 'Sales Report';
      case ReportType.commissions:
        return 'Commission Report';
      case ReportType.team:
        return 'Team Performance';
      case ReportType.network:
        return 'Network Analysis';
      case ReportType.performance:
        return 'Performance Report';
      case ReportType.financial:
        return 'Financial Summary';
      case ReportType.custom:
        return 'Custom Report';
    }
  }

  String get description {
    switch (this) {
      case ReportType.sales:
        return 'Detailed analysis of property sales and revenue';
      case ReportType.commissions:
        return 'Commission earnings and distribution analysis';
      case ReportType.team:
        return 'Team member performance and growth metrics';
      case ReportType.network:
        return 'MLM network structure and performance analysis';
      case ReportType.performance:
        return 'Individual and comparative performance metrics';
      case ReportType.financial:
        return 'Financial overview and profit analysis';
      case ReportType.custom:
        return 'Customizable report with selected metrics';
    }
  }

  IconData get icon {
    switch (this) {
      case ReportType.sales:
        return Icons.trending_up;
      case ReportType.commissions:
        return Icons.account_balance_wallet;
      case ReportType.team:
        return Icons.group;
      case ReportType.network:
        return Icons.account_tree;
      case ReportType.performance:
        return Icons.analytics;
      case ReportType.financial:
        return Icons.attach_money;
      case ReportType.custom:
        return Icons.tune;
    }
  }

  Color get color {
    switch (this) {
      case ReportType.sales:
        return const Color(0xFF4CAF50);
      case ReportType.commissions:
        return const Color(0xFFFF9800);
      case ReportType.team:
        return const Color(0xFF2196F3);
      case ReportType.network:
        return const Color(0xFF9C27B0);
      case ReportType.performance:
        return const Color(0xFF607D8B);
      case ReportType.financial:
        return const Color(0xFF795548);
      case ReportType.custom:
        return const Color(0xFF3F51B5);
    }
  }

  List<String> get availableMetrics {
    switch (this) {
      case ReportType.sales:
        return [
          'total_sales',
          'properties_sold',
          'average_sale_value',
          'sales_by_type',
          'monthly_trends',
          'conversion_rate',
        ];
      case ReportType.commissions:
        return [
          'total_commissions',
          'commission_by_type',
          'pending_commissions',
          'tier_performance',
          'growth_rate',
          'monthly_earnings',
        ];
      case ReportType.team:
        return [
          'team_size',
          'active_members',
          'recruitment_rate',
          'team_sales',
          'member_performance',
          'retention_rate',
        ];
      case ReportType.network:
        return [
          'network_size',
          'level_distribution',
          'network_growth',
          'downline_performance',
          'leadership_metrics',
          'network_health',
        ];
      case ReportType.performance:
        return [
          'goal_achievement',
          'performance_trends',
          'comparative_analysis',
          'efficiency_metrics',
          'activity_levels',
          'improvement_areas',
        ];
      case ReportType.financial:
        return [
          'revenue_breakdown',
          'profit_margins',
          'expense_analysis',
          'roi_metrics',
          'cash_flow',
          'financial_ratios',
        ];
      case ReportType.custom:
        return [
          'sales_metrics',
          'commission_metrics',
          'team_metrics',
          'network_metrics',
          'performance_metrics',
          'financial_metrics',
        ];
    }
  }
}

/// Report periods
enum ReportPeriod {
  daily,
  weekly,
  monthly,
  quarterly,
  yearly,
  custom,
}

extension ReportPeriodExtension on ReportPeriod {
  String get displayName {
    switch (this) {
      case ReportPeriod.daily:
        return 'Daily';
      case ReportPeriod.weekly:
        return 'Weekly';
      case ReportPeriod.monthly:
        return 'Monthly';
      case ReportPeriod.quarterly:
        return 'Quarterly';
      case ReportPeriod.yearly:
        return 'Yearly';
      case ReportPeriod.custom:
        return 'Custom Range';
    }
  }

  Duration get duration {
    switch (this) {
      case ReportPeriod.daily:
        return const Duration(days: 1);
      case ReportPeriod.weekly:
        return const Duration(days: 7);
      case ReportPeriod.monthly:
        return const Duration(days: 30);
      case ReportPeriod.quarterly:
        return const Duration(days: 90);
      case ReportPeriod.yearly:
        return const Duration(days: 365);
      case ReportPeriod.custom:
        return const Duration(days: 30); // Default for custom
    }
  }

  /// Get date range for period
  DateRange getDateRange() {
    final now = DateTime.now();
    switch (this) {
      case ReportPeriod.daily:
        final today = DateTime(now.year, now.month, now.day);
        return DateRange(today, today.add(const Duration(days: 1)));
      case ReportPeriod.weekly:
        final weekStart = now.subtract(Duration(days: now.weekday - 1));
        final weekStartDate = DateTime(weekStart.year, weekStart.month, weekStart.day);
        return DateRange(weekStartDate, weekStartDate.add(const Duration(days: 7)));
      case ReportPeriod.monthly:
        final monthStart = DateTime(now.year, now.month, 1);
        final nextMonth = DateTime(now.year, now.month + 1, 1);
        return DateRange(monthStart, nextMonth);
      case ReportPeriod.quarterly:
        final quarter = ((now.month - 1) ~/ 3) * 3 + 1;
        final quarterStart = DateTime(now.year, quarter, 1);
        final nextQuarter = DateTime(now.year, quarter + 3, 1);
        return DateRange(quarterStart, nextQuarter);
      case ReportPeriod.yearly:
        final yearStart = DateTime(now.year, 1, 1);
        final nextYear = DateTime(now.year + 1, 1, 1);
        return DateRange(yearStart, nextYear);
      case ReportPeriod.custom:
        final monthStart = DateTime(now.year, now.month, 1);
        return DateRange(monthStart, now);
    }
  }
}

/// Date range helper
class DateRange {
  final DateTime start;
  final DateTime end;

  const DateRange(this.start, this.end);
}

/// Report formats
enum ReportFormat {
  pdf,
  excel,
  csv,
  json,
}

extension ReportFormatExtension on ReportFormat {
  String get displayName {
    switch (this) {
      case ReportFormat.pdf:
        return 'PDF Document';
      case ReportFormat.excel:
        return 'Excel Spreadsheet';
      case ReportFormat.csv:
        return 'CSV File';
      case ReportFormat.json:
        return 'JSON Data';
    }
  }

  String get extension {
    switch (this) {
      case ReportFormat.pdf:
        return '.pdf';
      case ReportFormat.excel:
        return '.xlsx';
      case ReportFormat.csv:
        return '.csv';
      case ReportFormat.json:
        return '.json';
    }
  }

  IconData get icon {
    switch (this) {
      case ReportFormat.pdf:
        return Icons.picture_as_pdf;
      case ReportFormat.excel:
        return Icons.table_chart;
      case ReportFormat.csv:
        return Icons.grid_on;
      case ReportFormat.json:
        return Icons.code;
    }
  }
}

/// Report filter
class ReportFilter {
  final String field;
  final FilterOperator operator;
  final dynamic value;
  final String displayName;

  const ReportFilter({
    required this.field,
    required this.operator,
    required this.value,
    required this.displayName,
  });
}

/// Filter operators
enum FilterOperator {
  equals,
  notEquals,
  greaterThan,
  lessThan,
  greaterThanOrEqual,
  lessThanOrEqual,
  contains,
  startsWith,
  endsWith,
  between,
  inList,
}

/// Generated report model
class GeneratedReport {
  final String id;
  final ReportConfig config;
  final ReportData data;
  final ReportStatus status;
  final DateTime generatedAt;
  final String? filePath;
  final String? downloadUrl;
  final int? fileSize;
  final String? errorMessage;

  const GeneratedReport({
    required this.id,
    required this.config,
    required this.data,
    required this.status,
    required this.generatedAt,
    this.filePath,
    this.downloadUrl,
    this.fileSize,
    this.errorMessage,
  });

  /// Get formatted file size
  String get formattedFileSize {
    if (fileSize == null) return 'Unknown';
    
    if (fileSize! < 1024) {
      return '${fileSize} B';
    } else if (fileSize! < 1024 * 1024) {
      return '${(fileSize! / 1024).toStringAsFixed(1)} KB';
    } else {
      return '${(fileSize! / (1024 * 1024)).toStringAsFixed(1)} MB';
    }
  }
}

/// Report status
enum ReportStatus {
  pending,
  generating,
  completed,
  failed,
}

extension ReportStatusExtension on ReportStatus {
  String get displayName {
    switch (this) {
      case ReportStatus.pending:
        return 'Pending';
      case ReportStatus.generating:
        return 'Generating';
      case ReportStatus.completed:
        return 'Completed';
      case ReportStatus.failed:
        return 'Failed';
    }
  }

  Color get color {
    switch (this) {
      case ReportStatus.pending:
        return const Color(0xFFFF9800);
      case ReportStatus.generating:
        return const Color(0xFF2196F3);
      case ReportStatus.completed:
        return const Color(0xFF4CAF50);
      case ReportStatus.failed:
        return const Color(0xFFF44336);
    }
  }

  IconData get icon {
    switch (this) {
      case ReportStatus.pending:
        return Icons.schedule;
      case ReportStatus.generating:
        return Icons.refresh;
      case ReportStatus.completed:
        return Icons.check_circle;
      case ReportStatus.failed:
        return Icons.error;
    }
  }
}

/// Report data container
class ReportData {
  final Map<String, dynamic> summary;
  final List<ReportDataPoint> dataPoints;
  final List<ReportChart> charts;
  final Map<String, dynamic> metadata;

  const ReportData({
    required this.summary,
    required this.dataPoints,
    required this.charts,
    this.metadata = const {},
  });
}

/// Report data point
class ReportDataPoint {
  final String label;
  final dynamic value;
  final String? unit;
  final DateTime? timestamp;
  final Map<String, dynamic> attributes;

  const ReportDataPoint({
    required this.label,
    required this.value,
    this.unit,
    this.timestamp,
    this.attributes = const {},
  });
}

/// Report chart configuration
class ReportChart {
  final String id;
  final String title;
  final ChartType type;
  final List<ChartDataSeries> series;
  final Map<String, dynamic> options;

  const ReportChart({
    required this.id,
    required this.title,
    required this.type,
    required this.series,
    this.options = const {},
  });
}

/// Chart types
enum ChartType {
  line,
  bar,
  pie,
  area,
  scatter,
  donut,
}

/// Chart data series
class ChartDataSeries {
  final String name;
  final List<ChartDataPoint> data;
  final Color color;

  const ChartDataSeries({
    required this.name,
    required this.data,
    required this.color,
  });
}

/// Chart data point
class ChartDataPoint {
  final String label;
  final double value;
  final DateTime? timestamp;

  const ChartDataPoint({
    required this.label,
    required this.value,
    this.timestamp,
  });
}

/// Report template
class ReportTemplate {
  final String id;
  final String name;
  final String description;
  final ReportType type;
  final List<String> defaultMetrics;
  final List<ReportFilter> defaultFilters;
  final bool isSystem;
  final String? createdBy;
  final DateTime createdAt;

  const ReportTemplate({
    required this.id,
    required this.name,
    required this.description,
    required this.type,
    required this.defaultMetrics,
    this.defaultFilters = const [],
    this.isSystem = false,
    this.createdBy,
    required this.createdAt,
  });
}
