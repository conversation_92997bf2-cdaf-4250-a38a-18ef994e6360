import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/models/user_model.dart';

/// Service for admin referral code management
class ReferralManagementService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get all agents with their referral information
  static Future<List<AgentReferralInfo>> getAllAgentsWithReferrals() async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock data
        await Future.delayed(const Duration(seconds: 1));
        return [
          AgentReferralInfo(
            agent: UserModel(
              id: 'agent-001',
              email: '<EMAIL>',
              name: '<PERSON>',
              phoneNumber: '+91-98765-43210',
              role: AppConstants.agentRole,
              uplineId: 'admin-001',
              level: 1,
              isActive: true,
              createdAt: DateTime.now().subtract(const Duration(days: 30)),
              updatedAt: DateTime.now(),
              additionalInfo: {
                'referralCode': 'JOHN123',
                'uplineReferralCode': 'ADMIN001',
              },
            ),
            referralCode: 'JOHN123',
            uplineReferralCode: 'ADMIN001',
            downlineCount: 3,
            totalCommissions: 15000.0,
            isActive: true,
          ),
          AgentReferralInfo(
            agent: UserModel(
              id: 'agent-002',
              email: '<EMAIL>',
              name: 'Jane Smith',
              phoneNumber: '+91-98765-43211',
              role: AppConstants.agentRole,
              uplineId: 'agent-001',
              level: 2,
              isActive: true,
              createdAt: DateTime.now().subtract(const Duration(days: 15)),
              updatedAt: DateTime.now(),
              additionalInfo: {
                'referralCode': 'JANE456',
                'uplineReferralCode': 'JOHN123',
              },
            ),
            referralCode: 'JANE456',
            uplineReferralCode: 'JOHN123',
            downlineCount: 1,
            totalCommissions: 8500.0,
            isActive: true,
          ),
        ];
      }

      // Production mode - query Firestore
      final snapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: AppConstants.agentRole)
          .orderBy('createdAt', descending: true)
          .get();

      final List<AgentReferralInfo> agents = [];

      for (final doc in snapshot.docs) {
        final user = UserModel.fromFirestore(doc);
        final referralInfo = await _getAgentReferralInfo(user);
        agents.add(referralInfo);
      }

      return agents;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting agents with referrals: $e');
      }
      return [];
    }
  }

  /// Get referral information for a specific agent
  static Future<AgentReferralInfo> _getAgentReferralInfo(
    UserModel agent,
  ) async {
    try {
      // Get downline count
      final downlineCount = agent.downlineIds.length;

      // Get total commissions (mock for now)
      final totalCommissions = downlineCount * 2500.0; // Mock calculation

      return AgentReferralInfo(
        agent: agent,
        referralCode: (agent.additionalInfo?['referralCode'] as String?) ?? '',
        uplineReferralCode:
            (agent.additionalInfo?['uplineReferralCode'] as String?) ?? '',
        downlineCount: downlineCount,
        totalCommissions: totalCommissions,
        isActive: agent.isActive,
      );
    } catch (e) {
      return AgentReferralInfo(
        agent: agent,
        referralCode: (agent.additionalInfo?['referralCode'] as String?) ?? '',
        uplineReferralCode:
            (agent.additionalInfo?['uplineReferralCode'] as String?) ?? '',
        downlineCount: 0,
        totalCommissions: 0.0,
        isActive: agent.isActive,
      );
    }
  }

  /// Update agent's referral code
  static Future<ReferralUpdateResult> updateAgentReferralCode({
    required String agentId,
    required String newReferralCode,
  }) async {
    try {
      // Check if new referral code is already in use
      final existingAgent = await _findAgentByReferralCode(newReferralCode);
      if (existingAgent != null && existingAgent.id != agentId) {
        return ReferralUpdateResult.failure('Referral code already in use');
      }

      if (kIsWeb && kDebugMode) {
        // Development mode - simulate update
        await Future.delayed(const Duration(seconds: 1));
        return ReferralUpdateResult.success(
          'Referral code updated successfully',
        );
      }

      // Production mode - update Firestore
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .update({
            'additionalInfo.referralCode': newReferralCode.toUpperCase(),
            'updatedAt': Timestamp.now(),
          });

      return ReferralUpdateResult.success('Referral code updated successfully');
    } catch (e) {
      if (kDebugMode) {
        print('Error updating referral code: $e');
      }
      return ReferralUpdateResult.failure(
        'Failed to update referral code: ${e.toString()}',
      );
    }
  }

  /// Update agent's upline (change referral hierarchy)
  static Future<ReferralUpdateResult> updateAgentUpline({
    required String agentId,
    required String newUplineReferralCode,
  }) async {
    try {
      // Find new upline agent
      final newUpline = await _findAgentByReferralCode(newUplineReferralCode);
      if (newUpline == null) {
        return ReferralUpdateResult.failure('Upline referral code not found');
      }

      // Prevent circular references
      if (newUpline.id == agentId) {
        return ReferralUpdateResult.failure(
          'Cannot set agent as their own upline',
        );
      }

      if (kIsWeb && kDebugMode) {
        // Development mode - simulate update
        await Future.delayed(const Duration(seconds: 1));
        return ReferralUpdateResult.success('Upline updated successfully');
      }

      // Production mode - update Firestore
      final batch = _firestore.batch();

      // Update agent's upline
      batch.update(
        _firestore.collection(AppConstants.usersCollection).doc(agentId),
        {
          'uplineId': newUpline.id,
          'level': newUpline.level + 1,
          'additionalInfo.uplineReferralCode': newUplineReferralCode
              .toUpperCase(),
          'updatedAt': Timestamp.now(),
        },
      );

      // Add to new upline's downline
      batch.update(
        _firestore.collection(AppConstants.usersCollection).doc(newUpline.id),
        {
          'downlineIds': FieldValue.arrayUnion([agentId]),
          'updatedAt': Timestamp.now(),
        },
      );

      await batch.commit();

      return ReferralUpdateResult.success('Upline updated successfully');
    } catch (e) {
      if (kDebugMode) {
        print('Error updating upline: $e');
      }
      return ReferralUpdateResult.failure(
        'Failed to update upline: ${e.toString()}',
      );
    }
  }

  /// Find agent by referral code
  static Future<UserModel?> _findAgentByReferralCode(
    String referralCode,
  ) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - simulate lookup
        await Future.delayed(const Duration(milliseconds: 500));
        if (referralCode.toUpperCase() == 'ADMIN001') {
          return UserModel(
            id: 'admin-001',
            email: '<EMAIL>',
            name: 'Admin User',
            phoneNumber: '+91-98765-43210',
            role: AppConstants.adminRole,
            level: 0,
            isActive: true,
            createdAt: DateTime.now(),
            updatedAt: DateTime.now(),
            additionalInfo: {'referralCode': 'ADMIN001'},
          );
        }
        return null;
      }

      final snapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where(
            'additionalInfo.referralCode',
            isEqualTo: referralCode.toUpperCase(),
          )
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return UserModel.fromFirestore(snapshot.docs.first);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding agent by referral code: $e');
      }
      return null;
    }
  }

  /// Generate referral code analytics
  static Future<ReferralAnalytics> getReferralAnalytics() async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock analytics
        await Future.delayed(const Duration(seconds: 1));
        return ReferralAnalytics(
          totalAgents: 25,
          activeAgents: 22,
          totalReferrals: 18,
          avgDownlinePerAgent: 2.3,
          topPerformers: [
            TopPerformer(
              agentName: 'John Doe',
              referralCode: 'JOHN123',
              downlineCount: 8,
              totalCommissions: 25000.0,
            ),
            TopPerformer(
              agentName: 'Jane Smith',
              referralCode: 'JANE456',
              downlineCount: 6,
              totalCommissions: 18500.0,
            ),
          ],
        );
      }

      // Production mode - calculate real analytics
      final agents = await getAllAgentsWithReferrals();

      final totalAgents = agents.length;
      final activeAgents = agents.where((a) => a.isActive).length;
      final totalReferrals = agents.fold<int>(
        0,
        (total, a) => total + a.downlineCount,
      );
      final avgDownlinePerAgent = totalAgents > 0
          ? totalReferrals / totalAgents
          : 0.0;

      // Get top performers
      final sortedAgents = List<AgentReferralInfo>.from(agents)
        ..sort((a, b) => b.downlineCount.compareTo(a.downlineCount));

      final topPerformers = sortedAgents
          .take(5)
          .map(
            (agent) => TopPerformer(
              agentName: agent.agent.name,
              referralCode: agent.referralCode,
              downlineCount: agent.downlineCount,
              totalCommissions: agent.totalCommissions,
            ),
          )
          .toList();

      return ReferralAnalytics(
        totalAgents: totalAgents,
        activeAgents: activeAgents,
        totalReferrals: totalReferrals,
        avgDownlinePerAgent: avgDownlinePerAgent,
        topPerformers: topPerformers,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting referral analytics: $e');
      }
      return ReferralAnalytics(
        totalAgents: 0,
        activeAgents: 0,
        totalReferrals: 0,
        avgDownlinePerAgent: 0.0,
        topPerformers: [],
      );
    }
  }
}

/// Agent referral information model
class AgentReferralInfo {
  final UserModel agent;
  final String referralCode;
  final String uplineReferralCode;
  final int downlineCount;
  final double totalCommissions;
  final bool isActive;

  const AgentReferralInfo({
    required this.agent,
    required this.referralCode,
    required this.uplineReferralCode,
    required this.downlineCount,
    required this.totalCommissions,
    required this.isActive,
  });
}

/// Referral update result
class ReferralUpdateResult {
  final bool isSuccess;
  final String message;

  const ReferralUpdateResult._({
    required this.isSuccess,
    required this.message,
  });

  factory ReferralUpdateResult.success(String message) {
    return ReferralUpdateResult._(isSuccess: true, message: message);
  }

  factory ReferralUpdateResult.failure(String message) {
    return ReferralUpdateResult._(isSuccess: false, message: message);
  }
}

/// Referral analytics model
class ReferralAnalytics {
  final int totalAgents;
  final int activeAgents;
  final int totalReferrals;
  final double avgDownlinePerAgent;
  final List<TopPerformer> topPerformers;

  const ReferralAnalytics({
    required this.totalAgents,
    required this.activeAgents,
    required this.totalReferrals,
    required this.avgDownlinePerAgent,
    required this.topPerformers,
  });
}

/// Top performer model
class TopPerformer {
  final String agentName;
  final String referralCode;
  final int downlineCount;
  final double totalCommissions;

  const TopPerformer({
    required this.agentName,
    required this.referralCode,
    required this.downlineCount,
    required this.totalCommissions,
  });
}
