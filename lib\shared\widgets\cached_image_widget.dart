import 'dart:typed_data';
import 'package:flutter/material.dart';
import '../../services/image_storage_service.dart';

/// Widget to display images stored in Firestore as Base64
class CachedImageWidget extends StatefulWidget {
  final String? imageId;
  final double? width;
  final double? height;
  final BoxFit fit;
  final Widget? placeholder;
  final Widget? errorWidget;
  final BorderRadius? borderRadius;

  const CachedImageWidget({
    super.key,
    required this.imageId,
    this.width,
    this.height,
    this.fit = BoxFit.cover,
    this.placeholder,
    this.errorWidget,
    this.borderRadius,
  });

  @override
  State<CachedImageWidget> createState() => _CachedImageWidgetState();
}

class _CachedImageWidgetState extends State<CachedImageWidget> {
  Uint8List? _imageData;
  bool _isLoading = true;
  bool _hasError = false;

  @override
  void initState() {
    super.initState();
    _loadImage();
  }

  @override
  void didUpdateWidget(CachedImageWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    if (oldWidget.imageId != widget.imageId) {
      _loadImage();
    }
  }

  Future<void> _loadImage() async {
    if (widget.imageId == null || widget.imageId!.isEmpty) {
      setState(() {
        _isLoading = false;
        _hasError = true;
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _hasError = false;
    });

    try {
      final imageData = await ImageStorageService.getImage(widget.imageId!);
      if (mounted) {
        setState(() {
          _imageData = imageData;
          _isLoading = false;
          _hasError = imageData == null;
        });
      }
    } catch (e) {
      if (mounted) {
        setState(() {
          _isLoading = false;
          _hasError = true;
        });
      }
    }
  }

  @override
  Widget build(BuildContext context) {
    Widget child;

    if (_isLoading) {
      child = widget.placeholder ??
          Container(
            width: widget.width,
            height: widget.height,
            color: Colors.grey[800],
            child: const Center(
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.orange),
              ),
            ),
          );
    } else if (_hasError || _imageData == null) {
      child = widget.errorWidget ??
          Container(
            width: widget.width,
            height: widget.height,
            color: Colors.grey[800],
            child: const Icon(
              Icons.image_not_supported,
              color: Colors.grey,
              size: 32,
            ),
          );
    } else {
      child = Image.memory(
        _imageData!,
        width: widget.width,
        height: widget.height,
        fit: widget.fit,
        errorBuilder: (context, error, stackTrace) {
          return widget.errorWidget ??
              Container(
                width: widget.width,
                height: widget.height,
                color: Colors.grey[800],
                child: const Icon(
                  Icons.broken_image,
                  color: Colors.grey,
                  size: 32,
                ),
              );
        },
      );
    }

    if (widget.borderRadius != null) {
      child = ClipRRect(
        borderRadius: widget.borderRadius!,
        child: child,
      );
    }

    return child;
  }
}

/// Widget for uploading and displaying profile images
class ProfileImageWidget extends StatefulWidget {
  final String? currentImageId;
  final Function(String imageId)? onImageUploaded;
  final double size;
  final bool allowEdit;

  const ProfileImageWidget({
    super.key,
    this.currentImageId,
    this.onImageUploaded,
    this.size = 100,
    this.allowEdit = true,
  });

  @override
  State<ProfileImageWidget> createState() => _ProfileImageWidgetState();
}

class _ProfileImageWidgetState extends State<ProfileImageWidget> {
  bool _isUploading = false;

  @override
  Widget build(BuildContext context) {
    return Stack(
      children: [
        // Profile image
        Container(
          width: widget.size,
          height: widget.size,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            border: Border.all(
              color: const Color(0xFFFF6B35),
              width: 2,
            ),
          ),
          child: ClipOval(
            child: widget.currentImageId != null
                ? CachedImageWidget(
                    imageId: widget.currentImageId,
                    width: widget.size,
                    height: widget.size,
                    fit: BoxFit.cover,
                  )
                : Container(
                    color: Colors.grey[800],
                    child: Icon(
                      Icons.person,
                      size: widget.size * 0.6,
                      color: Colors.grey[400],
                    ),
                  ),
          ),
        ),
        
        // Edit button
        if (widget.allowEdit)
          Positioned(
            bottom: 0,
            right: 0,
            child: GestureDetector(
              onTap: _isUploading ? null : _uploadImage,
              child: Container(
                width: 32,
                height: 32,
                decoration: const BoxDecoration(
                  color: Color(0xFFFF6B35),
                  shape: BoxShape.circle,
                ),
                child: _isUploading
                    ? const Padding(
                        padding: EdgeInsets.all(8),
                        child: CircularProgressIndicator(
                          strokeWidth: 2,
                          valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                        ),
                      )
                    : const Icon(
                        Icons.camera_alt,
                        color: Colors.white,
                        size: 16,
                      ),
              ),
            ),
          ),
      ],
    );
  }

  Future<void> _uploadImage() async {
    setState(() => _isUploading = true);

    try {
      final imageId = await ImageStorageService.pickAndUploadImage(
        collection: 'profile_images',
        userId: 'current_user', // Replace with actual user ID
      );

      if (imageId != null && widget.onImageUploaded != null) {
        widget.onImageUploaded!(imageId);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error uploading image: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }
}

/// Widget for property image gallery
class PropertyImageGallery extends StatelessWidget {
  final List<String> imageIds;
  final double height;
  final Function(String imageId)? onImageTap;

  const PropertyImageGallery({
    super.key,
    required this.imageIds,
    this.height = 200,
    this.onImageTap,
  });

  @override
  Widget build(BuildContext context) {
    if (imageIds.isEmpty) {
      return Container(
        height: height,
        decoration: BoxDecoration(
          color: Colors.grey[800],
          borderRadius: BorderRadius.circular(12),
        ),
        child: const Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              Icon(
                Icons.image_not_supported,
                size: 48,
                color: Colors.grey,
              ),
              SizedBox(height: 8),
              Text(
                'No images available',
                style: TextStyle(color: Colors.grey),
              ),
            ],
          ),
        ),
      );
    }

    return SizedBox(
      height: height,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: imageIds.length,
        itemBuilder: (context, index) {
          return Padding(
            padding: EdgeInsets.only(
              right: index < imageIds.length - 1 ? 12 : 0,
            ),
            child: GestureDetector(
              onTap: () => onImageTap?.call(imageIds[index]),
              child: CachedImageWidget(
                imageId: imageIds[index],
                width: height * 1.5,
                height: height,
                borderRadius: BorderRadius.circular(12),
              ),
            ),
          );
        },
      ),
    );
  }
}
