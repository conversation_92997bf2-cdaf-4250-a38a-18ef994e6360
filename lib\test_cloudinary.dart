import 'package:flutter/material.dart';
import 'services/cloudinary_service.dart';
import 'shared/widgets/image_upload_widget.dart';

/// Simple test page for Cloudinary integration
class CloudinaryTestPage extends StatefulWidget {
  const CloudinaryTestPage({super.key});

  @override
  State<CloudinaryTestPage> createState() => _CloudinaryTestPageState();
}

class _CloudinaryTestPageState extends State<CloudinaryTestPage> {
  String? _uploadedImageUrl;
  final List<String> _multipleImages = [];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F0F),
      appBar: AppBar(
        title: const Text('Cloudinary Test'),
        backgroundColor: const Color(0xFF1A1A1A),
      ),
      body: SingleChildScrollView(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status Card
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF1A1A1A),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: CloudinaryService.isConfigured ? Colors.green : Colors.red,
                ),
              ),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Row(
                    children: [
                      Icon(
                        CloudinaryService.isConfigured ? Icons.check_circle : Icons.error,
                        color: CloudinaryService.isConfigured ? Colors.green : Colors.red,
                      ),
                      const SizedBox(width: 8),
                      Text(
                        CloudinaryService.isConfigured 
                          ? 'Cloudinary Configured ✅' 
                          : 'Cloudinary Not Configured ❌',
                        style: TextStyle(
                          color: CloudinaryService.isConfigured ? Colors.green : Colors.red,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  const SizedBox(height: 8),
                  Text(
                    CloudinaryService.isConfigured
                      ? 'Cloud: dvojmiyo7\nReady to upload images!'
                      : 'Please configure Cloudinary credentials',
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Single Image Upload Test
            const Text(
              'Single Image Upload Test',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            ImageUploadWidget(
              currentImageUrl: _uploadedImageUrl,
              width: double.infinity,
              height: 200,
              placeholder: 'Tap to test image upload',
              folder: 'test_uploads',
              onImageUploaded: (url) {
                setState(() {
                  _uploadedImageUrl = url;
                });
                ScaffoldMessenger.of(context).showSnackBar(
                  SnackBar(
                    content: Text('Image uploaded: $url'),
                    backgroundColor: Colors.green,
                  ),
                );
              },
              onImageRemoved: () {
                setState(() {
                  _uploadedImageUrl = null;
                });
              },
            ),
            
            if (_uploadedImageUrl != null) ...[
              const SizedBox(height: 16),
              Container(
                padding: const EdgeInsets.all(12),
                decoration: BoxDecoration(
                  color: const Color(0xFF1A1A1A),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    const Text(
                      'Uploaded Image URL:',
                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    SelectableText(
                      _uploadedImageUrl!,
                      style: const TextStyle(color: Colors.blue, fontSize: 12),
                    ),
                    const SizedBox(height: 8),
                    const Text(
                      'Optimized URLs:',
                      style: TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      'Thumbnail: ${CloudinaryService.getThumbnailUrl(_uploadedImageUrl!)}',
                      style: const TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                    const SizedBox(height: 2),
                    Text(
                      'Medium: ${CloudinaryService.getMediumUrl(_uploadedImageUrl!)}',
                      style: const TextStyle(color: Colors.grey, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ],
            
            const SizedBox(height: 32),
            
            // Multiple Images Upload Test
            const Text(
              'Multiple Images Upload Test',
              style: TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            MultiImageUploadWidget(
              currentImageUrls: _multipleImages,
              maxImages: 3,
              folder: 'test_gallery',
              onImagesChanged: (urls) {
                setState(() {
                  _multipleImages.clear();
                  _multipleImages.addAll(urls);
                });
              },
            ),
            
            const SizedBox(height: 32),
            
            // Instructions
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF1A1A1A),
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '📋 Test Instructions:',
                    style: TextStyle(
                      color: Colors.white,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '1. ✅ Cloudinary should show as configured\n'
                    '2. 📸 Try uploading a single image\n'
                    '3. 🖼️ Try uploading multiple images\n'
                    '4. 🔗 Check that URLs are generated\n'
                    '5. 🎯 Verify images display correctly',
                    style: TextStyle(color: Colors.grey, height: 1.5),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Benefits
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: Colors.green.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(color: Colors.green.withValues(alpha: 0.3)),
              ),
              child: const Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    '🎉 Cloudinary Benefits:',
                    style: TextStyle(
                      color: Colors.green,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  SizedBox(height: 8),
                  Text(
                    '• 🆓 25GB free storage + 25GB bandwidth\n'
                    '• ⚡ Automatic image optimization\n'
                    '• 🌍 Global CDN for fast delivery\n'
                    '• 📱 Responsive image transformations\n'
                    '• 🔒 Secure and reliable hosting',
                    style: TextStyle(color: Colors.white, height: 1.5),
                  ),
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }
}

/// Simple app to test Cloudinary
class CloudinaryTestApp extends StatelessWidget {
  const CloudinaryTestApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Cloudinary Test',
      theme: ThemeData.dark(),
      home: const CloudinaryTestPage(),
      debugShowCheckedModeBanner: false,
    );
  }
}
