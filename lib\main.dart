import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

// Import the full MLM app
import 'app/app.dart';
// Keep simple demo as fallback
import 'simple_demo.dart';
// Import Cloudinary test
import 'test_cloudinary.dart';

void main() async {
  // Uncomment the line below to test Cloudinary integration
  // runApp(const CloudinaryTestApp()); return;

  // Landing page first - v2.0

  // Initialize the full MLM app with Firebase
  try {
    await initializeApp();

    runApp(const ProviderScope(child: RamaSamriddhiApp()));
  } catch (e) {
    // If Firebase initialization fails, show error or fallback to demo
    if (kDebugMode) {
      print('Failed to initialize app: $e');
      // Fallback to simple demo in debug mode if Firebase fails
      runApp(const SimpleMLMDemo());
    } else {
      // Show error in production
      runApp(
        MaterialApp(
          home: Scaffold(
            body: Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  const Text(
                    'Firebase Configuration Required',
                    style: TextStyle(fontSize: 20, fontWeight: FontWeight.bold),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Error: $e',
                    textAlign: TextAlign.center,
                    style: const TextStyle(color: Colors.grey),
                  ),
                ],
              ),
            ),
          ),
        ),
      );
    }
  }
}
