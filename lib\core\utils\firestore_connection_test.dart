import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:flutter/foundation.dart';

/// Simple utility to test Firestore connection and permissions
class FirestoreConnectionTest {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Test basic Firestore connection
  static Future<bool> testConnection() async {
    try {
      if (kDebugMode) {
        print('🔄 Testing Firestore connection...');
      }

      // Try to read from a test collection
      final testDoc = await _firestore
          .collection('test')
          .doc('connection')
          .get();

      if (kDebugMode) {
        print('✅ Firestore connection successful');
        print('📄 Test document exists: ${testDoc.exists}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firestore connection failed: $e');
      }
      return false;
    }
  }

  /// Test authentication state
  static Future<bool> testAuth() async {
    try {
      final user = _auth.currentUser;

      if (kDebugMode) {
        print('🔄 Testing authentication...');
        print('👤 Current user: ${user?.email ?? 'Not authenticated'}');
        print('🆔 User ID: ${user?.uid ?? 'N/A'}');
        print('✅ Email verified: ${user?.emailVerified ?? false}');
      }

      return user != null;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Authentication test failed: $e');
      }
      return false;
    }
  }

  /// Test writing to Firestore
  static Future<bool> testWrite() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        if (kDebugMode) {
          print('❌ Cannot test write: User not authenticated');
        }
        return false;
      }

      if (kDebugMode) {
        print('🔄 Testing Firestore write permissions...');
      }

      // Try to write a test document
      await _firestore.collection('test').doc('write_test_${user.uid}').set({
        'timestamp': FieldValue.serverTimestamp(),
        'userId': user.uid,
        'email': user.email,
        'test': true,
      });

      if (kDebugMode) {
        print('✅ Firestore write successful');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Firestore write failed: $e');
        if (e is FirebaseException) {
          print('🔥 Firebase error code: ${e.code}');
          print('🔥 Firebase error message: ${e.message}');
        }
      }
      return false;
    }
  }

  /// Test reading from users collection
  static Future<bool> testUsersCollection() async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        if (kDebugMode) {
          print('❌ Cannot test users collection: User not authenticated');
        }
        return false;
      }

      if (kDebugMode) {
        print('🔄 Testing users collection access...');
      }

      // Try to read from users collection
      final usersSnapshot = await _firestore.collection('users').limit(1).get();

      if (kDebugMode) {
        print('✅ Users collection access successful');
        print('📊 Documents found: ${usersSnapshot.docs.length}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Users collection access failed: $e');
        if (e is FirebaseException) {
          print('🔥 Firebase error code: ${e.code}');
          print('🔥 Firebase error message: ${e.message}');
        }
      }
      return false;
    }
  }

  /// Run all tests
  static Future<Map<String, bool>> runAllTests() async {
    print('🚀 Running Firestore connection tests...');

    final results = <String, bool>{};

    results['connection'] = await testConnection();
    results['auth'] = await testAuth();
    results['write'] = await testWrite();
    results['usersCollection'] = await testUsersCollection();

    print('📊 Test Results:');
    results.forEach((test, result) {
      print('  ${result ? '✅' : '❌'} $test: ${result ? 'PASS' : 'FAIL'}');
    });

    return results;
  }

  /// Clean up test data
  static Future<void> cleanupTestData() async {
    try {
      final user = _auth.currentUser;
      if (user == null) return;

      if (kDebugMode) {
        print('🧹 Cleaning up test data...');
      }

      // Delete test documents
      await _firestore
          .collection('test')
          .doc('write_test_${user.uid}')
          .delete();

      if (kDebugMode) {
        print('✅ Test data cleaned up');
      }
    } catch (e) {
      if (kDebugMode) {
        print('⚠️ Error cleaning up test data: $e');
      }
    }
  }
}
