@echo off
echo ========================================
echo   Rama Realty MLM - Firebase Deployment
echo ========================================
echo.

echo Step 1: Checking prerequisites...
where firebase >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Firebase CLI not found!
    echo Please install with: npm install -g firebase-tools
    pause
    exit /b 1
)

where flutter >nul 2>&1
if %errorlevel% neq 0 (
    echo ERROR: Flutter not found!
    echo Please ensure Flutter is installed and in PATH
    pause
    exit /b 1
)

echo Prerequisites check passed!
echo.

echo Step 2: Firebase login...
firebase login
if %errorlevel% neq 0 (
    echo ERROR: Firebase login failed!
    pause
    exit /b 1
)

echo.
echo Step 3: Initialize Firebase project...
echo Please select your Firebase project when prompted
firebase use --add
if %errorlevel% neq 0 (
    echo ERROR: Firebase project selection failed!
    pause
    exit /b 1
)

echo.
echo Step 4: Clean and get dependencies...
flutter clean
flutter pub get
if %errorlevel% neq 0 (
    echo ERROR: Failed to get dependencies!
    pause
    exit /b 1
)

echo.
echo Step 5: Building web application...
flutter build web --release --dart-define=ENVIRONMENT=production
if %errorlevel% neq 0 (
    echo ERROR: Build failed!
    pause
    exit /b 1
)

echo.
echo Step 6: Deploying to Firebase...
firebase deploy
if %errorlevel% neq 0 (
    echo ERROR: Deployment failed!
    pause
    exit /b 1
)

echo.
echo ========================================
echo   Deployment Completed Successfully!
echo ========================================
echo.
echo Your Rama Realty MLM app is now live!
echo.
echo Next steps:
echo 1. Test the application in your browser
echo 2. Create admin user account
echo 3. Configure initial settings
echo.
pause
