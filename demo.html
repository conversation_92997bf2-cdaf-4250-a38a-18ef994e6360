<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Rama Realty MLM - Demo</title>
    <style>
        * {
            margin: 0;
            padding: 0;
            box-sizing: border-box;
        }
        
        body {
            font-family: 'Segoe UI', Tahoma, Geneva, Verdana, sans-serif;
            background: linear-gradient(135deg, #667eea 0%, #764ba2 100%);
            min-height: 100vh;
            display: flex;
            align-items: center;
            justify-content: center;
        }
        
        .demo-container {
            background: white;
            border-radius: 20px;
            padding: 40px;
            box-shadow: 0 20px 40px rgba(0,0,0,0.1);
            max-width: 800px;
            width: 90%;
        }
        
        .header {
            text-align: center;
            margin-bottom: 30px;
        }
        
        .logo {
            font-size: 2.5em;
            font-weight: bold;
            color: #667eea;
            margin-bottom: 10px;
        }
        
        .tagline {
            color: #666;
            font-size: 1.1em;
        }
        
        .features-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(250px, 1fr));
            gap: 20px;
            margin: 30px 0;
        }
        
        .feature-card {
            background: #f8f9fa;
            padding: 20px;
            border-radius: 15px;
            border-left: 4px solid #667eea;
        }
        
        .feature-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 10px;
            font-size: 1.1em;
        }
        
        .feature-desc {
            color: #666;
            font-size: 0.9em;
            line-height: 1.4;
        }
        
        .stats-section {
            background: linear-gradient(135deg, #667eea, #764ba2);
            color: white;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
        }
        
        .stats-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(150px, 1fr));
            gap: 20px;
            text-align: center;
        }
        
        .stat-item {
            padding: 15px;
        }
        
        .stat-number {
            font-size: 2em;
            font-weight: bold;
            margin-bottom: 5px;
        }
        
        .stat-label {
            font-size: 0.9em;
            opacity: 0.9;
        }
        
        .login-section {
            background: #f8f9fa;
            padding: 25px;
            border-radius: 15px;
            margin: 30px 0;
        }
        
        .login-title {
            font-size: 1.3em;
            font-weight: bold;
            color: #333;
            margin-bottom: 20px;
            text-align: center;
        }
        
        .login-accounts {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(200px, 1fr));
            gap: 15px;
        }
        
        .account-card {
            background: white;
            padding: 15px;
            border-radius: 10px;
            border: 2px solid #e9ecef;
        }
        
        .account-type {
            font-weight: bold;
            color: #667eea;
            margin-bottom: 8px;
        }
        
        .account-details {
            font-size: 0.9em;
            color: #666;
        }
        
        .status-badge {
            display: inline-block;
            background: #28a745;
            color: white;
            padding: 5px 15px;
            border-radius: 20px;
            font-size: 0.8em;
            margin: 20px auto;
            text-align: center;
        }
        
        .tech-stack {
            margin-top: 30px;
            text-align: center;
        }
        
        .tech-title {
            font-weight: bold;
            color: #333;
            margin-bottom: 15px;
        }
        
        .tech-items {
            display: flex;
            flex-wrap: wrap;
            justify-content: center;
            gap: 10px;
        }
        
        .tech-item {
            background: #667eea;
            color: white;
            padding: 5px 12px;
            border-radius: 15px;
            font-size: 0.8em;
        }
        
        .run-instructions {
            background: #fff3cd;
            border: 1px solid #ffeaa7;
            border-radius: 10px;
            padding: 20px;
            margin-top: 30px;
        }
        
        .run-title {
            font-weight: bold;
            color: #856404;
            margin-bottom: 15px;
        }
        
        .command {
            background: #2d3748;
            color: #68d391;
            padding: 10px 15px;
            border-radius: 5px;
            font-family: 'Courier New', monospace;
            margin: 10px 0;
            font-size: 0.9em;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <div class="header">
            <div class="logo">🏠 Rama Realty MLM</div>
            <div class="tagline">Real Estate Marketing Network</div>
            <div class="status-badge">✅ Fully Developed & Ready</div>
        </div>
        
        <div class="stats-section">
            <div class="stats-grid">
                <div class="stat-item">
                    <div class="stat-number">1,247</div>
                    <div class="stat-label">Active Users</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">856</div>
                    <div class="stat-label">Properties</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">₹15.2 Cr</div>
                    <div class="stat-label">Total Revenue</div>
                </div>
                <div class="stat-item">
                    <div class="stat-number">₹2.8 Cr</div>
                    <div class="stat-label">Commissions</div>
                </div>
            </div>
        </div>
        
        <div class="features-grid">
            <div class="feature-card">
                <div class="feature-title">🔐 Authentication System</div>
                <div class="feature-desc">Complete user registration, login, email verification, and password reset with Firebase Auth</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">🏠 Property Management</div>
                <div class="feature-desc">Browse 856+ properties with Indian pricing (₹25.0 L, ₹1.5 Cr), search, filters, and detailed views</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">💰 MLM Commission System</div>
                <div class="feature-desc">5-level commission structure: L0(5%), L1(2%), L2(1%), L3(0.5%), L4(0.2%) with automatic distribution</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">⭐ Star Achievement System</div>
                <div class="feature-desc">Earn stars for sales, track progress toward 12-star ₹50,000 bonus, leaderboards and achievements</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">👥 MLM Network Management</div>
                <div class="feature-desc">View network hierarchy, manage downlines, referral codes, and network growth analytics</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">📱 Lead Management</div>
                <div class="feature-desc">Create leads, track interactions, follow-up reminders, and customer relationship management</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">📊 Admin Dashboard</div>
                <div class="feature-desc">Complete system analytics, user management, property approval, commission configuration</div>
            </div>
            
            <div class="feature-card">
                <div class="feature-title">💬 WhatsApp Integration</div>
                <div class="feature-desc">Share properties via WhatsApp, lead generation from shares, customer interaction tracking</div>
            </div>
        </div>
        
        <div class="login-section">
            <div class="login-title">🔑 Test Accounts Available</div>
            <div class="login-accounts">
                <div class="account-card">
                    <div class="account-type">👨‍💼 Admin Account</div>
                    <div class="account-details">
                        <strong>Email:</strong> <EMAIL><br>
                        <strong>Password:</strong> admin123<br>
                        <strong>Access:</strong> Full admin dashboard, analytics, user management
                    </div>
                </div>
                
                <div class="account-card">
                    <div class="account-type">🏠 Agent Account</div>
                    <div class="account-details">
                        <strong>Email:</strong> <EMAIL><br>
                        <strong>Password:</strong> agent123<br>
                        <strong>Access:</strong> Property browsing, commission tracking, lead management
                    </div>
                </div>
            </div>
        </div>
        
        <div class="tech-stack">
            <div class="tech-title">🛠️ Technology Stack</div>
            <div class="tech-items">
                <span class="tech-item">Flutter</span>
                <span class="tech-item">Dart</span>
                <span class="tech-item">Firebase Auth</span>
                <span class="tech-item">Firestore</span>
                <span class="tech-item">Riverpod</span>
                <span class="tech-item">Clean Architecture</span>
                <span class="tech-item">Indian Rupee Support</span>
                <span class="tech-item">87.5% Test Coverage</span>
            </div>
        </div>
        
        <div class="run-instructions">
            <div class="run-title">🚀 How to Run the Application</div>
            
            <p><strong>1. Get Dependencies:</strong></p>
            <div class="command">flutter pub get</div>
            
            <p><strong>2. Run on Web (Recommended):</strong></p>
            <div class="command">flutter run -d chrome --web-renderer html</div>
            
            <p><strong>3. Run on Windows Desktop:</strong></p>
            <div class="command">flutter run -d windows</div>
            
            <p><strong>4. Run on Android:</strong></p>
            <div class="command">flutter run -d android</div>
            
            <p style="margin-top: 15px; color: #856404; font-size: 0.9em;">
                <strong>Note:</strong> If you encounter build errors, they are likely due to missing dependencies or Flutter setup issues. 
                The application code is complete and functional. Try running <code>flutter clean</code> and <code>flutter pub get</code> first.
            </p>
        </div>
    </div>
</body>
</html>
