import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:intl/intl.dart';

import 'commission_enums.dart';

/// Enhanced commission model with comprehensive tracking
class EnhancedCommissionModel {
  final String id;
  final String agentId;
  final String agentName;
  final String? propertyId;
  final String? propertyTitle;
  final CommissionType type;
  final CommissionStatus status;
  final double amount;
  final double baseAmount;
  final double percentage;
  final CommissionTier tier;
  final double tierMultiplier;
  final String? sourceAgentId;
  final String? sourceAgentName;
  final int level;
  final DateTime calculatedAt;
  final DateTime? approvedAt;
  final DateTime? paidAt;
  final String? approvedBy;
  final String? paidBy;
  final String? rejectionReason;
  final Map<String, dynamic> metadata;
  final String? transactionId;
  final String? paymentMethod;
  final DateTime createdAt;
  final DateTime updatedAt;

  const EnhancedCommissionModel({
    required this.id,
    required this.agentId,
    required this.agentName,
    this.propertyId,
    this.propertyTitle,
    required this.type,
    required this.status,
    required this.amount,
    required this.baseAmount,
    required this.percentage,
    required this.tier,
    required this.tierMultiplier,
    this.sourceAgentId,
    this.sourceAgentName,
    required this.level,
    required this.calculatedAt,
    this.approvedAt,
    this.paidAt,
    this.approvedBy,
    this.paidBy,
    this.rejectionReason,
    this.metadata = const {},
    this.transactionId,
    this.paymentMethod,
    required this.createdAt,
    required this.updatedAt,
  });

  /// Create from Firestore document
  factory EnhancedCommissionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return EnhancedCommissionModel(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      propertyId: data['propertyId'],
      propertyTitle: data['propertyTitle'],
      type: CommissionType.values.firstWhere(
        (t) => t.value == data['type'],
        orElse: () => CommissionType.directSale,
      ),
      status: CommissionStatus.values.firstWhere(
        (s) => s.value == data['status'],
        orElse: () => CommissionStatus.pending,
      ),
      amount: (data['amount'] ?? 0).toDouble(),
      baseAmount: (data['baseAmount'] ?? 0).toDouble(),
      percentage: (data['percentage'] ?? 0).toDouble(),
      tier: CommissionTier.values.firstWhere(
        (t) => t.name == data['tier'],
        orElse: () => CommissionTier.bronze,
      ),
      tierMultiplier: (data['tierMultiplier'] ?? 1.0).toDouble(),
      sourceAgentId: data['sourceAgentId'],
      sourceAgentName: data['sourceAgentName'],
      level: data['level'] ?? 0,
      calculatedAt: (data['calculatedAt'] as Timestamp).toDate(),
      approvedAt: data['approvedAt'] != null 
          ? (data['approvedAt'] as Timestamp).toDate() 
          : null,
      paidAt: data['paidAt'] != null 
          ? (data['paidAt'] as Timestamp).toDate() 
          : null,
      approvedBy: data['approvedBy'],
      paidBy: data['paidBy'],
      rejectionReason: data['rejectionReason'],
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      transactionId: data['transactionId'],
      paymentMethod: data['paymentMethod'],
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'agentId': agentId,
      'agentName': agentName,
      'propertyId': propertyId,
      'propertyTitle': propertyTitle,
      'type': type.value,
      'status': status.value,
      'amount': amount,
      'baseAmount': baseAmount,
      'percentage': percentage,
      'tier': tier.name,
      'tierMultiplier': tierMultiplier,
      'sourceAgentId': sourceAgentId,
      'sourceAgentName': sourceAgentName,
      'level': level,
      'calculatedAt': Timestamp.fromDate(calculatedAt),
      'approvedAt': approvedAt != null ? Timestamp.fromDate(approvedAt!) : null,
      'paidAt': paidAt != null ? Timestamp.fromDate(paidAt!) : null,
      'approvedBy': approvedBy,
      'paidBy': paidBy,
      'rejectionReason': rejectionReason,
      'metadata': metadata,
      'transactionId': transactionId,
      'paymentMethod': paymentMethod,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
    };
  }

  /// Copy with modifications
  EnhancedCommissionModel copyWith({
    String? id,
    String? agentId,
    String? agentName,
    String? propertyId,
    String? propertyTitle,
    CommissionType? type,
    CommissionStatus? status,
    double? amount,
    double? baseAmount,
    double? percentage,
    CommissionTier? tier,
    double? tierMultiplier,
    String? sourceAgentId,
    String? sourceAgentName,
    int? level,
    DateTime? calculatedAt,
    DateTime? approvedAt,
    DateTime? paidAt,
    String? approvedBy,
    String? paidBy,
    String? rejectionReason,
    Map<String, dynamic>? metadata,
    String? transactionId,
    String? paymentMethod,
    DateTime? createdAt,
    DateTime? updatedAt,
  }) {
    return EnhancedCommissionModel(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      propertyId: propertyId ?? this.propertyId,
      propertyTitle: propertyTitle ?? this.propertyTitle,
      type: type ?? this.type,
      status: status ?? this.status,
      amount: amount ?? this.amount,
      baseAmount: baseAmount ?? this.baseAmount,
      percentage: percentage ?? this.percentage,
      tier: tier ?? this.tier,
      tierMultiplier: tierMultiplier ?? this.tierMultiplier,
      sourceAgentId: sourceAgentId ?? this.sourceAgentId,
      sourceAgentName: sourceAgentName ?? this.sourceAgentName,
      level: level ?? this.level,
      calculatedAt: calculatedAt ?? this.calculatedAt,
      approvedAt: approvedAt ?? this.approvedAt,
      paidAt: paidAt ?? this.paidAt,
      approvedBy: approvedBy ?? this.approvedBy,
      paidBy: paidBy ?? this.paidBy,
      rejectionReason: rejectionReason ?? this.rejectionReason,
      metadata: metadata ?? this.metadata,
      transactionId: transactionId ?? this.transactionId,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
    );
  }

  /// Formatted amount string
  String get formattedAmount {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(amount);
  }

  /// Formatted base amount string
  String get formattedBaseAmount {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(baseAmount);
  }

  /// Formatted percentage string
  String get formattedPercentage {
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Days since calculation
  int get daysSinceCalculation {
    return DateTime.now().difference(calculatedAt).inDays;
  }

  /// Days since approval
  int? get daysSinceApproval {
    if (approvedAt == null) return null;
    return DateTime.now().difference(approvedAt!).inDays;
  }

  /// Is commission overdue for payment
  bool get isOverdue {
    if (status != CommissionStatus.approved) return false;
    if (approvedAt == null) return false;
    return DateTime.now().difference(approvedAt!).inDays > 30;
  }

  /// Commission source description
  String get sourceDescription {
    switch (type) {
      case CommissionType.directSale:
        return propertyTitle ?? 'Direct Sale';
      case CommissionType.referralBonus:
        return 'Referral: ${sourceAgentName ?? 'Unknown'}';
      case CommissionType.levelBonus:
        return 'Level $level: ${sourceAgentName ?? 'Unknown'}';
      case CommissionType.performanceBonus:
        return 'Performance Achievement';
      case CommissionType.teamBonus:
        return 'Team Performance';
      case CommissionType.leadershipBonus:
        return 'Leadership Achievement';
      case CommissionType.achievementBonus:
        return 'Special Achievement';
    }
  }

  /// Status description with timing
  String get statusDescription {
    switch (status) {
      case CommissionStatus.pending:
        return 'Pending approval for $daysSinceCalculation days';
      case CommissionStatus.approved:
        final days = daysSinceApproval ?? 0;
        return 'Approved $days days ago';
      case CommissionStatus.paid:
        return 'Paid via ${paymentMethod ?? 'Unknown'}';
      case CommissionStatus.rejected:
        return 'Rejected: ${rejectionReason ?? 'No reason provided'}';
      case CommissionStatus.onHold:
        return 'On hold for review';
    }
  }

  @override
  String toString() {
    return 'EnhancedCommissionModel(id: $id, type: ${type.displayName}, amount: $formattedAmount, status: ${status.displayName})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is EnhancedCommissionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Commission analytics model
class CommissionAnalytics {
  final double totalEarned;
  final double totalPending;
  final double totalPaid;
  final double monthlyAverage;
  final double growthRate;
  final Map<CommissionType, double> typeBreakdown;
  final Map<CommissionTier, double> tierBreakdown;
  final List<MonthlyCommission> monthlyData;
  final CommissionTier currentTier;
  final double nextTierProgress;

  const CommissionAnalytics({
    required this.totalEarned,
    required this.totalPending,
    required this.totalPaid,
    required this.monthlyAverage,
    required this.growthRate,
    required this.typeBreakdown,
    required this.tierBreakdown,
    required this.monthlyData,
    required this.currentTier,
    required this.nextTierProgress,
  });
}

/// Monthly commission data
class MonthlyCommission {
  final DateTime month;
  final double amount;
  final int count;

  const MonthlyCommission({
    required this.month,
    required this.amount,
    required this.count,
  });
}
