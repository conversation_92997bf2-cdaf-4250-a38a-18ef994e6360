import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/presentation/pages/profile_page.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../mlm/presentation/providers/mlm_providers.dart'
    hide recentActivitiesProvider;
import '../../../mlm/presentation/widgets/network_stats_widget.dart';
import '../../../properties/presentation/pages/properties_page.dart';
import '../../../commissions/presentation/pages/commissions_page.dart';
import '../../../stars/presentation/pages/stars_page.dart';

import '../providers/dashboard_providers.dart';
import '../widgets/dashboard_widgets.dart';

class DashboardPage extends ConsumerStatefulWidget {
  const DashboardPage({super.key});

  @override
  ConsumerState<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends ConsumerState<DashboardPage> {
  int _selectedIndex = 0;

  final List<Widget> _pages = [
    const DashboardHomeTab(),
    const PropertiesPage(),
    const CommissionsPage(),
    const StarsPage(),
    const ProfileTab(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: _pages[_selectedIndex],
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _selectedIndex,
        onTap: (index) => setState(() => _selectedIndex = index),
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.home_work),
            label: 'Properties',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.currency_rupee),
            label: 'Commissions',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.star), label: 'Stars'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }
}

class DashboardHomeTab extends ConsumerStatefulWidget {
  const DashboardHomeTab({super.key});

  @override
  ConsumerState<DashboardHomeTab> createState() => _DashboardHomeTabState();
}

class _DashboardHomeTabState extends ConsumerState<DashboardHomeTab> {
  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final dashboardStats = ref.watch(dashboardStatsProvider);
    final recentActivities = ref.watch(recentActivitiesProvider);

    if (currentUser == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(icon: const Icon(Icons.notifications), onPressed: () {}),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(dashboardStatsProvider);
          ref.invalidate(recentActivitiesProvider);
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Welcome Card with Gradient
              GradientWidgets.gradientCard(
                gradient: AppTheme.primaryGradient,
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: CircleAvatar(
                        radius: 32,
                        backgroundColor: Colors.white.withValues(alpha: 0.2),
                        backgroundImage: currentUser.profileImageUrl != null
                            ? NetworkImage(currentUser.profileImageUrl!)
                            : null,
                        child: currentUser.profileImageUrl == null
                            ? Text(
                                currentUser.name.isNotEmpty
                                    ? currentUser.name[0].toUpperCase()
                                    : 'U',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              )
                            : null,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Welcome back,',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            currentUser.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              GradientWidgets.levelBadge(
                                level: currentUser.level,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Level ${currentUser.level} Agent',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.9),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ProfilePage(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.edit, color: Colors.white),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Enhanced Stats Grid with Gradient Cards
              GridView.count(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                children: [
                  GradientWidgets.statsCard(
                    title: 'Total Stars',
                    value: '${currentUser.totalStars}',
                    icon: Icons.star,
                    subtitle: '+${currentUser.totalBonuses} bonuses',
                    onTap: () {
                      // Navigate to stars page
                    },
                  ),
                  GradientWidgets.statsCard(
                    title: 'Commissions',
                    value:
                        '₹${currentUser.totalCommissions.toStringAsFixed(0)}',
                    icon: Icons.account_balance_wallet,
                    subtitle: 'This month',
                    onTap: () {
                      // Navigate to commissions page
                    },
                  ),
                  GradientWidgets.statsCard(
                    title: 'Network Size',
                    value: '${currentUser.downlineIds.length}',
                    icon: Icons.group,
                    subtitle: 'Active agents',
                    onTap: () {
                      // Navigate to network page
                    },
                  ),
                  GradientWidgets.statsCard(
                    title: 'Properties',
                    value: '42', // TODO: Get actual property count
                    icon: Icons.home_work,
                    subtitle: 'Available',
                    onTap: () {
                      // Navigate to properties page
                    },
                  ),
                ],
              ),

              const SizedBox(height: 16),

              // Enhanced Stats Grid
              dashboardStats.when(
                data: (stats) => _buildEnhancedStatsGrid(context, stats),
                loading: () => _buildLoadingStatsGrid(),
                error: (error, _) =>
                    _buildErrorWidget('Error loading stats: $error'),
              ),

              const SizedBox(height: 24),

              // Quick Actions
              _buildQuickActionsWidget(context),

              const SizedBox(height: 16),

              // Recent Activities
              recentActivities.when(
                data: (activities) =>
                    _buildRecentActivitiesWidget(context, activities),
                loading: () => const Card(child: LinearProgressIndicator()),
                error: (error, _) =>
                    _buildErrorWidget('Error loading activities: $error'),
              ),

              const SizedBox(height: 16),

              // Network Overview
              Consumer(
                builder: (context, ref, child) {
                  final networkStats = ref.watch(networkStatsProvider);
                  final networkPerformance = ref.watch(
                    networkPerformanceProvider,
                  );

                  return networkStats.when(
                    data: (stats) {
                      if (stats == null) return const SizedBox.shrink();

                      return networkPerformance.when(
                        data: (performance) {
                          final totalSize =
                              performance['totalNetworkSize'] ?? 0;
                          return CompactNetworkStatsWidget(
                            stats: stats,
                            totalNetworkSize: totalSize,
                          );
                        },
                        loading: () => const SizedBox.shrink(),
                        error: (_, __) => const SizedBox.shrink(),
                      );
                    },
                    loading: () => const SizedBox.shrink(),
                    error: (_, __) => const SizedBox.shrink(),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(icon, size: 32, color: color),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodyMedium,
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildEnhancedStatsGrid(BuildContext context, DashboardStats stats) {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: [
        EnhancedStatCard(
          title: 'Total Stars',
          value: '${stats.totalStars}',
          icon: Icons.star,
          color: Colors.amber,
          trend: stats.starTrend,
          onTap: () => _navigateToTab(context, 3),
        ),
        EnhancedStatCard(
          title: 'This Month',
          value: _formatAmount(stats.thisMonthCommissions),
          subtitle: 'Commissions',
          icon: Icons.currency_rupee,
          color: Colors.green,
          trend: stats.commissionTrend,
          onTap: () => _navigateToTab(context, 2),
        ),
        EnhancedStatCard(
          title: 'Network Size',
          value: '${stats.networkSize}',
          icon: Icons.people,
          color: Colors.blue,
          onTap: () => _showNetworkDialog(context),
        ),
        EnhancedStatCard(
          title: 'Properties',
          value: '${stats.availableProperties}',
          icon: Icons.home_work,
          color: Colors.orange,
          onTap: () => _navigateToTab(context, 1),
        ),
      ],
    );
  }

  Widget _buildLoadingStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: List.generate(
        4,
        (index) =>
            const Card(child: Center(child: CircularProgressIndicator())),
      ),
    );
  }

  Widget _buildErrorWidget(String message) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Text(message, style: const TextStyle(color: Colors.red)),
      ),
    );
  }

  Widget _buildQuickActionsWidget(BuildContext context) {
    return QuickActionsWidget(
      onViewProperties: () => _navigateToTab(context, 1),
      onViewCommissions: () => _navigateToTab(context, 2),
      onViewNetwork: () => _showNetworkDialog(context),
      onShareReferral: () =>
          _shareReferralCode(context, ref.read(currentUserProvider)!),
    );
  }

  Widget _buildRecentActivitiesWidget(
    BuildContext context,
    List<ActivityItem> activities,
  ) {
    return RecentActivitiesWidget(activities: activities);
  }

  void _navigateToTab(BuildContext context, int tabIndex) {
    // Find the parent DashboardPage and update its selected index
    final dashboardState = context
        .findAncestorStateOfType<_DashboardPageState>();
    dashboardState?.setState(() {
      dashboardState._selectedIndex = tabIndex;
    });
  }

  void _showNetworkDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Overview'),
        content: const Text('Network visualization coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _shareReferralCode(BuildContext context, UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Referral Code'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Your referral code: ${AuthService.generateReferralCode(user.id)}',
            ),
            const SizedBox(height: 16),
            const Text(
              'Share this code with potential agents to earn bonuses!',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              // TODO: Implement actual sharing
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Referral code copied to clipboard!'),
                ),
              );
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(1)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(1)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }
}

// Placeholder tabs
class PropertiesTab extends StatelessWidget {
  const PropertiesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Properties Tab - Coming Soon')),
    );
  }
}

class NetworkTab extends StatelessWidget {
  const NetworkTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Network Tab - Coming Soon')),
    );
  }
}

class ProfileTab extends StatelessWidget {
  const ProfileTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfilePage();
  }
}
