import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../../../auth/presentation/pages/profile_page.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/services/auth_service.dart';
import 'package:share_plus/share_plus.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../../shared/widgets/qr_code_widget.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../mlm/presentation/providers/mlm_providers.dart'
    hide recentActivitiesProvider;
import '../../../mlm/presentation/widgets/network_stats_widget.dart';
import '../../../properties/presentation/pages/properties_page.dart';
import '../../../commissions/presentation/pages/commissions_page.dart';
import '../../../stars/presentation/pages/stars_page.dart';

import '../providers/dashboard_providers.dart';
import '../widgets/dashboard_widgets.dart';
import '../widgets/stat_card.dart';

class DashboardPage extends ConsumerStatefulWidget {
  final Widget child;
  const DashboardPage({super.key, required this.child});

  @override
  ConsumerState<DashboardPage> createState() => _DashboardPageState();
}

class _DashboardPageState extends ConsumerState<DashboardPage> {
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: widget.child,
      bottomNavigationBar: BottomNavigationBar(
        type: BottomNavigationBarType.fixed,
        currentIndex: _calculateSelectedIndex(
          GoRouter.of(
            context,
          ).routerDelegate.currentConfiguration.uri.toString(),
        ),
        onTap: (index) => _onItemTapped(index, context),
        selectedItemColor: Theme.of(context).primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(icon: Icon(Icons.dashboard), label: 'Home'),
          BottomNavigationBarItem(
            icon: Icon(Icons.home_work),
            label: 'Properties',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.currency_rupee),
            label: 'Commissions',
          ),
          BottomNavigationBarItem(icon: Icon(Icons.star), label: 'Stars'),
          BottomNavigationBarItem(icon: Icon(Icons.person), label: 'Profile'),
        ],
      ),
    );
  }

  int _calculateSelectedIndex(String location) {
    if (location.startsWith('/dashboard')) {
      return 0;
    }
    if (location.startsWith('/properties')) {
      return 1;
    }
    if (location.startsWith('/commissions')) {
      return 2;
    }
    if (location.startsWith('/stars')) {
      return 3;
    }
    if (location.startsWith('/profile')) {
      return 4;
    }
    return 0;
  }

  void _onItemTapped(int index, BuildContext context) {
    switch (index) {
      case 0:
        GoRouter.of(context).go('/dashboard');
        break;
      case 1:
        GoRouter.of(context).go('/properties');
        break;
      case 2:
        GoRouter.of(context).go('/commissions');
        break;
      case 3:
        GoRouter.of(context).go('/stars');
        break;
      case 4:
        GoRouter.of(context).go('/profile');
        break;
    }
  }
}

class DashboardHomeTab extends ConsumerStatefulWidget {
  const DashboardHomeTab({super.key});

  @override
  ConsumerState<DashboardHomeTab> createState() => _DashboardHomeTabState();
}

class _DashboardHomeTabState extends ConsumerState<DashboardHomeTab> {
  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final dashboardStats = ref.watch(dashboardStatsProvider);
    final recentActivities = ref.watch(recentActivitiesProvider);

    if (currentUser == null) {
      return const Center(child: CircularProgressIndicator());
    }

    return Scaffold(
      appBar: AppBar(
        title: const Text('Dashboard'),
        actions: [
          IconButton(
            icon: const Icon(Icons.qr_code),
            onPressed: () => _showQRCode(context, currentUser),
            tooltip: 'Share QR Code',
          ),
          IconButton(icon: const Icon(Icons.notifications), onPressed: () {}),
        ],
      ),
      body: RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(dashboardStatsProvider);
          ref.invalidate(recentActivitiesProvider);
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Enhanced Welcome Card with Gradient
              GradientWidgets.gradientCard(
                gradient: AppTheme.primaryGradient,
                child: Row(
                  children: [
                    Container(
                      decoration: BoxDecoration(
                        shape: BoxShape.circle,
                        border: Border.all(
                          color: Colors.white.withValues(alpha: 0.3),
                          width: 2,
                        ),
                      ),
                      child: CircleAvatar(
                        radius: 32,
                        backgroundColor: Colors.white.withValues(alpha: 0.2),
                        backgroundImage: currentUser.profileImageUrl != null
                            ? NetworkImage(currentUser.profileImageUrl!)
                            : null,
                        child: currentUser.profileImageUrl == null
                            ? Text(
                                currentUser.name.isNotEmpty
                                    ? currentUser.name[0].toUpperCase()
                                    : 'U',
                                style: const TextStyle(
                                  fontSize: 24,
                                  fontWeight: FontWeight.bold,
                                  color: Colors.white,
                                ),
                              )
                            : null,
                      ),
                    ),
                    const SizedBox(width: 20),
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            'Welcome back,',
                            style: TextStyle(
                              color: Colors.white.withValues(alpha: 0.9),
                              fontSize: 14,
                              fontWeight: FontWeight.w500,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            currentUser.name,
                            style: const TextStyle(
                              color: Colors.white,
                              fontSize: 20,
                              fontWeight: FontWeight.bold,
                            ),
                          ),
                          const SizedBox(height: 4),
                          Row(
                            children: [
                              GradientWidgets.levelBadge(
                                level: currentUser.level,
                              ),
                              const SizedBox(width: 8),
                              Text(
                                'Level ${currentUser.level} Agent',
                                style: TextStyle(
                                  color: Colors.white.withValues(alpha: 0.9),
                                  fontSize: 12,
                                  fontWeight: FontWeight.w600,
                                ),
                              ),
                            ],
                          ),
                        ],
                      ),
                    ),
                    IconButton(
                      onPressed: () {
                        Navigator.push(
                          context,
                          MaterialPageRoute(
                            builder: (context) => const ProfilePage(),
                          ),
                        );
                      },
                      icon: const Icon(Icons.edit, color: Colors.white),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Enhanced Stats Grid
              dashboardStats.when(
                data: (stats) => GridView.count(
                  shrinkWrap: true,
                  physics: const NeverScrollableScrollPhysics(),
                  crossAxisCount: 2,
                  crossAxisSpacing: 16,
                  mainAxisSpacing: 16,
                  childAspectRatio: 1.5,
                  children: [
                    StatCard(
                      title: 'Total Stars',
                      value: '${stats.totalStars}',
                      icon: Icons.star,
                      color: Colors.amber,
                      trend: stats.starTrend,
                      onTap: () => _navigateToTab(context, 3),
                    ),
                    StatCard(
                      title: 'This Month',
                      value: _formatAmount(stats.thisMonthCommissions),
                      subtitle: 'Commissions',
                      icon: Icons.currency_rupee,
                      color: Colors.green,
                      trend: stats.commissionTrend,
                      onTap: () => _navigateToTab(context, 2),
                    ),
                    StatCard(
                      title: 'Network Size',
                      value: '${stats.networkSize}',
                      icon: Icons.people,
                      color: Colors.blue,
                      onTap: () => _showNetworkDialog(context),
                    ),
                    StatCard(
                      title: 'Properties',
                      value: '${stats.availableProperties}',
                      icon: Icons.home_work,
                      color: Colors.orange,
                      onTap: () => _navigateToTab(context, 1),
                    ),
                  ],
                ),
                loading: () => _buildLoadingStatsGrid(),
                error: (error, _) => _buildErrorWidget(
                  'Error loading stats: $error',
                  onRetry: () => ref.invalidate(dashboardStatsProvider),
                ),
              ),

              const SizedBox(height: 24),

              // Quick Actions
              _buildQuickActionsWidget(context),

              const SizedBox(height: 16),

              // Recent Activities
              recentActivities.when(
                data: (activities) =>
                    _buildRecentActivitiesWidget(context, activities),
                loading: () => const Card(child: LinearProgressIndicator()),
                error: (error, _) => _buildErrorWidget(
                  'Error loading activities: $error',
                  onRetry: () => ref.invalidate(recentActivitiesProvider),
                ),
              ),

              const SizedBox(height: 16),

              // Network Overview
              Consumer(
                builder: (context, ref, child) {
                  final networkStats = ref.watch(networkStatsProvider);
                  final networkPerformance = ref.watch(
                    networkPerformanceProvider,
                  );

                  return networkStats.when(
                    data: (stats) {
                      if (stats == null) return const SizedBox.shrink();

                      return networkPerformance.when(
                        data: (performance) {
                          final totalSize =
                              performance['totalNetworkSize'] ?? 0;
                          return CompactNetworkStatsWidget(
                            stats: stats,
                            totalNetworkSize: totalSize,
                          );
                        },
                        loading: () => const SizedBox.shrink(),
                        error: (_, __) => const SizedBox.shrink(),
                      );
                    },
                    loading: () => const SizedBox.shrink(),
                    error: (_, __) => const SizedBox.shrink(),
                  );
                },
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildLoadingStatsGrid() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.5,
      children: List.generate(
        4,
        (index) =>
            const Card(child: Center(child: CircularProgressIndicator())),
      ),
    );
  }

  Widget _buildErrorWidget(String message, {VoidCallback? onRetry}) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error_outline, color: Colors.red, size: 48),
            const SizedBox(height: 16),
            Text(
              message,
              style: const TextStyle(color: Colors.red, fontSize: 16),
              textAlign: TextAlign.center,
            ),
            if (onRetry != null) ...[
              const SizedBox(height: 16),
              ElevatedButton.icon(
                onPressed: onRetry,
                icon: const Icon(Icons.refresh),
                label: const Text('Retry'),
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActionsWidget(BuildContext context) {
    return QuickActionsWidget(
      onViewProperties: () => _navigateToTab(context, 1),
      onViewCommissions: () => _navigateToTab(context, 2),
      onViewNetwork: () => _showNetworkDialog(context),
      onShareReferral: () =>
          _shareReferralCode(context, ref.read(currentUserProvider)!),
    );
  }

  Widget _buildRecentActivitiesWidget(
    BuildContext context,
    List<ActivityItem> activities,
  ) {
    return RecentActivitiesWidget(activities: activities);
  }

  void _navigateToTab(BuildContext context, int tabIndex) {
    // Find the parent DashboardPage and update its selected index
    switch (tabIndex) {
      case 0:
        GoRouter.of(context).go('/dashboard');
        break;
      case 1:
        GoRouter.of(context).go('/properties');
        break;
      case 2:
        GoRouter.of(context).go('/commissions');
        break;
      case 3:
        GoRouter.of(context).go('/stars');
        break;
      case 4:
        GoRouter.of(context).go('/profile');
        break;
    }
  }

  void _showNetworkDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Network Overview'),
        content: const Text('Network visualization coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _shareReferralCode(BuildContext context, UserModel user) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share Referral Code'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            Text(
              'Your referral code: ${AuthService.generateReferralCode(user.id)}',
            ),
            const SizedBox(height: 16),
            const Text(
              'Share this code with potential agents to earn bonuses!',
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
          ElevatedButton(
            onPressed: () {
              final referralCode = AuthService.generateReferralCode(user.id);
              Share.share(
                'Join me on Rama Realty! Use my referral code: $referralCode',
                subject: 'Rama Realty Referral',
              );
              Navigator.of(context).pop();
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(1)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(1)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }

  void _showQRCode(BuildContext context, UserModel currentUser) {
    // Get referral code from additionalInfo or generate one
    final referralCode =
        currentUser.additionalInfo?['referralCode'] as String? ??
        currentUser.id.substring(0, 8).toUpperCase();

    showDialog(
      context: context,
      builder: (context) => QRCodeWidget(
        referralCode: referralCode,
        agentName: currentUser.name,
        agentProfileUrl: currentUser.profileImageUrl,
        onClose: () => Navigator.of(context).pop(),
      ),
    );
  }
}

// Placeholder tabs
class PropertiesTab extends StatelessWidget {
  const PropertiesTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Properties Tab - Coming Soon')),
    );
  }
}

class NetworkTab extends StatelessWidget {
  const NetworkTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const Scaffold(
      body: Center(child: Text('Network Tab - Coming Soon')),
    );
  }
}

class ProfileTab extends StatelessWidget {
  const ProfileTab({super.key});

  @override
  Widget build(BuildContext context) {
    return const ProfilePage();
  }
}
