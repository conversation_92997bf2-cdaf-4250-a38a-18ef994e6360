# Firebase Setup Guide for Rama Realty MLM

## 🔥 Complete Firebase Integration Steps

### Step 1: Firebase Console Setup

1. **Create Firebase Project**
   - Go to: https://console.firebase.google.com/
   - Click "Create a project"
   - Project name: `rama-realty-mlm`
   - Enable Google Analytics (recommended)

2. **Enable Authentication**
   - Go to Authentication → Sign-in method
   - Enable "Email/Password"
   - Optionally enable "Google" for easier testing

3. **Create Firestore Database**
   - Go to Firestore Database → Create database
   - Start in "Test mode" (we'll secure it later)
   - Choose your preferred location

4. **Enable Storage**
   - Go to Storage → Get started
   - Start in test mode
   - This will store property images and profile pictures

### Step 2: Web App Configuration

1. **Add Web App**
   - In Firebase Console, click "Add app" → Web (</> icon)
   - App nickname: "Rama Realty MLM Web"
   - Enable Firebase Hosting: ✅
   - Click "Register app"

2. **Copy Configuration**
   Firebase will show you a config object like this:
   ```javascript
   const firebaseConfig = {
     apiKey: "AIzaSy...",
     authDomain: "rama-realty-mlm.firebaseapp.com",
     projectId: "rama-realty-mlm",
     storageBucket: "rama-realty-mlm.appspot.com",
     messagingSenderId: "123456789",
     appId: "1:123456789:web:abcdef",
     measurementId: "G-XXXXXXXXXX"
   };
   ```

### Step 3: Update Flutter Configuration

1. **Update lib/firebase_options.dart**
   Replace the placeholder values with your actual Firebase config:
   ```dart
   static const FirebaseOptions web = FirebaseOptions(
     apiKey: 'YOUR_ACTUAL_API_KEY',
     appId: 'YOUR_ACTUAL_APP_ID',
     messagingSenderId: 'YOUR_ACTUAL_SENDER_ID',
     projectId: 'rama-realty-mlm',
     authDomain: 'rama-realty-mlm.firebaseapp.com',
     storageBucket: 'rama-realty-mlm.appspot.com',
     measurementId: 'YOUR_ACTUAL_MEASUREMENT_ID',
   );
   ```

### Step 4: Test the Integration

1. **Build and Run**
   ```bash
   flutter build web
   python -m http.server 8080 -d build/web
   ```

2. **Test Authentication**
   - Try creating a new account
   - Test login/logout functionality
   - Verify user data is saved to Firestore

### Step 5: Sample Data Setup

Create a test admin user in Firebase Console:

1. **Go to Authentication → Users**
2. **Add user manually**:
   - Email: <EMAIL>
   - Password: (set a secure password)

3. **Add user document in Firestore**:
   - Collection: `users`
   - Document ID: (use the UID from Authentication)
   - Data:
   ```json
   {
     "email": "<EMAIL>",
     "name": "Admin User",
     "phoneNumber": "+91 **********",
     "role": "admin",
     "level": 0,
     "uplineId": null,
     "downlineIds": [],
     "totalSales": 0,
     "totalCommissions": 0,
     "totalStars": 0,
     "totalBonuses": 0,
     "isActive": true,
     "profileImageUrl": null,
     "createdAt": "2024-01-01T00:00:00Z",
     "updatedAt": "2024-01-01T00:00:00Z",
     "additionalInfo": {
       "referralCode": "ADMIN001",
       "tier": "diamond",
       "signupSource": "manual"
     }
   }
   ```

### Step 6: Security Rules

Update Firestore security rules in Firebase Console:

1. **Go to Firestore Database → Rules**
2. **Replace with the rules from firestore.rules file**
3. **Publish the rules**

### Step 7: Sample Properties Data

Add some sample properties in Firestore:

1. **Collection**: `properties`
2. **Sample Document**:
   ```json
   {
     "title": "Luxury Villa in Gurgaon",
     "description": "Beautiful 4BHK villa with modern amenities",
     "price": 15000000,
     "location": "Sector 47, Gurgaon",
     "propertyType": "Villa",
     "bedrooms": 4,
     "bathrooms": 4,
     "area": 2500,
     "amenities": ["Swimming Pool", "Gym", "Garden", "Parking"],
     "images": [],
     "agentId": "admin-user-id",
     "status": "available",
     "featured": true,
     "createdAt": "2024-01-01T00:00:00Z",
     "updatedAt": "2024-01-01T00:00:00Z"
   }
   ```

### Step 8: Testing Checklist

- [ ] Firebase project created
- [ ] Authentication enabled
- [ ] Firestore database created
- [ ] Web app registered
- [ ] Configuration updated in code
- [ ] App builds successfully
- [ ] User registration works
- [ ] User login works
- [ ] Data saves to Firestore
- [ ] Security rules applied
- [ ] Sample data added

### Step 9: Deployment (Optional)

1. **Install Firebase CLI**:
   ```bash
   npm install -g firebase-tools
   ```

2. **Login and Initialize**:
   ```bash
   firebase login
   firebase init hosting
   ```

3. **Deploy**:
   ```bash
   firebase deploy
   ```

## 🎯 Expected Results

After completing these steps:

1. **Authentication**: Users can register and login
2. **Data Storage**: All user data, properties, commissions saved to Firestore
3. **Real-time Updates**: Changes sync across all connected clients
4. **Security**: Proper access controls based on user roles
5. **Scalability**: Ready for production use

## 🚨 Troubleshooting

**Common Issues:**

1. **"Firebase not initialized"**
   - Check if configuration values are correct
   - Ensure Firebase project is active

2. **"Permission denied"**
   - Check Firestore security rules
   - Verify user authentication

3. **"Network error"**
   - Check internet connection
   - Verify Firebase project settings

**Need Help?**
- Firebase Documentation: https://firebase.google.com/docs
- Flutter Firebase: https://firebase.flutter.dev/
