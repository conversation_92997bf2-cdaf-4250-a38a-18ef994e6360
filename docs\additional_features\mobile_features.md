# Mobile-Specific Features

## Overview
This document outlines mobile-specific features that enhance the MLM app experience on smartphones and tablets.

## 1. Location-Based Services

### GPS Integration
```dart
// lib/features/location/services/location_service.dart
class LocationService {
  static final Geolocator _geolocator = Geolocator();

  // Get current location
  static Future<Position?> getCurrentLocation() async {
    try {
      // Check permissions
      final permission = await Geolocator.checkPermission();
      if (permission == LocationPermission.denied) {
        final requestedPermission = await Geolocator.requestPermission();
        if (requestedPermission == LocationPermission.denied) {
          return null;
        }
      }

      // Get current position
      return await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
      );
    } catch (e) {
      print('Error getting location: $e');
      return null;
    }
  }

  // Find nearby properties
  static Future<List<PropertyModel>> findNearbyProperties({
    required double latitude,
    required double longitude,
    double radiusKm = 5.0,
  }) async {
    final allProperties = await PropertyService.getAllProperties();
    final nearbyProperties = <PropertyModel>[];

    for (final property in allProperties) {
      if (property.latitude != null && property.longitude != null) {
        final distance = Geolocator.distanceBetween(
          latitude,
          longitude,
          property.latitude!,
          property.longitude!,
        ) / 1000; // Convert to kilometers

        if (distance <= radiusKm) {
          nearbyProperties.add(property.copyWith(
            distanceFromUser: distance,
          ));
        }
      }
    }

    // Sort by distance
    nearbyProperties.sort((a, b) => 
      (a.distanceFromUser ?? 0).compareTo(b.distanceFromUser ?? 0));

    return nearbyProperties;
  }

  // Find nearby agents
  static Future<List<UserModel>> findNearbyAgents({
    required double latitude,
    required double longitude,
    double radiusKm = 10.0,
  }) async {
    // Query agents with location data
    final agentsQuery = await FirebaseFirestore.instance
        .collection('users')
        .where('role', isEqualTo: AppConstants.agentRole)
        .where('isActive', isEqualTo: true)
        .get();

    final nearbyAgents = <UserModel>[];

    for (final doc in agentsQuery.docs) {
      final agent = UserModel.fromFirestore(doc);
      if (agent.latitude != null && agent.longitude != null) {
        final distance = Geolocator.distanceBetween(
          latitude,
          longitude,
          agent.latitude!,
          agent.longitude!,
        ) / 1000;

        if (distance <= radiusKm) {
          nearbyAgents.add(agent.copyWith(
            distanceFromUser: distance,
          ));
        }
      }
    }

    return nearbyAgents;
  }

  // Track agent location (for field work)
  static Stream<Position> trackAgentLocation() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      ),
    );
  }

  // Update agent location in database
  static Future<void> updateAgentLocation(String agentId, Position position) async {
    await FirebaseFirestore.instance
        .collection('users')
        .doc(agentId)
        .update({
      'latitude': position.latitude,
      'longitude': position.longitude,
      'lastLocationUpdate': FieldValue.serverTimestamp(),
    });
  }

  // Get directions to property
  static Future<void> openDirections({
    required double destinationLat,
    required double destinationLng,
    String? destinationName,
  }) async {
    final url = Platform.isIOS
        ? 'maps://?daddr=$destinationLat,$destinationLng'
        : 'google.navigation:q=$destinationLat,$destinationLng';

    if (await canLaunchUrl(Uri.parse(url))) {
      await launchUrl(Uri.parse(url));
    } else {
      // Fallback to web maps
      final webUrl = 'https://maps.google.com/maps?daddr=$destinationLat,$destinationLng';
      await launchUrl(Uri.parse(webUrl));
    }
  }
}
```

### Geofencing
```dart
// lib/features/location/services/geofencing_service.dart
class GeofencingService {
  static final Map<String, GeofenceRegion> _activeGeofences = {};

  // Create geofence around property
  static Future<void> createPropertyGeofence({
    required String propertyId,
    required double latitude,
    required double longitude,
    double radiusMeters = 100.0,
  }) async {
    final geofence = GeofenceRegion(
      id: 'property_$propertyId',
      latitude: latitude,
      longitude: longitude,
      radius: radiusMeters,
      triggers: [GeofenceTrigger.enter, GeofenceTrigger.exit],
    );

    _activeGeofences[geofence.id] = geofence;
    
    // Register with platform-specific geofencing service
    await _registerGeofence(geofence);
  }

  // Handle geofence events
  static void handleGeofenceEvent(GeofenceEvent event) {
    switch (event.type) {
      case GeofenceEventType.enter:
        _onGeofenceEnter(event);
        break;
      case GeofenceEventType.exit:
        _onGeofenceExit(event);
        break;
    }
  }

  static void _onGeofenceEnter(GeofenceEvent event) {
    if (event.geofenceId.startsWith('property_')) {
      final propertyId = event.geofenceId.replaceFirst('property_', '');
      
      // Send notification
      NotificationService.showLocalNotification(
        title: 'Property Visit',
        body: 'You\'re near a property. Would you like to check it out?',
        payload: 'property:$propertyId',
      );

      // Log visit
      _logPropertyVisit(propertyId, event.timestamp);
    }
  }

  static void _onGeofenceExit(GeofenceEvent event) {
    if (event.geofenceId.startsWith('property_')) {
      final propertyId = event.geofenceId.replaceFirst('property_', '');
      
      // Log exit
      _logPropertyExit(propertyId, event.timestamp);
    }
  }

  static Future<void> _logPropertyVisit(String propertyId, DateTime timestamp) async {
    final currentUser = AuthService.currentUser;
    if (currentUser != null) {
      await FirebaseFirestore.instance.collection('property_visits').add({
        'propertyId': propertyId,
        'agentId': currentUser.id,
        'visitType': 'geofence_enter',
        'timestamp': Timestamp.fromDate(timestamp),
      });
    }
  }
}

class GeofenceRegion {
  final String id;
  final double latitude;
  final double longitude;
  final double radius;
  final List<GeofenceTrigger> triggers;

  const GeofenceRegion({
    required this.id,
    required this.latitude,
    required this.longitude,
    required this.radius,
    required this.triggers,
  });
}

enum GeofenceTrigger { enter, exit, dwell }
enum GeofenceEventType { enter, exit, dwell }

class GeofenceEvent {
  final String geofenceId;
  final GeofenceEventType type;
  final DateTime timestamp;

  const GeofenceEvent({
    required this.geofenceId,
    required this.type,
    required this.timestamp,
  });
}
```

## 2. Camera & Media Features

### Property Photography
```dart
// lib/features/camera/services/camera_service.dart
class CameraService {
  // Take property photo
  static Future<String?> takePropertyPhoto({
    bool useFlash = false,
    CameraLensDirection preferredLens = CameraLensDirection.back,
  }) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? photo = await picker.pickImage(
        source: ImageSource.camera,
        preferredCameraDevice: preferredLens == CameraLensDirection.back 
            ? CameraDevice.rear 
            : CameraDevice.front,
        imageQuality: 85,
        maxWidth: 1920,
        maxHeight: 1080,
      );

      if (photo != null) {
        // Compress and optimize image
        final optimizedPath = await _optimizeImage(photo.path);
        return optimizedPath;
      }
    } catch (e) {
      print('Error taking photo: $e');
    }
    return null;
  }

  // Take multiple property photos
  static Future<List<String>> takeMultiplePhotos({
    int maxPhotos = 10,
  }) async {
    final photos = <String>[];
    
    for (int i = 0; i < maxPhotos; i++) {
      final photo = await takePropertyPhoto();
      if (photo != null) {
        photos.add(photo);
        
        // Ask if user wants to take more
        final takeMore = await _showTakeMoreDialog();
        if (!takeMore) break;
      } else {
        break;
      }
    }
    
    return photos;
  }

  // Record property video tour
  static Future<String?> recordVideoTour({
    Duration maxDuration = const Duration(minutes: 5),
  }) async {
    try {
      final ImagePicker picker = ImagePicker();
      final XFile? video = await picker.pickVideo(
        source: ImageSource.camera,
        maxDuration: maxDuration,
      );

      if (video != null) {
        // Compress video
        final compressedPath = await _compressVideo(video.path);
        return compressedPath;
      }
    } catch (e) {
      print('Error recording video: $e');
    }
    return null;
  }

  // Add watermark to property images
  static Future<String> addWatermark(String imagePath) async {
    final originalImage = img.decodeImage(File(imagePath).readAsBytesSync())!;
    
    // Create watermark
    final watermark = img.drawString(
      originalImage,
      'Rama Realty MLM',
      font: img.arial24,
      x: originalImage.width - 200,
      y: originalImage.height - 30,
      color: img.ColorRgba8(255, 255, 255, 128),
    );

    // Save watermarked image
    final watermarkedPath = imagePath.replaceAll('.jpg', '_watermarked.jpg');
    File(watermarkedPath).writeAsBytesSync(img.encodeJpg(watermark));
    
    return watermarkedPath;
  }

  // Extract image metadata
  static Future<ImageMetadata> extractImageMetadata(String imagePath) async {
    final file = File(imagePath);
    final bytes = await file.readAsBytes();
    final data = await readExifFromBytes(bytes);

    return ImageMetadata(
      filePath: imagePath,
      fileSize: await file.length(),
      dimensions: await _getImageDimensions(imagePath),
      timestamp: data['DateTime']?.toString(),
      location: _extractGPSLocation(data),
      cameraInfo: _extractCameraInfo(data),
    );
  }

  static Future<String> _optimizeImage(String imagePath) async {
    final originalImage = img.decodeImage(File(imagePath).readAsBytesSync())!;
    
    // Resize if too large
    img.Image resizedImage = originalImage;
    if (originalImage.width > 1920 || originalImage.height > 1080) {
      resizedImage = img.copyResize(
        originalImage,
        width: originalImage.width > originalImage.height ? 1920 : null,
        height: originalImage.height > originalImage.width ? 1080 : null,
      );
    }

    // Compress and save
    final optimizedPath = imagePath.replaceAll('.jpg', '_optimized.jpg');
    File(optimizedPath).writeAsBytesSync(img.encodeJpg(resizedImage, quality: 85));
    
    return optimizedPath;
  }
}

class ImageMetadata {
  final String filePath;
  final int fileSize;
  final Size dimensions;
  final String? timestamp;
  final LatLng? location;
  final CameraInfo? cameraInfo;

  const ImageMetadata({
    required this.filePath,
    required this.fileSize,
    required this.dimensions,
    this.timestamp,
    this.location,
    this.cameraInfo,
  });
}

class CameraInfo {
  final String? make;
  final String? model;
  final String? software;

  const CameraInfo({this.make, this.model, this.software});
}
```

### AR Property Visualization
```dart
// lib/features/ar/services/ar_service.dart
class ARService {
  // Initialize AR session
  static Future<ARSession?> initializeAR() async {
    try {
      final isARSupported = await ARCoreController.checkArCoreAvailability();
      if (!isARSupported) {
        return null;
      }

      final session = ARSession();
      await session.initialize();
      return session;
    } catch (e) {
      print('Error initializing AR: $e');
      return null;
    }
  }

  // Place virtual property information in AR
  static Future<void> placePropertyInfo({
    required ARSession session,
    required PropertyModel property,
    required Vector3 position,
  }) async {
    final infoPanel = ARNode(
      type: ARNodeType.plane,
      position: position,
      content: PropertyInfoPanel(property: property),
    );

    await session.addNode(infoPanel);
  }

  // Measure distances in AR
  static Future<double?> measureDistance({
    required ARSession session,
    required Vector3 point1,
    required Vector3 point2,
  }) async {
    try {
      final distance = _calculateDistance(point1, point2);
      
      // Add measurement visualization
      final measurementLine = ARNode(
        type: ARNodeType.line,
        startPosition: point1,
        endPosition: point2,
        content: MeasurementLine(distance: distance),
      );

      await session.addNode(measurementLine);
      return distance;
    } catch (e) {
      print('Error measuring distance: $e');
      return null;
    }
  }

  // Virtual property staging
  static Future<void> addVirtualFurniture({
    required ARSession session,
    required FurnitureItem furniture,
    required Vector3 position,
  }) async {
    final furnitureNode = ARNode(
      type: ARNodeType.model,
      position: position,
      modelPath: furniture.modelPath,
      scale: furniture.scale,
    );

    await session.addNode(furnitureNode);
  }

  static double _calculateDistance(Vector3 point1, Vector3 point2) {
    final dx = point2.x - point1.x;
    final dy = point2.y - point1.y;
    final dz = point2.z - point1.z;
    return sqrt(dx * dx + dy * dy + dz * dz);
  }
}

class ARSession {
  final List<ARNode> _nodes = [];

  Future<void> initialize() async {
    // Initialize AR session
  }

  Future<void> addNode(ARNode node) async {
    _nodes.add(node);
    // Add node to AR scene
  }

  Future<void> removeNode(ARNode node) async {
    _nodes.remove(node);
    // Remove node from AR scene
  }

  void dispose() {
    _nodes.clear();
    // Dispose AR session
  }
}

class ARNode {
  final ARNodeType type;
  final Vector3 position;
  final Vector3? startPosition;
  final Vector3? endPosition;
  final Widget? content;
  final String? modelPath;
  final Vector3? scale;

  const ARNode({
    required this.type,
    required this.position,
    this.startPosition,
    this.endPosition,
    this.content,
    this.modelPath,
    this.scale,
  });
}

enum ARNodeType { plane, line, model, text }

class Vector3 {
  final double x;
  final double y;
  final double z;

  const Vector3(this.x, this.y, this.z);
}
```

## 3. Offline Capabilities

### Data Synchronization
```dart
// lib/features/offline/services/offline_sync_service.dart
class OfflineSyncService {
  static final Hive _hive = Hive;
  static const String _propertiesBox = 'offline_properties';
  static const String _commissionsBox = 'offline_commissions';
  static const String _pendingActionsBox = 'pending_actions';

  // Initialize offline storage
  static Future<void> initialize() async {
    await _hive.openBox(_propertiesBox);
    await _hive.openBox(_commissionsBox);
    await _hive.openBox(_pendingActionsBox);
  }

  // Cache properties for offline access
  static Future<void> cacheProperties(List<PropertyModel> properties) async {
    final box = _hive.box(_propertiesBox);
    
    for (final property in properties) {
      await box.put(property.id, property.toJson());
    }
  }

  // Get cached properties
  static Future<List<PropertyModel>> getCachedProperties() async {
    final box = _hive.box(_propertiesBox);
    final properties = <PropertyModel>[];
    
    for (final key in box.keys) {
      final propertyData = box.get(key);
      if (propertyData != null) {
        properties.add(PropertyModel.fromJson(propertyData));
      }
    }
    
    return properties;
  }

  // Queue action for when online
  static Future<void> queueAction(OfflineAction action) async {
    final box = _hive.box(_pendingActionsBox);
    await box.add(action.toJson());
  }

  // Process pending actions when online
  static Future<void> processPendingActions() async {
    final box = _hive.box(_pendingActionsBox);
    final actions = <OfflineAction>[];
    
    for (final key in box.keys) {
      final actionData = box.get(key);
      if (actionData != null) {
        actions.add(OfflineAction.fromJson(actionData));
      }
    }

    // Process actions in order
    for (final action in actions) {
      try {
        await _processAction(action);
        await box.delete(action.id);
      } catch (e) {
        print('Error processing action ${action.id}: $e');
        // Keep action in queue for retry
      }
    }
  }

  // Check connectivity and sync
  static Future<void> checkAndSync() async {
    final connectivity = await Connectivity().checkConnectivity();
    
    if (connectivity != ConnectivityResult.none) {
      await processPendingActions();
      await _syncLatestData();
    }
  }

  static Future<void> _processAction(OfflineAction action) async {
    switch (action.type) {
      case OfflineActionType.createProperty:
        await PropertyService.addProperty(
          PropertyModel.fromJson(action.data),
        );
        break;
      case OfflineActionType.updateProperty:
        await PropertyService.updateProperty(
          action.data['id'],
          PropertyModel.fromJson(action.data),
        );
        break;
      case OfflineActionType.createGoal:
        await DashboardService.createGoal(
          agentId: action.data['agentId'],
          title: action.data['title'],
          description: action.data['description'],
          type: GoalType.values.firstWhere((t) => t.name == action.data['type']),
          targetValue: action.data['targetValue'],
          deadline: DateTime.parse(action.data['deadline']),
          reward: action.data['reward'],
        );
        break;
    }
  }

  static Future<void> _syncLatestData() async {
    // Sync properties
    final latestProperties = await PropertyService.getAllProperties();
    await cacheProperties(latestProperties);

    // Sync commissions
    final currentUser = AuthService.currentUser;
    if (currentUser != null) {
      final commissions = await CommissionService.getAgentCommissions(currentUser.id);
      await _cacheCommissions(commissions);
    }
  }

  static Future<void> _cacheCommissions(List<EnhancedCommissionModel> commissions) async {
    final box = _hive.box(_commissionsBox);
    
    for (final commission in commissions) {
      await box.put(commission.id, commission.toJson());
    }
  }
}

class OfflineAction {
  final String id;
  final OfflineActionType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  const OfflineAction({
    required this.id,
    required this.type,
    required this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
  };

  factory OfflineAction.fromJson(Map<String, dynamic> json) => OfflineAction(
    id: json['id'],
    type: OfflineActionType.values.firstWhere((t) => t.name == json['type']),
    data: Map<String, dynamic>.from(json['data']),
    timestamp: DateTime.parse(json['timestamp']),
  );
}

enum OfflineActionType {
  createProperty,
  updateProperty,
  deleteProperty,
  createGoal,
  updateGoal,
  createCommission,
}
```

## 4. Push Notifications

### Advanced Notification System
```dart
// lib/features/notifications/services/push_notification_service.dart
class PushNotificationService {
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();

  // Initialize notifications
  static Future<void> initialize() async {
    // Request permissions
    await _messaging.requestPermission(
      alert: true,
      badge: true,
      sound: true,
      provisional: false,
    );

    // Initialize local notifications
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    await _localNotifications.initialize(
      const InitializationSettings(
        android: androidSettings,
        iOS: iosSettings,
      ),
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    // Handle background messages
    FirebaseMessaging.onBackgroundMessage(_firebaseMessagingBackgroundHandler);
    
    // Handle foreground messages
    FirebaseMessaging.onMessage.listen(_handleForegroundMessage);
    
    // Handle notification taps when app is in background
    FirebaseMessaging.onMessageOpenedApp.listen(_handleNotificationTap);
  }

  // Subscribe to topics
  static Future<void> subscribeToTopics(List<String> topics) async {
    for (final topic in topics) {
      await _messaging.subscribeToTopic(topic);
    }
  }

  // Send local notification
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    const androidDetails = AndroidNotificationDetails(
      'mlm_channel',
      'MLM Notifications',
      channelDescription: 'Notifications for MLM app',
      importance: Importance.high,
      priority: Priority.high,
    );
    
    const iosDetails = DarwinNotificationDetails(
      presentAlert: true,
      presentBadge: true,
      presentSound: true,
    );

    await _localNotifications.show(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      const NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      ),
      payload: payload,
    );
  }

  // Schedule notification
  static Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
  }) async {
    await _localNotifications.zonedSchedule(
      DateTime.now().millisecondsSinceEpoch ~/ 1000,
      title,
      body,
      tz.TZDateTime.from(scheduledTime, tz.local),
      const NotificationDetails(
        android: AndroidNotificationDetails(
          'scheduled_channel',
          'Scheduled Notifications',
          channelDescription: 'Scheduled notifications for MLM app',
        ),
        iOS: DarwinNotificationDetails(),
      ),
      payload: payload,
      uiLocalNotificationDateInterpretation: 
          UILocalNotificationDateInterpretation.absoluteTime,
    );
  }

  static void _handleForegroundMessage(RemoteMessage message) {
    showLocalNotification(
      title: message.notification?.title ?? 'New Notification',
      body: message.notification?.body ?? '',
      payload: message.data['action'],
    );
  }

  static void _handleNotificationTap(RemoteMessage message) {
    final action = message.data['action'];
    if (action != null) {
      _processNotificationAction(action);
    }
  }

  static void _onNotificationTapped(NotificationResponse response) {
    if (response.payload != null) {
      _processNotificationAction(response.payload!);
    }
  }

  static void _processNotificationAction(String action) {
    final parts = action.split(':');
    final actionType = parts[0];
    final actionData = parts.length > 1 ? parts[1] : null;

    switch (actionType) {
      case 'property':
        if (actionData != null) {
          NavigationService.navigateToProperty(actionData);
        }
        break;
      case 'commission':
        if (actionData != null) {
          NavigationService.navigateToCommission(actionData);
        }
        break;
      case 'goal':
        NavigationService.navigateToGoals();
        break;
      case 'network':
        NavigationService.navigateToNetwork();
        break;
    }
  }
}

@pragma('vm:entry-point')
Future<void> _firebaseMessagingBackgroundHandler(RemoteMessage message) async {
  await Firebase.initializeApp();
  // Handle background message
}
```
