import 'dart:convert';
import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'package:connectivity_plus/connectivity_plus.dart';

import '../../properties/models/property_model.dart';
import '../../commissions/models/enhanced_commission_model.dart';
import '../../dashboard/models/dashboard_models.dart';

/// Service for handling offline data synchronization
class OfflineSyncService {
  static const String _propertiesKey = 'offline_properties';
  static const String _commissionsKey = 'offline_commissions';
  static const String _dashboardKey = 'offline_dashboard';
  static const String _pendingActionsKey = 'pending_actions';
  static const String _lastSyncKey = 'last_sync_time';

  static SharedPreferences? _prefs;
  static bool _isInitialized = false;

  /// Initialize offline storage
  static Future<void> initialize() async {
    if (_isInitialized) return;
    
    try {
      _prefs = await SharedPreferences.getInstance();
      _isInitialized = true;
      
      if (kDebugMode) {
        print('OfflineSyncService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing OfflineSyncService: $e');
      }
    }
  }

  /// Check if device is online
  static Future<bool> isOnline() async {
    try {
      final connectivityResult = await Connectivity().checkConnectivity();
      return connectivityResult != ConnectivityResult.none;
    } catch (e) {
      return false;
    }
  }

  /// Cache properties for offline access
  static Future<void> cacheProperties(List<PropertyModel> properties) async {
    await _ensureInitialized();
    
    try {
      final propertiesJson = properties.map((p) => p.toJson()).toList();
      final jsonString = jsonEncode(propertiesJson);
      await _prefs!.setString(_propertiesKey, jsonString);
      
      if (kDebugMode) {
        print('Cached ${properties.length} properties for offline access');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error caching properties: $e');
      }
    }
  }

  /// Get cached properties
  static Future<List<PropertyModel>> getCachedProperties() async {
    await _ensureInitialized();
    
    try {
      final jsonString = _prefs!.getString(_propertiesKey);
      if (jsonString == null) return [];

      final List<dynamic> propertiesJson = jsonDecode(jsonString);
      return propertiesJson
          .map((json) => PropertyModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting cached properties: $e');
      }
      return [];
    }
  }

  /// Cache commissions for offline access
  static Future<void> cacheCommissions(List<EnhancedCommissionModel> commissions) async {
    await _ensureInitialized();
    
    try {
      final commissionsJson = commissions.map((c) => c.toJson()).toList();
      final jsonString = jsonEncode(commissionsJson);
      await _prefs!.setString(_commissionsKey, jsonString);
      
      if (kDebugMode) {
        print('Cached ${commissions.length} commissions for offline access');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error caching commissions: $e');
      }
    }
  }

  /// Get cached commissions
  static Future<List<EnhancedCommissionModel>> getCachedCommissions() async {
    await _ensureInitialized();
    
    try {
      final jsonString = _prefs!.getString(_commissionsKey);
      if (jsonString == null) return [];

      final List<dynamic> commissionsJson = jsonDecode(jsonString);
      return commissionsJson
          .map((json) => EnhancedCommissionModel.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting cached commissions: $e');
      }
      return [];
    }
  }

  /// Cache dashboard data for offline access
  static Future<void> cacheDashboardData(AgentDashboardData dashboardData) async {
    await _ensureInitialized();
    
    try {
      final jsonString = jsonEncode(dashboardData.toJson());
      await _prefs!.setString(_dashboardKey, jsonString);
      
      if (kDebugMode) {
        print('Cached dashboard data for offline access');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error caching dashboard data: $e');
      }
    }
  }

  /// Get cached dashboard data
  static Future<AgentDashboardData?> getCachedDashboardData() async {
    await _ensureInitialized();
    
    try {
      final jsonString = _prefs!.getString(_dashboardKey);
      if (jsonString == null) return null;

      final Map<String, dynamic> dashboardJson = jsonDecode(jsonString);
      return AgentDashboardData.fromJson(dashboardJson);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting cached dashboard data: $e');
      }
      return null;
    }
  }

  /// Queue action for when online
  static Future<void> queueAction(OfflineAction action) async {
    await _ensureInitialized();
    
    try {
      final existingActions = await _getPendingActions();
      existingActions.add(action);
      
      final actionsJson = existingActions.map((a) => a.toJson()).toList();
      final jsonString = jsonEncode(actionsJson);
      await _prefs!.setString(_pendingActionsKey, jsonString);
      
      if (kDebugMode) {
        print('Queued action: ${action.type.name}');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error queuing action: $e');
      }
    }
  }

  /// Get pending actions
  static Future<List<OfflineAction>> _getPendingActions() async {
    await _ensureInitialized();
    
    try {
      final jsonString = _prefs!.getString(_pendingActionsKey);
      if (jsonString == null) return [];

      final List<dynamic> actionsJson = jsonDecode(jsonString);
      return actionsJson
          .map((json) => OfflineAction.fromJson(json))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting pending actions: $e');
      }
      return [];
    }
  }

  /// Process pending actions when online
  static Future<void> processPendingActions() async {
    if (!await isOnline()) {
      if (kDebugMode) {
        print('Device is offline, skipping action processing');
      }
      return;
    }

    final actions = await _getPendingActions();
    if (actions.isEmpty) return;

    if (kDebugMode) {
      print('Processing ${actions.length} pending actions');
    }

    final successfulActions = <OfflineAction>[];

    for (final action in actions) {
      try {
        await _processAction(action);
        successfulActions.add(action);
        
        if (kDebugMode) {
          print('Successfully processed action: ${action.type.name}');
        }
      } catch (e) {
        if (kDebugMode) {
          print('Error processing action ${action.id}: $e');
        }
        // Keep action in queue for retry
      }
    }

    // Remove successful actions from queue
    if (successfulActions.isNotEmpty) {
      final remainingActions = actions
          .where((action) => !successfulActions.contains(action))
          .toList();
      
      await _savePendingActions(remainingActions);
    }
  }

  /// Process individual action
  static Future<void> _processAction(OfflineAction action) async {
    switch (action.type) {
      case OfflineActionType.createProperty:
        // Mock implementation - would call actual service
        await Future.delayed(const Duration(milliseconds: 500));
        break;
        
      case OfflineActionType.updateProperty:
        // Mock implementation - would call actual service
        await Future.delayed(const Duration(milliseconds: 500));
        break;
        
      case OfflineActionType.createGoal:
        // Mock implementation - would call actual service
        await Future.delayed(const Duration(milliseconds: 500));
        break;
        
      case OfflineActionType.updateGoal:
        // Mock implementation - would call actual service
        await Future.delayed(const Duration(milliseconds: 500));
        break;
    }
  }

  /// Save pending actions
  static Future<void> _savePendingActions(List<OfflineAction> actions) async {
    await _ensureInitialized();
    
    try {
      final actionsJson = actions.map((a) => a.toJson()).toList();
      final jsonString = jsonEncode(actionsJson);
      await _prefs!.setString(_pendingActionsKey, jsonString);
    } catch (e) {
      if (kDebugMode) {
        print('Error saving pending actions: $e');
      }
    }
  }

  /// Check connectivity and sync
  static Future<void> checkAndSync() async {
    if (await isOnline()) {
      await processPendingActions();
      await _updateLastSyncTime();
      
      if (kDebugMode) {
        print('Sync completed successfully');
      }
    }
  }

  /// Update last sync time
  static Future<void> _updateLastSyncTime() async {
    await _ensureInitialized();
    
    try {
      await _prefs!.setInt(_lastSyncKey, DateTime.now().millisecondsSinceEpoch);
    } catch (e) {
      if (kDebugMode) {
        print('Error updating last sync time: $e');
      }
    }
  }

  /// Get last sync time
  static Future<DateTime?> getLastSyncTime() async {
    await _ensureInitialized();
    
    try {
      final timestamp = _prefs!.getInt(_lastSyncKey);
      if (timestamp == null) return null;
      
      return DateTime.fromMillisecondsSinceEpoch(timestamp);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting last sync time: $e');
      }
      return null;
    }
  }

  /// Clear all cached data
  static Future<void> clearCache() async {
    await _ensureInitialized();
    
    try {
      await _prefs!.remove(_propertiesKey);
      await _prefs!.remove(_commissionsKey);
      await _prefs!.remove(_dashboardKey);
      await _prefs!.remove(_pendingActionsKey);
      await _prefs!.remove(_lastSyncKey);
      
      if (kDebugMode) {
        print('All cached data cleared');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error clearing cache: $e');
      }
    }
  }

  /// Ensure service is initialized
  static Future<void> _ensureInitialized() async {
    if (!_isInitialized) {
      await initialize();
    }
  }
}

/// Offline action model
class OfflineAction {
  final String id;
  final OfflineActionType type;
  final Map<String, dynamic> data;
  final DateTime timestamp;

  const OfflineAction({
    required this.id,
    required this.type,
    required this.data,
    required this.timestamp,
  });

  Map<String, dynamic> toJson() => {
    'id': id,
    'type': type.name,
    'data': data,
    'timestamp': timestamp.toIso8601String(),
  };

  factory OfflineAction.fromJson(Map<String, dynamic> json) => OfflineAction(
    id: json['id'],
    type: OfflineActionType.values.firstWhere((t) => t.name == json['type']),
    data: Map<String, dynamic>.from(json['data']),
    timestamp: DateTime.parse(json['timestamp']),
  );

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is OfflineAction &&
          runtimeType == other.runtimeType &&
          id == other.id;

  @override
  int get hashCode => id.hashCode;
}

/// Types of offline actions
enum OfflineActionType {
  createProperty,
  updateProperty,
  deleteProperty,
  createGoal,
  updateGoal,
  deleteGoal,
  createCommission,
  updateCommission,
}
