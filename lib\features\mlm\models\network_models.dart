import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../core/models/user_model.dart';
import '../../commissions/models/commission_enums.dart';

/// Network node representing an agent in the MLM tree
class NetworkNode {
  final String id;
  final String name;
  final String email;
  final String? phoneNumber;
  final String? profileImageUrl;
  final int level;
  final CommissionTier tier;
  final double totalSales;
  final double totalCommissions;
  final int downlineCount;
  final bool isActive;
  final DateTime joinDate;
  final String? uplineId;
  final List<NetworkNode> children;
  final NetworkPerformance performance;
  final Map<String, dynamic> metadata;

  const NetworkNode({
    required this.id,
    required this.name,
    required this.email,
    this.phoneNumber,
    this.profileImageUrl,
    required this.level,
    required this.tier,
    required this.totalSales,
    required this.totalCommissions,
    required this.downlineCount,
    required this.isActive,
    required this.joinDate,
    this.uplineId,
    this.children = const [],
    required this.performance,
    this.metadata = const {},
  });

  /// Create from UserModel
  factory NetworkNode.fromUser(
    UserModel user, {
    List<NetworkNode> children = const [],
    NetworkPerformance? performance,
  }) {
    return NetworkNode(
      id: user.id,
      name: user.name,
      email: user.email,
      phoneNumber: user.phoneNumber,
      profileImageUrl: user.profileImageUrl,
      level: user.level,
      tier: CommissionTier.bronze, // Will be calculated based on performance
      totalSales: user.totalSales,
      totalCommissions: user.totalCommissions,
      downlineCount: user.downlineIds.length,
      isActive: user.isActive,
      joinDate: user.createdAt,
      uplineId: user.uplineId,
      children: children,
      performance: performance ?? NetworkPerformance.empty(),
      metadata: user.additionalInfo ?? {},
    );
  }

  /// Copy with modifications
  NetworkNode copyWith({
    String? id,
    String? name,
    String? email,
    String? phoneNumber,
    String? profileImageUrl,
    int? level,
    CommissionTier? tier,
    double? totalSales,
    double? totalCommissions,
    int? downlineCount,
    bool? isActive,
    DateTime? joinDate,
    String? uplineId,
    List<NetworkNode>? children,
    NetworkPerformance? performance,
    Map<String, dynamic>? metadata,
  }) {
    return NetworkNode(
      id: id ?? this.id,
      name: name ?? this.name,
      email: email ?? this.email,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      level: level ?? this.level,
      tier: tier ?? this.tier,
      totalSales: totalSales ?? this.totalSales,
      totalCommissions: totalCommissions ?? this.totalCommissions,
      downlineCount: downlineCount ?? this.downlineCount,
      isActive: isActive ?? this.isActive,
      joinDate: joinDate ?? this.joinDate,
      uplineId: uplineId ?? this.uplineId,
      children: children ?? this.children,
      performance: performance ?? this.performance,
      metadata: metadata ?? this.metadata,
    );
  }

  /// Get formatted sales amount
  String get formattedSales {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(totalSales);
  }

  /// Get formatted commissions amount
  String get formattedCommissions {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(totalCommissions);
  }

  /// Get initials for avatar
  String get initials {
    final parts = name.split(' ');
    if (parts.length >= 2) {
      return '${parts[0][0]}${parts[1][0]}'.toUpperCase();
    }
    return name.isNotEmpty ? name[0].toUpperCase() : 'A';
  }

  /// Get days since joining
  int get daysSinceJoining {
    return DateTime.now().difference(joinDate).inDays;
  }

  /// Check if node has children
  bool get hasChildren => children.isNotEmpty;

  /// Get total network size (including self)
  int get totalNetworkSize {
    int size = 1; // Self
    for (final child in children) {
      size += child.totalNetworkSize;
    }
    return size;
  }

  /// Get all descendants at a specific level
  List<NetworkNode> getNodesAtLevel(int targetLevel) {
    if (level == targetLevel) return [this];

    final nodes = <NetworkNode>[];
    for (final child in children) {
      nodes.addAll(child.getNodesAtLevel(targetLevel));
    }
    return nodes;
  }

  /// Find node by ID in the tree
  NetworkNode? findNodeById(String nodeId) {
    if (id == nodeId) return this;

    for (final child in children) {
      final found = child.findNodeById(nodeId);
      if (found != null) return found;
    }
    return null;
  }

  @override
  String toString() {
    return 'NetworkNode(id: $id, name: $name, level: $level, children: ${children.length})';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is NetworkNode && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Network performance metrics
class NetworkPerformance {
  final double monthlyGrowth;
  final double quarterlyGrowth;
  final double yearlyGrowth;
  final int newRecruits;
  final int activeAgents;
  final double teamSales;
  final double teamCommissions;
  final double conversionRate;
  final double retentionRate;
  final Map<String, double> monthlyMetrics;

  const NetworkPerformance({
    required this.monthlyGrowth,
    required this.quarterlyGrowth,
    required this.yearlyGrowth,
    required this.newRecruits,
    required this.activeAgents,
    required this.teamSales,
    required this.teamCommissions,
    required this.conversionRate,
    required this.retentionRate,
    this.monthlyMetrics = const {},
  });

  /// Create empty performance
  factory NetworkPerformance.empty() {
    return const NetworkPerformance(
      monthlyGrowth: 0,
      quarterlyGrowth: 0,
      yearlyGrowth: 0,
      newRecruits: 0,
      activeAgents: 0,
      teamSales: 0,
      teamCommissions: 0,
      conversionRate: 0,
      retentionRate: 0,
    );
  }

  /// Get performance color based on growth
  Color get performanceColor {
    if (monthlyGrowth >= 20) return const Color(0xFF4CAF50); // Green
    if (monthlyGrowth >= 10) return const Color(0xFF8BC34A); // Light Green
    if (monthlyGrowth >= 0) return const Color(0xFFFF9800); // Orange
    return const Color(0xFFF44336); // Red
  }

  /// Get performance icon
  IconData get performanceIcon {
    if (monthlyGrowth >= 10) return Icons.trending_up;
    if (monthlyGrowth >= 0) return Icons.trending_flat;
    return Icons.trending_down;
  }

  /// Get performance description
  String get performanceDescription {
    if (monthlyGrowth >= 20) return 'Excellent';
    if (monthlyGrowth >= 10) return 'Good';
    if (monthlyGrowth >= 0) return 'Average';
    return 'Needs Improvement';
  }
}

/// Network visualization configuration
class NetworkVisualizationConfig {
  final double nodeSize;
  final double nodeSpacing;
  final double levelSpacing;
  final bool showPerformanceColors;
  final bool showTierBadges;
  final bool showSalesData;
  final bool enableInteraction;
  final int maxLevelsToShow;
  final NetworkLayoutType layoutType;

  const NetworkVisualizationConfig({
    this.nodeSize = 60,
    this.nodeSpacing = 80,
    this.levelSpacing = 120,
    this.showPerformanceColors = true,
    this.showTierBadges = true,
    this.showSalesData = true,
    this.enableInteraction = true,
    this.maxLevelsToShow = 5,
    this.layoutType = NetworkLayoutType.tree,
  });

  NetworkVisualizationConfig copyWith({
    double? nodeSize,
    double? nodeSpacing,
    double? levelSpacing,
    bool? showPerformanceColors,
    bool? showTierBadges,
    bool? showSalesData,
    bool? enableInteraction,
    int? maxLevelsToShow,
    NetworkLayoutType? layoutType,
  }) {
    return NetworkVisualizationConfig(
      nodeSize: nodeSize ?? this.nodeSize,
      nodeSpacing: nodeSpacing ?? this.nodeSpacing,
      levelSpacing: levelSpacing ?? this.levelSpacing,
      showPerformanceColors:
          showPerformanceColors ?? this.showPerformanceColors,
      showTierBadges: showTierBadges ?? this.showTierBadges,
      showSalesData: showSalesData ?? this.showSalesData,
      enableInteraction: enableInteraction ?? this.enableInteraction,
      maxLevelsToShow: maxLevelsToShow ?? this.maxLevelsToShow,
      layoutType: layoutType ?? this.layoutType,
    );
  }
}

/// Network layout types
enum NetworkLayoutType { tree, radial, force, hierarchical }

extension NetworkLayoutTypeExtension on NetworkLayoutType {
  String get displayName {
    switch (this) {
      case NetworkLayoutType.tree:
        return 'Tree Layout';
      case NetworkLayoutType.radial:
        return 'Radial Layout';
      case NetworkLayoutType.force:
        return 'Force Layout';
      case NetworkLayoutType.hierarchical:
        return 'Hierarchical Layout';
    }
  }

  IconData get icon {
    switch (this) {
      case NetworkLayoutType.tree:
        return Icons.account_tree;
      case NetworkLayoutType.radial:
        return Icons.radio_button_checked;
      case NetworkLayoutType.force:
        return Icons.scatter_plot;
      case NetworkLayoutType.hierarchical:
        return Icons.stairs;
    }
  }
}

/// Network statistics
class NetworkStatistics {
  final int totalAgents;
  final int activeAgents;
  final int inactiveAgents;
  final double totalSales;
  final double totalCommissions;
  final Map<int, int> levelDistribution;
  final Map<CommissionTier, int> tierDistribution;
  final double averageTeamSize;
  final double networkGrowthRate;
  final List<TopPerformer> topPerformers;

  const NetworkStatistics({
    required this.totalAgents,
    required this.activeAgents,
    required this.inactiveAgents,
    required this.totalSales,
    required this.totalCommissions,
    required this.levelDistribution,
    required this.tierDistribution,
    required this.averageTeamSize,
    required this.networkGrowthRate,
    required this.topPerformers,
  });

  /// Get activity rate
  double get activityRate {
    if (totalAgents == 0) return 0;
    return (activeAgents / totalAgents) * 100;
  }

  /// Get formatted total sales
  String get formattedTotalSales {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(totalSales);
  }

  /// Get formatted total commissions
  String get formattedTotalCommissions {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(totalCommissions);
  }
}

/// Top performer in network
class TopPerformer {
  final String id;
  final String name;
  final String? profileImageUrl;
  final double sales;
  final double commissions;
  final int teamSize;
  final CommissionTier tier;

  const TopPerformer({
    required this.id,
    required this.name,
    this.profileImageUrl,
    required this.sales,
    required this.commissions,
    required this.teamSize,
    required this.tier,
  });
}
