import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/lead_model.dart';
import '../../../../core/models/agent_favorite_model.dart';
import '../../../../core/services/lead_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

/// Agent leads provider
final agentLeadsProvider = FutureProvider.autoDispose<List<LeadModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];

  return await LeadService.getLeadsByAgent(currentUser.id);
});

/// Agent leads by status provider
final agentLeadsByStatusProvider = FutureProvider.autoDispose.family<List<LeadModel>, String>((ref, status) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];

  return await LeadService.getLeadsByAgent(currentUser.id, status: status);
});

/// Hot leads provider (high priority and interested leads)
final hotLeadsProvider = FutureProvider.autoDispose<List<LeadModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];

  final allLeads = await LeadService.getLeadsByAgent(currentUser.id);
  return allLeads.where((lead) => lead.isHotLead).toList();
});

/// Follow-up needed leads provider
final followUpNeededLeadsProvider = FutureProvider.autoDispose<List<LeadModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];

  final allLeads = await LeadService.getLeadsByAgent(currentUser.id);
  return allLeads.where((lead) => lead.needsFollowUp).toList();
});

/// Agent favorites provider
final agentFavoritesProvider = FutureProvider.autoDispose<List<AgentFavoriteModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];

  return await LeadService.getAgentFavorites(currentUser.id);
});

/// Agent portfolio provider
final agentPortfolioProvider = FutureProvider.autoDispose<List<AgentPropertyPortfolioModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];

  return await LeadService.getAgentPortfolio(currentUser.id);
});

/// Lead statistics provider
final leadStatisticsProvider = FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return {};

  return await LeadService.getLeadStatistics(currentUser.id);
});

/// Property sharing analytics provider
final propertySharingAnalyticsProvider = FutureProvider.autoDispose<List<PropertySharingModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];

  final now = DateTime.now();
  final startDate = DateTime(now.year, now.month - 1, 1); // Last month
  
  return await LeadService.getPropertySharingAnalytics(
    currentUser.id,
    startDate: startDate,
    endDate: now,
  );
});

/// Lead interactions provider for a specific lead
final leadInteractionsProvider = FutureProvider.autoDispose.family<List<LeadInteractionModel>, String>((ref, leadId) async {
  return await LeadService.getLeadInteractions(leadId);
});

/// Property leads provider (leads for a specific property)
final propertyLeadsProvider = FutureProvider.autoDispose.family<List<LeadModel>, String>((ref, propertyId) async {
  return await LeadService.getLeadsByProperty(propertyId);
});

/// Check if property is in favorites provider
final isPropertyInFavoritesProvider = FutureProvider.autoDispose.family<bool, String>((ref, propertyId) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return false;

  return await LeadService.isPropertyInFavorites(currentUser.id, propertyId);
});

/// Lead filter state provider
final leadFilterProvider = StateNotifierProvider<LeadFilterNotifier, LeadFilter>((ref) {
  return LeadFilterNotifier();
});

/// Lead filter state
class LeadFilter {
  final String? status;
  final String? priority;
  final String? source;
  final DateTime? startDate;
  final DateTime? endDate;
  final String sortBy;
  final bool sortAscending;

  const LeadFilter({
    this.status,
    this.priority,
    this.source,
    this.startDate,
    this.endDate,
    this.sortBy = 'updatedAt',
    this.sortAscending = false,
  });

  LeadFilter copyWith({
    String? status,
    String? priority,
    String? source,
    DateTime? startDate,
    DateTime? endDate,
    String? sortBy,
    bool? sortAscending,
  }) {
    return LeadFilter(
      status: status ?? this.status,
      priority: priority ?? this.priority,
      source: source ?? this.source,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

/// Lead filter notifier
class LeadFilterNotifier extends StateNotifier<LeadFilter> {
  LeadFilterNotifier() : super(const LeadFilter());

  void updateFilter(LeadFilter newFilter) {
    state = newFilter;
  }

  void setStatus(String? status) {
    state = state.copyWith(status: status);
  }

  void setPriority(String? priority) {
    state = state.copyWith(priority: priority);
  }

  void setSource(String? source) {
    state = state.copyWith(source: source);
  }

  void setDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);
  }

  void setSorting(String sortBy, bool ascending) {
    state = state.copyWith(sortBy: sortBy, sortAscending: ascending);
  }

  void resetFilters() {
    state = const LeadFilter();
  }
}

/// Filtered leads provider based on current filter
final filteredLeadsProvider = FutureProvider.autoDispose<List<LeadModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];

  final filter = ref.watch(leadFilterProvider);
  
  // Get all leads first
  List<LeadModel> leads = await LeadService.getLeadsByAgent(
    currentUser.id,
    status: filter.status,
    priority: filter.priority,
  );

  // Apply additional filters
  if (filter.source != null) {
    leads = leads.where((lead) => lead.source == filter.source).toList();
  }

  if (filter.startDate != null) {
    leads = leads.where((lead) => lead.createdAt.isAfter(filter.startDate!)).toList();
  }

  if (filter.endDate != null) {
    leads = leads.where((lead) => lead.createdAt.isBefore(filter.endDate!)).toList();
  }

  // Apply sorting
  leads.sort((a, b) {
    int comparison = 0;
    
    switch (filter.sortBy) {
      case 'customerName':
        comparison = a.customerName.compareTo(b.customerName);
        break;
      case 'status':
        comparison = a.status.compareTo(b.status);
        break;
      case 'priority':
        comparison = a.priority.compareTo(b.priority);
        break;
      case 'createdAt':
        comparison = a.createdAt.compareTo(b.createdAt);
        break;
      case 'updatedAt':
      default:
        comparison = a.updatedAt.compareTo(b.updatedAt);
        break;
    }
    
    return filter.sortAscending ? comparison : -comparison;
  });

  return leads;
});

/// Agent performance metrics provider
final agentPerformanceMetricsProvider = FutureProvider.autoDispose<AgentPerformanceMetrics>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return const AgentPerformanceMetrics();

  final futures = await Future.wait([
    LeadService.getLeadStatistics(currentUser.id),
    LeadService.getAgentPortfolio(currentUser.id),
    LeadService.getPropertySharingAnalytics(currentUser.id),
  ]);

  final leadStats = futures[0] as Map<String, dynamic>;
  final portfolio = futures[1] as List<AgentPropertyPortfolioModel>;
  final sharingAnalytics = futures[2] as List<PropertySharingModel>;

  return AgentPerformanceMetrics(
    totalLeads: leadStats['totalLeads'] ?? 0,
    conversionRate: leadStats['conversionRate'] ?? 0.0,
    hotLeads: leadStats['hotLeads'] ?? 0,
    followUpNeeded: leadStats['followUpNeeded'] ?? 0,
    totalProperties: portfolio.length,
    activeProperties: portfolio.where((p) => p.status != 'inactive').length,
    totalShares: sharingAnalytics.length,
    averagePerformanceScore: portfolio.isNotEmpty 
        ? portfolio.map((p) => p.performanceScore).reduce((a, b) => a + b) / portfolio.length
        : 0.0,
  );
});

/// Agent performance metrics model
class AgentPerformanceMetrics {
  final int totalLeads;
  final double conversionRate;
  final int hotLeads;
  final int followUpNeeded;
  final int totalProperties;
  final int activeProperties;
  final int totalShares;
  final double averagePerformanceScore;

  const AgentPerformanceMetrics({
    this.totalLeads = 0,
    this.conversionRate = 0.0,
    this.hotLeads = 0,
    this.followUpNeeded = 0,
    this.totalProperties = 0,
    this.activeProperties = 0,
    this.totalShares = 0,
    this.averagePerformanceScore = 0.0,
  });
}
