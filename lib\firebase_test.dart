import 'package:flutter/material.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

/// Simple Firebase test widget to verify connection
class FirebaseTestWidget extends StatefulWidget {
  const FirebaseTestWidget({super.key});

  @override
  State<FirebaseTestWidget> createState() => _FirebaseTestWidgetState();
}

class _FirebaseTestWidgetState extends State<FirebaseTestWidget> {
  final _emailController = TextEditingController();
  final _passwordController = TextEditingController();
  String _status = 'Ready to test Firebase connection';
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: const Color(0xFF0F0F0F),
      appBar: AppBar(
        title: const Text('Firebase Connection Test'),
        backgroundColor: const Color(0xFF1A1A1A),
      ),
      body: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.stretch,
          children: [
            // Status Card
            Container(
              padding: const EdgeInsets.all(16),
              decoration: BoxDecoration(
                color: const Color(0xFF1A1A1A),
                borderRadius: BorderRadius.circular(12),
                border: Border.all(
                  color: _status.contains('Success') 
                    ? Colors.green 
                    : _status.contains('Error')
                      ? Colors.red
                      : Colors.grey,
                ),
              ),
              child: Text(
                _status,
                style: TextStyle(
                  color: _status.contains('Success') 
                    ? Colors.green 
                    : _status.contains('Error')
                      ? Colors.red
                      : Colors.white,
                  fontSize: 16,
                ),
                textAlign: TextAlign.center,
              ),
            ),
            
            const SizedBox(height: 32),
            
            // Email Field
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Test Email',
                hintText: '<EMAIL>',
                border: OutlineInputBorder(),
                labelStyle: TextStyle(color: Colors.white),
                hintStyle: TextStyle(color: Colors.grey),
              ),
              style: const TextStyle(color: Colors.white),
              keyboardType: TextInputType.emailAddress,
            ),
            
            const SizedBox(height: 16),
            
            // Password Field
            TextField(
              controller: _passwordController,
              decoration: const InputDecoration(
                labelText: 'Test Password',
                hintText: 'minimum 6 characters',
                border: OutlineInputBorder(),
                labelStyle: TextStyle(color: Colors.white),
                hintStyle: TextStyle(color: Colors.grey),
              ),
              style: const TextStyle(color: Colors.white),
              obscureText: true,
            ),
            
            const SizedBox(height: 24),
            
            // Test Buttons
            ElevatedButton(
              onPressed: _isLoading ? null : _testFirebaseConnection,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFFFF6B35),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: _isLoading
                ? const CircularProgressIndicator(color: Colors.white)
                : const Text(
                    'Test Firebase Connection',
                    style: TextStyle(color: Colors.white, fontSize: 16),
                  ),
            ),
            
            const SizedBox(height: 12),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testUserCreation,
              style: ElevatedButton.styleFrom(
                backgroundColor: const Color(0xFF8B5CF6),
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Test User Creation',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
            
            const SizedBox(height: 12),
            
            ElevatedButton(
              onPressed: _isLoading ? null : _testFirestoreWrite,
              style: ElevatedButton.styleFrom(
                backgroundColor: Colors.green,
                padding: const EdgeInsets.symmetric(vertical: 16),
              ),
              child: const Text(
                'Test Firestore Write',
                style: TextStyle(color: Colors.white, fontSize: 16),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Future<void> _testFirebaseConnection() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing Firebase connection...';
    });

    try {
      // Test Firebase Auth instance
      final auth = FirebaseAuth.instance;
      final currentUser = auth.currentUser;
      
      // Test Firestore instance
      final firestore = FirebaseFirestore.instance;
      
      setState(() {
        _status = 'Success! Firebase is connected.\n'
                 'Auth: ${auth.app.name}\n'
                 'Current User: ${currentUser?.email ?? 'None'}\n'
                 'Firestore: ${firestore.app.name}';
      });
    } catch (e) {
      setState(() {
        _status = 'Error connecting to Firebase:\n$e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testUserCreation() async {
    if (_emailController.text.isEmpty || _passwordController.text.isEmpty) {
      setState(() {
        _status = 'Error: Please enter email and password';
      });
      return;
    }

    setState(() {
      _isLoading = true;
      _status = 'Creating test user...';
    });

    try {
      final credential = await FirebaseAuth.instance.createUserWithEmailAndPassword(
        email: _emailController.text.trim(),
        password: _passwordController.text,
      );

      setState(() {
        _status = 'Success! User created:\n'
                 'UID: ${credential.user?.uid}\n'
                 'Email: ${credential.user?.email}';
      });
    } on FirebaseAuthException catch (e) {
      setState(() {
        _status = 'Firebase Auth Error:\n'
                 'Code: ${e.code}\n'
                 'Message: ${e.message}';
      });
    } catch (e) {
      setState(() {
        _status = 'Error creating user:\n$e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  Future<void> _testFirestoreWrite() async {
    setState(() {
      _isLoading = true;
      _status = 'Testing Firestore write...';
    });

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        setState(() {
          _status = 'Error: No authenticated user. Create a user first.';
        });
        return;
      }

      // Test writing to Firestore
      await FirebaseFirestore.instance.collection('test').doc('connection').set({
        'timestamp': FieldValue.serverTimestamp(),
        'userId': user.uid,
        'email': user.email,
        'message': 'Firebase connection test successful!',
      });

      setState(() {
        _status = 'Success! Firestore write completed.\n'
                 'Document created in "test" collection.';
      });
    } catch (e) {
      setState(() {
        _status = 'Error writing to Firestore:\n$e';
      });
    } finally {
      setState(() => _isLoading = false);
    }
  }

  @override
  void dispose() {
    _emailController.dispose();
    _passwordController.dispose();
    super.dispose();
  }
}
