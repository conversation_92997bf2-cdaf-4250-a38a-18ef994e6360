{"hosting": {"public": "build/web", "ignore": ["firebase.json", "**/.*", "**/node_modules/**"], "rewrites": [{"source": "**", "destination": "/index.html"}], "headers": [{"source": "**/*.@(js|css|woff2|woff|ttf|eot)", "headers": [{"key": "Cache-Control", "value": "max-age=31536000"}]}, {"source": "**/*.@(png|jpg|jpeg|gif|svg|webp|ico)", "headers": [{"key": "Cache-Control", "value": "max-age=86400"}]}], "cleanUrls": true, "trailingSlash": false}, "firestore": {"rules": "firestore.rules", "indexes": "deployment/database_optimization.json"}, "storage": {"rules": "storage.rules"}}