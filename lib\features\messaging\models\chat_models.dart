import 'package:cloud_firestore/cloud_firestore.dart';

/// Chat message model
class ChatMessage {
  final String id;
  final String senderId;
  final String content;
  final MessageType type;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;
  final bool isRead;
  final bool isEdited;
  final DateTime? editedAt;
  final Map<String, String> reactions;

  const ChatMessage({
    required this.id,
    required this.senderId,
    required this.content,
    required this.type,
    required this.metadata,
    required this.timestamp,
    required this.isRead,
    required this.isEdited,
    this.editedAt,
    required this.reactions,
  });

  factory ChatMessage.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ChatMessage(
      id: doc.id,
      senderId: data['senderId'] ?? '',
      content: data['content'] ?? '',
      type: MessageType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => MessageType.text,
      ),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false,
      isEdited: data['isEdited'] ?? false,
      editedAt: (data['editedAt'] as Timestamp?)?.toDate(),
      reactions: Map<String, String>.from(data['reactions'] ?? {}),
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'senderId': senderId,
    'content': content,
    'type': type.name,
    'metadata': metadata,
    'timestamp': timestamp.toIso8601String(),
    'isRead': isRead,
    'isEdited': isEdited,
    'editedAt': editedAt?.toIso8601String(),
    'reactions': reactions,
  };

  factory ChatMessage.fromJson(Map<String, dynamic> json) => ChatMessage(
    id: json['id'],
    senderId: json['senderId'],
    content: json['content'],
    type: MessageType.values.firstWhere((t) => t.name == json['type']),
    metadata: Map<String, dynamic>.from(json['metadata']),
    timestamp: DateTime.parse(json['timestamp']),
    isRead: json['isRead'],
    isEdited: json['isEdited'],
    editedAt: json['editedAt'] != null ? DateTime.parse(json['editedAt']) : null,
    reactions: Map<String, String>.from(json['reactions']),
  );

  ChatMessage copyWith({
    String? id,
    String? senderId,
    String? content,
    MessageType? type,
    Map<String, dynamic>? metadata,
    DateTime? timestamp,
    bool? isRead,
    bool? isEdited,
    DateTime? editedAt,
    Map<String, String>? reactions,
  }) {
    return ChatMessage(
      id: id ?? this.id,
      senderId: senderId ?? this.senderId,
      content: content ?? this.content,
      type: type ?? this.type,
      metadata: metadata ?? this.metadata,
      timestamp: timestamp ?? this.timestamp,
      isRead: isRead ?? this.isRead,
      isEdited: isEdited ?? this.isEdited,
      editedAt: editedAt ?? this.editedAt,
      reactions: reactions ?? this.reactions,
    );
  }
}

/// Chat model
class Chat {
  final String id;
  final String name;
  final String description;
  final String imageUrl;
  final ChatType type;
  final List<String> memberIds;
  final List<String> adminIds;
  final String createdBy;
  final DateTime createdAt;
  final DateTime updatedAt;
  final String lastMessage;
  final DateTime lastMessageTime;
  final String lastMessageSender;
  final bool isActive;

  const Chat({
    required this.id,
    required this.name,
    required this.description,
    required this.imageUrl,
    required this.type,
    required this.memberIds,
    required this.adminIds,
    required this.createdBy,
    required this.createdAt,
    required this.updatedAt,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.lastMessageSender,
    required this.isActive,
  });

  factory Chat.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Chat(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'] ?? '',
      imageUrl: data['imageUrl'] ?? '',
      type: ChatType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => ChatType.direct,
      ),
      memberIds: List<String>.from(data['memberIds'] ?? []),
      adminIds: List<String>.from(data['adminIds'] ?? []),
      createdBy: data['createdBy'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastMessage: data['lastMessage'] ?? '',
      lastMessageTime: (data['lastMessageTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastMessageSender: data['lastMessageSender'] ?? '',
      isActive: data['isActive'] ?? true,
    );
  }

  Map<String, dynamic> toJson() => {
    'id': id,
    'name': name,
    'description': description,
    'imageUrl': imageUrl,
    'type': type.name,
    'memberIds': memberIds,
    'adminIds': adminIds,
    'createdBy': createdBy,
    'createdAt': createdAt.toIso8601String(),
    'updatedAt': updatedAt.toIso8601String(),
    'lastMessage': lastMessage,
    'lastMessageTime': lastMessageTime.toIso8601String(),
    'lastMessageSender': lastMessageSender,
    'isActive': isActive,
  };

  factory Chat.fromJson(Map<String, dynamic> json) => Chat(
    id: json['id'],
    name: json['name'],
    description: json['description'],
    imageUrl: json['imageUrl'],
    type: ChatType.values.firstWhere((t) => t.name == json['type']),
    memberIds: List<String>.from(json['memberIds']),
    adminIds: List<String>.from(json['adminIds']),
    createdBy: json['createdBy'],
    createdAt: DateTime.parse(json['createdAt']),
    updatedAt: DateTime.parse(json['updatedAt']),
    lastMessage: json['lastMessage'],
    lastMessageTime: DateTime.parse(json['lastMessageTime']),
    lastMessageSender: json['lastMessageSender'],
    isActive: json['isActive'],
  );

  Chat copyWith({
    String? id,
    String? name,
    String? description,
    String? imageUrl,
    ChatType? type,
    List<String>? memberIds,
    List<String>? adminIds,
    String? createdBy,
    DateTime? createdAt,
    DateTime? updatedAt,
    String? lastMessage,
    DateTime? lastMessageTime,
    String? lastMessageSender,
    bool? isActive,
  }) {
    return Chat(
      id: id ?? this.id,
      name: name ?? this.name,
      description: description ?? this.description,
      imageUrl: imageUrl ?? this.imageUrl,
      type: type ?? this.type,
      memberIds: memberIds ?? this.memberIds,
      adminIds: adminIds ?? this.adminIds,
      createdBy: createdBy ?? this.createdBy,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastMessage: lastMessage ?? this.lastMessage,
      lastMessageTime: lastMessageTime ?? this.lastMessageTime,
      lastMessageSender: lastMessageSender ?? this.lastMessageSender,
      isActive: isActive ?? this.isActive,
    );
  }

  /// Get display name for the chat
  String getDisplayName(String currentUserId) {
    if (type == ChatType.group) {
      return name.isNotEmpty ? name : 'Group Chat';
    } else {
      // For direct chats, return the other user's name
      // This would need to be resolved with user data
      return 'Direct Chat';
    }
  }

  /// Check if user is admin
  bool isUserAdmin(String userId) {
    return adminIds.contains(userId);
  }

  /// Get member count
  int get memberCount => memberIds.length;

  /// Check if chat has unread messages for user
  bool hasUnreadMessages(String userId) {
    return lastMessageSender != userId && lastMessageSender.isNotEmpty;
  }
}

/// Message types
enum MessageType {
  text,
  image,
  video,
  audio,
  document,
  property,
  location,
  system,
}

/// Chat types
enum ChatType {
  direct,
  group,
  channel,
}

/// Message type extensions
extension MessageTypeExtension on MessageType {
  String get displayName {
    switch (this) {
      case MessageType.text:
        return 'Text';
      case MessageType.image:
        return 'Image';
      case MessageType.video:
        return 'Video';
      case MessageType.audio:
        return 'Audio';
      case MessageType.document:
        return 'Document';
      case MessageType.property:
        return 'Property';
      case MessageType.location:
        return 'Location';
      case MessageType.system:
        return 'System';
    }
  }

  String get icon {
    switch (this) {
      case MessageType.text:
        return '💬';
      case MessageType.image:
        return '🖼️';
      case MessageType.video:
        return '🎥';
      case MessageType.audio:
        return '🎵';
      case MessageType.document:
        return '📄';
      case MessageType.property:
        return '🏠';
      case MessageType.location:
        return '📍';
      case MessageType.system:
        return 'ℹ️';
    }
  }
}

/// Chat type extensions
extension ChatTypeExtension on ChatType {
  String get displayName {
    switch (this) {
      case ChatType.direct:
        return 'Direct';
      case ChatType.group:
        return 'Group';
      case ChatType.channel:
        return 'Channel';
    }
  }

  String get icon {
    switch (this) {
      case ChatType.direct:
        return '👤';
      case ChatType.group:
        return '👥';
      case ChatType.channel:
        return '📢';
    }
  }
}
