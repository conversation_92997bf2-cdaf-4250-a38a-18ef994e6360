import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import '../../domain/auth_state.dart';
import '../../../../core/services/auth_service.dart';
import '../../../../core/models/user_model.dart';

/// Authentication state provider
final authStateProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

/// Authentication state notifier
class AuthNotifier extends StateNotifier<AuthState> {
  AuthNotifier() : super(const AuthInitial()) {
    _init();
  }

  void _init() {
    // Listen to auth state changes
    AuthService.authStateChanges.listen((User? user) async {
      if (user == null) {
        state = const AuthUnauthenticated();
      } else {
        // Get user profile from Firestore
        final userProfile = await AuthService.getCurrentUserProfile();
        if (userProfile != null) {
          state = AuthAuthenticated(userProfile);
        } else {
          state = const AuthUnauthenticated();
        }
      }
    });
  }

  /// Sign in with email and password
  Future<void> signIn(String email, String password) async {
    state = const AuthLoading();
    
    final result = await AuthService.signInWithEmailAndPassword(
      email: email,
      password: password,
    );

    if (result.isSuccess && result.user != null) {
      state = AuthAuthenticated(result.user!);
    } else {
      state = AuthError(result.message ?? 'Sign in failed');
    }
  }

  /// Register new user
  Future<void> register({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    String? referralCode,
  }) async {
    state = const AuthLoading();
    
    final result = await AuthService.registerWithEmailAndPassword(
      email: email,
      password: password,
      name: name,
      phoneNumber: phoneNumber,
      referralCode: referralCode,
    );

    if (result.isSuccess && result.user != null) {
      state = AuthAuthenticated(result.user!);
    } else {
      state = AuthError(result.message ?? 'Registration failed');
    }
  }

  /// Sign out
  Future<void> signOut() async {
    await AuthService.signOut();
    state = const AuthUnauthenticated();
  }

  /// Send password reset email
  Future<bool> sendPasswordResetEmail(String email) async {
    final result = await AuthService.sendPasswordResetEmail(email);
    return result.isSuccess;
  }

  /// Update user profile
  Future<void> updateProfile(UserModel updatedUser) async {
    final result = await AuthService.updateUserProfile(updatedUser);
    if (result.isSuccess && result.user != null) {
      state = AuthAuthenticated(result.user!);
    }
  }

  /// Clear error state
  void clearError() {
    if (state is AuthError) {
      state = const AuthUnauthenticated();
    }
  }
}

/// Login form state provider
final loginStateProvider = StateNotifierProvider<LoginNotifier, LoginState>((ref) {
  return LoginNotifier();
});

/// Login form notifier
class LoginNotifier extends StateNotifier<LoginState> {
  LoginNotifier() : super(const LoginState());

  void updateEmail(String email) {
    state = state.copyWith(email: email, errorMessage: null);
  }

  void updatePassword(String password) {
    state = state.copyWith(password: password, errorMessage: null);
  }

  void togglePasswordVisibility() {
    state = state.copyWith(obscurePassword: !state.obscurePassword);
  }

  void toggleRememberMe() {
    state = state.copyWith(rememberMe: !state.rememberMe);
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(errorMessage: error, isLoading: false);
  }

  void clearForm() {
    state = const LoginState();
  }

  /// Validate login form
  String? validateForm() {
    if (state.email.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(state.email)) {
      return 'Please enter a valid email';
    }
    if (state.password.isEmpty) {
      return 'Password is required';
    }
    if (state.password.length < 6) {
      return 'Password must be at least 6 characters';
    }
    return null;
  }
}

/// Registration form state provider
final registrationStateProvider = StateNotifierProvider<RegistrationNotifier, RegistrationState>((ref) {
  return RegistrationNotifier();
});

/// Registration form notifier
class RegistrationNotifier extends StateNotifier<RegistrationState> {
  RegistrationNotifier() : super(const RegistrationState());

  void updateName(String name) {
    state = state.copyWith(name: name, errorMessage: null);
  }

  void updateEmail(String email) {
    state = state.copyWith(email: email, errorMessage: null);
  }

  void updatePhoneNumber(String phoneNumber) {
    state = state.copyWith(phoneNumber: phoneNumber, errorMessage: null);
  }

  void updatePassword(String password) {
    state = state.copyWith(password: password, errorMessage: null);
  }

  void updateConfirmPassword(String confirmPassword) {
    state = state.copyWith(confirmPassword: confirmPassword, errorMessage: null);
  }

  void updateReferralCode(String referralCode) {
    state = state.copyWith(referralCode: referralCode, errorMessage: null);
  }

  void togglePasswordVisibility() {
    state = state.copyWith(obscurePassword: !state.obscurePassword);
  }

  void toggleConfirmPasswordVisibility() {
    state = state.copyWith(obscureConfirmPassword: !state.obscureConfirmPassword);
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(errorMessage: error, isLoading: false);
  }

  void clearForm() {
    state = const RegistrationState();
  }

  /// Validate registration form
  String? validateForm() {
    if (state.name.trim().isEmpty) {
      return 'Name is required';
    }
    if (state.name.trim().length < 2) {
      return 'Name must be at least 2 characters';
    }
    if (state.email.isEmpty) {
      return 'Email is required';
    }
    if (!RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(state.email)) {
      return 'Please enter a valid email';
    }
    if (state.phoneNumber.isEmpty) {
      return 'Phone number is required';
    }
    if (!RegExp(r'^[0-9]{10}$').hasMatch(state.phoneNumber.replaceAll(RegExp(r'[^\d]'), ''))) {
      return 'Please enter a valid 10-digit phone number';
    }
    if (state.password.isEmpty) {
      return 'Password is required';
    }
    if (state.password.length < 8) {
      return 'Password must be at least 8 characters';
    }
    if (state.confirmPassword.isEmpty) {
      return 'Please confirm your password';
    }
    if (state.password != state.confirmPassword) {
      return 'Passwords do not match';
    }
    return null;
  }
}

/// Current user provider
final currentUserProvider = Provider<UserModel?>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState is AuthAuthenticated ? authState.user : null;
});

/// Is authenticated provider
final isAuthenticatedProvider = Provider<bool>((ref) {
  final authState = ref.watch(authStateProvider);
  return authState is AuthAuthenticated;
});
