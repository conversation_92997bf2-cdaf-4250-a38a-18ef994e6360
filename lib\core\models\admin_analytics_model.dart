import 'package:cloud_firestore/cloud_firestore.dart';

/// Admin analytics model for comprehensive system insights
class AdminAnalyticsModel {
  final SystemOverview systemOverview;
  final UserAnalytics userAnalytics;
  final PropertyAnalytics propertyAnalytics;
  final CommissionAnalytics commissionAnalytics;
  final StarAnalytics starAnalytics;
  final PerformanceMetrics performanceMetrics;
  final DateTime generatedAt;

  const AdminAnalyticsModel({
    required this.systemOverview,
    required this.userAnalytics,
    required this.propertyAnalytics,
    required this.commissionAnalytics,
    required this.starAnalytics,
    required this.performanceMetrics,
    required this.generatedAt,
  });
}

/// System overview metrics
class SystemOverview {
  final int totalUsers;
  final int activeUsers;
  final int totalProperties;
  final int approvedProperties;
  final double totalCommissionsPaid;
  final int totalStarsAwarded;
  final int totalTransactions;
  final double systemRevenue;
  final Map<String, int> userGrowthTrend; // Last 12 months
  final Map<String, double> revenueGrowthTrend; // Last 12 months

  const SystemOverview({
    required this.totalUsers,
    required this.activeUsers,
    required this.totalProperties,
    required this.approvedProperties,
    required this.totalCommissionsPaid,
    required this.totalStarsAwarded,
    required this.totalTransactions,
    required this.systemRevenue,
    required this.userGrowthTrend,
    required this.revenueGrowthTrend,
  });

  /// Get user growth percentage
  double get userGrowthPercentage {
    final values = userGrowthTrend.values.toList();
    if (values.length < 2) return 0.0;
    final current = values.last.toDouble();
    final previous = values[values.length - 2].toDouble();
    if (previous == 0) return 0.0;
    return ((current - previous) / previous) * 100;
  }

  /// Get revenue growth percentage
  double get revenueGrowthPercentage {
    final values = revenueGrowthTrend.values.toList();
    if (values.length < 2) return 0.0;
    final current = values.last;
    final previous = values[values.length - 2];
    if (previous == 0) return 0.0;
    return ((current - previous) / previous) * 100;
  }
}

/// User analytics and demographics
class UserAnalytics {
  final Map<int, int> usersByLevel; // MLM level distribution
  final Map<String, int> usersByState; // Geographic distribution
  final Map<String, int> userRegistrationTrend; // Monthly registrations
  final List<TopPerformer> topPerformers;
  final int activeThisMonth;
  final int newRegistrationsThisMonth;
  final double averageStarsPerUser;
  final double averageCommissionPerUser;

  const UserAnalytics({
    required this.usersByLevel,
    required this.usersByState,
    required this.userRegistrationTrend,
    required this.topPerformers,
    required this.activeThisMonth,
    required this.newRegistrationsThisMonth,
    required this.averageStarsPerUser,
    required this.averageCommissionPerUser,
  });
}

/// Property analytics and insights
class PropertyAnalytics {
  final Map<String, int> propertiesByType; // Residential, Commercial, etc.
  final Map<String, int> propertiesByState; // Geographic distribution
  final Map<String, int> propertiesByStatus; // For sale, sold, etc.
  final Map<String, double> averagePriceByType; // Average prices
  final Map<String, int> propertyAdditionTrend; // Monthly additions
  final int pendingApprovals;
  final int featuredProperties;
  final double averagePropertyPrice;
  final int propertiesWithLeads;

  const PropertyAnalytics({
    required this.propertiesByType,
    required this.propertiesByState,
    required this.propertiesByStatus,
    required this.averagePriceByType,
    required this.propertyAdditionTrend,
    required this.pendingApprovals,
    required this.featuredProperties,
    required this.averagePropertyPrice,
    required this.propertiesWithLeads,
  });
}

/// Commission analytics and financial insights
class CommissionAnalytics {
  final Map<String, double> commissionsByMonth; // Monthly commission trends
  final Map<int, double> commissionsByLevel; // Commission distribution by MLM level
  final Map<String, double> commissionsByAgent; // Top earning agents
  final double totalPaidCommissions;
  final double totalPendingCommissions;
  final double averageCommissionPerTransaction;
  final int totalTransactions;
  final Map<String, int> transactionTrend; // Monthly transaction count

  const CommissionAnalytics({
    required this.commissionsByMonth,
    required this.commissionsByLevel,
    required this.commissionsByAgent,
    required this.totalPaidCommissions,
    required this.totalPendingCommissions,
    required this.averageCommissionPerTransaction,
    required this.totalTransactions,
    required this.transactionTrend,
  });

  /// Get commission growth percentage
  double get commissionGrowthPercentage {
    final values = commissionsByMonth.values.toList();
    if (values.length < 2) return 0.0;
    final current = values.last;
    final previous = values[values.length - 2];
    if (previous == 0) return 0.0;
    return ((current - previous) / previous) * 100;
  }
}

/// Star analytics and achievement insights
class StarAnalytics {
  final Map<String, int> starsByMonth; // Monthly star awards
  final Map<int, int> starDistribution; // Users by star count ranges
  final List<StarAchiever> topStarEarners;
  final int usersNearingBonus; // Users with 10-11 stars
  final int bonusesAwarded;
  final double totalBonusAmount;
  final Map<String, int> starSourceDistribution; // Direct sales vs upline

  const StarAnalytics({
    required this.starsByMonth,
    required this.starDistribution,
    required this.topStarEarners,
    required this.usersNearingBonus,
    required this.bonusesAwarded,
    required this.totalBonusAmount,
    required this.starSourceDistribution,
  });
}

/// Performance metrics and KPIs
class PerformanceMetrics {
  final double conversionRate; // Leads to sales conversion
  final double averageResponseTime; // Hours to respond to leads
  final double customerSatisfactionScore;
  final int activeLeads;
  final int convertedLeads;
  final Map<String, double> performanceTrends; // Monthly KPI trends
  final double systemEfficiency; // Overall system performance score

  const PerformanceMetrics({
    required this.conversionRate,
    required this.averageResponseTime,
    required this.customerSatisfactionScore,
    required this.activeLeads,
    required this.convertedLeads,
    required this.performanceTrends,
    required this.systemEfficiency,
  });
}

/// Top performer model
class TopPerformer {
  final String userId;
  final String name;
  final int totalStars;
  final double totalCommissions;
  final int networkSize;
  final double performanceScore;
  final String profileImageUrl;

  const TopPerformer({
    required this.userId,
    required this.name,
    required this.totalStars,
    required this.totalCommissions,
    required this.networkSize,
    required this.performanceScore,
    this.profileImageUrl = '',
  });

  /// Get formatted commission amount
  String get formattedCommissions {
    if (totalCommissions >= 10000000) {
      return '₹${(totalCommissions / 10000000).toStringAsFixed(1)} Cr';
    } else if (totalCommissions >= 100000) {
      return '₹${(totalCommissions / 100000).toStringAsFixed(1)} L';
    } else if (totalCommissions >= 1000) {
      return '₹${(totalCommissions / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${totalCommissions.toStringAsFixed(0)}';
    }
  }
}

/// Star achiever model
class StarAchiever {
  final String userId;
  final String name;
  final int totalStars;
  final int starsThisMonth;
  final DateTime lastStarEarned;
  final String profileImageUrl;

  const StarAchiever({
    required this.userId,
    required this.name,
    required this.totalStars,
    required this.starsThisMonth,
    required this.lastStarEarned,
    this.profileImageUrl = '',
  });

  /// Check if user is eligible for bonus
  bool get isEligibleForBonus => totalStars >= 12;

  /// Get days since last star
  int get daysSinceLastStar {
    return DateTime.now().difference(lastStarEarned).inDays;
  }
}

/// Admin configuration model
class AdminConfigModel {
  final String id;
  final Map<int, double> commissionRates; // MLM level commission rates
  final double starBonusAmount; // 12-star bonus amount
  final Map<String, dynamic> systemSettings;
  final Map<String, bool> featureFlags;
  final DateTime updatedAt;
  final String updatedBy;

  const AdminConfigModel({
    required this.id,
    required this.commissionRates,
    required this.starBonusAmount,
    required this.systemSettings,
    required this.featureFlags,
    required this.updatedAt,
    required this.updatedBy,
  });

  /// Create from Firestore document
  factory AdminConfigModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AdminConfigModel(
      id: doc.id,
      commissionRates: Map<int, double>.from(data['commissionRates'] ?? {}),
      starBonusAmount: (data['starBonusAmount'] ?? 0.0).toDouble(),
      systemSettings: data['systemSettings'] ?? {},
      featureFlags: Map<String, bool>.from(data['featureFlags'] ?? {}),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      updatedBy: data['updatedBy'] ?? '',
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'commissionRates': commissionRates,
      'starBonusAmount': starBonusAmount,
      'systemSettings': systemSettings,
      'featureFlags': featureFlags,
      'updatedAt': Timestamp.fromDate(updatedAt),
      'updatedBy': updatedBy,
    };
  }

  /// Create a copy with updated fields
  AdminConfigModel copyWith({
    String? id,
    Map<int, double>? commissionRates,
    double? starBonusAmount,
    Map<String, dynamic>? systemSettings,
    Map<String, bool>? featureFlags,
    DateTime? updatedAt,
    String? updatedBy,
  }) {
    return AdminConfigModel(
      id: id ?? this.id,
      commissionRates: commissionRates ?? this.commissionRates,
      starBonusAmount: starBonusAmount ?? this.starBonusAmount,
      systemSettings: systemSettings ?? this.systemSettings,
      featureFlags: featureFlags ?? this.featureFlags,
      updatedAt: updatedAt ?? this.updatedAt,
      updatedBy: updatedBy ?? this.updatedBy,
    );
  }
}
