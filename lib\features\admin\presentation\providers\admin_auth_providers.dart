import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../domain/admin_auth_state.dart';
import '../../services/admin_auth_service.dart';

/// Admin authentication state provider
final adminAuthStateProvider = StateNotifierProvider<AdminAuthNotifier, AdminAuthState>((ref) {
  return AdminAuthNotifier();
});

/// Admin authentication notifier
class AdminAuthNotifier extends StateNotifier<AdminAuthState> {
  AdminAuthNotifier() : super(const AdminUnauthenticated());

  /// Admin sign in
  Future<void> signIn(String email, String password) async {
    state = const AdminLoading();
    
    final result = await AdminAuthService.authenticateAdmin(
      email: email,
      password: password,
    );

    if (result.isSuccess && result.admin != null) {
      state = AdminAuthenticated(result.admin!);
    } else {
      state = AdminError(result.message ?? 'Admin sign in failed');
    }
  }

  /// Admin sign out
  void signOut() {
    state = const AdminUnauthenticated();
  }

  /// Clear error
  void clearError() {
    if (state is AdminError) {
      state = const AdminUnauthenticated();
    }
  }
}

/// Admin login form state
class AdminLoginState {
  final String email;
  final String password;
  final bool isLoading;
  final String? error;
  final bool rememberMe;

  const AdminLoginState({
    this.email = '',
    this.password = '',
    this.isLoading = false,
    this.error,
    this.rememberMe = false,
  });

  AdminLoginState copyWith({
    String? email,
    String? password,
    bool? isLoading,
    String? error,
    bool? rememberMe,
  }) {
    return AdminLoginState(
      email: email ?? this.email,
      password: password ?? this.password,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      rememberMe: rememberMe ?? this.rememberMe,
    );
  }
}

/// Admin login form provider
final adminLoginStateProvider = StateNotifierProvider<AdminLoginNotifier, AdminLoginState>((ref) {
  return AdminLoginNotifier();
});

/// Admin login form notifier
class AdminLoginNotifier extends StateNotifier<AdminLoginState> {
  AdminLoginNotifier() : super(const AdminLoginState());

  void setEmail(String email) {
    state = state.copyWith(email: email, error: null);
  }

  void setPassword(String password) {
    state = state.copyWith(password: password, error: null);
  }

  void toggleRememberMe() {
    state = state.copyWith(rememberMe: !state.rememberMe);
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(error: error, isLoading: false);
  }

  void clearError() {
    state = state.copyWith(error: null);
  }

  String? validateForm() {
    if (state.email.isEmpty) {
      return 'Email is required';
    }
    
    if (!state.email.contains('@')) {
      return 'Please enter a valid email';
    }
    
    if (state.password.isEmpty) {
      return 'Password is required';
    }
    
    if (state.password.length < 6) {
      return 'Password must be at least 6 characters';
    }
    
    return null;
  }
}

/// Current admin user provider
final currentAdminProvider = Provider<AdminUser?>((ref) {
  final authState = ref.watch(adminAuthStateProvider);
  
  if (authState is AdminAuthenticated) {
    return authState.admin;
  }
  
  return null;
});

/// Admin permissions provider
final adminPermissionsProvider = Provider<List<String>>((ref) {
  final admin = ref.watch(currentAdminProvider);
  return admin?.permissions ?? [];
});

/// Permission checker provider
final hasAdminPermissionProvider = Provider.family<bool, String>((ref, permission) {
  final permissions = ref.watch(adminPermissionsProvider);
  return permissions.contains(permission) || permissions.contains('all');
});
