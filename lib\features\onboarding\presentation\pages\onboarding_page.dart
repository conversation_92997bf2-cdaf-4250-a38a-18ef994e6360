import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../../app/routes/app_router.dart';
import '../../models/onboarding_data.dart';
import '../../providers/onboarding_provider.dart';
import '../widgets/basic_info_step.dart';
import '../widgets/profile_image_step.dart';
import '../widgets/onboarding_complete_step.dart';

/// Main onboarding page with step-by-step flow
class OnboardingPage extends ConsumerWidget {
  const OnboardingPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onboardingState = ref.watch(onboardingProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkBackgroundGradient,
        ),
        child: Safe<PERSON><PERSON>(
          child: Column(
            children: [
              // Progress Header
              _buildProgressHeader(context, onboardingState.currentStep),

              // Content
              Expanded(child: _buildStepContent(context, ref, onboardingState)),

              // Navigation Buttons
              if (onboardingState.currentStep != OnboardingStep.complete)
                _buildNavigationButtons(context, ref, onboardingState),
            ],
          ),
        ),
      ),
    );
  }

  /// Build progress header with step indicators
  Widget _buildProgressHeader(
    BuildContext context,
    OnboardingStep currentStep,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Column(
        children: [
          // Welcome Text
          Text(
            'Welcome to Rama Realty MLM',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Let\'s set up your agent profile',
            style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),

          // Progress Indicators
          Row(
            children: [
              _buildProgressIndicator(
                step: 1,
                title: 'Basic Info',
                isActive: currentStep == OnboardingStep.basicInfo,
                isCompleted: currentStep.index > OnboardingStep.basicInfo.index,
              ),
              Expanded(
                child: Container(
                  height: 2,
                  color: currentStep.index > OnboardingStep.basicInfo.index
                      ? AppTheme.primaryColor
                      : AppTheme.darkBorder,
                ),
              ),
              _buildProgressIndicator(
                step: 2,
                title: 'Profile Image',
                isActive: currentStep == OnboardingStep.profileImage,
                isCompleted:
                    currentStep.index > OnboardingStep.profileImage.index,
              ),
              Expanded(
                child: Container(
                  height: 2,
                  color: currentStep.index > OnboardingStep.profileImage.index
                      ? AppTheme.primaryColor
                      : AppTheme.darkBorder,
                ),
              ),
              _buildProgressIndicator(
                step: 3,
                title: 'Complete',
                isActive: currentStep == OnboardingStep.complete,
                isCompleted: false,
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build individual progress indicator
  Widget _buildProgressIndicator({
    required int step,
    required String title,
    required bool isActive,
    required bool isCompleted,
  }) {
    return Column(
      children: [
        Container(
          width: 40,
          height: 40,
          decoration: BoxDecoration(
            shape: BoxShape.circle,
            gradient: isActive || isCompleted ? AppTheme.primaryGradient : null,
            color: isActive || isCompleted ? null : AppTheme.darkBorder,
            border: Border.all(
              color: isActive
                  ? AppTheme.primaryColor
                  : isCompleted
                  ? AppTheme.primaryColor
                  : AppTheme.darkBorder,
              width: 2,
            ),
          ),
          child: Center(
            child: isCompleted
                ? const Icon(Icons.check, color: Colors.white, size: 20)
                : Text(
                    step.toString(),
                    style: TextStyle(
                      color: isActive || isCompleted
                          ? Colors.white
                          : AppTheme.darkSecondaryText,
                      fontSize: 16,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
          ),
        ),
        const SizedBox(height: 8),
        Text(
          title,
          style: TextStyle(
            color: isActive
                ? AppTheme.primaryColor
                : isCompleted
                ? AppTheme.primaryColor
                : AppTheme.darkSecondaryText,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Build step content based on current step
  Widget _buildStepContent(
    BuildContext context,
    WidgetRef ref,
    OnboardingState state,
  ) {
    switch (state.currentStep) {
      case OnboardingStep.basicInfo:
        return const BasicInfoStep();
      case OnboardingStep.profileImage:
        return const ProfileImageStep();
      case OnboardingStep.complete:
        return const OnboardingCompleteStep();
    }
  }

  /// Build navigation buttons
  Widget _buildNavigationButtons(
    BuildContext context,
    WidgetRef ref,
    OnboardingState state,
  ) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Back Button
          if (state.currentStep != OnboardingStep.basicInfo)
            Expanded(
              child: OutlinedButton(
                onPressed: state.isLoading
                    ? null
                    : () =>
                          ref.read(onboardingProvider.notifier).previousStep(),
                style: OutlinedButton.styleFrom(
                  side: const BorderSide(color: AppTheme.primaryColor),
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  shape: RoundedRectangleBorder(
                    borderRadius: BorderRadius.circular(12),
                  ),
                ),
                child: const Text('Back'),
              ),
            ),

          if (state.currentStep != OnboardingStep.basicInfo)
            const SizedBox(width: 16),

          // Next/Complete Button
          Expanded(
            flex: state.currentStep == OnboardingStep.basicInfo ? 1 : 2,
            child: _buildPrimaryButton(context, ref, state),
          ),
        ],
      ),
    );
  }

  /// Build primary action button
  Widget _buildPrimaryButton(
    BuildContext context,
    WidgetRef ref,
    OnboardingState state,
  ) {
    String buttonText;
    VoidCallback? onPressed;

    switch (state.currentStep) {
      case OnboardingStep.basicInfo:
        buttonText = 'Continue';
        onPressed = state.data.isBasicInfoComplete && !state.isLoading
            ? () => ref.read(onboardingProvider.notifier).nextStep()
            : null;
        break;
      case OnboardingStep.profileImage:
        buttonText = 'Complete Setup';
        onPressed = !state.isLoading
            ? () async {
                final user = await ref
                    .read(onboardingProvider.notifier)
                    .completeOnboarding();
                if (user != null && context.mounted) {
                  // Navigate to dashboard or show success
                  context.go('/dashboard');
                }
              }
            : null;
        break;
      case OnboardingStep.complete:
        buttonText = 'Get Started';
        onPressed = () => context.go('/dashboard');
        break;
    }

    if (state.isLoading) {
      return Container(
        padding: const EdgeInsets.symmetric(vertical: 16),
        decoration: AppTheme.createGradientDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: 12,
        ),
        child: const Row(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
              ),
            ),
            SizedBox(width: 12),
            Text(
              'Processing...',
              style: TextStyle(
                color: Colors.white,
                fontSize: 16,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      );
    }

    return GradientWidgets.gradientButton(
      text: buttonText,
      onPressed: onPressed ?? () {},
      icon: Icons.arrow_forward,
    );
  }
}
