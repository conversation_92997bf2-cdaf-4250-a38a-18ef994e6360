import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/models/user_model.dart';
import '../../commissions/models/commission_enums.dart';
import '../../commissions/services/commission_analytics_service.dart';
import '../../mlm/services/network_service.dart';
import '../models/dashboard_models.dart';

/// Service for managing agent dashboard data
class DashboardService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get complete dashboard data for an agent
  static Future<AgentDashboardData> getAgentDashboardData(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock data
        return _getMockDashboardData(agentId);
      }

      // Get agent information
      final agent = await _getAgentById(agentId);
      if (agent == null) {
        throw Exception('Agent not found');
      }

      // Get dashboard components in parallel
      final futures = await Future.wait([
        _getDashboardMetrics(agentId),
        _getDashboardGoals(agentId),
        _getRecentActivities(agentId),
        _getDashboardNotifications(agentId),
        _getTeamOverview(agentId),
        _getPerformanceInsights(agentId),
      ]);

      final metrics = futures[0] as DashboardMetrics;
      final goals = futures[1] as List<DashboardGoal>;
      final activities = futures[2] as List<RecentActivity>;
      final notifications = futures[3] as List<DashboardNotification>;
      final teamOverview = futures[4] as TeamOverview;
      final insights = futures[5] as PerformanceInsights;

      // Calculate current tier
      final currentTier = _calculateTier(metrics.totalSales);

      return AgentDashboardData(
        agentId: agentId,
        agentName: agent.name,
        agentEmail: agent.email,
        currentTier: currentTier,
        metrics: metrics,
        goals: goals,
        recentActivities: activities,
        notifications: notifications,
        teamOverview: teamOverview,
        insights: insights,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting dashboard data: $e');
      }
      rethrow;
    }
  }

  /// Get agent by ID
  static Future<UserModel?> _getAgentById(String agentId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .get();

      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get dashboard metrics
  static Future<DashboardMetrics> _getDashboardMetrics(String agentId) async {
    try {
      // Get commission analytics
      final analytics = await CommissionAnalyticsService.getAgentAnalytics(agentId);
      
      // Calculate monthly target and progress
      final now = DateTime.now();
      final monthStart = DateTime(now.year, now.month, 1);
      
      // Get monthly sales data
      final monthlyTarget = 1000000.0; // 10L - would come from agent settings
      final monthlyProgress = analytics.monthlyData.isNotEmpty
          ? analytics.monthlyData.last.amount
          : 0.0;

      // Get properties sold count
      final propertiesSold = await _getPropertiesSoldCount(agentId);
      
      // Get team size
      final networkStats = await NetworkService.getNetworkStatistics(agentId);
      
      return DashboardMetrics(
        totalSales: analytics.totalEarned,
        totalCommissions: analytics.totalPaid,
        monthlyTarget: monthlyTarget,
        monthlyProgress: monthlyProgress,
        propertiesSold: propertiesSold,
        teamSize: networkStats.totalAgents,
        conversionRate: 0.15, // Would be calculated from leads data
        growthRate: analytics.growthRate,
        monthlyData: Map.fromEntries(
          analytics.monthlyData.map((m) => MapEntry(
            '${m.month.year}-${m.month.month}',
            m.amount,
          )),
        ),
      );
    } catch (e) {
      return const DashboardMetrics(
        totalSales: 0,
        totalCommissions: 0,
        monthlyTarget: 1000000,
        monthlyProgress: 0,
        propertiesSold: 0,
        teamSize: 0,
        conversionRate: 0,
        growthRate: 0,
      );
    }
  }

  /// Get properties sold count
  static Future<int> _getPropertiesSoldCount(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection('commissions')
          .where('agentId', isEqualTo: agentId)
          .where('type', isEqualTo: CommissionType.directSale.value)
          .where('status', isEqualTo: CommissionStatus.paid.value)
          .get();

      return snapshot.docs.length;
    } catch (e) {
      return 0;
    }
  }

  /// Get dashboard goals
  static Future<List<DashboardGoal>> _getDashboardGoals(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection('goals')
          .where('agentId', isEqualTo: agentId)
          .where('status', whereIn: [GoalStatus.active.name, GoalStatus.paused.name])
          .orderBy('deadline')
          .limit(5)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return DashboardGoal(
          id: doc.id,
          title: data['title'] ?? '',
          description: data['description'] ?? '',
          type: GoalType.values.firstWhere(
            (t) => t.name == data['type'],
            orElse: () => GoalType.custom,
          ),
          targetValue: (data['targetValue'] ?? 0).toDouble(),
          currentValue: (data['currentValue'] ?? 0).toDouble(),
          deadline: (data['deadline'] as Timestamp).toDate(),
          status: GoalStatus.values.firstWhere(
            (s) => s.name == data['status'],
            orElse: () => GoalStatus.active,
          ),
          reward: data['reward'],
          createdAt: (data['createdAt'] as Timestamp).toDate(),
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get recent activities
  static Future<List<RecentActivity>> _getRecentActivities(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection('activities')
          .where('agentId', isEqualTo: agentId)
          .orderBy('timestamp', descending: true)
          .limit(10)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return RecentActivity(
          id: doc.id,
          type: ActivityType.values.firstWhere(
            (t) => t.name == data['type'],
            orElse: () => ActivityType.training,
          ),
          title: data['title'] ?? '',
          description: data['description'] ?? '',
          timestamp: (data['timestamp'] as Timestamp).toDate(),
          metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get dashboard notifications
  static Future<List<DashboardNotification>> _getDashboardNotifications(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection('notifications')
          .where('agentId', isEqualTo: agentId)
          .orderBy('timestamp', descending: true)
          .limit(10)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return DashboardNotification(
          id: doc.id,
          type: NotificationType.values.firstWhere(
            (t) => t.name == data['type'],
            orElse: () => NotificationType.system,
          ),
          title: data['title'] ?? '',
          message: data['message'] ?? '',
          timestamp: (data['timestamp'] as Timestamp).toDate(),
          isRead: data['isRead'] ?? false,
          priority: NotificationPriority.values.firstWhere(
            (p) => p.name == data['priority'],
            orElse: () => NotificationPriority.normal,
          ),
          actionData: Map<String, dynamic>.from(data['actionData'] ?? {}),
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get team overview
  static Future<TeamOverview> _getTeamOverview(String agentId) async {
    try {
      final networkStats = await NetworkService.getNetworkStatistics(agentId);
      
      // Get team members data
      final teamMembers = networkStats.topPerformers.map((performer) => TeamMember(
        id: performer.id,
        name: performer.name,
        profileImageUrl: performer.profileImageUrl,
        sales: performer.sales,
        tier: performer.tier,
        level: 1, // Would be calculated from network structure
      )).toList();

      return TeamOverview(
        totalMembers: networkStats.totalAgents,
        activeMembers: networkStats.activeAgents,
        newMembersThisMonth: 3, // Would be calculated from recent joins
        teamSales: networkStats.totalSales,
        teamCommissions: networkStats.totalCommissions,
        topPerformers: teamMembers,
      );
    } catch (e) {
      return const TeamOverview(
        totalMembers: 0,
        activeMembers: 0,
        newMembersThisMonth: 0,
        teamSales: 0,
        teamCommissions: 0,
        topPerformers: [],
      );
    }
  }

  /// Get performance insights
  static Future<PerformanceInsights> _getPerformanceInsights(String agentId) async {
    try {
      // Generate insights based on performance data
      final insights = <Insight>[
        const Insight(
          title: 'Strong Sales Performance',
          description: 'Your sales are 25% above target this month',
          type: InsightType.positive,
          impact: 0.8,
        ),
        const Insight(
          title: 'Team Growth Opportunity',
          description: 'Consider recruiting 2-3 new agents to boost team performance',
          type: InsightType.opportunity,
          impact: 0.6,
        ),
      ];

      final recommendations = <Recommendation>[
        const Recommendation(
          title: 'Focus on High-Value Properties',
          description: 'Target properties above ₹50L for better commission rates',
          type: RecommendationType.strategy,
          priority: 1,
        ),
        const Recommendation(
          title: 'Attend Sales Training',
          description: 'Improve conversion rate with advanced sales techniques',
          type: RecommendationType.training,
          priority: 2,
        ),
      ];

      return PerformanceInsights(
        insights: insights,
        recommendations: recommendations,
        trend: PerformanceTrend.improving,
      );
    } catch (e) {
      return const PerformanceInsights(
        insights: [],
        recommendations: [],
        trend: PerformanceTrend.stable,
      );
    }
  }

  /// Calculate tier based on sales
  static CommissionTier _calculateTier(double totalSales) {
    for (final tier in CommissionTier.values.reversed) {
      if (totalSales >= tier.minimumSales) {
        return tier;
      }
    }
    return CommissionTier.bronze;
  }

  /// Create a new goal
  static Future<String?> createGoal({
    required String agentId,
    required String title,
    required String description,
    required GoalType type,
    required double targetValue,
    required DateTime deadline,
    String? reward,
  }) async {
    try {
      final docRef = await _firestore.collection('goals').add({
        'agentId': agentId,
        'title': title,
        'description': description,
        'type': type.name,
        'targetValue': targetValue,
        'currentValue': 0.0,
        'deadline': Timestamp.fromDate(deadline),
        'status': GoalStatus.active.name,
        'reward': reward,
        'createdAt': Timestamp.now(),
        'updatedAt': Timestamp.now(),
      });

      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating goal: $e');
      }
      return null;
    }
  }

  /// Update goal progress
  static Future<bool> updateGoalProgress(String goalId, double newValue) async {
    try {
      await _firestore.collection('goals').doc(goalId).update({
        'currentValue': newValue,
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating goal progress: $e');
      }
      return false;
    }
  }

  /// Mark notification as read
  static Future<bool> markNotificationAsRead(String notificationId) async {
    try {
      await _firestore.collection('notifications').doc(notificationId).update({
        'isRead': true,
        'updatedAt': Timestamp.now(),
      });

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error marking notification as read: $e');
      }
      return false;
    }
  }

  /// Get mock dashboard data for development
  static AgentDashboardData _getMockDashboardData(String agentId) {
    return AgentDashboardData(
      agentId: agentId,
      agentName: 'John Doe',
      agentEmail: '<EMAIL>',
      currentTier: CommissionTier.silver,
      metrics: const DashboardMetrics(
        totalSales: 2500000,
        totalCommissions: 125000,
        monthlyTarget: 1000000,
        monthlyProgress: 750000,
        propertiesSold: 8,
        teamSize: 12,
        conversionRate: 0.18,
        growthRate: 15.5,
        monthlyData: {
          '2024-10': 650000,
          '2024-11': 800000,
          '2024-12': 750000,
        },
      ),
      goals: [
        DashboardGoal(
          id: 'goal1',
          title: 'Monthly Sales Target',
          description: 'Achieve ₹10L in sales this month',
          type: GoalType.sales,
          targetValue: 1000000,
          currentValue: 750000,
          deadline: DateTime.now().add(const Duration(days: 15)),
          status: GoalStatus.active,
          reward: '₹5,000 bonus',
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
        ),
        DashboardGoal(
          id: 'goal2',
          title: 'Team Expansion',
          description: 'Recruit 3 new agents',
          type: GoalType.recruitment,
          targetValue: 3,
          currentValue: 1,
          deadline: DateTime.now().add(const Duration(days: 30)),
          status: GoalStatus.active,
          createdAt: DateTime.now().subtract(const Duration(days: 10)),
        ),
      ],
      recentActivities: [
        RecentActivity(
          id: 'activity1',
          type: ActivityType.propertySold,
          title: 'Property Sold',
          description: '3BHK Apartment in Whitefield for ₹85L',
          timestamp: DateTime.now().subtract(const Duration(hours: 2)),
        ),
        RecentActivity(
          id: 'activity2',
          type: ActivityType.commissionEarned,
          title: 'Commission Earned',
          description: '₹21,250 commission from recent sale',
          timestamp: DateTime.now().subtract(const Duration(hours: 3)),
        ),
        RecentActivity(
          id: 'activity3',
          type: ActivityType.agentReferred,
          title: 'New Agent Referred',
          description: 'Jane Smith joined your team',
          timestamp: DateTime.now().subtract(const Duration(days: 1)),
        ),
      ],
      notifications: [
        DashboardNotification(
          id: 'notif1',
          type: NotificationType.commission,
          title: 'Commission Approved',
          message: 'Your commission of ₹21,250 has been approved',
          timestamp: DateTime.now().subtract(const Duration(hours: 1)),
          priority: NotificationPriority.high,
        ),
        DashboardNotification(
          id: 'notif2',
          type: NotificationType.goal,
          title: 'Goal Progress',
          message: 'You\'re 75% towards your monthly sales target',
          timestamp: DateTime.now().subtract(const Duration(hours: 6)),
        ),
      ],
      teamOverview: const TeamOverview(
        totalMembers: 12,
        activeMembers: 10,
        newMembersThisMonth: 2,
        teamSales: 5000000,
        teamCommissions: 250000,
        topPerformers: [
          TeamMember(
            id: 'member1',
            name: 'Jane Smith',
            sales: 1800000,
            tier: CommissionTier.silver,
            level: 1,
          ),
          TeamMember(
            id: 'member2',
            name: 'Mike Johnson',
            sales: 1200000,
            tier: CommissionTier.bronze,
            level: 1,
          ),
        ],
      ),
      insights: const PerformanceInsights(
        insights: [
          Insight(
            title: 'Excellent Sales Performance',
            description: 'Your sales are 25% above target this month',
            type: InsightType.positive,
            impact: 0.8,
          ),
          Insight(
            title: 'Team Growth Opportunity',
            description: 'Consider recruiting 2-3 new agents',
            type: InsightType.opportunity,
            impact: 0.6,
          ),
        ],
        recommendations: [
          Recommendation(
            title: 'Focus on Premium Properties',
            description: 'Target high-value properties for better commissions',
            type: RecommendationType.strategy,
            priority: 1,
          ),
          Recommendation(
            title: 'Attend Training Session',
            description: 'Improve your conversion rate with advanced techniques',
            type: RecommendationType.training,
            priority: 2,
          ),
        ],
        trend: PerformanceTrend.improving,
      ),
    );
  }
}
