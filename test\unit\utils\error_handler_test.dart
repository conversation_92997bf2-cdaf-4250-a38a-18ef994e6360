import 'package:flutter_test/flutter_test.dart';
import 'package:rama_realty_mlm/core/utils/error_handler.dart';
import 'package:firebase_auth/firebase_auth.dart';

void main() {
  group('ErrorHandler Tests', () {
    group('Firebase Auth Error Handling', () {
      test('should handle invalid email error correctly', () {
        // Arrange
        final error = FirebaseAuthException(
          code: 'invalid-email',
          message: 'The email address is not valid.',
        );

        // Act
        final result = ErrorHandler.handleFirebaseAuthError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(result.message, 'Please enter a valid email address.');
        expect(result.errorCode, 'invalid-email');
        expect(result.severity, ErrorSeverity.warning);
      });

      test('should handle weak password error correctly', () {
        // Arrange
        final error = FirebaseAuthException(
          code: 'weak-password',
          message: 'The password provided is too weak.',
        );

        // Act
        final result = ErrorHandler.handleFirebaseAuthError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(result.message, 'Password must be at least 6 characters long.');
        expect(result.errorCode, 'weak-password');
        expect(result.severity, ErrorSeverity.warning);
      });

      test('should handle email already in use error correctly', () {
        // Arrange
        final error = FirebaseAuthException(
          code: 'email-already-in-use',
          message: 'An account already exists for this email.',
        );

        // Act
        final result = ErrorHandler.handleFirebaseAuthError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'An account with this email already exists. Please sign in instead.',
        );
        expect(result.errorCode, 'email-already-in-use');
        expect(result.severity, ErrorSeverity.error);
      });

      test('should handle user not found error correctly', () {
        // Arrange
        final error = FirebaseAuthException(
          code: 'user-not-found',
          message: 'No user found for this email.',
        );

        // Act
        final result = ErrorHandler.handleFirebaseAuthError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'No account found with this email. Please check your email or create a new account.',
        );
        expect(result.errorCode, 'user-not-found');
        expect(result.severity, ErrorSeverity.error);
      });

      test('should handle wrong password error correctly', () {
        // Arrange
        final error = FirebaseAuthException(
          code: 'wrong-password',
          message: 'Wrong password provided.',
        );

        // Act
        final result = ErrorHandler.handleFirebaseAuthError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(result.message, 'Incorrect password. Please try again.');
        expect(result.errorCode, 'wrong-password');
        expect(result.severity, ErrorSeverity.error);
      });

      test('should handle too many requests error correctly', () {
        // Arrange
        final error = FirebaseAuthException(
          code: 'too-many-requests',
          message: 'Too many unsuccessful login attempts.',
        );

        // Act
        final result = ErrorHandler.handleFirebaseAuthError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'Too many failed attempts. Please try again later.',
        );
        expect(result.errorCode, 'too-many-requests');
        expect(result.severity, ErrorSeverity.critical);
      });

      test('should handle network request failed error correctly', () {
        // Arrange
        final error = FirebaseAuthException(
          code: 'network-request-failed',
          message: 'Network error occurred.',
        );

        // Act
        final result = ErrorHandler.handleFirebaseAuthError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'Network error. Please check your internet connection and try again.',
        );
        expect(result.errorCode, 'network-request-failed');
        expect(result.severity, ErrorSeverity.error);
      });

      test('should handle unknown auth error correctly', () {
        // Arrange
        final error = FirebaseAuthException(
          code: 'unknown-error',
          message: 'An unknown error occurred.',
        );

        // Act
        final result = ErrorHandler.handleFirebaseAuthError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'An unexpected error occurred. Please try again.',
        );
        expect(result.errorCode, 'unknown-error');
        expect(result.severity, ErrorSeverity.error);
      });
    });

    group('Firestore Error Handling', () {
      test('should handle permission denied error correctly', () {
        // Arrange
        final error = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'permission-denied',
          message: 'Missing or insufficient permissions.',
        );

        // Act
        final result = ErrorHandler.handleFirestoreError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'You do not have permission to perform this action.',
        );
        expect(result.errorCode, 'permission-denied');
        expect(result.severity, ErrorSeverity.critical);
      });

      test('should handle not found error correctly', () {
        // Arrange
        final error = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'not-found',
          message: 'Document not found.',
        );

        // Act
        final result = ErrorHandler.handleFirestoreError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(result.message, 'The requested data was not found.');
        expect(result.errorCode, 'not-found');
        expect(result.severity, ErrorSeverity.warning);
      });

      test('should handle unavailable error correctly', () {
        // Arrange
        final error = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'unavailable',
          message: 'Service unavailable.',
        );

        // Act
        final result = ErrorHandler.handleFirestoreError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'Service is temporarily unavailable. Please try again later.',
        );
        expect(result.errorCode, 'unavailable');
        expect(result.severity, ErrorSeverity.error);
      });

      test('should handle quota exceeded error correctly', () {
        // Arrange
        final error = FirebaseException(
          plugin: 'cloud_firestore',
          code: 'resource-exhausted',
          message: 'Quota exceeded.',
        );

        // Act
        final result = ErrorHandler.handleFirestoreError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'Service quota exceeded. Please try again later.',
        );
        expect(result.errorCode, 'resource-exhausted');
        expect(result.severity, ErrorSeverity.critical);
      });
    });

    group('Validation Error Handling', () {
      test('should handle email validation correctly', () {
        // Test valid emails
        expect(ErrorHandler.validateEmail('<EMAIL>'), isNull);
        expect(ErrorHandler.validateEmail('<EMAIL>'), isNull);
        expect(ErrorHandler.validateEmail('<EMAIL>'), isNull);

        // Test invalid emails
        expect(ErrorHandler.validateEmail(''), 'Email is required');
        expect(
          ErrorHandler.validateEmail('invalid-email'),
          'Please enter a valid email address',
        );
        expect(
          ErrorHandler.validateEmail('test@'),
          'Please enter a valid email address',
        );
        expect(
          ErrorHandler.validateEmail('@domain.com'),
          'Please enter a valid email address',
        );
        expect(
          ErrorHandler.validateEmail('test.domain.com'),
          'Please enter a valid email address',
        );
      });

      test('should handle phone number validation correctly', () {
        // Test valid Indian phone numbers
        expect(ErrorHandler.validatePhoneNumber('+************'), isNull);
        expect(ErrorHandler.validatePhoneNumber('9876543210'), isNull);
        expect(ErrorHandler.validatePhoneNumber('+91 9876543210'), isNull);

        // Test invalid phone numbers
        expect(
          ErrorHandler.validatePhoneNumber(''),
          'Phone number is required',
        );
        expect(
          ErrorHandler.validatePhoneNumber('123'),
          'Please enter a valid phone number',
        );
        expect(
          ErrorHandler.validatePhoneNumber('abcdefghij'),
          'Please enter a valid phone number',
        );
        expect(
          ErrorHandler.validatePhoneNumber('+1234567890'),
          'Please enter a valid Indian phone number',
        );
      });

      test('should handle password validation correctly', () {
        // Test valid passwords
        expect(ErrorHandler.validatePassword('password123'), isNull);
        expect(ErrorHandler.validatePassword('MySecurePass1'), isNull);
        expect(ErrorHandler.validatePassword('123456'), isNull);

        // Test invalid passwords
        expect(ErrorHandler.validatePassword(''), 'Password is required');
        expect(
          ErrorHandler.validatePassword('123'),
          'Password must be at least 6 characters long',
        );
        expect(
          ErrorHandler.validatePassword('12345'),
          'Password must be at least 6 characters long',
        );
      });

      test('should handle name validation correctly', () {
        // Test valid names
        expect(ErrorHandler.validateName('John Doe'), isNull);
        expect(ErrorHandler.validateName('Rajesh Kumar'), isNull);
        expect(ErrorHandler.validateName('A'), isNull);

        // Test invalid names
        expect(ErrorHandler.validateName(''), 'Name is required');
        expect(ErrorHandler.validateName('   '), 'Name is required');
      });

      test('should handle amount validation correctly', () {
        // Test valid amounts
        expect(ErrorHandler.validateAmount('1000000'), isNull);
        expect(ErrorHandler.validateAmount('50.5'), isNull);
        expect(ErrorHandler.validateAmount('0'), isNull);

        // Test invalid amounts
        expect(ErrorHandler.validateAmount(''), 'Amount is required');
        expect(
          ErrorHandler.validateAmount('abc'),
          'Please enter a valid amount',
        );
        expect(ErrorHandler.validateAmount('-100'), 'Amount must be positive');
      });

      test('should handle referral code validation correctly', () {
        // Test valid referral codes
        expect(ErrorHandler.validateReferralCode('ABC12345'), isNull);
        expect(ErrorHandler.validateReferralCode('XYZ98765'), isNull);
        expect(ErrorHandler.validateReferralCode(''), isNull); // Optional field

        // Test invalid referral codes
        expect(
          ErrorHandler.validateReferralCode('123'),
          'Referral code must be 8 characters long',
        );
        expect(
          ErrorHandler.validateReferralCode('ABCDEFGHI'),
          'Referral code must be 8 characters long',
        );
        expect(
          ErrorHandler.validateReferralCode('abc12345'),
          'Referral code must be uppercase letters and numbers',
        );
      });
    });

    group('Network Error Handling', () {
      test('should handle network timeout correctly', () {
        // Arrange
        final error = Exception('Connection timeout');

        // Act
        final result = ErrorHandler.handleNetworkError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'Connection timeout. Please check your internet connection and try again.',
        );
        expect(result.severity, ErrorSeverity.error);
      });

      test('should handle no internet connection correctly', () {
        // Arrange
        final error = Exception('No internet connection');

        // Act
        final result = ErrorHandler.handleNetworkError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'No internet connection. Please check your network settings.',
        );
        expect(result.severity, ErrorSeverity.error);
      });

      test('should handle server error correctly', () {
        // Arrange
        final error = Exception('Server error 500');

        // Act
        final result = ErrorHandler.handleNetworkError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(result.message, 'Server error. Please try again later.');
        expect(result.severity, ErrorSeverity.error);
      });
    });

    group('Generic Error Handling', () {
      test('should handle unknown errors correctly', () {
        // Arrange
        final error = Exception('Unknown error');

        // Act
        final result = ErrorHandler.handleGenericError(error);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'An unexpected error occurred. Please try again.',
        );
        expect(result.severity, ErrorSeverity.error);
      });

      test('should handle null errors correctly', () {
        // Act
        final result = ErrorHandler.handleGenericError(null);

        // Assert
        expect(result.isUserFriendly, true);
        expect(
          result.message,
          'An unexpected error occurred. Please try again.',
        );
        expect(result.severity, ErrorSeverity.error);
      });
    });

    group('Error Logging', () {
      test('should log errors with correct severity', () {
        // Arrange
        final error = AppError(
          message: 'Test error',
          errorCode: 'test-error',
          severity: ErrorSeverity.critical,
          isUserFriendly: true,
        );

        // Act & Assert
        expect(() => ErrorHandler.logError(error), returnsNormally);
      });

      test('should format error messages correctly', () {
        // Arrange
        final error = AppError(
          message: 'Test error',
          errorCode: 'test-error',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
          stackTrace: StackTrace.current,
        );

        // Act
        final formatted = ErrorHandler.formatErrorForLogging(error);

        // Assert
        expect(formatted, contains('Test error'));
        expect(formatted, contains('test-error'));
        expect(formatted, contains('ERROR'));
      });
    });

    group('Error Recovery', () {
      test('should suggest appropriate recovery actions', () {
        // Test network error recovery
        final networkError = AppError(
          message: 'Network error',
          errorCode: 'network-error',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
        );

        final networkRecovery = ErrorHandler.getRecoveryActions(networkError);
        expect(networkRecovery, contains('Check internet connection'));
        expect(networkRecovery, contains('Try again'));

        // Test auth error recovery
        final authError = AppError(
          message: 'Authentication failed',
          errorCode: 'auth-failed',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
        );

        final authRecovery = ErrorHandler.getRecoveryActions(authError);
        expect(authRecovery, contains('Check credentials'));
        expect(authRecovery, contains('Reset password'));
      });
    });
  });
}
