import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/user_model.dart';
import '../models/property_model.dart';
import '../services/user_service.dart';
import '../constants/app_constants.dart';

/// Utility class for creating test data in Firestore
class FirestoreTestData {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Create sample users for testing
  static Future<void> createSampleUsers() async {
    if (!kDebugMode) {
      print('Test data creation is only available in debug mode');
      return;
    }

    try {
      // Admin user
      final adminUser = UserModel(
        id: 'admin-001',
        email: '<EMAIL>',
        name: 'Admin User',
        phoneNumber: '+91-9876543210',
        role: AppConstants.adminRole,
        level: 10,
        totalSales: 5000000.0,
        totalCommissions: 500000.0,
        isActive: true,
        createdAt: DateTime.now().subtract(const Duration(days: 365)),
        updatedAt: DateTime.now(),
        additionalInfo: {
          'tier': 'platinum',
          'referralCode': 'ADM001',
          'signupSource': 'direct',
          'totalStars': 100,
          'totalBonuses': 50000.0,
        },
      );

      // Sample agents
      final agents = [
        UserModel(
          id: 'agent-001',
          email: '<EMAIL>',
          name: 'Rajesh Kumar',
          phoneNumber: '+91-9876543211',
          role: AppConstants.agentRole,
          level: 5,
          totalSales: 2500000.0,
          totalCommissions: 125000.0,
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 180)),
          updatedAt: DateTime.now(),
          uplineId: 'admin-001',
          additionalInfo: {
            'tier': 'gold',
            'referralCode': 'RAJ001',
            'signupSource': 'referral',
            'totalStars': 50,
            'totalBonuses': 25000.0,
          },
        ),
        UserModel(
          id: 'agent-002',
          email: '<EMAIL>',
          name: 'Priya Sharma',
          phoneNumber: '+91-9876543212',
          role: AppConstants.agentRole,
          level: 3,
          totalSales: 1500000.0,
          totalCommissions: 75000.0,
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 120)),
          updatedAt: DateTime.now(),
          uplineId: 'agent-001',
          additionalInfo: {
            'tier': 'silver',
            'referralCode': 'PRI001',
            'signupSource': 'referral',
            'totalStars': 30,
            'totalBonuses': 15000.0,
          },
        ),
        UserModel(
          id: 'agent-003',
          email: '<EMAIL>',
          name: 'Amit Patel',
          phoneNumber: '+91-9876543213',
          role: AppConstants.agentRole,
          level: 2,
          totalSales: 800000.0,
          totalCommissions: 40000.0,
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 60)),
          updatedAt: DateTime.now(),
          uplineId: 'agent-002',
          additionalInfo: {
            'tier': 'bronze',
            'referralCode': 'AMI001',
            'signupSource': 'referral',
            'totalStars': 15,
            'totalBonuses': 8000.0,
          },
        ),
      ];

      // Create admin user
      await UserService.createUserProfile(adminUser);
      print('✅ Created admin user: ${adminUser.name}');

      // Create agent users
      for (final agent in agents) {
        await UserService.createUserProfile(agent);
        print('✅ Created agent: ${agent.name}');
      }

      print('🎉 Sample users created successfully!');
    } catch (e) {
      print('❌ Error creating sample users: $e');
    }
  }

  /// Create sample properties for testing
  static Future<void> createSampleProperties() async {
    if (!kDebugMode) {
      print('Test data creation is only available in debug mode');
      return;
    }

    try {
      final properties = [
        PropertyModel(
          id: 'prop-001',
          title: 'Luxury Villa in Gurgaon',
          description: 'Beautiful 4BHK villa with modern amenities',
          price: 15000000.0,
          location: 'Sector 47, Gurgaon',
          propertyType: 'Villa',
          bedrooms: 4,
          bathrooms: 4,
          area: 3500.0,
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 30)),
          updatedAt: DateTime.now(),
          agentId: 'agent-001',
          images: [
            'https://example.com/villa1.jpg',
            'https://example.com/villa2.jpg',
          ],
          amenities: ['Swimming Pool', 'Gym', 'Garden', 'Parking'],
          additionalInfo: {
            'furnishing': 'Semi-Furnished',
            'facing': 'North',
            'floor': 'Ground + 2',
          },
        ),
        PropertyModel(
          id: 'prop-002',
          title: 'Modern Apartment in Noida',
          description: 'Spacious 3BHK apartment with city view',
          price: 8500000.0,
          location: 'Sector 62, Noida',
          propertyType: 'Apartment',
          bedrooms: 3,
          bathrooms: 3,
          area: 1800.0,
          isActive: true,
          createdAt: DateTime.now().subtract(const Duration(days: 15)),
          updatedAt: DateTime.now(),
          agentId: 'agent-002',
          images: [
            'https://example.com/apt1.jpg',
            'https://example.com/apt2.jpg',
          ],
          amenities: ['Lift', 'Security', 'Power Backup', 'Parking'],
          additionalInfo: {
            'furnishing': 'Unfurnished',
            'facing': 'East',
            'floor': '12th Floor',
          },
        ),
      ];

      for (final property in properties) {
        await _firestore
            .collection(AppConstants.propertiesCollection)
            .doc(property.id)
            .set(property.toJson());
        print('✅ Created property: ${property.title}');
      }

      print('🎉 Sample properties created successfully!');
    } catch (e) {
      print('❌ Error creating sample properties: $e');
    }
  }

  /// Create all sample data
  static Future<void> createAllSampleData() async {
    print('🚀 Creating sample data for Rama Samriddhi MLM...');
    await createSampleUsers();
    await createSampleProperties();
    print('✨ All sample data created successfully!');
  }

  /// Clear all test data (use with caution!)
  static Future<void> clearAllTestData() async {
    if (!kDebugMode) {
      print('Test data clearing is only available in debug mode');
      return;
    }

    try {
      // Clear users
      final usersSnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .get();
      
      for (final doc in usersSnapshot.docs) {
        await doc.reference.delete();
      }

      // Clear properties
      final propertiesSnapshot = await _firestore
          .collection(AppConstants.propertiesCollection)
          .get();
      
      for (final doc in propertiesSnapshot.docs) {
        await doc.reference.delete();
      }

      print('🧹 All test data cleared successfully!');
    } catch (e) {
      print('❌ Error clearing test data: $e');
    }
  }
}
