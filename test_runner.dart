import 'dart:io';
import 'dart:convert';

/// Comprehensive test runner for Rama Realty MLM application
class TestRunner {
  static const String testResultsFile = 'test_results.json';
  static const String coverageFile = 'coverage/lcov.info';
  
  /// Run all tests with comprehensive reporting
  static Future<void> runAllTests() async {
    print('🚀 Starting comprehensive test suite for Rama Realty MLM...\n');
    
    final results = <String, dynamic>{
      'timestamp': DateTime.now().toIso8601String(),
      'testSuites': <String, dynamic>{},
      'coverage': <String, dynamic>{},
      'performance': <String, dynamic>{},
      'summary': <String, dynamic>{},
    };

    try {
      // 1. Run unit tests
      print('📋 Running unit tests...');
      final unitResults = await _runUnitTests();
      results['testSuites']['unit'] = unitResults;
      _printTestResults('Unit Tests', unitResults);

      // 2. Run widget tests
      print('\n🎨 Running widget tests...');
      final widgetResults = await _runWidgetTests();
      results['testSuites']['widget'] = widgetResults;
      _printTestResults('Widget Tests', widgetResults);

      // 3. Run integration tests
      print('\n🔗 Running integration tests...');
      final integrationResults = await _runIntegrationTests();
      results['testSuites']['integration'] = integrationResults;
      _printTestResults('Integration Tests', integrationResults);

      // 4. Run performance tests
      print('\n⚡ Running performance tests...');
      final performanceResults = await _runPerformanceTests();
      results['testSuites']['performance'] = performanceResults;
      _printTestResults('Performance Tests', performanceResults);

      // 5. Generate test coverage
      print('\n📊 Generating test coverage...');
      final coverageResults = await _generateCoverage();
      results['coverage'] = coverageResults;
      _printCoverageResults(coverageResults);

      // 6. Run code quality checks
      print('\n🔍 Running code quality checks...');
      final qualityResults = await _runQualityChecks();
      results['quality'] = qualityResults;
      _printQualityResults(qualityResults);

      // 7. Generate summary
      final summary = _generateSummary(results);
      results['summary'] = summary;
      _printSummary(summary);

      // 8. Save results
      await _saveResults(results);
      
      print('\n✅ Test suite completed successfully!');
      print('📄 Results saved to $testResultsFile');
      
    } catch (e) {
      print('\n❌ Test suite failed: $e');
      exit(1);
    }
  }

  /// Run unit tests
  static Future<Map<String, dynamic>> _runUnitTests() async {
    final result = await Process.run(
      'flutter',
      ['test', 'test/unit/', '--reporter=json'],
      workingDirectory: '.',
    );

    return _parseTestOutput(result);
  }

  /// Run widget tests
  static Future<Map<String, dynamic>> _runWidgetTests() async {
    final result = await Process.run(
      'flutter',
      ['test', 'test/widget/', '--reporter=json'],
      workingDirectory: '.',
    );

    return _parseTestOutput(result);
  }

  /// Run integration tests
  static Future<Map<String, dynamic>> _runIntegrationTests() async {
    final result = await Process.run(
      'flutter',
      ['test', 'integration_test/', '--reporter=json'],
      workingDirectory: '.',
    );

    return _parseTestOutput(result);
  }

  /// Run performance tests
  static Future<Map<String, dynamic>> _runPerformanceTests() async {
    final result = await Process.run(
      'flutter',
      ['test', 'test/performance/', '--reporter=json'],
      workingDirectory: '.',
    );

    return _parseTestOutput(result);
  }

  /// Generate test coverage
  static Future<Map<String, dynamic>> _generateCoverage() async {
    // Run tests with coverage
    final testResult = await Process.run(
      'flutter',
      ['test', '--coverage'],
      workingDirectory: '.',
    );

    if (testResult.exitCode != 0) {
      return {'error': 'Failed to generate coverage'};
    }

    // Generate HTML coverage report
    final genHtmlResult = await Process.run(
      'genhtml',
      ['coverage/lcov.info', '-o', 'coverage/html'],
      workingDirectory: '.',
    );

    // Parse coverage data
    final coverageData = await _parseCoverageData();
    
    return {
      'success': testResult.exitCode == 0,
      'htmlGenerated': genHtmlResult.exitCode == 0,
      'data': coverageData,
    };
  }

  /// Run code quality checks
  static Future<Map<String, dynamic>> _runQualityChecks() async {
    final results = <String, dynamic>{};

    // Run dart analyze
    final analyzeResult = await Process.run(
      'dart',
      ['analyze', '--fatal-infos'],
      workingDirectory: '.',
    );

    results['analyze'] = {
      'success': analyzeResult.exitCode == 0,
      'output': analyzeResult.stdout,
      'errors': analyzeResult.stderr,
    };

    // Run dart format check
    final formatResult = await Process.run(
      'dart',
      ['format', '--set-exit-if-changed', '.'],
      workingDirectory: '.',
    );

    results['format'] = {
      'success': formatResult.exitCode == 0,
      'output': formatResult.stdout,
      'errors': formatResult.stderr,
    };

    return results;
  }

  /// Parse test output
  static Map<String, dynamic> _parseTestOutput(ProcessResult result) {
    try {
      if (result.stdout.toString().isEmpty) {
        return {
          'success': result.exitCode == 0,
          'totalTests': 0,
          'passedTests': 0,
          'failedTests': 0,
          'skippedTests': 0,
          'duration': 0,
          'output': result.stderr.toString(),
        };
      }

      final lines = result.stdout.toString().split('\n');
      final jsonLines = lines.where((line) => line.trim().startsWith('{')).toList();
      
      if (jsonLines.isEmpty) {
        return _parseTextOutput(result);
      }

      int totalTests = 0;
      int passedTests = 0;
      int failedTests = 0;
      int skippedTests = 0;
      double duration = 0;

      for (final line in jsonLines) {
        try {
          final json = jsonDecode(line);
          if (json['type'] == 'testDone') {
            totalTests++;
            if (json['result'] == 'success') {
              passedTests++;
            } else if (json['result'] == 'failure') {
              failedTests++;
            } else {
              skippedTests++;
            }
            duration += (json['time'] ?? 0) / 1000; // Convert to seconds
          }
        } catch (e) {
          // Skip invalid JSON lines
        }
      }

      return {
        'success': result.exitCode == 0,
        'totalTests': totalTests,
        'passedTests': passedTests,
        'failedTests': failedTests,
        'skippedTests': skippedTests,
        'duration': duration,
        'output': result.stdout.toString(),
      };
    } catch (e) {
      return _parseTextOutput(result);
    }
  }

  /// Parse text output when JSON parsing fails
  static Map<String, dynamic> _parseTextOutput(ProcessResult result) {
    final output = result.stdout.toString();
    final lines = output.split('\n');
    
    // Look for test summary line
    final summaryLine = lines.firstWhere(
      (line) => line.contains('tests passed') || line.contains('All tests passed'),
      orElse: () => '',
    );

    int totalTests = 0;
    int passedTests = 0;
    int failedTests = 0;

    if (summaryLine.isNotEmpty) {
      final regex = RegExp(r'(\d+) tests? passed');
      final match = regex.firstMatch(summaryLine);
      if (match != null) {
        passedTests = int.parse(match.group(1)!);
        totalTests = passedTests;
      }
    }

    return {
      'success': result.exitCode == 0,
      'totalTests': totalTests,
      'passedTests': passedTests,
      'failedTests': failedTests,
      'skippedTests': 0,
      'duration': 0,
      'output': output,
    };
  }

  /// Parse coverage data
  static Future<Map<String, dynamic>> _parseCoverageData() async {
    try {
      final file = File(coverageFile);
      if (!await file.exists()) {
        return {'error': 'Coverage file not found'};
      }

      final content = await file.readAsString();
      final lines = content.split('\n');
      
      int totalLines = 0;
      int coveredLines = 0;
      
      for (final line in lines) {
        if (line.startsWith('LF:')) {
          totalLines += int.parse(line.substring(3));
        } else if (line.startsWith('LH:')) {
          coveredLines += int.parse(line.substring(3));
        }
      }
      
      final percentage = totalLines > 0 ? (coveredLines / totalLines) * 100 : 0;
      
      return {
        'totalLines': totalLines,
        'coveredLines': coveredLines,
        'percentage': percentage.toStringAsFixed(2),
      };
    } catch (e) {
      return {'error': 'Failed to parse coverage data: $e'};
    }
  }

  /// Generate test summary
  static Map<String, dynamic> _generateSummary(Map<String, dynamic> results) {
    final testSuites = results['testSuites'] as Map<String, dynamic>;
    
    int totalTests = 0;
    int totalPassed = 0;
    int totalFailed = 0;
    double totalDuration = 0;
    
    for (final suite in testSuites.values) {
      if (suite is Map<String, dynamic>) {
        totalTests += (suite['totalTests'] as int? ?? 0);
        totalPassed += (suite['passedTests'] as int? ?? 0);
        totalFailed += (suite['failedTests'] as int? ?? 0);
        totalDuration += (suite['duration'] as double? ?? 0);
      }
    }
    
    final coverage = results['coverage'] as Map<String, dynamic>?;
    final coveragePercentage = coverage?['data']?['percentage'] ?? '0.0';
    
    final quality = results['quality'] as Map<String, dynamic>?;
    final analyzeSuccess = quality?['analyze']?['success'] ?? false;
    final formatSuccess = quality?['format']?['success'] ?? false;
    
    return {
      'totalTests': totalTests,
      'totalPassed': totalPassed,
      'totalFailed': totalFailed,
      'successRate': totalTests > 0 ? ((totalPassed / totalTests) * 100).toStringAsFixed(2) : '0.0',
      'totalDuration': totalDuration.toStringAsFixed(2),
      'coveragePercentage': coveragePercentage,
      'codeQuality': {
        'analyzeSuccess': analyzeSuccess,
        'formatSuccess': formatSuccess,
        'overallSuccess': analyzeSuccess && formatSuccess,
      },
      'overallSuccess': totalFailed == 0 && analyzeSuccess && formatSuccess,
    };
  }

  /// Print test results
  static void _printTestResults(String suiteName, Map<String, dynamic> results) {
    final success = results['success'] as bool? ?? false;
    final total = results['totalTests'] as int? ?? 0;
    final passed = results['passedTests'] as int? ?? 0;
    final failed = results['failedTests'] as int? ?? 0;
    final duration = results['duration'] as double? ?? 0;
    
    final status = success ? '✅' : '❌';
    print('$status $suiteName: $passed/$total passed, $failed failed (${duration.toStringAsFixed(2)}s)');
  }

  /// Print coverage results
  static void _printCoverageResults(Map<String, dynamic> results) {
    final data = results['data'] as Map<String, dynamic>?;
    if (data != null && !data.containsKey('error')) {
      final percentage = data['percentage'] as String? ?? '0.0';
      final covered = data['coveredLines'] as int? ?? 0;
      final total = data['totalLines'] as int? ?? 0;
      print('📊 Coverage: $percentage% ($covered/$total lines covered)');
    } else {
      print('❌ Coverage: Failed to generate');
    }
  }

  /// Print quality results
  static void _printQualityResults(Map<String, dynamic> results) {
    final analyze = results['analyze'] as Map<String, dynamic>?;
    final format = results['format'] as Map<String, dynamic>?;
    
    final analyzeStatus = (analyze?['success'] ?? false) ? '✅' : '❌';
    final formatStatus = (format?['success'] ?? false) ? '✅' : '❌';
    
    print('$analyzeStatus Code Analysis');
    print('$formatStatus Code Formatting');
  }

  /// Print summary
  static void _printSummary(Map<String, dynamic> summary) {
    print('\n📋 TEST SUMMARY');
    print('=' * 50);
    print('Total Tests: ${summary['totalTests']}');
    print('Passed: ${summary['totalPassed']}');
    print('Failed: ${summary['totalFailed']}');
    print('Success Rate: ${summary['successRate']}%');
    print('Duration: ${summary['totalDuration']}s');
    print('Coverage: ${summary['coveragePercentage']}%');
    
    final quality = summary['codeQuality'] as Map<String, dynamic>;
    final qualityStatus = quality['overallSuccess'] ? '✅ PASS' : '❌ FAIL';
    print('Code Quality: $qualityStatus');
    
    final overallStatus = summary['overallSuccess'] ? '✅ PASS' : '❌ FAIL';
    print('Overall: $overallStatus');
    print('=' * 50);
  }

  /// Save results to file
  static Future<void> _saveResults(Map<String, dynamic> results) async {
    final file = File(testResultsFile);
    final jsonString = const JsonEncoder.withIndent('  ').convert(results);
    await file.writeAsString(jsonString);
  }
}

/// Main entry point
void main(List<String> args) async {
  if (args.contains('--help') || args.contains('-h')) {
    print('Rama Realty MLM Test Runner');
    print('Usage: dart test_runner.dart [options]');
    print('Options:');
    print('  --help, -h    Show this help message');
    print('  --unit        Run only unit tests');
    print('  --widget      Run only widget tests');
    print('  --integration Run only integration tests');
    print('  --performance Run only performance tests');
    print('  --coverage    Generate coverage report only');
    return;
  }

  await TestRunner.runAllTests();
}
