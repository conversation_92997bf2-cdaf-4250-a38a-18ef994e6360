import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:flutter/foundation.dart';

import '../core/constants/app_constants.dart';
import '../shared/themes/app_theme.dart';
import 'routes/app_router.dart';
import '../firebase_options.dart';

class RamaSamriddhiApp extends ConsumerWidget {
  const RamaSamriddhiApp({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return MaterialApp.router(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.dark, // Default to dark theme for better experience
      debugShowCheckedModeBanner: false,
      routerConfig: router,
    );
  }
}

/// Initialize Firebase and other services
Future<void> initializeApp() async {
  WidgetsFlutterBinding.ensureInitialized();

  // Initialize Firebase
  try {
    await Firebase.initializeApp(
      options: DefaultFirebaseOptions.currentPlatform,
    );
    if (kDebugMode) {
      print('Firebase initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Firebase initialization failed: $e');
    }
    rethrow; // Re-throw to be caught by main.dart
  }

  // Initialize other services
  try {
    // Initialize authentication service
    await _initializeAuthService();

    // Initialize Hive for offline storage
    await _initializeHive();

    // Initialize notification service
    await _initializePushNotifications();

    if (kDebugMode) {
      print('All services initialized successfully');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing services: $e');
    }
    // Don't rethrow service errors - app can still work
  }
}

/// Initialize Hive for offline storage
Future<void> _initializeHive() async {
  try {
    // Initialize Hive (mock implementation for now)
    if (kDebugMode) {
      print('Hive initialized for offline storage');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing Hive: $e');
    }
  }
}

/// Initialize authentication service
Future<void> _initializeAuthService() async {
  try {
    // Firebase Auth initializes automatically with Firebase.initializeApp()
    // No additional initialization needed

    if (kDebugMode) {
      print('Authentication service ready');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing auth service: $e');
    }
  }
}

/// Initialize push notifications
Future<void> _initializePushNotifications() async {
  try {
    // Initialize push notifications (mock implementation for now)
    if (kDebugMode) {
      print('Push notifications initialized');
    }
  } catch (e) {
    if (kDebugMode) {
      print('Error initializing push notifications: $e');
    }
  }
}

/// Simple test widget to verify the app runs
class TestMainNavigation extends StatelessWidget {
  const TestMainNavigation({super.key});

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: Container(
        decoration: BoxDecoration(gradient: AppTheme.primaryGradient),
        child: Center(
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            children: [
              // App Logo
              Container(
                width: 120,
                height: 120,
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(24),
                ),
                child: const Icon(
                  Icons.home_work,
                  size: 64,
                  color: Colors.white,
                ),
              ),

              const SizedBox(height: 32),

              // App Name
              const Text(
                'Rama Samriddhi',
                style: TextStyle(
                  color: Color(0xFFFF4500), // Bright Bold Orange
                  fontSize: 32,
                  fontWeight: FontWeight.w900, // Extra bold
                  letterSpacing: 1.0,
                ),
              ),

              const SizedBox(height: 8),

              // Tagline
              Text(
                'Building Networks, Creating Success',
                style: TextStyle(
                  color: Colors.white.withValues(alpha: 0.9),
                  fontSize: 16,
                ),
              ),

              const SizedBox(height: 48),

              // Success Message
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 24,
                  vertical: 16,
                ),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: const Text(
                  '🎉 App Integration Successful! 🎉',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
              ),

              const SizedBox(height: 24),

              // Features List
              Container(
                padding: const EdgeInsets.all(20),
                margin: const EdgeInsets.symmetric(horizontal: 32),
                decoration: BoxDecoration(
                  color: Colors.white.withValues(alpha: 0.1),
                  borderRadius: BorderRadius.circular(12),
                ),
                child: Column(
                  children: [
                    Text(
                      'Integrated Features:',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 12),
                    _buildFeatureItem('✅ Enhanced Dashboard'),
                    _buildFeatureItem('✅ Property Management'),
                    _buildFeatureItem('✅ Commission Analytics'),
                    _buildFeatureItem('✅ Network Visualization'),
                    _buildFeatureItem('✅ Advanced Reporting'),
                    _buildFeatureItem('✅ Real-time Messaging'),
                    _buildFeatureItem('✅ Location Services'),
                    _buildFeatureItem('✅ Offline Support'),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildFeatureItem(String text) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 2),
      child: Text(
        text,
        style: TextStyle(
          color: Colors.white.withValues(alpha: 0.8),
          fontSize: 14,
        ),
      ),
    );
  }
}
