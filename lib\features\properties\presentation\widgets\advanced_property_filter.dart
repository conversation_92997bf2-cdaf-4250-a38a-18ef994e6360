import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../models/property_enums.dart';
import '../../models/property_filter_model.dart';

/// Advanced property filter widget with comprehensive filtering options
class AdvancedPropertyFilter extends ConsumerStatefulWidget {
  final PropertyFilterModel initialFilter;
  final Function(PropertyFilterModel) onFilterChanged;
  final VoidCallback? onClose;

  const AdvancedPropertyFilter({
    super.key,
    required this.initialFilter,
    required this.onFilterChanged,
    this.onClose,
  });

  @override
  ConsumerState<AdvancedPropertyFilter> createState() =>
      _AdvancedPropertyFilterState();
}

class _AdvancedPropertyFilterState extends ConsumerState<AdvancedPropertyFilter>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  late PropertyFilterModel _currentFilter;

  // Controllers for text inputs
  final _searchController = TextEditingController();
  final _minPriceController = TextEditingController();
  final _maxPriceController = TextEditingController();
  final _minAreaController = TextEditingController();
  final _maxAreaController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _currentFilter = widget.initialFilter;

    // Initialize controllers
    _searchController.text = _currentFilter.searchQuery ?? '';
    _minPriceController.text = _currentFilter.customMinPrice?.toString() ?? '';
    _maxPriceController.text = _currentFilter.customMaxPrice?.toString() ?? '';
    _minAreaController.text = _currentFilter.minArea?.toString() ?? '';
    _maxAreaController.text = _currentFilter.maxArea?.toString() ?? '';
  }

  @override
  void dispose() {
    _tabController.dispose();
    _searchController.dispose();
    _minPriceController.dispose();
    _maxPriceController.dispose();
    _minAreaController.dispose();
    _maxAreaController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      height: MediaQuery.of(context).size.height * 0.8,
      decoration: const BoxDecoration(
        gradient: AppTheme.darkBackgroundGradient,
        borderRadius: BorderRadius.only(
          topLeft: Radius.circular(24),
          topRight: Radius.circular(24),
        ),
      ),
      child: Column(
        children: [
          // Header
          _buildHeader(),

          // Tab Bar
          _buildTabBar(),

          // Tab Content
          Expanded(
            child: TabBarView(
              controller: _tabController,
              children: [
                _buildBasicFilters(),
                _buildPriceAreaFilters(),
                _buildSpecificationFilters(),
                _buildLocationFilters(),
              ],
            ),
          ),

          // Action Buttons
          _buildActionButtons(),
        ],
      ),
    );
  }

  /// Build header with title and close button
  Widget _buildHeader() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          Text(
            'Filter Properties',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (_currentFilter.hasActiveFilters)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                '${_currentFilter.activeFilterCount} active',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          const SizedBox(width: 12),
          IconButton(
            onPressed: widget.onClose,
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: AppTheme.darkCard,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Icon(
                Icons.close,
                color: AppTheme.darkPrimaryText,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 20),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.darkSecondaryText,
        labelStyle: const TextStyle(fontSize: 12, fontWeight: FontWeight.w600),
        tabs: const [
          Tab(text: 'Basic'),
          Tab(text: 'Price & Area'),
          Tab(text: 'Specs'),
          Tab(text: 'Location'),
        ],
      ),
    );
  }

  /// Build basic filters tab
  Widget _buildBasicFilters() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Search
          _buildSectionTitle('Search'),
          _buildSearchField(),

          const SizedBox(height: 24),

          // Property Types
          _buildSectionTitle('Property Type'),
          _buildPropertyTypeChips(),

          const SizedBox(height: 24),

          // Property Status
          _buildSectionTitle('Status'),
          _buildPropertyStatusChips(),

          const SizedBox(height: 24),

          // Sort Options
          _buildSectionTitle('Sort By'),
          _buildSortOptions(),
        ],
      ),
    );
  }

  /// Build price and area filters tab
  Widget _buildPriceAreaFilters() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Price Range
          _buildSectionTitle('Price Range'),
          _buildPriceRangeChips(),

          const SizedBox(height: 16),

          // Custom Price Range
          _buildCustomPriceRange(),

          const SizedBox(height: 24),

          // Area Range
          _buildSectionTitle('Area (sq ft)'),
          _buildAreaRange(),
        ],
      ),
    );
  }

  /// Build specification filters tab
  Widget _buildSpecificationFilters() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Bedrooms
          _buildSectionTitle('Bedrooms'),
          _buildBedroomSelector(),

          const SizedBox(height: 24),

          // Bathrooms
          _buildSectionTitle('Bathrooms'),
          _buildBathroomSelector(),

          const SizedBox(height: 24),

          // Featured Properties
          _buildSectionTitle('Special Properties'),
          _buildSpecialPropertyToggles(),
        ],
      ),
    );
  }

  /// Build location filters tab
  Widget _buildLocationFilters() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(20),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          _buildSectionTitle('Location Filters'),
          const SizedBox(height: 16),
          Text(
            'Location filtering will be available in the next update.',
            style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// Build section title
  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Text(
        title,
        style: TextStyle(
          color: AppTheme.darkPrimaryText,
          fontSize: 16,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  /// Build search field
  Widget _buildSearchField() {
    return Container(
      decoration: AppTheme.createCardDecoration(),
      child: TextField(
        controller: _searchController,
        style: TextStyle(color: AppTheme.darkPrimaryText),
        decoration: InputDecoration(
          hintText: 'Search properties...',
          hintStyle: TextStyle(color: AppTheme.darkHintText),
          prefixIcon: Icon(Icons.search, color: AppTheme.primaryColor),
          border: InputBorder.none,
          contentPadding: const EdgeInsets.all(16),
        ),
        onChanged: (value) {
          setState(() {
            _currentFilter = _currentFilter.copyWith(searchQuery: value);
          });
        },
      ),
    );
  }

  /// Build property type chips
  Widget _buildPropertyTypeChips() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: PropertyType.values.map((type) {
        final isSelected = _currentFilter.selectedTypes.contains(type);
        return GestureDetector(
          onTap: () {
            setState(() {
              final types = List<PropertyType>.from(
                _currentFilter.selectedTypes,
              );
              if (isSelected) {
                types.remove(type);
              } else {
                types.add(type);
              }
              _currentFilter = _currentFilter.copyWith(selectedTypes: types);
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              gradient: isSelected ? AppTheme.primaryGradient : null,
              color: isSelected ? null : AppTheme.darkCard,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : AppTheme.darkBorder,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  type.icon,
                  color: isSelected ? Colors.white : type.color,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  type.displayName,
                  style: TextStyle(
                    color: isSelected ? Colors.white : AppTheme.darkPrimaryText,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Build property status chips
  Widget _buildPropertyStatusChips() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: PropertyStatus.values.where((s) => s.isAvailable).map((status) {
        final isSelected = _currentFilter.selectedStatuses.contains(status);
        return GestureDetector(
          onTap: () {
            setState(() {
              final statuses = List<PropertyStatus>.from(
                _currentFilter.selectedStatuses,
              );
              if (isSelected) {
                statuses.remove(status);
              } else {
                statuses.add(status);
              }
              _currentFilter = _currentFilter.copyWith(
                selectedStatuses: statuses,
              );
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              gradient: isSelected ? AppTheme.primaryGradient : null,
              color: isSelected ? null : AppTheme.darkCard,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : AppTheme.darkBorder,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  status.icon,
                  color: isSelected ? Colors.white : status.color,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  status.displayName,
                  style: TextStyle(
                    color: isSelected ? Colors.white : AppTheme.darkPrimaryText,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Build sort options
  Widget _buildSortOptions() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: PropertySortOption.values.map((option) {
        final isSelected = _currentFilter.sortOption == option;
        return GestureDetector(
          onTap: () {
            setState(() {
              _currentFilter = _currentFilter.copyWith(sortOption: option);
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              gradient: isSelected ? AppTheme.secondaryGradient : null,
              color: isSelected ? null : AppTheme.darkCard,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected
                    ? AppTheme.secondaryColor
                    : AppTheme.darkBorder,
              ),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Icon(
                  option.icon,
                  color: isSelected ? Colors.white : AppTheme.darkSecondaryText,
                  size: 16,
                ),
                const SizedBox(width: 6),
                Text(
                  option.displayName,
                  style: TextStyle(
                    color: isSelected ? Colors.white : AppTheme.darkPrimaryText,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Build price range chips
  Widget _buildPriceRangeChips() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: PriceRange.values.where((r) => r != PriceRange.custom).map((
        range,
      ) {
        final isSelected = _currentFilter.priceRange == range;
        return GestureDetector(
          onTap: () {
            setState(() {
              _currentFilter = _currentFilter.copyWith(
                priceRange: isSelected ? null : range,
                customMinPrice: null,
                customMaxPrice: null,
              );
              _minPriceController.clear();
              _maxPriceController.clear();
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
            decoration: BoxDecoration(
              gradient: isSelected ? AppTheme.primaryGradient : null,
              color: isSelected ? null : AppTheme.darkCard,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : AppTheme.darkBorder,
              ),
            ),
            child: Text(
              range.displayName,
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.darkPrimaryText,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }).toList(),
    );
  }

  /// Build custom price range
  Widget _buildCustomPriceRange() {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: AppTheme.createCardDecoration(),
            child: TextField(
              controller: _minPriceController,
              style: TextStyle(color: AppTheme.darkPrimaryText),
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'Min Price',
                hintStyle: TextStyle(color: AppTheme.darkHintText),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
              onChanged: (value) {
                setState(() {
                  _currentFilter = _currentFilter.copyWith(
                    customMinPrice: double.tryParse(value),
                    priceRange: null,
                  );
                });
              },
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Container(
            decoration: AppTheme.createCardDecoration(),
            child: TextField(
              controller: _maxPriceController,
              style: TextStyle(color: AppTheme.darkPrimaryText),
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'Max Price',
                hintStyle: TextStyle(color: AppTheme.darkHintText),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
              onChanged: (value) {
                setState(() {
                  _currentFilter = _currentFilter.copyWith(
                    customMaxPrice: double.tryParse(value),
                    priceRange: null,
                  );
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build area range
  Widget _buildAreaRange() {
    return Row(
      children: [
        Expanded(
          child: Container(
            decoration: AppTheme.createCardDecoration(),
            child: TextField(
              controller: _minAreaController,
              style: TextStyle(color: AppTheme.darkPrimaryText),
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'Min Area',
                hintStyle: TextStyle(color: AppTheme.darkHintText),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
              onChanged: (value) {
                setState(() {
                  _currentFilter = _currentFilter.copyWith(
                    minArea: double.tryParse(value),
                  );
                });
              },
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: Container(
            decoration: AppTheme.createCardDecoration(),
            child: TextField(
              controller: _maxAreaController,
              style: TextStyle(color: AppTheme.darkPrimaryText),
              keyboardType: TextInputType.number,
              decoration: InputDecoration(
                hintText: 'Max Area',
                hintStyle: TextStyle(color: AppTheme.darkHintText),
                border: InputBorder.none,
                contentPadding: const EdgeInsets.all(16),
              ),
              onChanged: (value) {
                setState(() {
                  _currentFilter = _currentFilter.copyWith(
                    maxArea: double.tryParse(value),
                  );
                });
              },
            ),
          ),
        ),
      ],
    );
  }

  /// Build bedroom selector
  Widget _buildBedroomSelector() {
    return Wrap(
      spacing: 8,
      children: List.generate(6, (index) {
        final bedrooms = index == 0 ? null : index;
        final isSelected = _currentFilter.minBedrooms == bedrooms;
        return GestureDetector(
          onTap: () {
            setState(() {
              _currentFilter = _currentFilter.copyWith(
                minBedrooms: isSelected ? null : bedrooms,
              );
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: isSelected ? AppTheme.primaryGradient : null,
              color: isSelected ? null : AppTheme.darkCard,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : AppTheme.darkBorder,
              ),
            ),
            child: Text(
              index == 0 ? 'Any' : '${index}+ BHK',
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.darkPrimaryText,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }),
    );
  }

  /// Build bathroom selector
  Widget _buildBathroomSelector() {
    return Wrap(
      spacing: 8,
      children: List.generate(5, (index) {
        final bathrooms = index == 0 ? null : index;
        final isSelected = _currentFilter.minBathrooms == bathrooms;
        return GestureDetector(
          onTap: () {
            setState(() {
              _currentFilter = _currentFilter.copyWith(
                minBathrooms: isSelected ? null : bathrooms,
              );
            });
          },
          child: Container(
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: BoxDecoration(
              gradient: isSelected ? AppTheme.primaryGradient : null,
              color: isSelected ? null : AppTheme.darkCard,
              borderRadius: BorderRadius.circular(20),
              border: Border.all(
                color: isSelected ? AppTheme.primaryColor : AppTheme.darkBorder,
              ),
            ),
            child: Text(
              index == 0 ? 'Any' : '${index}+ Bath',
              style: TextStyle(
                color: isSelected ? Colors.white : AppTheme.darkPrimaryText,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        );
      }),
    );
  }

  /// Build special property toggles
  Widget _buildSpecialPropertyToggles() {
    return Column(
      children: [
        _buildToggleOption(
          'Featured Properties Only',
          _currentFilter.isFeatured ?? false,
          (value) {
            setState(() {
              _currentFilter = _currentFilter.copyWith(
                isFeatured: value ? true : null,
              );
            });
          },
        ),
      ],
    );
  }

  /// Build toggle option
  Widget _buildToggleOption(
    String title,
    bool value,
    Function(bool) onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: AppTheme.createCardDecoration(),
      child: Row(
        children: [
          Expanded(
            child: Text(
              title,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 14,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return Container(
      padding: const EdgeInsets.all(20),
      child: Row(
        children: [
          // Clear All Button
          Expanded(
            child: OutlinedButton(
              onPressed: () {
                setState(() {
                  _currentFilter = const PropertyFilterModel();
                  _searchController.clear();
                  _minPriceController.clear();
                  _maxPriceController.clear();
                  _minAreaController.clear();
                  _maxAreaController.clear();
                });
              },
              style: OutlinedButton.styleFrom(
                side: BorderSide(color: AppTheme.darkBorder),
                padding: const EdgeInsets.symmetric(vertical: 16),
                shape: RoundedRectangleBorder(
                  borderRadius: BorderRadius.circular(12),
                ),
              ),
              child: Text(
                'Clear All',
                style: TextStyle(
                  color: AppTheme.darkSecondaryText,
                  fontSize: 16,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ),

          const SizedBox(width: 16),

          // Apply Filters Button
          Expanded(
            flex: 2,
            child: GradientWidgets.gradientButton(
              text: 'Apply Filters',
              onPressed: () {
                widget.onFilterChanged(_currentFilter);
                widget.onClose?.call();
              },
              icon: Icons.check,
            ),
          ),
        ],
      ),
    );
  }
}
