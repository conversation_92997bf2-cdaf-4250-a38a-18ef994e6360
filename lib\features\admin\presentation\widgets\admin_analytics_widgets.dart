import 'package:flutter/material.dart';
import '../../../../core/models/admin_analytics_model.dart';

/// System overview widget showing key metrics
class SystemOverviewWidget extends StatelessWidget {
  final SystemOverview overview;

  const SystemOverviewWidget({
    super.key,
    required this.overview,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'System Overview',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        GridView.count(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          crossAxisCount: 2,
          crossAxisSpacing: 16,
          mainAxisSpacing: 16,
          childAspectRatio: 1.5,
          children: [
            _buildOverviewCard(
              context,
              'Total Users',
              '${overview.totalUsers}',
              '${overview.activeUsers} active',
              Icons.people,
              Colors.blue,
              overview.userGrowthPercentage,
            ),
            _buildOverviewCard(
              context,
              'Properties',
              '${overview.totalProperties}',
              '${overview.approvedProperties} approved',
              Icons.home_work,
              Colors.green,
              0.0, // Placeholder
            ),
            _buildOverviewCard(
              context,
              'Total Revenue',
              _formatAmount(overview.systemRevenue),
              'System revenue',
              Icons.currency_rupee,
              Colors.purple,
              overview.revenueGrowthPercentage,
            ),
            _buildOverviewCard(
              context,
              'Commissions',
              _formatAmount(overview.totalCommissionsPaid),
              '${overview.totalTransactions} transactions',
              Icons.payment,
              Colors.orange,
              0.0, // Placeholder
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildOverviewCard(
    BuildContext context,
    String title,
    String value,
    String subtitle,
    IconData icon,
    Color color,
    double growthPercentage,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: color.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Icon(icon, color: color, size: 20),
                ),
                const Spacer(),
                if (growthPercentage != 0.0)
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                    decoration: BoxDecoration(
                      color: growthPercentage > 0 
                          ? Colors.green.withValues(alpha: 0.1)
                          : Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Row(
                      mainAxisSize: MainAxisSize.min,
                      children: [
                        Icon(
                          growthPercentage > 0 ? Icons.trending_up : Icons.trending_down,
                          size: 12,
                          color: growthPercentage > 0 ? Colors.green : Colors.red,
                        ),
                        const SizedBox(width: 2),
                        Text(
                          '${growthPercentage.abs().toStringAsFixed(1)}%',
                          style: TextStyle(
                            fontSize: 10,
                            color: growthPercentage > 0 ? Colors.green : Colors.red,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),
              ],
            ),
            
            const SizedBox(height: 12),
            
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
            
            const SizedBox(height: 4),
            
            Text(
              value,
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 4),
            
            Text(
              subtitle,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      ),
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(1)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(1)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }
}

/// Quick stats grid widget
class QuickStatsGrid extends StatelessWidget {
  final AdminAnalyticsModel analytics;

  const QuickStatsGrid({
    super.key,
    required this.analytics,
  });

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Quick Stats',
          style: Theme.of(context).textTheme.titleLarge?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
        
        const SizedBox(height: 16),
        
        Row(
          children: [
            Expanded(
              child: _buildQuickStatCard(
                context,
                'Stars Awarded',
                '${analytics.systemOverview.totalStarsAwarded}',
                Icons.star,
                Colors.amber,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildQuickStatCard(
                context,
                'Avg Stars/User',
                analytics.userAnalytics.averageStarsPerUser.toStringAsFixed(1),
                Icons.person,
                Colors.blue,
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: _buildQuickStatCard(
                context,
                'Conversion Rate',
                '${analytics.performanceMetrics.conversionRate.toStringAsFixed(1)}%',
                Icons.trending_up,
                Colors.green,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildQuickStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 8),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            const SizedBox(height: 4),
            Text(
              title,
              style: Theme.of(context).textTheme.bodySmall?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
          ],
        ),
      ),
    );
  }
}

/// Recent activities widget
class RecentActivitiesWidget extends StatelessWidget {
  final AdminAnalyticsModel analytics;

  const RecentActivitiesWidget({
    super.key,
    required this.analytics,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Recent Activities',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Placeholder activities
            _buildActivityItem(
              context,
              'New user registration',
              '2 minutes ago',
              Icons.person_add,
              Colors.green,
            ),
            _buildActivityItem(
              context,
              'Property approved',
              '15 minutes ago',
              Icons.check_circle,
              Colors.blue,
            ),
            _buildActivityItem(
              context,
              'Commission distributed',
              '1 hour ago',
              Icons.payment,
              Colors.orange,
            ),
            _buildActivityItem(
              context,
              'Star bonus awarded',
              '2 hours ago',
              Icons.star,
              Colors.amber,
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActivityItem(
    BuildContext context,
    String title,
    String time,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  time,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// System alerts widget
class SystemAlertsWidget extends StatelessWidget {
  final AdminAnalyticsModel analytics;

  const SystemAlertsWidget({
    super.key,
    required this.analytics,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Alerts',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            
            const SizedBox(height: 16),
            
            // Generate alerts based on analytics
            if (analytics.propertyAnalytics.pendingApprovals > 0)
              _buildAlertItem(
                context,
                'Pending Approvals',
                '${analytics.propertyAnalytics.pendingApprovals} properties need approval',
                Icons.pending_actions,
                Colors.orange,
              ),
            
            if (analytics.starAnalytics.usersNearingBonus > 0)
              _buildAlertItem(
                context,
                'Bonus Eligible',
                '${analytics.starAnalytics.usersNearingBonus} users nearing 12-star bonus',
                Icons.star_border,
                Colors.amber,
              ),
            
            if (analytics.performanceMetrics.activeLeads > 50)
              _buildAlertItem(
                context,
                'High Lead Volume',
                '${analytics.performanceMetrics.activeLeads} active leads',
                Icons.trending_up,
                Colors.blue,
              ),
            
            // Default message if no alerts
            if (analytics.propertyAnalytics.pendingApprovals == 0 &&
                analytics.starAnalytics.usersNearingBonus == 0 &&
                analytics.performanceMetrics.activeLeads <= 50)
              _buildAlertItem(
                context,
                'All Good',
                'No critical alerts at this time',
                Icons.check_circle,
                Colors.green,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildAlertItem(
    BuildContext context,
    String title,
    String description,
    IconData icon,
    Color color,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: color.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(icon, color: color, size: 16),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  description,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}

/// User analytics widget
class UserAnalyticsWidget extends StatelessWidget {
  final UserAnalytics userAnalytics;

  const UserAnalyticsWidget({
    super.key,
    required this.userAnalytics,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'User Analytics',
              style: Theme.of(context).textTheme.titleLarge?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            // User distribution by level
            Text(
              'MLM Level Distribution',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 8),

            ...userAnalytics.usersByLevel.entries.map((entry) =>
              _buildLevelDistributionItem(context, entry.key, entry.value),
            ),

            const SizedBox(height: 16),

            // Top performers
            Text(
              'Top Performers',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.w600,
              ),
            ),

            const SizedBox(height: 8),

            ...userAnalytics.topPerformers.take(5).map((performer) =>
              _buildTopPerformerItem(context, performer),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildLevelDistributionItem(BuildContext context, int level, int count) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Container(
            width: 24,
            height: 24,
            decoration: BoxDecoration(
              color: _getLevelColor(level).withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(4),
            ),
            child: Center(
              child: Text(
                'L$level',
                style: TextStyle(
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                  color: _getLevelColor(level),
                ),
              ),
            ),
          ),

          const SizedBox(width: 12),

          Expanded(
            child: Text('Level $level'),
          ),

          Text(
            '$count users',
            style: Theme.of(context).textTheme.bodySmall?.copyWith(
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTopPerformerItem(BuildContext context, TopPerformer performer) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          CircleAvatar(
            radius: 16,
            backgroundColor: Colors.blue.withValues(alpha: 0.1),
            child: Text(
              performer.name.isNotEmpty ? performer.name[0].toUpperCase() : 'U',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.blue,
              ),
            ),
          ),

          const SizedBox(width: 12),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  performer.name,
                  style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  '${performer.totalStars} stars • ${performer.formattedCommissions}',
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Text(
              '${performer.performanceScore.toStringAsFixed(0)}%',
              style: const TextStyle(
                fontSize: 12,
                fontWeight: FontWeight.bold,
                color: Colors.green,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Color _getLevelColor(int level) {
    switch (level) {
      case 0: return Colors.purple;
      case 1: return Colors.blue;
      case 2: return Colors.green;
      case 3: return Colors.orange;
      case 4: return Colors.red;
      default: return Colors.grey;
    }
  }
}
