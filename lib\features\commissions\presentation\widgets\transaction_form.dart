import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import '../../../../core/models/property_model.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/services/commission_service.dart';
import '../../../../core/services/property_service.dart';
import '../providers/commission_providers.dart';

/// Transaction form widget for admin to manually enter commission
class TransactionForm extends ConsumerStatefulWidget {
  final Function(String)? onTransactionCreated;

  const TransactionForm({
    super.key,
    this.onTransactionCreated,
  });

  @override
  ConsumerState<TransactionForm> createState() => _TransactionFormState();
}

class _TransactionFormState extends ConsumerState<TransactionForm> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  // Text controllers
  late TextEditingController _propertyAmountController;
  late TextEditingController _commissionAmountController;
  late TextEditingController _buyerNameController;
  late TextEditingController _buyerContactController;
  late TextEditingController _notesController;

  // Selected values
  PropertyModel? _selectedProperty;
  UserModel? _selectedAgent;
  String _selectedType = 'sale';

  // Data lists
  List<PropertyModel> _properties = [];
  List<UserModel> _agents = [];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadData();
  }

  void _initializeControllers() {
    _propertyAmountController = TextEditingController();
    _commissionAmountController = TextEditingController();
    _buyerNameController = TextEditingController();
    _buyerContactController = TextEditingController();
    _notesController = TextEditingController();

    // Add listener to calculate commission rate
    _propertyAmountController.addListener(_updateCommissionRate);
    _commissionAmountController.addListener(_updateCommissionRate);
  }

  void _updateCommissionRate() {
    final propertyAmount = double.tryParse(_propertyAmountController.text) ?? 0;
    final commissionAmount = double.tryParse(_commissionAmountController.text) ?? 0;
    
    if (propertyAmount > 0 && commissionAmount > 0) {
      final rate = (commissionAmount / propertyAmount) * 100;
      // Update form state for display
      ref.read(transactionFormProvider.notifier).updatePropertyAmount(propertyAmount);
      ref.read(transactionFormProvider.notifier).updateCommissionAmount(commissionAmount);
    }
  }

  Future<void> _loadData() async {
    try {
      // Load available properties (sold/rented properties for commission entry)
      final properties = await PropertyService.getProperties(isApproved: true);
      
      // Load all agents from Firestore
      final usersQuery = await FirebaseFirestore.instance
          .collection('users')
          .where('role', isEqualTo: 'agent')
          .get();
      final agents = usersQuery.docs.map((doc) => UserModel.fromFirestore(doc)).toList();
      
      setState(() {
        _properties = properties;
        _agents = agents.where((user) => !user.isAdmin).toList();
      });
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text('Error loading data: $e')),
        );
      }
    }
  }

  @override
  void dispose() {
    _propertyAmountController.dispose();
    _commissionAmountController.dispose();
    _buyerNameController.dispose();
    _buyerContactController.dispose();
    _notesController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formState = ref.watch(transactionFormProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Create Transaction'),
        actions: [
          TextButton(
            onPressed: formState.isLoading ? null : _createTransaction,
            child: formState.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Create'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Scrollbar(
          controller: _scrollController,
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Error message
                if (formState.error != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      formState.error!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),

                // Property Selection
                _buildSectionTitle('Property Information'),
                _buildPropertyDropdown(),
                
                const SizedBox(height: 16),

                // Agent Selection
                _buildSectionTitle('Agent Information'),
                _buildAgentDropdown(),
                
                const SizedBox(height: 24),

                // Transaction Details
                _buildSectionTitle('Transaction Details'),
                _buildTransactionTypeDropdown(),
                
                const SizedBox(height: 16),

                // Property Amount (Final agreed amount)
                _buildTextField(
                  controller: _propertyAmountController,
                  label: 'Final Property Amount (₹)',
                  hint: 'Enter the final agreed sale/rental amount',
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]'))],
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Property amount is required';
                    final amount = double.tryParse(value!);
                    if (amount == null || amount <= 0) return 'Enter valid amount';
                    return null;
                  },
                  suffixText: 'INR',
                ),

                const SizedBox(height: 16),

                // Commission Amount (Manually entered by admin)
                _buildTextField(
                  controller: _commissionAmountController,
                  label: 'Total Commission Amount (₹)',
                  hint: 'Enter the total commission to be distributed',
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]'))],
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Commission amount is required';
                    final amount = double.tryParse(value!);
                    if (amount == null || amount <= 0) return 'Enter valid commission amount';
                    
                    final propertyAmount = double.tryParse(_propertyAmountController.text) ?? 0;
                    if (amount > propertyAmount) return 'Commission cannot exceed property amount';
                    
                    return null;
                  },
                  suffixText: 'INR',
                ),

                const SizedBox(height: 8),

                // Commission Rate Display
                if (formState.commissionRate > 0)
                  Container(
                    padding: const EdgeInsets.all(12),
                    decoration: BoxDecoration(
                      color: Colors.blue.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
                    ),
                    child: Row(
                      children: [
                        const Icon(Icons.info, color: Colors.blue, size: 20),
                        const SizedBox(width: 8),
                        Text(
                          'Commission Rate: ${formState.commissionRate.toStringAsFixed(2)}%',
                          style: const TextStyle(
                            color: Colors.blue,
                            fontWeight: FontWeight.w500,
                          ),
                        ),
                      ],
                    ),
                  ),

                const SizedBox(height: 24),

                // MLM Distribution Preview
                _buildMLMDistributionPreview(),

                const SizedBox(height: 24),

                // Buyer Information
                _buildSectionTitle('Buyer Information (Optional)'),
                _buildTextField(
                  controller: _buyerNameController,
                  label: 'Buyer Name',
                  hint: 'Enter buyer\'s name',
                ),

                const SizedBox(height: 16),

                _buildTextField(
                  controller: _buyerContactController,
                  label: 'Buyer Contact',
                  hint: 'Enter buyer\'s phone or email',
                ),

                const SizedBox(height: 24),

                // Notes
                _buildSectionTitle('Additional Notes'),
                _buildTextField(
                  controller: _notesController,
                  label: 'Notes',
                  hint: 'Any additional information about the transaction',
                  maxLines: 3,
                ),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildPropertyDropdown() {
    return DropdownButtonFormField<PropertyModel>(
      value: _selectedProperty,
      decoration: const InputDecoration(
        labelText: 'Select Property',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.home_work),
      ),
      items: _properties.map((property) => DropdownMenuItem(
        value: property,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              property.title,
              style: const TextStyle(fontWeight: FontWeight.w500),
              overflow: TextOverflow.ellipsis,
            ),
            Text(
              '${property.city} • ${property.formattedPrice}',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      )).toList(),
      onChanged: (property) {
        setState(() {
          _selectedProperty = property;
        });
        if (property != null) {
          ref.read(transactionFormProvider.notifier).updateProperty(property.id, property.title);
          // Pre-fill property amount if available
          _propertyAmountController.text = property.price.toString();
        }
      },
      validator: (value) => value == null ? 'Please select a property' : null,
    );
  }

  Widget _buildAgentDropdown() {
    return DropdownButtonFormField<UserModel>(
      value: _selectedAgent,
      decoration: const InputDecoration(
        labelText: 'Select Selling Agent',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.person),
      ),
      items: _agents.map((agent) => DropdownMenuItem(
        value: agent,
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              agent.name,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            Text(
              'Level ${agent.level} • ${agent.totalStars} stars',
              style: TextStyle(
                fontSize: 12,
                color: Colors.grey[600],
              ),
            ),
          ],
        ),
      )).toList(),
      onChanged: (agent) {
        setState(() {
          _selectedAgent = agent;
        });
        if (agent != null) {
          ref.read(transactionFormProvider.notifier).updateAgent(agent.id, agent.name);
        }
      },
      validator: (value) => value == null ? 'Please select an agent' : null,
    );
  }

  Widget _buildTransactionTypeDropdown() {
    return DropdownButtonFormField<String>(
      value: _selectedType,
      decoration: const InputDecoration(
        labelText: 'Transaction Type',
        border: OutlineInputBorder(),
        prefixIcon: Icon(Icons.category),
      ),
      items: const [
        DropdownMenuItem(value: 'sale', child: Text('Sale')),
        DropdownMenuItem(value: 'rental', child: Text('Rental')),
      ],
      onChanged: (type) {
        setState(() {
          _selectedType = type!;
        });
        ref.read(transactionFormProvider.notifier).updateType(type!);
      },
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    String? suffixText,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        suffixText: suffixText,
        border: const OutlineInputBorder(),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      validator: validator,
    );
  }

  Widget _buildMLMDistributionPreview() {
    final formState = ref.watch(transactionFormProvider);
    
    if (formState.commissionAmount <= 0 || _selectedAgent == null) {
      return const SizedBox.shrink();
    }

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'MLM Commission Distribution Preview',
              style: Theme.of(context).textTheme.titleSmall?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            Text(
              'Total Commission: ${_formatAmount(formState.commissionAmount)}',
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
            const SizedBox(height: 8),
            const Text(
              'Distribution (based on default rates):',
              style: TextStyle(fontSize: 12, color: Colors.grey),
            ),
            const SizedBox(height: 8),
            ...List.generate(5, (index) {
              final rate = [0.05, 0.02, 0.01, 0.005, 0.002][index];
              final amount = formState.commissionAmount * rate;
              final levelName = index == 0 ? 'Direct Sale' : 'Level $index Upline';
              
              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 2),
                child: Row(
                  children: [
                    SizedBox(
                      width: 100,
                      child: Text(
                        levelName,
                        style: const TextStyle(fontSize: 12),
                      ),
                    ),
                    Text(
                      '${(rate * 100).toStringAsFixed(1)}%',
                      style: const TextStyle(fontSize: 12, color: Colors.grey),
                    ),
                    const Spacer(),
                    Text(
                      _formatAmount(amount),
                      style: const TextStyle(
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(2)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(2)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(2)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }

  Future<void> _createTransaction() async {
    if (!_formKey.currentState!.validate()) return;

    final formNotifier = ref.read(transactionFormProvider.notifier);
    formNotifier.setLoading(true);

    try {
      final result = await CommissionService.createTransactionWithCommission(
        propertyId: _selectedProperty!.id,
        agentId: _selectedAgent!.id,
        propertyAmount: double.parse(_propertyAmountController.text),
        commissionAmount: double.parse(_commissionAmountController.text),
        type: _selectedType,
        buyerName: _buyerNameController.text.isNotEmpty ? _buyerNameController.text : null,
        buyerContact: _buyerContactController.text.isNotEmpty ? _buyerContactController.text : null,
        notes: _notesController.text.isNotEmpty ? _notesController.text : null,
      );

      if (result.isSuccess) {
        formNotifier.setLoading(false);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Transaction created and commissions distributed successfully!'),
              backgroundColor: Colors.green,
            ),
          );
          
          widget.onTransactionCreated?.call(result.transaction!.id);
          Navigator.of(context).pop(result.transaction);
        }
      } else {
        formNotifier.setError(result.message);
      }
    } catch (e) {
      formNotifier.setError('Failed to create transaction: $e');
    }
  }
}
