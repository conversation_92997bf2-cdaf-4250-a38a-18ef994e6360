import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../models/dashboard_models.dart';
import '../../services/dashboard_service.dart';

/// Goal management widget for creating and tracking goals
class GoalManagementWidget extends ConsumerStatefulWidget {
  final String agentId;
  final List<DashboardGoal> goals;
  final Function(DashboardGoal)? onGoalUpdated;

  const GoalManagementWidget({
    super.key,
    required this.agentId,
    required this.goals,
    this.onGoalUpdated,
  });

  @override
  ConsumerState<GoalManagementWidget> createState() => _GoalManagementWidgetState();
}

class _GoalManagementWidgetState extends ConsumerState<GoalManagementWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header with Add Goal Button
        _buildHeader(),
        
        const SizedBox(height: 16),
        
        // Tab Bar
        _buildTabBar(),
        
        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildActiveGoalsTab(),
              _buildCompletedGoalsTab(),
              _buildGoalInsightsTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// Build header with add goal button
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Text(
            'Goal Management',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          GradientWidgets.gradientButton(
            text: 'Add Goal',
            onPressed: _showAddGoalDialog,
            icon: Icons.add,
            padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
          ),
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.darkSecondaryText,
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'Active'),
          Tab(text: 'Completed'),
          Tab(text: 'Insights'),
        ],
      ),
    );
  }

  /// Build active goals tab
  Widget _buildActiveGoalsTab() {
    final activeGoals = widget.goals.where((g) => g.status == GoalStatus.active).toList();
    
    if (activeGoals.isEmpty) {
      return _buildEmptyState('No active goals', 'Create your first goal to start tracking progress');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: activeGoals.length,
      itemBuilder: (context, index) {
        final goal = activeGoals[index];
        return _buildGoalCard(goal);
      },
    );
  }

  /// Build completed goals tab
  Widget _buildCompletedGoalsTab() {
    final completedGoals = widget.goals.where((g) => g.status == GoalStatus.completed).toList();
    
    if (completedGoals.isEmpty) {
      return _buildEmptyState('No completed goals', 'Complete your first goal to see it here');
    }

    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: completedGoals.length,
      itemBuilder: (context, index) {
        final goal = completedGoals[index];
        return _buildGoalCard(goal, isCompleted: true);
      },
    );
  }

  /// Build goal insights tab
  Widget _buildGoalInsightsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Goal Statistics
          _buildGoalStatistics(),
          
          const SizedBox(height: 24),
          
          // Goal Performance Chart
          _buildGoalPerformanceChart(),
          
          const SizedBox(height: 24),
          
          // Goal Recommendations
          _buildGoalRecommendations(),
        ],
      ),
    );
  }

  /// Build goal card
  Widget _buildGoalCard(DashboardGoal goal, {bool isCompleted = false}) {
    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: AppTheme.createCardDecoration(
        addGlow: !isCompleted && goal.progressPercentage >= 80,
      ),
      child: Column(
        children: [
          // Goal Header
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: goal.progressColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                goal.type.icon,
                color: goal.progressColor,
                size: 20,
              ),
            ),
            title: Text(
              goal.title,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontWeight: FontWeight.bold,
                decoration: isCompleted ? TextDecoration.lineThrough : null,
              ),
            ),
            subtitle: Text(
              goal.description,
              style: TextStyle(
                color: AppTheme.darkSecondaryText,
                fontSize: 12,
              ),
            ),
            trailing: isCompleted
                ? Icon(Icons.check_circle, color: AppTheme.successColor)
                : _buildGoalMenu(goal),
          ),
          
          // Progress Section
          if (!isCompleted) ...[
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16),
              child: Column(
                children: [
                  // Progress Bar
                  Row(
                    children: [
                      Expanded(
                        child: LinearProgressIndicator(
                          value: goal.progressPercentage / 100,
                          backgroundColor: AppTheme.darkBorder,
                          valueColor: AlwaysStoppedAnimation<Color>(goal.progressColor),
                          minHeight: 6,
                        ),
                      ),
                      const SizedBox(width: 12),
                      Text(
                        '${goal.progressPercentage.toStringAsFixed(0)}%',
                        style: TextStyle(
                          color: goal.progressColor,
                          fontSize: 14,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                  
                  const SizedBox(height: 8),
                  
                  // Progress Details
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceBetween,
                    children: [
                      Text(
                        '${goal.currentValue.toStringAsFixed(0)} / ${goal.targetValue.toStringAsFixed(0)}',
                        style: TextStyle(
                          color: AppTheme.darkSecondaryText,
                          fontSize: 12,
                        ),
                      ),
                      Row(
                        children: [
                          Icon(
                            goal.isOverdue ? Icons.warning : Icons.schedule,
                            color: goal.isOverdue ? AppTheme.errorColor : AppTheme.darkSecondaryText,
                            size: 12,
                          ),
                          const SizedBox(width: 4),
                          Text(
                            goal.isOverdue 
                                ? 'Overdue'
                                : '${goal.daysRemaining} days left',
                            style: TextStyle(
                              color: goal.isOverdue ? AppTheme.errorColor : AppTheme.darkSecondaryText,
                              fontSize: 12,
                            ),
                          ),
                        ],
                      ),
                    ],
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
          ],
          
          // Reward Section
          if (goal.reward != null) ...[
            Container(
              width: double.infinity,
              padding: const EdgeInsets.all(12),
              margin: const EdgeInsets.symmetric(horizontal: 16),
              decoration: BoxDecoration(
                color: AppTheme.warningColor.withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: AppTheme.warningColor.withValues(alpha: 0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    Icons.emoji_events,
                    color: AppTheme.warningColor,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Reward: ${goal.reward}',
                    style: TextStyle(
                      color: AppTheme.warningColor,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            ),
            
            const SizedBox(height: 16),
          ],
        ],
      ),
    );
  }

  /// Build goal menu
  Widget _buildGoalMenu(DashboardGoal goal) {
    return PopupMenuButton<String>(
      icon: Icon(Icons.more_vert, color: AppTheme.darkSecondaryText),
      onSelected: (value) => _handleGoalAction(goal, value),
      itemBuilder: (context) => [
        const PopupMenuItem(
          value: 'edit',
          child: Row(
            children: [
              Icon(Icons.edit, size: 16),
              SizedBox(width: 8),
              Text('Edit Goal'),
            ],
          ),
        ),
        const PopupMenuItem(
          value: 'update_progress',
          child: Row(
            children: [
              Icon(Icons.update, size: 16),
              SizedBox(width: 8),
              Text('Update Progress'),
            ],
          ),
        ),
        if (goal.status == GoalStatus.active)
          const PopupMenuItem(
            value: 'pause',
            child: Row(
              children: [
                Icon(Icons.pause, size: 16),
                SizedBox(width: 8),
                Text('Pause Goal'),
              ],
            ),
          ),
        const PopupMenuItem(
          value: 'delete',
          child: Row(
            children: [
              Icon(Icons.delete, size: 16, color: Colors.red),
              SizedBox(width: 8),
              Text('Delete Goal', style: TextStyle(color: Colors.red)),
            ],
          ),
        ),
      ],
    );
  }

  /// Build empty state
  Widget _buildEmptyState(String title, String subtitle) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.flag_outlined,
            size: 64,
            color: AppTheme.darkSecondaryText,
          ),
          const SizedBox(height: 16),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            subtitle,
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          GradientWidgets.gradientButton(
            text: 'Create Goal',
            onPressed: _showAddGoalDialog,
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  /// Build goal statistics
  Widget _buildGoalStatistics() {
    final totalGoals = widget.goals.length;
    final activeGoals = widget.goals.where((g) => g.status == GoalStatus.active).length;
    final completedGoals = widget.goals.where((g) => g.status == GoalStatus.completed).length;
    final completionRate = totalGoals > 0 ? (completedGoals / totalGoals * 100) : 0.0;

    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Goal Statistics',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildStatItem('Total', '$totalGoals', AppTheme.primaryColor),
              ),
              Expanded(
                child: _buildStatItem('Active', '$activeGoals', AppTheme.warningColor),
              ),
              Expanded(
                child: _buildStatItem('Completed', '$completedGoals', AppTheme.successColor),
              ),
              Expanded(
                child: _buildStatItem('Rate', '${completionRate.toStringAsFixed(0)}%', AppTheme.secondaryColor),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build stat item
  Widget _buildStatItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: AppTheme.darkSecondaryText,
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  /// Build goal performance chart
  Widget _buildGoalPerformanceChart() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Goal Performance',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Goal performance charts will be available in the next update.',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Build goal recommendations
  Widget _buildGoalRecommendations() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Goal Recommendations',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Personalized goal recommendations will be available in the next update.',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Show add goal dialog
  void _showAddGoalDialog() {
    showDialog(
      context: context,
      builder: (context) => AddGoalDialog(
        agentId: widget.agentId,
        onGoalCreated: (goal) {
          // Handle goal creation
          widget.onGoalUpdated?.call(goal);
        },
      ),
    );
  }

  /// Handle goal actions
  void _handleGoalAction(DashboardGoal goal, String action) {
    switch (action) {
      case 'edit':
        _showEditGoalDialog(goal);
        break;
      case 'update_progress':
        _showUpdateProgressDialog(goal);
        break;
      case 'pause':
        // Handle pause goal
        break;
      case 'delete':
        _showDeleteConfirmation(goal);
        break;
    }
  }

  /// Show edit goal dialog
  void _showEditGoalDialog(DashboardGoal goal) {
    // Implementation for edit goal dialog
  }

  /// Show update progress dialog
  void _showUpdateProgressDialog(DashboardGoal goal) {
    // Implementation for update progress dialog
  }

  /// Show delete confirmation
  void _showDeleteConfirmation(DashboardGoal goal) {
    // Implementation for delete confirmation
  }
}

/// Add goal dialog
class AddGoalDialog extends StatefulWidget {
  final String agentId;
  final Function(DashboardGoal) onGoalCreated;

  const AddGoalDialog({
    super.key,
    required this.agentId,
    required this.onGoalCreated,
  });

  @override
  State<AddGoalDialog> createState() => _AddGoalDialogState();
}

class _AddGoalDialogState extends State<AddGoalDialog> {
  final _formKey = GlobalKey<FormState>();
  final _titleController = TextEditingController();
  final _descriptionController = TextEditingController();
  final _targetController = TextEditingController();
  final _rewardController = TextEditingController();
  
  GoalType _selectedType = GoalType.sales;
  DateTime _selectedDeadline = DateTime.now().add(const Duration(days: 30));
  bool _isLoading = false;

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _targetController.dispose();
    _rewardController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      backgroundColor: AppTheme.darkCard,
      title: Text(
        'Create New Goal',
        style: TextStyle(color: AppTheme.darkPrimaryText),
      ),
      content: SizedBox(
        width: 400,
        child: Form(
          key: _formKey,
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Goal Type Dropdown
              DropdownButtonFormField<GoalType>(
                value: _selectedType,
                decoration: InputDecoration(
                  labelText: 'Goal Type',
                  labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
                ),
                dropdownColor: AppTheme.darkCard,
                style: TextStyle(color: AppTheme.darkPrimaryText),
                items: GoalType.values.map((type) => DropdownMenuItem(
                  value: type,
                  child: Text(type.displayName),
                )).toList(),
                onChanged: (value) {
                  setState(() {
                    _selectedType = value!;
                  });
                },
              ),
              
              const SizedBox(height: 16),
              
              // Title Field
              TextFormField(
                controller: _titleController,
                style: TextStyle(color: AppTheme.darkPrimaryText),
                decoration: InputDecoration(
                  labelText: 'Goal Title',
                  labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
                ),
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a goal title';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Description Field
              TextFormField(
                controller: _descriptionController,
                style: TextStyle(color: AppTheme.darkPrimaryText),
                decoration: InputDecoration(
                  labelText: 'Description',
                  labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
                ),
                maxLines: 2,
              ),
              
              const SizedBox(height: 16),
              
              // Target Value Field
              TextFormField(
                controller: _targetController,
                style: TextStyle(color: AppTheme.darkPrimaryText),
                decoration: InputDecoration(
                  labelText: 'Target Value',
                  labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
                ),
                keyboardType: TextInputType.number,
                validator: (value) {
                  if (value == null || value.isEmpty) {
                    return 'Please enter a target value';
                  }
                  if (double.tryParse(value) == null) {
                    return 'Please enter a valid number';
                  }
                  return null;
                },
              ),
              
              const SizedBox(height: 16),
              
              // Reward Field
              TextFormField(
                controller: _rewardController,
                style: TextStyle(color: AppTheme.darkPrimaryText),
                decoration: InputDecoration(
                  labelText: 'Reward (Optional)',
                  labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
                ),
              ),
            ],
          ),
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: Text(
            'Cancel',
            style: TextStyle(color: AppTheme.darkSecondaryText),
          ),
        ),
        GradientWidgets.gradientButton(
          text: _isLoading ? 'Creating...' : 'Create Goal',
          onPressed: _isLoading ? null : _createGoal,
          icon: _isLoading ? null : Icons.add,
        ),
      ],
    );
  }

  /// Create goal
  Future<void> _createGoal() async {
    if (!_formKey.currentState!.validate()) return;

    setState(() => _isLoading = true);

    try {
      final goalId = await DashboardService.createGoal(
        agentId: widget.agentId,
        title: _titleController.text,
        description: _descriptionController.text,
        type: _selectedType,
        targetValue: double.parse(_targetController.text),
        deadline: _selectedDeadline,
        reward: _rewardController.text.isNotEmpty ? _rewardController.text : null,
      );

      if (goalId != null) {
        final goal = DashboardGoal(
          id: goalId,
          title: _titleController.text,
          description: _descriptionController.text,
          type: _selectedType,
          targetValue: double.parse(_targetController.text),
          currentValue: 0,
          deadline: _selectedDeadline,
          status: GoalStatus.active,
          reward: _rewardController.text.isNotEmpty ? _rewardController.text : null,
          createdAt: DateTime.now(),
        );

        widget.onGoalCreated(goal);
        Navigator.of(context).pop();
      }
    } catch (e) {
      // Handle error
    } finally {
      setState(() => _isLoading = false);
    }
  }
}
