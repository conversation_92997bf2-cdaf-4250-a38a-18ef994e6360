import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/star_model.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import 'mlm_service.dart';

/// Service for managing star-based reward system
class StarService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Award stars for a property sale/rental transaction
  /// Implements the rule: 1 star per sale to selling agent, 1 star to immediate upline on first sale only
  static Future<StarResult> awardStarsForTransaction({
    required String transactionId,
    required String sellingAgentId,
    required String propertyId,
    required String propertyTitle,
  }) async {
    try {
      final awardedStars = <StarModel>[];

      // Get selling agent details
      final sellingAgentDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(sellingAgentId)
          .get();

      if (!sellingAgentDoc.exists) {
        return StarResult.failure('Selling agent not found');
      }

      final sellingAgent = UserModel.fromFirestore(sellingAgentDoc);

      // 1. Award 1 star to selling agent for direct sale
      final directSaleStar = await _awardStarToAgent(
        agentId: sellingAgentId,
        agentName: sellingAgent.name,
        transactionId: transactionId,
        propertyId: propertyId,
        propertyTitle: propertyTitle,
        type: 'sale',
        source: 'direct_sale',
        notes: 'Star earned for direct property sale',
      );

      if (directSaleStar != null) {
        awardedStars.add(directSaleStar);
      }

      // 2. Award 1 star to immediate upline (only on their first sale)
      if (sellingAgent.uplineId != null) {
        final uplineDoc = await _firestore
            .collection(AppConstants.usersCollection)
            .doc(sellingAgent.uplineId!)
            .get();

        if (uplineDoc.exists) {
          final uplineAgent = UserModel.fromFirestore(uplineDoc);

          // Check if this is the first sale by any downline of this upline
          final isFirstDownlineSale = await _isFirstDownlineSale(
            sellingAgent.uplineId!,
          );

          if (isFirstDownlineSale) {
            final uplineStar = await _awardStarToAgent(
              agentId: uplineAgent.id,
              agentName: uplineAgent.name,
              transactionId: transactionId,
              propertyId: propertyId,
              propertyTitle: propertyTitle,
              type: 'upline_bonus',
              source: 'first_downline_sale',
              notes:
                  'Star earned for first downline sale (${sellingAgent.name})',
            );

            if (uplineStar != null) {
              awardedStars.add(uplineStar);
            }
          }
        }
      }

      return StarResult.success(awardedStars);
    } catch (e) {
      return StarResult.failure('Failed to award stars: ${e.toString()}');
    }
  }

  /// Award a star to a specific agent
  static Future<StarModel?> _awardStarToAgent({
    required String agentId,
    required String agentName,
    required String transactionId,
    String? propertyId,
    String? propertyTitle,
    required String type,
    required String source,
    String? notes,
  }) async {
    try {
      // Create star record
      final starData = {
        'agentId': agentId,
        'agentName': agentName,
        'transactionId': transactionId,
        'propertyId': propertyId,
        'propertyTitle': propertyTitle,
        'count': 1,
        'type': type,
        'source': source,
        'earnedAt': Timestamp.now(),
        'notes': notes,
      };

      final starRef = await _firestore
          .collection(AppConstants.starsCollection)
          .add(starData);

      // Update user total stars
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .update({
            'totalStars': FieldValue.increment(1),
            'updatedAt': Timestamp.now(),
          });

      // Check for 12-star bonus eligibility
      await _checkAndCreateStarBonus(agentId);

      // Create notification
      await _createStarNotification(agentId, type, source);

      // Return the created star model
      final starDoc = await starRef.get();
      return StarModel.fromFirestore(starDoc);
    } catch (e) {
      print('Error awarding star to agent: $e');
      return null;
    }
  }

  /// Check if this is the first sale by any downline of the given upline
  static Future<bool> _isFirstDownlineSale(String uplineId) async {
    try {
      // Get all downlines of this upline
      final downlines = await MLMService.getDirectDownlines(uplineId);

      // Check if any downline has made a sale before
      for (final downline in downlines) {
        final existingStars = await _firestore
            .collection(AppConstants.starsCollection)
            .where('agentId', isEqualTo: downline.id)
            .where('type', isEqualTo: 'sale')
            .limit(1)
            .get();

        if (existingStars.docs.isNotEmpty) {
          return false; // Not the first sale
        }
      }

      return true; // This is the first sale by any downline
    } catch (e) {
      print('Error checking first downline sale: $e');
      return false;
    }
  }

  /// Check and create star bonus when agent reaches 12-star milestones
  static Future<void> _checkAndCreateStarBonus(String agentId) async {
    try {
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .get();

      if (!userDoc.exists) return;

      final user = UserModel.fromFirestore(userDoc);

      // Check if user has reached a 12-star milestone
      if (user.totalStars >= AppConstants.starBenchmark &&
          user.totalStars % AppConstants.starBenchmark == 0) {
        // Check if bonus already exists for this milestone
        final existingBonus = await _firestore
            .collection('star_bonuses')
            .where('agentId', isEqualTo: agentId)
            .where('starCount', isEqualTo: user.totalStars)
            .limit(1)
            .get();

        if (existingBonus.docs.isEmpty) {
          // Create new star bonus
          await _createStarBonus(user);
        }
      }
    } catch (e) {
      print('Error checking star bonus: $e');
    }
  }

  /// Create a star bonus for the agent
  static Future<void> _createStarBonus(UserModel agent) async {
    try {
      // Get bonus configuration (default: ₹10,000 per 12 stars)
      final bonusAmount = await _getBonusAmount(agent.totalStars);

      final starBonus = StarBonusModel(
        id: '',
        agentId: agent.id,
        agentName: agent.name,
        starCount: agent.totalStars,
        bonusAmount: bonusAmount,
        bonusType: 'monetary',
        status: 'pending',
        eligibleAt: DateTime.now(),
        notes: '12-star milestone bonus for reaching ${agent.totalStars} stars',
      );

      // Save star bonus
      await _firestore.collection('star_bonuses').add(starBonus.toFirestore());

      // Update user total bonuses
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agent.id)
          .update({
            'totalBonuses': FieldValue.increment(bonusAmount),
            'updatedAt': Timestamp.now(),
          });

      // Create notification
      await _createBonusNotification(agent.id, agent.totalStars, bonusAmount);
    } catch (e) {
      print('Error creating star bonus: $e');
    }
  }

  /// Get bonus amount for star milestone (configurable by admin)
  static Future<double> _getBonusAmount(int starCount) async {
    try {
      // Try to get configured bonus amount from admin config
      final configDoc = await _firestore
          .collection(AppConstants.adminConfigCollection)
          .doc('star_bonus_config')
          .get();

      if (configDoc.exists) {
        final config = configDoc.data() as Map<String, dynamic>;
        final bonusPerMilestone =
            config['bonusPerMilestone']?.toDouble() ?? 10000.0;
        final milestoneMultiplier =
            config['milestoneMultiplier']?.toDouble() ?? 1.0;

        // Calculate bonus (can be progressive)
        final milestoneNumber = (starCount / AppConstants.starBenchmark)
            .floor();
        return bonusPerMilestone * milestoneMultiplier * milestoneNumber;
      }

      // Default bonus: ₹10,000 per 12-star milestone
      return 10000.0;
    } catch (e) {
      print('Error getting bonus amount: $e');
      return 10000.0; // Default fallback
    }
  }

  /// Get star history for an agent
  static Future<List<StarModel>> getAgentStarHistory(String agentId) async {
    try {
      // Always use mock data for now to avoid Firestore index issues
      if (true) {
        return _getMockStarHistory(agentId);
      }

      final query = await _firestore
          .collection(AppConstants.starsCollection)
          .where('agentId', isEqualTo: agentId)
          .orderBy('earnedAt', descending: true)
          .get();

      return query.docs.map((doc) => StarModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting agent star history: $e');
      return [];
    }
  }

  /// Get star bonus history for an agent
  static Future<List<StarBonusModel>> getAgentStarBonuses(
    String agentId,
  ) async {
    try {
      final query = await _firestore
          .collection('star_bonuses')
          .where('agentId', isEqualTo: agentId)
          .orderBy('eligibleAt', descending: true)
          .get();

      return query.docs
          .map((doc) => StarBonusModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting agent star bonuses: $e');
      return [];
    }
  }

  /// Get star statistics for an agent
  static Future<StarStatsModel> getAgentStarStats(String agentId) async {
    try {
      final stars = await getAgentStarHistory(agentId);
      final bonuses = await getAgentStarBonuses(agentId);

      final totalStars = stars.fold<int>(0, (sum, star) => sum + star.count);
      final directSaleStars = stars
          .where((s) => s.type == 'sale')
          .fold<int>(0, (sum, star) => sum + star.count);
      final uplineBonusStars = stars
          .where((s) => s.type == 'upline_bonus')
          .fold<int>(0, (sum, star) => sum + star.count);

      final starsToNextBonus =
          AppConstants.starBenchmark -
          (totalStars % AppConstants.starBenchmark);
      final progressToNextBonus =
          ((totalStars % AppConstants.starBenchmark) /
              AppConstants.starBenchmark) *
          100;

      // Calculate stars by month
      final starsByMonth = <String, int>{};
      for (final star in stars) {
        final monthKey =
            '${star.earnedAt.year}-${star.earnedAt.month.toString().padLeft(2, '0')}';
        starsByMonth[monthKey] = (starsByMonth[monthKey] ?? 0) + star.count;
      }

      // Calculate stars by type
      final starsByType = <String, int>{};
      for (final star in stars) {
        starsByType[star.type] = (starsByType[star.type] ?? 0) + star.count;
      }

      return StarStatsModel(
        totalStars: totalStars,
        directSaleStars: directSaleStars,
        uplineBonusStars: uplineBonusStars,
        progressToNextBonus: progressToNextBonus,
        starsToNextBonus: starsToNextBonus == AppConstants.starBenchmark
            ? 0
            : starsToNextBonus,
        lastStarEarned: stars.isNotEmpty ? stars.first.earnedAt : null,
        bonusHistory: bonuses,
        starsByMonth: starsByMonth,
        starsByType: starsByType,
      );
    } catch (e) {
      print('Error getting agent star stats: $e');
      return const StarStatsModel();
    }
  }

  /// Get all star bonuses (Admin only)
  static Future<List<StarBonusModel>> getAllStarBonuses() async {
    try {
      final query = await _firestore
          .collection('star_bonuses')
          .orderBy('eligibleAt', descending: true)
          .get();

      return query.docs
          .map((doc) => StarBonusModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting all star bonuses: $e');
      return [];
    }
  }

  /// Award star bonus (Admin only)
  static Future<StarResult> awardStarBonus(
    String bonusId,
    String adminId,
  ) async {
    try {
      await _firestore.collection('star_bonuses').doc(bonusId).update({
        'status': 'awarded',
        'awardedAt': Timestamp.now(),
        'awardedBy': adminId,
      });

      return StarResult.success([]);
    } catch (e) {
      return StarResult.failure('Failed to award star bonus: ${e.toString()}');
    }
  }

  /// Create star notification
  static Future<void> _createStarNotification(
    String agentId,
    String type,
    String source,
  ) async {
    try {
      String title = 'Star Earned! ⭐';
      String message = 'You earned 1 star for ';

      switch (type) {
        case 'sale':
          message += 'completing a property sale!';
          break;
        case 'upline_bonus':
          message += 'your downline\'s first sale!';
          break;
        default:
          message += 'your achievement!';
      }

      await _firestore.collection(AppConstants.notificationsCollection).add({
        'userId': agentId,
        'type': AppConstants.starNotification,
        'title': title,
        'message': message,
        'isRead': false,
        'createdAt': Timestamp.now(),
        'data': {'starType': type, 'starSource': source},
      });
    } catch (e) {
      print('Error creating star notification: $e');
    }
  }

  /// Create bonus notification
  static Future<void> _createBonusNotification(
    String agentId,
    int starCount,
    double bonusAmount,
  ) async {
    try {
      final formattedAmount = _formatAmount(bonusAmount);

      await _firestore.collection(AppConstants.notificationsCollection).add({
        'userId': agentId,
        'type': AppConstants.bonusNotification,
        'title': '🎉 Star Bonus Achieved!',
        'message':
            'Congratulations! You have earned $formattedAmount for reaching $starCount stars!',
        'isRead': false,
        'createdAt': Timestamp.now(),
        'data': {
          'bonusType': 'star_bonus',
          'starCount': starCount,
          'bonusAmount': bonusAmount,
        },
      });
    } catch (e) {
      print('Error creating bonus notification: $e');
    }
  }

  /// Format amount in Indian Rupees
  static String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(2)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(2)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(2)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }

  /// Get star leaderboard
  static Future<List<Map<String, dynamic>>> getStarLeaderboard({
    int limit = 10,
  }) async {
    try {
      final query = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: AppConstants.agentRole)
          .orderBy('totalStars', descending: true)
          .limit(limit)
          .get();

      return query.docs.map((doc) {
        final user = UserModel.fromFirestore(doc);
        return {
          'agent': user,
          'rank': 0, // Will be set after sorting
          'totalStars': user.totalStars,
          'totalCommissions': user.totalCommissions,
          'totalBonuses': user.totalBonuses,
        };
      }).toList();
    } catch (e) {
      print('Error getting star leaderboard: $e');
      return [];
    }
  }

  /// Generate mock star history for development
  static List<StarModel> _getMockStarHistory(String agentId) {
    final now = DateTime.now();
    return [
      StarModel(
        id: 'star-1-$agentId',
        agentId: agentId,
        agentName: 'Mock Agent',
        transactionId: 'trans-1',
        propertyId: 'prop-1',
        count: 1,
        type: 'sale',
        source: 'property_sale',
        earnedAt: now.subtract(const Duration(days: 5)),
        notes: 'Star earned for property sale',
      ),
      StarModel(
        id: 'star-2-$agentId',
        agentId: agentId,
        agentName: 'Mock Agent',
        transactionId: 'trans-2',
        propertyId: 'prop-2',
        count: 1,
        type: 'referral',
        source: 'team_sale',
        earnedAt: now.subtract(const Duration(days: 15)),
        notes: 'Star earned for team member sale',
      ),
      StarModel(
        id: 'star-3-$agentId',
        agentId: agentId,
        agentName: 'Mock Agent',
        transactionId: 'trans-3',
        propertyId: 'prop-3',
        count: 2,
        type: 'bonus',
        source: 'milestone',
        earnedAt: now.subtract(const Duration(days: 30)),
        notes: 'Bonus stars for reaching milestone',
      ),
    ];
  }
}

/// Star operation result wrapper
class StarResult {
  final bool isSuccess;
  final List<StarModel> stars;
  final String? message;

  StarResult._(this.isSuccess, this.stars, this.message);

  factory StarResult.success(List<StarModel> stars, {String? message}) {
    return StarResult._(true, stars, message);
  }

  factory StarResult.failure(String message) {
    return StarResult._(false, [], message);
  }
}
