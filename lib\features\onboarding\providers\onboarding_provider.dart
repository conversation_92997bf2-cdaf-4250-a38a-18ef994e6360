import 'dart:async';
import 'dart:io';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';

import '../models/onboarding_data.dart';
import '../services/onboarding_service.dart';
import '../../../core/models/user_model.dart';

/// Provider for onboarding state management
final onboardingProvider = StateNotifierProvider<OnboardingNotifier, OnboardingState>((ref) {
  return OnboardingNotifier();
});

/// Notifier for managing onboarding flow
class OnboardingNotifier extends StateNotifier<OnboardingState> {
  OnboardingNotifier() : super(const OnboardingState());

  Timer? _referralCodeDebounceTimer;

  /// Update agent name
  void updateName(String name) {
    final validation = OnboardingValidator.validateName(name);
    
    state = state.copyWith(
      data: state.data.copyWith(
        name: name,
        isNameValid: validation.isValid,
        nameError: validation.error,
      ),
    );
  }

  /// Update phone number
  void updatePhoneNumber(String phone) {
    final validation = OnboardingValidator.validatePhoneNumber(phone);
    
    state = state.copyWith(
      data: state.data.copyWith(
        phoneNumber: phone,
        isPhoneValid: validation.isValid,
        phoneError: validation.error,
      ),
    );
  }

  /// Update referral code with debounced verification
  void updateReferralCode(String code) {
    // Cancel previous timer
    _referralCodeDebounceTimer?.cancel();
    
    // Validate format first
    final formatValidation = OnboardingValidator.validateReferralCodeFormat(code);
    
    state = state.copyWith(
      data: state.data.copyWith(
        referralCode: code,
        isReferralCodeValid: formatValidation.isValid,
        referralCodeError: formatValidation.error,
      ),
      isReferralCodeVerifying: false,
      referralCodeOwnerName: null,
    );

    // If format is valid and code is not empty, verify with server
    if (formatValidation.isValid && code.trim().isNotEmpty) {
      state = state.copyWith(isReferralCodeVerifying: true);
      
      _referralCodeDebounceTimer = Timer(const Duration(milliseconds: 800), () {
        _verifyReferralCode(code.trim().toUpperCase());
      });
    } else if (code.trim().isEmpty) {
      // Empty code is valid (will be assigned to admin)
      state = state.copyWith(
        data: state.data.copyWith(isReferralCodeValid: true),
        referralCodeOwnerName: 'Admin (Auto-assigned)',
      );
    }
  }

  /// Verify referral code with server
  Future<void> _verifyReferralCode(String code) async {
    try {
      final result = await OnboardingService.verifyReferralCode(code);
      
      if (mounted) {
        if (result.isSuccess) {
          state = state.copyWith(
            data: state.data.copyWith(
              isReferralCodeValid: true,
              referralCodeError: null,
            ),
            isReferralCodeVerifying: false,
            isReferralCodeValid: true,
            referralCodeOwnerName: result.ownerName,
          );
        } else {
          state = state.copyWith(
            data: state.data.copyWith(
              isReferralCodeValid: false,
              referralCodeError: result.error,
            ),
            isReferralCodeVerifying: false,
            isReferralCodeValid: false,
            referralCodeOwnerName: null,
          );
        }
      }
    } catch (e) {
      if (mounted) {
        state = state.copyWith(
          data: state.data.copyWith(
            isReferralCodeValid: false,
            referralCodeError: 'Failed to verify referral code',
          ),
          isReferralCodeVerifying: false,
          isReferralCodeValid: false,
          referralCodeOwnerName: null,
        );
      }
    }
  }

  /// Move to next step
  void nextStep() {
    switch (state.currentStep) {
      case OnboardingStep.basicInfo:
        if (state.data.isBasicInfoComplete) {
          state = state.copyWith(currentStep: OnboardingStep.profileImage);
        }
        break;
      case OnboardingStep.profileImage:
        state = state.copyWith(currentStep: OnboardingStep.complete);
        break;
      case OnboardingStep.complete:
        break;
    }
  }

  /// Move to previous step
  void previousStep() {
    switch (state.currentStep) {
      case OnboardingStep.basicInfo:
        break;
      case OnboardingStep.profileImage:
        state = state.copyWith(currentStep: OnboardingStep.basicInfo);
        break;
      case OnboardingStep.complete:
        state = state.copyWith(currentStep: OnboardingStep.profileImage);
        break;
    }
  }

  /// Update profile image
  void updateProfileImage(File? imageFile) {
    state = state.copyWith(
      data: state.data.copyWith(profileImage: imageFile),
    );
  }

  /// Skip profile image step
  void skipProfileImage() {
    state = state.copyWith(currentStep: OnboardingStep.complete);
  }

  /// Complete onboarding process
  Future<UserModel?> completeOnboarding() async {
    final currentUser = FirebaseAuth.instance.currentUser;
    if (currentUser == null) {
      state = state.copyWith(error: 'User not authenticated');
      return null;
    }

    state = state.copyWith(isLoading: true, error: null);

    try {
      final result = await OnboardingService.completeOnboarding(
        email: currentUser.email!,
        data: state.data,
      );

      if (result.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          currentStep: OnboardingStep.complete,
        );
        return result.user;
      } else {
        state = state.copyWith(
          isLoading: false,
          error: result.error,
        );
        return null;
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to complete onboarding: ${e.toString()}',
      );
      return null;
    }
  }

  /// Reset onboarding state
  void reset() {
    _referralCodeDebounceTimer?.cancel();
    state = const OnboardingState();
  }

  /// Clear error
  void clearError() {
    state = state.copyWith(error: null);
  }

  @override
  void dispose() {
    _referralCodeDebounceTimer?.cancel();
    super.dispose();
  }
}

/// Provider to check if user needs onboarding
final needsOnboardingProvider = FutureProvider<bool>((ref) async {
  final currentUser = FirebaseAuth.instance.currentUser;
  if (currentUser == null) return false;

  return !(await OnboardingService.hasCompletedOnboarding(currentUser.uid));
});

/// Provider for referral code suggestions
final referralCodeSuggestionsProvider = Provider<List<String>>((ref) {
  return [
    'ADMIN001',
    'AGENT001',
    'TEST123',
    'DEMO456',
  ];
});
