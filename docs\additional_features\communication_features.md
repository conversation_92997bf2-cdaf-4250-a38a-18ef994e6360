# Communication & Collaboration Features

## Overview
This document outlines advanced communication and collaboration features to enhance team coordination and client interaction.

## 1. In-App Messaging System

### Real-time Chat
```dart
// lib/features/messaging/services/chat_service.dart
class ChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Send message
  static Future<void> sendMessage({
    required String chatId,
    required String senderId,
    required String content,
    MessageType type = MessageType.text,
    Map<String, dynamic>? metadata,
  }) async {
    await _firestore.collection('chats').doc(chatId).collection('messages').add({
      'senderId': senderId,
      'content': content,
      'type': type.name,
      'metadata': metadata ?? {},
      'timestamp': FieldValue.serverTimestamp(),
      'isRead': false,
      'isEdited': false,
      'reactions': {},
    });

    // Update chat last message
    await _firestore.collection('chats').doc(chatId).update({
      'lastMessage': content,
      'lastMessageTime': FieldValue.serverTimestamp(),
      'lastMessageSender': senderId,
    });
  }

  // Create group chat
  static Future<String> createGroupChat({
    required String name,
    required List<String> memberIds,
    required String createdBy,
    String? description,
    String? imageUrl,
  }) async {
    final chatDoc = await _firestore.collection('chats').add({
      'name': name,
      'description': description,
      'imageUrl': imageUrl,
      'type': ChatType.group.name,
      'memberIds': memberIds,
      'adminIds': [createdBy],
      'createdBy': createdBy,
      'createdAt': FieldValue.serverTimestamp(),
      'lastMessage': '',
      'lastMessageTime': FieldValue.serverTimestamp(),
      'isActive': true,
    });

    // Send welcome message
    await sendMessage(
      chatId: chatDoc.id,
      senderId: 'system',
      content: '$name group created',
      type: MessageType.system,
    );

    return chatDoc.id;
  }

  // Get chat messages stream
  static Stream<List<ChatMessage>> getChatMessages(String chatId) {
    return _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ChatMessage.fromFirestore(doc))
            .toList());
  }

  // Get user chats
  static Stream<List<Chat>> getUserChats(String userId) {
    return _firestore
        .collection('chats')
        .where('memberIds', arrayContains: userId)
        .where('isActive', isEqualTo: true)
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Chat.fromFirestore(doc))
            .toList());
  }

  // Mark messages as read
  static Future<void> markMessagesAsRead(String chatId, String userId) async {
    final unreadMessages = await _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .where('senderId', isNotEqualTo: userId)
        .where('isRead', isEqualTo: false)
        .get();

    final batch = _firestore.batch();
    for (final doc in unreadMessages.docs) {
      batch.update(doc.reference, {'isRead': true});
    }
    await batch.commit();
  }

  // Send property share
  static Future<void> shareProperty({
    required String chatId,
    required String senderId,
    required String propertyId,
    String? message,
  }) async {
    final property = await PropertyService.getProperty(propertyId);
    
    await sendMessage(
      chatId: chatId,
      senderId: senderId,
      content: message ?? 'Check out this property!',
      type: MessageType.property,
      metadata: {
        'propertyId': propertyId,
        'propertyTitle': property?.title,
        'propertyPrice': property?.price,
        'propertyImage': property?.images.first,
      },
    );
  }

  // Send location
  static Future<void> shareLocation({
    required String chatId,
    required String senderId,
    required double latitude,
    required double longitude,
    String? address,
  }) async {
    await sendMessage(
      chatId: chatId,
      senderId: senderId,
      content: 'Shared location',
      type: MessageType.location,
      metadata: {
        'latitude': latitude,
        'longitude': longitude,
        'address': address,
      },
    );
  }
}

// Message models
class ChatMessage {
  final String id;
  final String senderId;
  final String content;
  final MessageType type;
  final Map<String, dynamic> metadata;
  final DateTime timestamp;
  final bool isRead;
  final bool isEdited;
  final Map<String, String> reactions;

  const ChatMessage({
    required this.id,
    required this.senderId,
    required this.content,
    required this.type,
    required this.metadata,
    required this.timestamp,
    required this.isRead,
    required this.isEdited,
    required this.reactions,
  });

  factory ChatMessage.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ChatMessage(
      id: doc.id,
      senderId: data['senderId'] ?? '',
      content: data['content'] ?? '',
      type: MessageType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => MessageType.text,
      ),
      metadata: Map<String, dynamic>.from(data['metadata'] ?? {}),
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isRead: data['isRead'] ?? false,
      isEdited: data['isEdited'] ?? false,
      reactions: Map<String, String>.from(data['reactions'] ?? {}),
    );
  }
}

enum MessageType {
  text,
  image,
  video,
  audio,
  document,
  property,
  location,
  system,
}

class Chat {
  final String id;
  final String name;
  final String? description;
  final String? imageUrl;
  final ChatType type;
  final List<String> memberIds;
  final List<String> adminIds;
  final String createdBy;
  final DateTime createdAt;
  final String lastMessage;
  final DateTime lastMessageTime;
  final bool isActive;

  const Chat({
    required this.id,
    required this.name,
    this.description,
    this.imageUrl,
    required this.type,
    required this.memberIds,
    required this.adminIds,
    required this.createdBy,
    required this.createdAt,
    required this.lastMessage,
    required this.lastMessageTime,
    required this.isActive,
  });

  factory Chat.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Chat(
      id: doc.id,
      name: data['name'] ?? '',
      description: data['description'],
      imageUrl: data['imageUrl'],
      type: ChatType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => ChatType.direct,
      ),
      memberIds: List<String>.from(data['memberIds'] ?? []),
      adminIds: List<String>.from(data['adminIds'] ?? []),
      createdBy: data['createdBy'] ?? '',
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      lastMessage: data['lastMessage'] ?? '',
      lastMessageTime: (data['lastMessageTime'] as Timestamp?)?.toDate() ?? DateTime.now(),
      isActive: data['isActive'] ?? true,
    );
  }
}

enum ChatType {
  direct,
  group,
  channel,
}
```

### Video Calling Integration
```dart
// lib/features/video_call/services/video_call_service.dart
class VideoCallService {
  // Initialize video call
  static Future<CallSession> initiateCall({
    required String callerId,
    required List<String> participantIds,
    CallType type = CallType.video,
  }) async {
    final sessionId = _generateSessionId();
    
    // Create call session in database
    await _firestore.collection('call_sessions').doc(sessionId).set({
      'callerId': callerId,
      'participantIds': participantIds,
      'type': type.name,
      'status': CallStatus.ringing.name,
      'startTime': FieldValue.serverTimestamp(),
      'endTime': null,
      'duration': 0,
    });

    // Send call notifications to participants
    for (final participantId in participantIds) {
      await NotificationService.sendCallNotification(
        userId: participantId,
        callerId: callerId,
        sessionId: sessionId,
        type: type,
      );
    }

    return CallSession(
      id: sessionId,
      callerId: callerId,
      participantIds: participantIds,
      type: type,
      status: CallStatus.ringing,
      startTime: DateTime.now(),
    );
  }

  // Join call
  static Future<void> joinCall(String sessionId, String userId) async {
    await _firestore.collection('call_sessions').doc(sessionId).update({
      'status': CallStatus.active.name,
      'joinedParticipants': FieldValue.arrayUnion([userId]),
    });
  }

  // End call
  static Future<void> endCall(String sessionId) async {
    await _firestore.collection('call_sessions').doc(sessionId).update({
      'status': CallStatus.ended.name,
      'endTime': FieldValue.serverTimestamp(),
    });
  }

  // Screen sharing
  static Future<void> startScreenShare(String sessionId, String userId) async {
    await _firestore.collection('call_sessions').doc(sessionId).update({
      'screenSharingUser': userId,
      'isScreenSharing': true,
    });
  }

  static Future<void> stopScreenShare(String sessionId) async {
    await _firestore.collection('call_sessions').doc(sessionId).update({
      'screenSharingUser': null,
      'isScreenSharing': false,
    });
  }
}

class CallSession {
  final String id;
  final String callerId;
  final List<String> participantIds;
  final CallType type;
  final CallStatus status;
  final DateTime startTime;
  final DateTime? endTime;
  final Duration? duration;

  const CallSession({
    required this.id,
    required this.callerId,
    required this.participantIds,
    required this.type,
    required this.status,
    required this.startTime,
    this.endTime,
    this.duration,
  });
}

enum CallType { audio, video }
enum CallStatus { ringing, active, ended, missed }
```

## 2. Team Collaboration Tools

### Project Management
```dart
// lib/features/collaboration/services/project_service.dart
class ProjectService {
  // Create project
  static Future<String> createProject({
    required String name,
    required String description,
    required String createdBy,
    required List<String> teamMemberIds,
    DateTime? deadline,
    ProjectPriority priority = ProjectPriority.medium,
  }) async {
    final projectDoc = await _firestore.collection('projects').add({
      'name': name,
      'description': description,
      'createdBy': createdBy,
      'teamMemberIds': teamMemberIds,
      'deadline': deadline != null ? Timestamp.fromDate(deadline) : null,
      'priority': priority.name,
      'status': ProjectStatus.planning.name,
      'progress': 0.0,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Create default tasks
    await _createDefaultTasks(projectDoc.id);

    // Notify team members
    for (final memberId in teamMemberIds) {
      await NotificationService.sendProjectNotification(
        userId: memberId,
        projectId: projectDoc.id,
        message: 'You\'ve been added to project: $name',
      );
    }

    return projectDoc.id;
  }

  // Add task to project
  static Future<String> addTask({
    required String projectId,
    required String title,
    required String description,
    required String assignedTo,
    required String createdBy,
    DateTime? dueDate,
    TaskPriority priority = TaskPriority.medium,
  }) async {
    final taskDoc = await _firestore
        .collection('projects')
        .doc(projectId)
        .collection('tasks')
        .add({
      'title': title,
      'description': description,
      'assignedTo': assignedTo,
      'createdBy': createdBy,
      'dueDate': dueDate != null ? Timestamp.fromDate(dueDate) : null,
      'priority': priority.name,
      'status': TaskStatus.todo.name,
      'progress': 0.0,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Update project progress
    await _updateProjectProgress(projectId);

    // Notify assignee
    await NotificationService.sendTaskNotification(
      userId: assignedTo,
      taskId: taskDoc.id,
      message: 'New task assigned: $title',
    );

    return taskDoc.id;
  }

  // Update task status
  static Future<void> updateTaskStatus({
    required String projectId,
    required String taskId,
    required TaskStatus status,
    String? comment,
  }) async {
    await _firestore
        .collection('projects')
        .doc(projectId)
        .collection('tasks')
        .doc(taskId)
        .update({
      'status': status.name,
      'progress': status == TaskStatus.completed ? 100.0 : 
                  status == TaskStatus.inProgress ? 50.0 : 0.0,
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Add comment if provided
    if (comment != null) {
      await _addTaskComment(projectId, taskId, comment);
    }

    // Update project progress
    await _updateProjectProgress(projectId);
  }

  // Get project tasks
  static Stream<List<ProjectTask>> getProjectTasks(String projectId) {
    return _firestore
        .collection('projects')
        .doc(projectId)
        .collection('tasks')
        .orderBy('createdAt', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ProjectTask.fromFirestore(doc))
            .toList());
  }
}

// Project models
class Project {
  final String id;
  final String name;
  final String description;
  final String createdBy;
  final List<String> teamMemberIds;
  final DateTime? deadline;
  final ProjectPriority priority;
  final ProjectStatus status;
  final double progress;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Project({
    required this.id,
    required this.name,
    required this.description,
    required this.createdBy,
    required this.teamMemberIds,
    this.deadline,
    required this.priority,
    required this.status,
    required this.progress,
    required this.createdAt,
    required this.updatedAt,
  });
}

class ProjectTask {
  final String id;
  final String title;
  final String description;
  final String assignedTo;
  final String createdBy;
  final DateTime? dueDate;
  final TaskPriority priority;
  final TaskStatus status;
  final double progress;
  final DateTime createdAt;
  final DateTime updatedAt;

  const ProjectTask({
    required this.id,
    required this.title,
    required this.description,
    required this.assignedTo,
    required this.createdBy,
    this.dueDate,
    required this.priority,
    required this.status,
    required this.progress,
    required this.createdAt,
    required this.updatedAt,
  });

  factory ProjectTask.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ProjectTask(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      assignedTo: data['assignedTo'] ?? '',
      createdBy: data['createdBy'] ?? '',
      dueDate: (data['dueDate'] as Timestamp?)?.toDate(),
      priority: TaskPriority.values.firstWhere(
        (p) => p.name == data['priority'],
        orElse: () => TaskPriority.medium,
      ),
      status: TaskStatus.values.firstWhere(
        (s) => s.name == data['status'],
        orElse: () => TaskStatus.todo,
      ),
      progress: (data['progress'] ?? 0.0).toDouble(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}

enum ProjectStatus { planning, active, onHold, completed, cancelled }
enum ProjectPriority { low, medium, high, urgent }
enum TaskStatus { todo, inProgress, review, completed }
enum TaskPriority { low, medium, high, urgent }
```

## 3. Client Communication

### CRM Integration
```dart
// lib/features/crm/services/client_service.dart
class ClientService {
  // Add new client
  static Future<String> addClient({
    required String name,
    required String email,
    required String phoneNumber,
    required String agentId,
    String? address,
    ClientStatus status = ClientStatus.lead,
    Map<String, dynamic>? preferences,
  }) async {
    final clientDoc = await _firestore.collection('clients').add({
      'name': name,
      'email': email,
      'phoneNumber': phoneNumber,
      'address': address,
      'agentId': agentId,
      'status': status.name,
      'preferences': preferences ?? {},
      'interactions': [],
      'lastContactDate': FieldValue.serverTimestamp(),
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Create initial interaction
    await addClientInteraction(
      clientId: clientDoc.id,
      agentId: agentId,
      type: InteractionType.initial,
      notes: 'Client added to system',
    );

    return clientDoc.id;
  }

  // Add client interaction
  static Future<void> addClientInteraction({
    required String clientId,
    required String agentId,
    required InteractionType type,
    required String notes,
    DateTime? scheduledFollowUp,
  }) async {
    await _firestore
        .collection('clients')
        .doc(clientId)
        .collection('interactions')
        .add({
      'agentId': agentId,
      'type': type.name,
      'notes': notes,
      'scheduledFollowUp': scheduledFollowUp != null 
          ? Timestamp.fromDate(scheduledFollowUp) 
          : null,
      'timestamp': FieldValue.serverTimestamp(),
    });

    // Update client last contact date
    await _firestore.collection('clients').doc(clientId).update({
      'lastContactDate': FieldValue.serverTimestamp(),
    });

    // Schedule follow-up reminder if needed
    if (scheduledFollowUp != null) {
      await _scheduleFollowUpReminder(clientId, agentId, scheduledFollowUp);
    }
  }

  // Get client interactions
  static Stream<List<ClientInteraction>> getClientInteractions(String clientId) {
    return _firestore
        .collection('clients')
        .doc(clientId)
        .collection('interactions')
        .orderBy('timestamp', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ClientInteraction.fromFirestore(doc))
            .toList());
  }

  // Update client status
  static Future<void> updateClientStatus(String clientId, ClientStatus status) async {
    await _firestore.collection('clients').doc(clientId).update({
      'status': status.name,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  // Get clients for agent
  static Stream<List<Client>> getAgentClients(String agentId) {
    return _firestore
        .collection('clients')
        .where('agentId', isEqualTo: agentId)
        .orderBy('lastContactDate', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Client.fromFirestore(doc))
            .toList());
  }
}

// Client models
class Client {
  final String id;
  final String name;
  final String email;
  final String phoneNumber;
  final String? address;
  final String agentId;
  final ClientStatus status;
  final Map<String, dynamic> preferences;
  final DateTime lastContactDate;
  final DateTime createdAt;
  final DateTime updatedAt;

  const Client({
    required this.id,
    required this.name,
    required this.email,
    required this.phoneNumber,
    this.address,
    required this.agentId,
    required this.status,
    required this.preferences,
    required this.lastContactDate,
    required this.createdAt,
    required this.updatedAt,
  });

  factory Client.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return Client(
      id: doc.id,
      name: data['name'] ?? '',
      email: data['email'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      address: data['address'],
      agentId: data['agentId'] ?? '',
      status: ClientStatus.values.firstWhere(
        (s) => s.name == data['status'],
        orElse: () => ClientStatus.lead,
      ),
      preferences: Map<String, dynamic>.from(data['preferences'] ?? {}),
      lastContactDate: (data['lastContactDate'] as Timestamp?)?.toDate() ?? DateTime.now(),
      createdAt: (data['createdAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
      updatedAt: (data['updatedAt'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}

class ClientInteraction {
  final String id;
  final String agentId;
  final InteractionType type;
  final String notes;
  final DateTime? scheduledFollowUp;
  final DateTime timestamp;

  const ClientInteraction({
    required this.id,
    required this.agentId,
    required this.type,
    required this.notes,
    this.scheduledFollowUp,
    required this.timestamp,
  });

  factory ClientInteraction.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    return ClientInteraction(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      type: InteractionType.values.firstWhere(
        (t) => t.name == data['type'],
        orElse: () => InteractionType.call,
      ),
      notes: data['notes'] ?? '',
      scheduledFollowUp: (data['scheduledFollowUp'] as Timestamp?)?.toDate(),
      timestamp: (data['timestamp'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }
}

enum ClientStatus { lead, prospect, client, inactive }
enum InteractionType { initial, call, email, meeting, property_viewing, follow_up }
```
