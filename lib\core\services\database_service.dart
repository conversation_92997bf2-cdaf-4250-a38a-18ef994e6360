import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

/// Comprehensive database service for Firestore operations
class DatabaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Generic CRUD operations

  /// Create a new document
  static Future<String> create(String collection, Map<String, dynamic> data) async {
    try {
      final docRef = await _firestore.collection(collection).add({
        ...data,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      if (kDebugMode) {
        print('Document created in $collection: ${docRef.id}');
      }
      
      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating document in $collection: $e');
      }
      rethrow;
    }
  }

  /// Read a document by ID
  static Future<Map<String, dynamic>?> read(String collection, String id) async {
    try {
      final doc = await _firestore.collection(collection).doc(id).get();
      if (doc.exists) {
        return {'id': doc.id, ...doc.data()!};
      }
      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error reading document from $collection: $e');
      }
      return null;
    }
  }

  /// Update a document
  static Future<void> update(String collection, String id, Map<String, dynamic> data) async {
    try {
      await _firestore.collection(collection).doc(id).update({
        ...data,
        'updatedAt': FieldValue.serverTimestamp(),
      });
      
      if (kDebugMode) {
        print('Document updated in $collection: $id');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error updating document in $collection: $e');
      }
      rethrow;
    }
  }

  /// Delete a document
  static Future<void> delete(String collection, String id) async {
    try {
      await _firestore.collection(collection).doc(id).delete();
      
      if (kDebugMode) {
        print('Document deleted from $collection: $id');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting document from $collection: $e');
      }
      rethrow;
    }
  }

  /// Query operations

  /// Query documents with filters
  static Future<List<Map<String, dynamic>>> query(
    String collection, {
    List<QueryFilter>? filters,
    List<QueryOrder>? orderBy,
    int? limit,
  }) async {
    try {
      Query query = _firestore.collection(collection);

      // Apply filters
      if (filters != null) {
        for (final filter in filters) {
          switch (filter.operator) {
            case '==':
              query = query.where(filter.field, isEqualTo: filter.value);
              break;
            case '!=':
              query = query.where(filter.field, isNotEqualTo: filter.value);
              break;
            case '>':
              query = query.where(filter.field, isGreaterThan: filter.value);
              break;
            case '>=':
              query = query.where(filter.field, isGreaterThanOrEqualTo: filter.value);
              break;
            case '<':
              query = query.where(filter.field, isLessThan: filter.value);
              break;
            case '<=':
              query = query.where(filter.field, isLessThanOrEqualTo: filter.value);
              break;
            case 'array-contains':
              query = query.where(filter.field, arrayContains: filter.value);
              break;
            case 'array-contains-any':
              query = query.where(filter.field, arrayContainsAny: filter.value);
              break;
            case 'in':
              query = query.where(filter.field, whereIn: filter.value);
              break;
            case 'not-in':
              query = query.where(filter.field, whereNotIn: filter.value);
              break;
          }
        }
      }

      // Apply ordering
      if (orderBy != null) {
        for (final order in orderBy) {
          query = query.orderBy(order.field, descending: order.descending);
        }
      }

      // Apply limit
      if (limit != null) {
        query = query.limit(limit);
      }

      final snapshot = await query.get();
      return snapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data() as Map<String, dynamic>,
      }).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error querying $collection: $e');
      }
      return [];
    }
  }

  /// Batch operations
  static Future<void> batchWrite(List<BatchOperation> operations) async {
    try {
      final batch = _firestore.batch();

      for (final operation in operations) {
        switch (operation.type) {
          case BatchOperationType.create:
            final docRef = _firestore.collection(operation.collection).doc();
            batch.set(docRef, {
              ...operation.data!,
              'createdAt': FieldValue.serverTimestamp(),
              'updatedAt': FieldValue.serverTimestamp(),
            });
            break;
          case BatchOperationType.update:
            final docRef = _firestore.collection(operation.collection).doc(operation.id);
            batch.update(docRef, {
              ...operation.data!,
              'updatedAt': FieldValue.serverTimestamp(),
            });
            break;
          case BatchOperationType.delete:
            final docRef = _firestore.collection(operation.collection).doc(operation.id);
            batch.delete(docRef);
            break;
        }
      }

      await batch.commit();
      
      if (kDebugMode) {
        print('Batch operation completed with ${operations.length} operations');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error in batch operation: $e');
      }
      rethrow;
    }
  }

  /// Real-time subscriptions
  static Stream<List<Map<String, dynamic>>> streamCollection(
    String collection, {
    List<QueryFilter>? filters,
    List<QueryOrder>? orderBy,
    int? limit,
  }) {
    try {
      Query query = _firestore.collection(collection);

      // Apply filters and ordering (same as query method)
      if (filters != null) {
        for (final filter in filters) {
          switch (filter.operator) {
            case '==':
              query = query.where(filter.field, isEqualTo: filter.value);
              break;
            case '!=':
              query = query.where(filter.field, isNotEqualTo: filter.value);
              break;
            case '>':
              query = query.where(filter.field, isGreaterThan: filter.value);
              break;
            case '>=':
              query = query.where(filter.field, isGreaterThanOrEqualTo: filter.value);
              break;
            case '<':
              query = query.where(filter.field, isLessThan: filter.value);
              break;
            case '<=':
              query = query.where(filter.field, isLessThanOrEqualTo: filter.value);
              break;
            case 'array-contains':
              query = query.where(filter.field, arrayContains: filter.value);
              break;
          }
        }
      }

      if (orderBy != null) {
        for (final order in orderBy) {
          query = query.orderBy(order.field, descending: order.descending);
        }
      }

      if (limit != null) {
        query = query.limit(limit);
      }

      return query.snapshots().map((snapshot) => 
        snapshot.docs.map((doc) => {
          'id': doc.id,
          ...doc.data() as Map<String, dynamic>,
        }).toList()
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error streaming $collection: $e');
      }
      return Stream.value([]);
    }
  }

  /// Stream a single document
  static Stream<Map<String, dynamic>?> streamDocument(String collection, String id) {
    try {
      return _firestore.collection(collection).doc(id).snapshots().map((doc) {
        if (doc.exists) {
          return {'id': doc.id, ...doc.data()!};
        }
        return null;
      });
    } catch (e) {
      if (kDebugMode) {
        print('Error streaming document from $collection: $e');
      }
      return Stream.value(null);
    }
  }

  /// Transaction operations
  static Future<T> runTransaction<T>(
    Future<T> Function(Transaction transaction) updateFunction,
  ) async {
    try {
      return await _firestore.runTransaction(updateFunction);
    } catch (e) {
      if (kDebugMode) {
        print('Error in transaction: $e');
      }
      rethrow;
    }
  }

  /// Aggregation queries
  static Future<int> count(String collection, {List<QueryFilter>? filters}) async {
    try {
      Query query = _firestore.collection(collection);

      if (filters != null) {
        for (final filter in filters) {
          query = query.where(filter.field, isEqualTo: filter.value);
        }
      }

      final snapshot = await query.count().get();
      return snapshot.count ?? 0;
    } catch (e) {
      if (kDebugMode) {
        print('Error counting documents in $collection: $e');
      }
      return 0;
    }
  }

  /// Pagination support
  static Future<QueryResult> paginatedQuery(
    String collection, {
    List<QueryFilter>? filters,
    List<QueryOrder>? orderBy,
    int limit = 20,
    DocumentSnapshot? startAfter,
  }) async {
    try {
      Query query = _firestore.collection(collection);

      // Apply filters
      if (filters != null) {
        for (final filter in filters) {
          query = query.where(filter.field, isEqualTo: filter.value);
        }
      }

      // Apply ordering
      if (orderBy != null) {
        for (final order in orderBy) {
          query = query.orderBy(order.field, descending: order.descending);
        }
      }

      // Apply pagination
      if (startAfter != null) {
        query = query.startAfterDocument(startAfter);
      }

      query = query.limit(limit);

      final snapshot = await query.get();
      final documents = snapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data() as Map<String, dynamic>,
      }).toList();

      return QueryResult(
        documents: documents,
        lastDocument: snapshot.docs.isNotEmpty ? snapshot.docs.last : null,
        hasMore: snapshot.docs.length == limit,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error in paginated query for $collection: $e');
      }
      return QueryResult(documents: [], lastDocument: null, hasMore: false);
    }
  }
}

/// Helper classes
class QueryFilter {
  final String field;
  final dynamic value;
  final String operator;

  const QueryFilter(this.field, this.value, {this.operator = '=='});
}

class QueryOrder {
  final String field;
  final bool descending;

  const QueryOrder(this.field, {this.descending = false});
}

class BatchOperation {
  final BatchOperationType type;
  final String collection;
  final String? id;
  final Map<String, dynamic>? data;

  const BatchOperation({
    required this.type,
    required this.collection,
    this.id,
    this.data,
  });
}

enum BatchOperationType { create, update, delete }

class QueryResult {
  final List<Map<String, dynamic>> documents;
  final DocumentSnapshot? lastDocument;
  final bool hasMore;

  const QueryResult({
    required this.documents,
    required this.lastDocument,
    required this.hasMore,
  });
}
