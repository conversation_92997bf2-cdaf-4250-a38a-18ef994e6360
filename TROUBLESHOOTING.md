# Troubleshooting Guide - Rama Realty MLM App

## 🚨 Common Issues and Solutions

### 1. **Flutter Commands Hanging**
If `flutter run` or `flutter pub get` commands hang:

```bash
# Kill Flutter daemon
flutter daemon --shutdown

# Clear Flutter cache
flutter pub cache repair

# Restart and try again
flutter clean
flutter pub get
flutter run -d windows
```

### 2. **Developer Mode Not Enabled (Windows)**
**Error**: "Building with plugins requires symlink support"

**Solution**:
1. Press `Windows + R`
2. Type: `ms-settings:developers`
3. Enable "Developer Mode"
4. Restart computer if prompted

### 3. **Build Errors**
If you get compilation errors:

```bash
# Complete clean and rebuild
flutter clean
flutter pub cache repair
flutter pub get
flutter run -d windows --verbose
```

### 4. **Missing Dependencies**
**Error**: Package not found or import errors

**Solution**:
```bash
# Repair pub cache
flutter pub cache repair

# Get dependencies
flutter pub get

# If still failing, delete pubspec.lock and try again
del pubspec.lock
flutter pub get
```

### 5. **Network Issues**
If downloads are slow or failing:

```bash
# Use different mirror (if in restricted network)
flutter config --android-sdk <path>

# Or try with VPN if corporate network blocks downloads
```

## 🔧 Manual Run Steps

### Option 1: Command Line
```bash
cd C:\code\MLM_project\rama_realty_mlm
flutter clean
flutter pub get
flutter run -d windows
```

### Option 2: Using Scripts
```bash
# Run the batch file
run_app.bat

# Or PowerShell script
powershell -ExecutionPolicy Bypass -File run_app.ps1
```

### Option 3: IDE
1. Open project in **VS Code** or **Android Studio**
2. Select **Windows** device from device selector
3. Press **F5** or click **Run** button

## 🌐 Alternative: Run on Web

If Windows desktop continues to have issues:

```bash
flutter run -d chrome --web-renderer html
```

## 📱 Alternative: Run on Android

If you have Android Studio with emulator:

```bash
# Start Android emulator first
flutter emulators --launch <emulator_name>

# Then run app
flutter run -d android
```

## 🔍 Diagnostic Commands

### Check Flutter Installation
```bash
flutter doctor -v
```

### Check Available Devices
```bash
flutter devices
```

### Check Dependencies
```bash
flutter pub deps
```

### Verbose Build Output
```bash
flutter run -d windows --verbose
```

## 🎯 Expected Behavior When Working

### Successful Build Output:
```
Launching lib\main.dart on Windows in debug mode...
Building Windows application...
✓ Built build\windows\runner\Debug\rama_realty_mlm.exe
Installing and launching...
Syncing files to device Windows...
Flutter run key commands.
r Hot reload. 🔥
R Hot restart.
h List all available interactive commands.
d Detach (terminate "flutter run" but leave application running).
c Clear the screen
q Quit (terminate the application on the device).
```

### App Should Open With:
- **Login Screen** with Rama Realty MLM branding
- **Email/Password** input fields
- **Test accounts** working:
  - Admin: `<EMAIL>` / `admin123`
  - Agent: `<EMAIL>` / `agent123`

## 🆘 If Nothing Works

### Last Resort Options:

1. **Use the Demo HTML**: Open `demo.html` in browser to see app features
2. **Check Flutter Version**: Ensure Flutter 3.0+ is installed
3. **Reinstall Flutter**: Download fresh Flutter SDK
4. **Use Different Platform**: Try web or Android instead

### Contact Information:
- Check Flutter documentation: https://docs.flutter.dev/
- Flutter community: https://flutter.dev/community
- Stack Overflow: Tag questions with `flutter` and `windows`

## 📋 System Requirements

### Minimum Requirements:
- **Windows 10** version 1903 or higher
- **Flutter SDK** 3.0 or higher
- **Visual Studio 2019** or higher (for Windows desktop)
- **Developer Mode** enabled
- **8GB RAM** minimum (16GB recommended)
- **5GB free disk space**

### Recommended Setup:
- **Windows 11** latest version
- **Flutter SDK** latest stable
- **Visual Studio 2022** Community Edition
- **16GB RAM** or more
- **SSD storage** for better performance

---

**Remember**: The Rama Realty MLM app is fully functional with all features implemented. Build issues are typically environment-related, not code problems!
