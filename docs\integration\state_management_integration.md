# State Management Integration Guide

## Overview
This guide explains how to integrate the MLM features with Riverpod state management for optimal performance and data consistency.

## Provider Structure

```dart
// lib/core/providers/app_providers.dart

// User Authentication Provider
final authProvider = StateNotifierProvider<AuthNotifier, AuthState>((ref) {
  return AuthNotifier();
});

// Current User Provider
final currentUserProvider = Provider<UserModel?>((ref) {
  final authState = ref.watch(authProvider);
  return authState.user;
});

// Dashboard Data Provider
final dashboardProvider = FutureProvider.family<AgentDashboardData, String>((ref, agentId) async {
  return DashboardService.getAgentDashboardData(agentId);
});

// Properties Provider
final propertiesProvider = StateNotifierProvider<PropertiesNotifier, PropertiesState>((ref) {
  return PropertiesNotifier();
});

// Commission Analytics Provider
final commissionAnalyticsProvider = FutureProvider.family<CommissionAnalytics, String>((ref, agentId) async {
  return CommissionAnalyticsService.getAgentAnalytics(agentId);
});

// Network Data Provider
final networkProvider = FutureProvider.family<NetworkNode?, String>((ref, agentId) async {
  return NetworkService.buildNetworkTree(agentId);
});

// Network Statistics Provider
final networkStatsProvider = FutureProvider.family<NetworkStatistics, String>((ref, agentId) async {
  return NetworkService.getNetworkStatistics(agentId);
});

// Reports Provider
final reportsProvider = StateNotifierProvider<ReportsNotifier, ReportsState>((ref) {
  return ReportsNotifier();
});

// Goals Provider
final goalsProvider = StateNotifierProvider.family<GoalsNotifier, GoalsState, String>((ref, agentId) {
  return GoalsNotifier(agentId);
});
```

## State Notifiers

```dart
// lib/core/providers/properties_provider.dart
class PropertiesNotifier extends StateNotifier<PropertiesState> {
  PropertiesNotifier() : super(const PropertiesState.loading()) {
    loadProperties();
  }

  Future<void> loadProperties() async {
    try {
      state = const PropertiesState.loading();
      final properties = await PropertyService.getAllProperties();
      state = PropertiesState.loaded(properties);
    } catch (e) {
      state = PropertiesState.error(e.toString());
    }
  }

  Future<void> addProperty(PropertyModel property) async {
    try {
      await PropertyService.addProperty(property);
      await loadProperties(); // Refresh the list
    } catch (e) {
      state = PropertiesState.error(e.toString());
    }
  }

  void updateFilters(PropertyFilters filters) {
    if (state is PropertiesLoaded) {
      final currentState = state as PropertiesLoaded;
      final filteredProperties = _applyFilters(currentState.properties, filters);
      state = PropertiesState.loaded(filteredProperties, filters: filters);
    }
  }

  List<PropertyModel> _applyFilters(List<PropertyModel> properties, PropertyFilters filters) {
    return properties.where((property) {
      // Apply filter logic
      if (filters.priceRange != null) {
        if (property.price < filters.priceRange!.start || 
            property.price > filters.priceRange!.end) {
          return false;
        }
      }
      
      if (filters.propertyType != null && property.type != filters.propertyType) {
        return false;
      }
      
      if (filters.location != null && !property.location.contains(filters.location!)) {
        return false;
      }
      
      return true;
    }).toList();
  }
}

// Properties State
@freezed
class PropertiesState with _$PropertiesState {
  const factory PropertiesState.loading() = PropertiesLoading;
  const factory PropertiesState.loaded(
    List<PropertyModel> properties, {
    PropertyFilters? filters,
  }) = PropertiesLoaded;
  const factory PropertiesState.error(String message) = PropertiesError;
}
```

```dart
// lib/core/providers/reports_provider.dart
class ReportsNotifier extends StateNotifier<ReportsState> {
  ReportsNotifier() : super(const ReportsState.initial()) {
    loadReportHistory();
  }

  Future<void> loadReportHistory() async {
    try {
      state = state.copyWith(isLoading: true);
      final reports = await ReportService.getReportHistory();
      state = state.copyWith(
        isLoading: false,
        reports: reports,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: e.toString(),
      );
    }
  }

  Future<void> generateReport(ReportConfig config) async {
    try {
      state = state.copyWith(isGenerating: true);
      final report = await ReportService.generateReport(config);
      
      // Add to reports list
      final updatedReports = [report, ...state.reports];
      state = state.copyWith(
        isGenerating: false,
        reports: updatedReports,
      );
    } catch (e) {
      state = state.copyWith(
        isGenerating: false,
        error: e.toString(),
      );
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// Reports State
@freezed
class ReportsState with _$ReportsState {
  const factory ReportsState.initial({
    @Default([]) List<GeneratedReport> reports,
    @Default(false) bool isLoading,
    @Default(false) bool isGenerating,
    String? error,
  }) = _ReportsState;
}
```

## Provider Dependencies

```dart
// lib/core/providers/dependent_providers.dart

// Dashboard provider that depends on multiple data sources
final enhancedDashboardProvider = FutureProvider.family<AgentDashboardData, String>((ref, agentId) async {
  // Watch multiple providers and combine their data
  final commissionAnalytics = await ref.watch(commissionAnalyticsProvider(agentId).future);
  final networkStats = await ref.watch(networkStatsProvider(agentId).future);
  
  // Combine data from multiple sources
  return DashboardService.buildEnhancedDashboard(
    agentId: agentId,
    commissionAnalytics: commissionAnalytics,
    networkStats: networkStats,
  );
});

// Filtered properties provider
final filteredPropertiesProvider = Provider.family<List<PropertyModel>, PropertyFilters>((ref, filters) {
  final propertiesState = ref.watch(propertiesProvider);
  
  return propertiesState.when(
    loading: () => [],
    loaded: (properties, _) => _applyFilters(properties, filters),
    error: (_) => [],
  );
});

// Commission summary provider
final commissionSummaryProvider = FutureProvider.family<CommissionSummary, String>((ref, agentId) async {
  final analytics = await ref.watch(commissionAnalyticsProvider(agentId).future);
  final networkStats = await ref.watch(networkStatsProvider(agentId).future);
  
  return CommissionSummary(
    totalEarned: analytics.totalEarned,
    teamCommissions: networkStats.totalCommissions,
    growthRate: analytics.growthRate,
    tierProgress: analytics.nextTierProgress,
  );
});
```

## Auto-Refresh Providers

```dart
// lib/core/providers/auto_refresh_providers.dart

// Auto-refreshing dashboard data
final autoRefreshDashboardProvider = StreamProvider.family<AgentDashboardData, String>((ref, agentId) {
  return Stream.periodic(const Duration(minutes: 5), (count) => count)
      .asyncMap((_) => DashboardService.getAgentDashboardData(agentId));
});

// Real-time commission updates
final realtimeCommissionsProvider = StreamProvider.family<List<EnhancedCommissionModel>, String>((ref, agentId) {
  return FirebaseFirestore.instance
      .collection('commissions')
      .where('agentId', isEqualTo: agentId)
      .orderBy('createdAt', descending: true)
      .limit(10)
      .snapshots()
      .map((snapshot) => snapshot.docs
          .map((doc) => EnhancedCommissionModel.fromFirestore(doc))
          .toList());
});

// Network updates stream
final networkUpdatesProvider = StreamProvider.family<NetworkNode?, String>((ref, agentId) {
  return Stream.periodic(const Duration(minutes: 10), (count) => count)
      .asyncMap((_) => NetworkService.buildNetworkTree(agentId));
});
```

## Provider Invalidation

```dart
// lib/core/providers/provider_invalidation.dart
class ProviderInvalidationService {
  static void invalidateUserData(WidgetRef ref, String agentId) {
    // Invalidate all user-related providers
    ref.invalidate(dashboardProvider(agentId));
    ref.invalidate(commissionAnalyticsProvider(agentId));
    ref.invalidate(networkProvider(agentId));
    ref.invalidate(networkStatsProvider(agentId));
    ref.invalidate(goalsProvider(agentId));
  }

  static void invalidateAfterPropertyUpdate(WidgetRef ref) {
    // Invalidate property-related providers
    ref.invalidate(propertiesProvider);
    
    // Also invalidate dashboard as it might show property stats
    final currentUser = ref.read(currentUserProvider);
    if (currentUser != null) {
      ref.invalidate(dashboardProvider(currentUser.id));
    }
  }

  static void invalidateAfterCommissionUpdate(WidgetRef ref, String agentId) {
    // Invalidate commission-related providers
    ref.invalidate(commissionAnalyticsProvider(agentId));
    ref.invalidate(dashboardProvider(agentId));
    
    // Also invalidate network stats as they include commission data
    ref.invalidate(networkStatsProvider(agentId));
  }
}
```

## Error Handling

```dart
// lib/core/providers/error_handling.dart
class ProviderErrorHandler {
  static Widget handleAsyncError(AsyncValue<dynamic> asyncValue, {
    required Widget Function() onLoading,
    required Widget Function(dynamic data) onData,
    Widget Function(Object error, StackTrace stackTrace)? onError,
  }) {
    return asyncValue.when(
      loading: onLoading,
      data: onData,
      error: onError ?? (error, stackTrace) => ErrorWidget(
        error: error,
        onRetry: () {
          // Implement retry logic
        },
      ),
    );
  }
}

// Usage in widgets
class DashboardWidget extends ConsumerWidget {
  final String agentId;
  
  const DashboardWidget({super.key, required this.agentId});
  
  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final dashboardAsync = ref.watch(dashboardProvider(agentId));
    
    return ProviderErrorHandler.handleAsyncError(
      dashboardAsync,
      onLoading: () => const LoadingWidget(),
      onData: (data) => DashboardContent(data: data),
      onError: (error, stackTrace) => ErrorWidget(
        error: error,
        onRetry: () => ref.invalidate(dashboardProvider(agentId)),
      ),
    );
  }
}
```

## Performance Optimization

```dart
// lib/core/providers/performance_optimization.dart

// Cached providers for expensive operations
final cachedNetworkProvider = FutureProvider.family<NetworkNode?, String>((ref, agentId) async {
  // Keep the result for 10 minutes
  ref.keepAlive();
  Timer(const Duration(minutes: 10), () {
    ref.invalidateSelf();
  });
  
  return NetworkService.buildNetworkTree(agentId);
});

// Debounced search provider
final debouncedSearchProvider = FutureProvider.family<List<PropertyModel>, String>((ref, query) async {
  // Debounce search requests
  await Future.delayed(const Duration(milliseconds: 500));
  
  // Check if the query is still the same
  if (ref.state.hasValue && query != ref.state.value) {
    throw Exception('Query changed');
  }
  
  return PropertyService.searchProperties(query);
});

// Paginated data provider
final paginatedPropertiesProvider = StateNotifierProvider.family<PaginatedPropertiesNotifier, PaginatedState<PropertyModel>, PropertyFilters>((ref, filters) {
  return PaginatedPropertiesNotifier(filters);
});
```
