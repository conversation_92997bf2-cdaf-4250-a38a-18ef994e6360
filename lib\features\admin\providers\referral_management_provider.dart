import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../services/referral_management_service.dart';

/// Provider for all agents with referral information
final agentsWithReferralsProvider = FutureProvider<List<AgentReferralInfo>>((
  ref,
) async {
  return await ReferralManagementService.getAllAgentsWithReferrals();
});

/// Provider for referral analytics
final referralAnalyticsProvider = FutureProvider<ReferralAnalytics>((
  ref,
) async {
  return await ReferralManagementService.getReferralAnalytics();
});

/// Provider for referral management state
final referralManagementProvider =
    StateNotifierProvider<ReferralManagementNotifier, ReferralManagementState>((
      ref,
    ) {
      return ReferralManagementNotifier();
    });

/// Referral management state
class ReferralManagementState {
  final bool isLoading;
  final String? error;
  final String? successMessage;
  final List<AgentReferralInfo> agents;
  final ReferralAnalytics? analytics;
  final String searchQuery;
  final ReferralSortOption sortOption;
  final bool sortAscending;

  const ReferralManagementState({
    this.isLoading = false,
    this.error,
    this.successMessage,
    this.agents = const [],
    this.analytics,
    this.searchQuery = '',
    this.sortOption = ReferralSortOption.name,
    this.sortAscending = true,
  });

  ReferralManagementState copyWith({
    bool? isLoading,
    String? error,
    String? successMessage,
    List<AgentReferralInfo>? agents,
    ReferralAnalytics? analytics,
    String? searchQuery,
    ReferralSortOption? sortOption,
    bool? sortAscending,
  }) {
    return ReferralManagementState(
      isLoading: isLoading ?? this.isLoading,
      error: error,
      successMessage: successMessage,
      agents: agents ?? this.agents,
      analytics: analytics ?? this.analytics,
      searchQuery: searchQuery ?? this.searchQuery,
      sortOption: sortOption ?? this.sortOption,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  /// Get filtered and sorted agents
  List<AgentReferralInfo> get filteredAgents {
    var filtered = agents;

    // Apply search filter
    if (searchQuery.isNotEmpty) {
      filtered = filtered.where((agent) {
        final query = searchQuery.toLowerCase();
        return agent.agent.name.toLowerCase().contains(query) ||
            agent.agent.email.toLowerCase().contains(query) ||
            agent.referralCode.toLowerCase().contains(query) ||
            agent.uplineReferralCode.toLowerCase().contains(query);
      }).toList();
    }

    // Apply sorting
    filtered.sort((a, b) {
      int comparison;
      switch (sortOption) {
        case ReferralSortOption.name:
          comparison = a.agent.name.compareTo(b.agent.name);
          break;
        case ReferralSortOption.referralCode:
          comparison = a.referralCode.compareTo(b.referralCode);
          break;
        case ReferralSortOption.downlineCount:
          comparison = a.downlineCount.compareTo(b.downlineCount);
          break;
        case ReferralSortOption.commissions:
          comparison = a.totalCommissions.compareTo(b.totalCommissions);
          break;
        case ReferralSortOption.joinDate:
          comparison = a.agent.createdAt.compareTo(b.agent.createdAt);
          break;
        case ReferralSortOption.level:
          comparison = a.agent.level.compareTo(b.agent.level);
          break;
      }
      return sortAscending ? comparison : -comparison;
    });

    return filtered;
  }
}

/// Referral management notifier
class ReferralManagementNotifier
    extends StateNotifier<ReferralManagementState> {
  ReferralManagementNotifier() : super(const ReferralManagementState());

  /// Load agents and analytics
  Future<void> loadData() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final agents =
          await ReferralManagementService.getAllAgentsWithReferrals();
      final analytics = await ReferralManagementService.getReferralAnalytics();

      state = state.copyWith(
        isLoading: false,
        agents: agents,
        analytics: analytics,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load data: ${e.toString()}',
      );
    }
  }

  /// Update agent referral code
  Future<void> updateReferralCode(
    String agentId,
    String newReferralCode,
  ) async {
    state = state.copyWith(isLoading: true, error: null, successMessage: null);

    try {
      final result = await ReferralManagementService.updateAgentReferralCode(
        agentId: agentId,
        newReferralCode: newReferralCode,
      );

      if (result.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          successMessage: result.message,
        );
        // Reload data to reflect changes
        await loadData();
      } else {
        state = state.copyWith(isLoading: false, error: result.message);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update referral code: ${e.toString()}',
      );
    }
  }

  /// Update agent upline
  Future<void> updateUpline(
    String agentId,
    String newUplineReferralCode,
  ) async {
    state = state.copyWith(isLoading: true, error: null, successMessage: null);

    try {
      final result = await ReferralManagementService.updateAgentUpline(
        agentId: agentId,
        newUplineReferralCode: newUplineReferralCode,
      );

      if (result.isSuccess) {
        state = state.copyWith(
          isLoading: false,
          successMessage: result.message,
        );
        // Reload data to reflect changes
        await loadData();
      } else {
        state = state.copyWith(isLoading: false, error: result.message);
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to update upline: ${e.toString()}',
      );
    }
  }

  /// Update search query
  void updateSearchQuery(String query) {
    state = state.copyWith(searchQuery: query);
  }

  /// Update sort option
  void updateSortOption(ReferralSortOption option) {
    if (state.sortOption == option) {
      // Toggle sort direction if same option
      state = state.copyWith(sortAscending: !state.sortAscending);
    } else {
      // Change sort option and reset to ascending
      state = state.copyWith(sortOption: option, sortAscending: true);
    }
  }

  /// Clear messages
  void clearMessages() {
    state = state.copyWith(error: null, successMessage: null);
  }

  /// Refresh data
  Future<void> refresh() async {
    await loadData();
  }
}

/// Sort options for referral management
enum ReferralSortOption {
  name,
  referralCode,
  downlineCount,
  commissions,
  joinDate,
  level,
}

/// Extension for sort option display names
extension ReferralSortOptionExtension on ReferralSortOption {
  String get displayName {
    switch (this) {
      case ReferralSortOption.name:
        return 'Name';
      case ReferralSortOption.referralCode:
        return 'Referral Code';
      case ReferralSortOption.downlineCount:
        return 'Downline Count';
      case ReferralSortOption.commissions:
        return 'Commissions';
      case ReferralSortOption.joinDate:
        return 'Join Date';
      case ReferralSortOption.level:
        return 'Level';
    }
  }

  IconData get icon {
    switch (this) {
      case ReferralSortOption.name:
        return Icons.person;
      case ReferralSortOption.referralCode:
        return Icons.code;
      case ReferralSortOption.downlineCount:
        return Icons.group;
      case ReferralSortOption.commissions:
        return Icons.currency_rupee;
      case ReferralSortOption.joinDate:
        return Icons.calendar_today;
      case ReferralSortOption.level:
        return Icons.stairs;
    }
  }
}

/// Provider for filtered agents based on current state
final filteredAgentsProvider = Provider<List<AgentReferralInfo>>((ref) {
  final state = ref.watch(referralManagementProvider);
  return state.filteredAgents;
});
