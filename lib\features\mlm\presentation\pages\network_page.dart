import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/mlm_providers.dart';
import '../widgets/network_tree_widget.dart';
import '../widgets/network_stats_widget.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/providers/app_providers.dart' as core_providers;
import '../../../../shared/themes/app_theme.dart';

class NetworkPage extends ConsumerStatefulWidget {
  const NetworkPage({super.key});

  @override
  ConsumerState<NetworkPage> createState() => _NetworkPageState();
}

class _NetworkPageState extends ConsumerState<NetworkPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final networkTreeAsync = ref.watch(networkTreeProvider);
    final viewMode = ref.watch(networkViewModeProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('MLM Network'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          PopupMenuButton<NetworkViewMode>(
            icon: const Icon(Icons.view_module),
            onSelected: (mode) {
              ref.read(networkViewModeProvider.notifier).state = mode;
            },
            itemBuilder: (context) => [
              const PopupMenuItem(
                value: NetworkViewMode.tree,
                child: Row(
                  children: [
                    Icon(Icons.account_tree),
                    SizedBox(width: 8),
                    Text('Tree View'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: NetworkViewMode.list,
                child: Row(
                  children: [
                    Icon(Icons.list),
                    SizedBox(width: 8),
                    Text('List View'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: NetworkViewMode.grid,
                child: Row(
                  children: [
                    Icon(Icons.grid_view),
                    SizedBox(width: 8),
                    Text('Grid View'),
                  ],
                ),
              ),
              const PopupMenuItem(
                value: NetworkViewMode.chart,
                child: Row(
                  children: [
                    Icon(Icons.bar_chart),
                    SizedBox(width: 8),
                    Text('Analytics'),
                  ],
                ),
              ),
            ],
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.account_tree), text: 'Network'),
            Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
            Tab(icon: Icon(Icons.people), text: 'Team'),
            Tab(icon: Icon(Icons.timeline), text: 'Activity'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // Network Tab
          _buildNetworkTab(networkTreeAsync, viewMode),

          // Analytics Tab
          _buildAnalyticsTab(),

          // Team Tab
          _buildTeamTab(),

          // Activity Tab
          _buildActivityTab(),
        ],
      ),
    );
  }

  Widget _buildNetworkTab(AsyncValue networkTreeAsync, NetworkViewMode viewMode) {
    return networkTreeAsync.when(
      data: (networkTree) {
        if (networkTree == null) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.account_tree, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No network data available'),
                Text('Start building your network by sharing your referral code'),
              ],
            ),
          );
        }

        switch (viewMode) {
          case NetworkViewMode.tree:
            return NetworkTreeWidget(
              rootNode: networkTree,
              onUserTap: _showUserDetails,
            );
          case NetworkViewMode.list:
            return _buildListView();
          case NetworkViewMode.grid:
            return _buildGridView();
          case NetworkViewMode.chart:
            return _buildAnalyticsTab();
        }
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading network: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(networkTreeProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsTab() {
    final networkStatsAsync = ref.watch(networkStatsProvider);
    final networkPerformanceAsync = ref.watch(networkPerformanceProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        children: [
          // Network Stats
          networkStatsAsync.when(
            data: (stats) {
              if (stats == null) return const SizedBox.shrink();

              return networkPerformanceAsync.when(
                data: (performance) {
                  final totalSize = performance['totalNetworkSize'] ?? 0;
                  return NetworkStatsWidget(
                    stats: stats,
                    totalNetworkSize: totalSize,
                  );
                },
                loading: () => const CircularProgressIndicator(),
                error: (_, __) => const Text('Error loading performance data'),
              );
            },
            loading: () => const CircularProgressIndicator(),
            error: (error, _) => Text('Error: $error'),
          ),
        ],
      ),
    );
  }

  Widget _buildTeamTab() {
    final directDownlinesAsync = ref.watch(directDownlinesProvider);
    final topPerformersAsync = ref.watch(topPerformersProvider(
      const TopPerformersParams(limit: 10, sortBy: 'totalStars'),
    ));

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Direct Team
          Text(
            'Direct Team',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          directDownlinesAsync.when(
            data: (downlines) => _buildTeamList(downlines),
            loading: () => const CircularProgressIndicator(),
            error: (error, _) => Text('Error: $error'),
          ),

          const SizedBox(height: 32),

          // Top Performers
          Text(
            'Top Performers',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          topPerformersAsync.when(
            data: (performers) => _buildTopPerformersList(performers),
            loading: () => const CircularProgressIndicator(),
            error: (error, _) => Text('Error: $error'),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityTab() {
    final recentActivitiesAsync = ref.watch(recentActivitiesProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Activities',
            style: Theme.of(context).textTheme.titleLarge?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          recentActivitiesAsync.when(
            data: (activities) => _buildActivitiesList(activities),
            loading: () => const CircularProgressIndicator(),
            error: (error, _) => Text('Error: $error'),
          ),
        ],
      ),
    );
  }

  Widget _buildListView() {
    final filteredDataAsync = ref.watch(filteredNetworkDataProvider);

    return Column(
      children: [
        // Filter bar
        _buildFilterBar(),

        // List
        Expanded(
          child: filteredDataAsync.when(
            data: (users) => ListView.builder(
              itemCount: users.length,
              itemBuilder: (context, index) {
                final user = users[index];
                return _buildUserListTile(user);
              },
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => Center(child: Text('Error: $error')),
          ),
        ),
      ],
    );
  }

  Widget _buildGridView() {
    final filteredDataAsync = ref.watch(filteredNetworkDataProvider);

    return Column(
      children: [
        // Filter bar
        _buildFilterBar(),

        // Grid
        Expanded(
          child: filteredDataAsync.when(
            data: (users) => GridView.builder(
              padding: const EdgeInsets.all(16),
              gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
                crossAxisCount: 2,
                crossAxisSpacing: 16,
                mainAxisSpacing: 16,
                childAspectRatio: 0.8,
              ),
              itemCount: users.length,
              itemBuilder: (context, index) {
                final user = users[index];
                return _buildUserCard(user);
              },
            ),
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => Center(child: Text('Error: $error')),
          ),
        ),
      ],
    );
  }

  Widget _buildFilterBar() {
    final filter = ref.watch(networkFilterProvider);
    final levelsAsync = ref.watch(networkLevelsProvider);

    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            Row(
              children: [
                // Level filter
                Expanded(
                  child: levelsAsync.when(
                    data: (levels) => DropdownButtonFormField<int?>(
                      value: filter.selectedLevel,
                      decoration: const InputDecoration(
                        labelText: 'Filter by Level',
                        border: OutlineInputBorder(),
                      ),
                      items: [
                        const DropdownMenuItem<int?>(
                          value: null,
                          child: Text('All Levels'),
                        ),
                        ...levels.map((level) => DropdownMenuItem<int?>(
                              value: level,
                              child: Text('Level $level'),
                            )),
                      ],
                      onChanged: (value) {
                        ref.read(networkFilterProvider.notifier).setLevel(value);
                      },
                    ),
                    loading: () => const CircularProgressIndicator(),
                    error: (_, __) => const Text('Error loading levels'),
                  ),
                ),

                const SizedBox(width: 16),

                // Active only toggle
                Expanded(
                  child: CheckboxListTile(
                    title: const Text('Active Only'),
                    value: filter.showActiveOnly,
                    onChanged: (value) {
                      ref.read(networkFilterProvider.notifier).setShowActiveOnly(value ?? true);
                    },
                  ),
                ),
              ],
            ),

            const SizedBox(height: 16),

            // Sort options
            Row(
              children: [
                Expanded(
                  child: DropdownButtonFormField<String>(
                    value: filter.sortBy,
                    decoration: const InputDecoration(
                      labelText: 'Sort by',
                      border: OutlineInputBorder(),
                    ),
                    items: const [
                      DropdownMenuItem(value: 'name', child: Text('Name')),
                      DropdownMenuItem(value: 'level', child: Text('Level')),
                      DropdownMenuItem(value: 'stars', child: Text('Stars')),
                      DropdownMenuItem(value: 'commissions', child: Text('Commissions')),
                      DropdownMenuItem(value: 'joinDate', child: Text('Join Date')),
                    ],
                    onChanged: (value) {
                      if (value != null) {
                        ref.read(networkFilterProvider.notifier).setSortBy(value);
                      }
                    },
                  ),
                ),

                const SizedBox(width: 16),

                IconButton(
                  icon: Icon(
                    filter.sortAscending ? Icons.arrow_upward : Icons.arrow_downward,
                  ),
                  onPressed: () {
                    ref.read(networkFilterProvider.notifier).setSortAscending(!filter.sortAscending);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUserListTile(UserModel user) {
    return ListTile(
      leading: CircleAvatar(
        backgroundColor: Theme.of(context).primaryColor,
        backgroundImage: user.profileImageUrl != null
            ? NetworkImage(user.profileImageUrl!)
            : null,
        child: user.profileImageUrl == null
            ? Text(
                user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                style: const TextStyle(color: Colors.white),
              )
            : null,
      ),
      title: Text(user.name),
      subtitle: Text('Level ${user.level} • ${user.totalStars} stars'),
      trailing: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        crossAxisAlignment: CrossAxisAlignment.end,
        children: [
          Text(
            '₹${user.totalCommissions.toStringAsFixed(0)}',
            style: const TextStyle(fontWeight: FontWeight.bold),
          ),
          Text(
            '${user.downlineIds.length} downlines',
            style: Theme.of(context).textTheme.bodySmall,
          ),
        ],
      ),
      onTap: () => _showUserDetails(user),
    );
  }

  Widget _buildUserCard(UserModel user) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: [
            CircleAvatar(
              radius: 30,
              backgroundColor: Theme.of(context).primaryColor,
              backgroundImage: user.profileImageUrl != null
                  ? NetworkImage(user.profileImageUrl!)
                  : null,
              child: user.profileImageUrl == null
                  ? Text(
                      user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                      style: const TextStyle(color: Colors.white, fontSize: 20),
                    )
                  : null,
            ),

            const SizedBox(height: 8),

            Text(
              user.name,
              style: const TextStyle(fontWeight: FontWeight.bold),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),

            const SizedBox(height: 4),

            Text(
              'Level ${user.level}',
              style: Theme.of(context).textTheme.bodySmall,
            ),

            const SizedBox(height: 8),

            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                Column(
                  children: [
                    const Icon(Icons.star, size: 16, color: Colors.amber),
                    Text(
                      '${user.totalStars}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
                Column(
                  children: [
                    const Icon(Icons.group, size: 16, color: Colors.blue),
                    Text(
                      '${user.downlineIds.length}',
                      style: const TextStyle(fontSize: 12),
                    ),
                  ],
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildTeamList(List<UserModel> team) {
    if (team.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.people_outline, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text('No team members yet'),
                Text('Share your referral code to build your team'),
              ],
            ),
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: team.length,
      itemBuilder: (context, index) {
        return _buildUserListTile(team[index]);
      },
    );
  }

  Widget _buildTopPerformersList(List<UserModel> performers) {
    if (performers.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Center(
            child: Text('No performance data available'),
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: performers.length,
      itemBuilder: (context, index) {
        final user = performers[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getRankColor(index),
            child: Text(
              '${index + 1}',
              style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
            ),
          ),
          title: Text(user.name),
          subtitle: Text('${user.totalStars} stars • ₹${user.totalCommissions.toStringAsFixed(0)}'),
          trailing: Icon(
            _getRankIcon(index),
            color: _getRankColor(index),
          ),
          onTap: () => _showUserDetails(user),
        );
      },
    );
  }

  Widget _buildActivitiesList(List<Map<String, dynamic>> activities) {
    if (activities.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(32),
          child: Center(
            child: Column(
              children: [
                Icon(Icons.timeline, size: 48, color: Colors.grey),
                SizedBox(height: 16),
                Text('No recent activities'),
              ],
            ),
          ),
        ),
      );
    }

    return ListView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      itemCount: activities.length,
      itemBuilder: (context, index) {
        final activity = activities[index];
        return ListTile(
          leading: CircleAvatar(
            backgroundColor: _getActivityColor(activity['type']),
            child: Icon(
              _getActivityIcon(activity['type']),
              color: Colors.white,
            ),
          ),
          title: Text(activity['description']),
          subtitle: Text(_formatTimestamp(activity['timestamp'])),
          onTap: () {
            // Navigate to user details if applicable
            if (activity['userId'] != null) {
              // TODO: Navigate to user details
            }
          },
        );
      },
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Network'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Search by name, email, or phone',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (query) {
                ref.read(networkSearchProvider.notifier).searchUsers(query);
              },
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final searchState = ref.watch(networkSearchProvider);

                if (searchState.isLoading) {
                  return const CircularProgressIndicator();
                }

                if (searchState.error != null) {
                  return Text(
                    searchState.error!,
                    style: const TextStyle(color: Colors.red),
                  );
                }

                if (searchState.results.isEmpty && searchState.query.isNotEmpty) {
                  return const Text('No results found');
                }

                return SizedBox(
                  height: 200,
                  width: double.maxFinite,
                  child: ListView.builder(
                    itemCount: searchState.results.length,
                    itemBuilder: (context, index) {
                      final user = searchState.results[index];
                      return ListTile(
                        title: Text(user.name),
                        subtitle: Text('Level ${user.level}'),
                        onTap: () {
                          Navigator.of(context).pop();
                          _showUserDetails(user);
                        },
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(networkSearchProvider.notifier).clearSearch();
              Navigator.of(context).pop();
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showUserDetails(UserModel user) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 16),

              // User info
              CircleAvatar(
                radius: 40,
                backgroundColor: Theme.of(context).primaryColor,
                backgroundImage: user.profileImageUrl != null
                    ? NetworkImage(user.profileImageUrl!)
                    : null,
                child: user.profileImageUrl == null
                    ? Text(
                        user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                        style: const TextStyle(color: Colors.white, fontSize: 24),
                      )
                    : null,
              ),

              const SizedBox(height: 16),

              Text(
                user.name,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),

              Text(
                user.email,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),

              const SizedBox(height: 24),

              // Stats
              Expanded(
                child: ListView(
                  controller: scrollController,
                  children: [
                    _buildDetailCard('Level', 'Level ${user.level}', Icons.layers),
                    _buildDetailCard('Total Stars', '${user.totalStars}', Icons.star),
                    _buildDetailCard('Commissions', '₹${user.totalCommissions.toStringAsFixed(2)}', Icons.currency_rupee),
                    _buildDetailCard('Downlines', '${user.downlineIds.length}', Icons.group),
                    if (user.uplineId != null) _buildMentorCard(user.uplineId!),
                    _buildDetailCard('Phone', user.phoneNumber, Icons.phone),
                    _buildDetailCard('Joined', _formatDate(user.createdAt), Icons.calendar_today),
                  ],
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailCard(String title, String value, IconData icon) {
    return Card(
      child: ListTile(
        leading: Icon(icon),
        title: Text(title),
        trailing: Text(
          value,
          style: const TextStyle(fontWeight: FontWeight.bold),
        ),
      ),
    );
  }

  Widget _buildMentorCard(String mentorId) {
    final mentorAsync = ref.watch(core_providers.userUplineProvider(mentorId));

    return mentorAsync.when(
      data: (mentor) {
        if (mentor == null) return const SizedBox.shrink();

        return Card(
          color: AppTheme.primaryColor.withOpacity(0.1),
          child: ListTile(
            leading: Icon(
              Icons.school,
              color: AppTheme.primaryColor,
            ),
            title: const Text(
              'Agent Mentor',
              style: TextStyle(
                fontWeight: FontWeight.w600,
                color: AppTheme.primaryColor,
              ),
            ),
            trailing: Text(
              mentor.name,
              style: const TextStyle(
                fontWeight: FontWeight.bold,
                color: Colors.white,
              ),
            ),
            onTap: () => _showUserDetails(mentor),
          ),
        );
      },
      loading: () => Card(
        child: ListTile(
          leading: Icon(Icons.school, color: Colors.grey),
          title: Text('Agent Mentor'),
          trailing: Text('Loading...'),
        ),
      ),
      error: (_, __) => const SizedBox.shrink(),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 0: return Colors.amber; // Gold
      case 1: return Colors.grey; // Silver
      case 2: return Colors.brown; // Bronze
      default: return Colors.blue;
    }
  }

  IconData _getRankIcon(int rank) {
    switch (rank) {
      case 0: return Icons.emoji_events;
      case 1: return Icons.military_tech;
      case 2: return Icons.workspace_premium;
      default: return Icons.star;
    }
  }

  Color _getActivityColor(String type) {
    switch (type) {
      case 'new_member': return Colors.green;
      case 'sale': return Colors.blue;
      case 'commission': return Colors.orange;
      default: return Colors.grey;
    }
  }

  IconData _getActivityIcon(String type) {
    switch (type) {
      case 'new_member': return Icons.person_add;
      case 'sale': return Icons.shopping_cart;
      case 'commission': return Icons.currency_rupee;
      default: return Icons.notifications;
    }
  }

  String _formatTimestamp(DateTime timestamp) {
    final now = DateTime.now();
    final difference = now.difference(timestamp);

    if (difference.inDays > 0) {
      return '${difference.inDays} days ago';
    } else if (difference.inHours > 0) {
      return '${difference.inHours} hours ago';
    } else if (difference.inMinutes > 0) {
      return '${difference.inMinutes} minutes ago';
    } else {
      return 'Just now';
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }
}
