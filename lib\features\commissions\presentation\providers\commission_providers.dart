import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/commission_model.dart';
import '../../../../core/services/commission_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

/// Agent commissions provider
final agentCommissionsProvider = FutureProvider.autoDispose<List<CommissionModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  return await CommissionService.getAgentCommissions(currentUser.id);
});

/// All commissions provider (Admin only)
final allCommissionsProvider = FutureProvider.autoDispose<List<CommissionModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) return [];
  
  return await CommissionService.getAllCommissions();
});

/// All transactions provider (Admin only)
final allTransactionsProvider = FutureProvider.autoDispose<List<TransactionModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) return [];
  
  return await CommissionService.getAllTransactions();
});

/// Commission statistics provider (Admin only)
final commissionStatsProvider = FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) return {};
  
  return await CommissionService.getCommissionStatistics();
});

/// Transaction form provider
final transactionFormProvider = StateNotifierProvider<TransactionFormNotifier, TransactionFormState>((ref) {
  return TransactionFormNotifier();
});

/// Commission filter provider
final commissionFilterProvider = StateNotifierProvider<CommissionFilterNotifier, CommissionFilter>((ref) {
  return CommissionFilterNotifier();
});

/// Filtered commissions provider
final filteredCommissionsProvider = FutureProvider.autoDispose<List<CommissionModel>>((ref) async {
  final filter = ref.watch(commissionFilterProvider);
  final currentUser = ref.watch(currentUserProvider);
  
  if (currentUser == null) return [];
  
  List<CommissionModel> commissions;
  if (currentUser.isAdmin) {
    commissions = await CommissionService.getAllCommissions();
  } else {
    commissions = await CommissionService.getAgentCommissions(currentUser.id);
  }
  
  // Apply filters
  if (filter.status != null) {
    commissions = commissions.where((c) => c.status == filter.status).toList();
  }
  
  if (filter.level != null) {
    commissions = commissions.where((c) => c.level == filter.level).toList();
  }
  
  if (filter.startDate != null) {
    commissions = commissions.where((c) => c.createdAt.isAfter(filter.startDate!)).toList();
  }
  
  if (filter.endDate != null) {
    commissions = commissions.where((c) => c.createdAt.isBefore(filter.endDate!)).toList();
  }
  
  // Sort commissions
  commissions.sort((a, b) {
    switch (filter.sortBy) {
      case 'amount':
        return filter.sortAscending ? a.amount.compareTo(b.amount) : b.amount.compareTo(a.amount);
      case 'level':
        return filter.sortAscending ? a.level.compareTo(b.level) : b.level.compareTo(a.level);
      case 'status':
        return filter.sortAscending ? a.status.compareTo(b.status) : b.status.compareTo(a.status);
      case 'date':
      default:
        return filter.sortAscending ? a.createdAt.compareTo(b.createdAt) : b.createdAt.compareTo(a.createdAt);
    }
  });
  
  return commissions;
});

/// Transaction form state
class TransactionFormState {
  final String propertyId;
  final String propertyTitle;
  final String agentId;
  final String agentName;
  final double propertyAmount;
  final double commissionAmount;
  final String type;
  final String buyerName;
  final String buyerContact;
  final String notes;
  final bool isLoading;
  final String? error;

  const TransactionFormState({
    this.propertyId = '',
    this.propertyTitle = '',
    this.agentId = '',
    this.agentName = '',
    this.propertyAmount = 0.0,
    this.commissionAmount = 0.0,
    this.type = 'sale',
    this.buyerName = '',
    this.buyerContact = '',
    this.notes = '',
    this.isLoading = false,
    this.error,
  });

  TransactionFormState copyWith({
    String? propertyId,
    String? propertyTitle,
    String? agentId,
    String? agentName,
    double? propertyAmount,
    double? commissionAmount,
    String? type,
    String? buyerName,
    String? buyerContact,
    String? notes,
    bool? isLoading,
    String? error,
  }) {
    return TransactionFormState(
      propertyId: propertyId ?? this.propertyId,
      propertyTitle: propertyTitle ?? this.propertyTitle,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      propertyAmount: propertyAmount ?? this.propertyAmount,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      type: type ?? this.type,
      buyerName: buyerName ?? this.buyerName,
      buyerContact: buyerContact ?? this.buyerContact,
      notes: notes ?? this.notes,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }

  double get commissionRate => propertyAmount > 0 ? (commissionAmount / propertyAmount) * 100 : 0;
}

/// Transaction form notifier
class TransactionFormNotifier extends StateNotifier<TransactionFormState> {
  TransactionFormNotifier() : super(const TransactionFormState());

  void updateProperty(String propertyId, String propertyTitle) {
    state = state.copyWith(propertyId: propertyId, propertyTitle: propertyTitle, error: null);
  }

  void updateAgent(String agentId, String agentName) {
    state = state.copyWith(agentId: agentId, agentName: agentName, error: null);
  }

  void updatePropertyAmount(double amount) {
    state = state.copyWith(propertyAmount: amount, error: null);
  }

  void updateCommissionAmount(double amount) {
    state = state.copyWith(commissionAmount: amount, error: null);
  }

  void updateType(String type) {
    state = state.copyWith(type: type, error: null);
  }

  void updateBuyerName(String name) {
    state = state.copyWith(buyerName: name, error: null);
  }

  void updateBuyerContact(String contact) {
    state = state.copyWith(buyerContact: contact, error: null);
  }

  void updateNotes(String notes) {
    state = state.copyWith(notes: notes, error: null);
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(error: error, isLoading: false);
  }

  void resetForm() {
    state = const TransactionFormState();
  }

  String? validateForm() {
    if (state.propertyId.isEmpty) return 'Property is required';
    if (state.agentId.isEmpty) return 'Agent is required';
    if (state.propertyAmount <= 0) return 'Property amount must be greater than 0';
    if (state.commissionAmount <= 0) return 'Commission amount must be greater than 0';
    if (state.commissionAmount > state.propertyAmount) return 'Commission cannot exceed property amount';
    return null;
  }
}

/// Commission filter
class CommissionFilter {
  final String? status;
  final int? level;
  final DateTime? startDate;
  final DateTime? endDate;
  final String sortBy;
  final bool sortAscending;

  const CommissionFilter({
    this.status,
    this.level,
    this.startDate,
    this.endDate,
    this.sortBy = 'date',
    this.sortAscending = false,
  });

  CommissionFilter copyWith({
    String? status,
    int? level,
    DateTime? startDate,
    DateTime? endDate,
    String? sortBy,
    bool? sortAscending,
  }) {
    return CommissionFilter(
      status: status ?? this.status,
      level: level ?? this.level,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommissionFilter &&
        other.status == status &&
        other.level == level &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.sortBy == sortBy &&
        other.sortAscending == sortAscending;
  }

  @override
  int get hashCode => Object.hash(status, level, startDate, endDate, sortBy, sortAscending);
}

/// Commission filter notifier
class CommissionFilterNotifier extends StateNotifier<CommissionFilter> {
  CommissionFilterNotifier() : super(const CommissionFilter());

  void setStatus(String? status) {
    state = state.copyWith(status: status);
  }

  void setLevel(int? level) {
    state = state.copyWith(level: level);
  }

  void setDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);
  }

  void setSortBy(String sortBy) {
    state = state.copyWith(sortBy: sortBy);
  }

  void setSortAscending(bool ascending) {
    state = state.copyWith(sortAscending: ascending);
  }

  void resetFilters() {
    state = const CommissionFilter();
  }
}
