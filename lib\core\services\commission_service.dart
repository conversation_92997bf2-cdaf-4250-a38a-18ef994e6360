import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/commission_model.dart';
import '../models/user_model.dart';
import '../models/property_model.dart';
import '../constants/app_constants.dart';
import 'mlm_service.dart';
import 'star_service.dart';

/// Service for managing commission operations
class CommissionService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Create transaction and distribute commissions (Admin only)
  /// This is the main function where admin manually enters commission amount
  static Future<CommissionResult> createTransactionWithCommission({
    required String propertyId,
    required String agentId,
    required double propertyAmount,
    required double commissionAmount, // Manually entered by admin
    required String type, // 'sale' or 'rental'
    String? buyerName,
    String? buyerContact,
    String? notes,
  }) async {
    try {
      // Get property details
      final propertyDoc = await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyId)
          .get();

      if (!propertyDoc.exists) {
        return CommissionResult.failure('Property not found');
      }

      final property = PropertyModel.fromFirestore(propertyDoc);

      // Get selling agent details
      final agentDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .get();

      if (!agentDoc.exists) {
        return CommissionResult.failure('Agent not found');
      }

      final agent = UserModel.fromFirestore(agentDoc);

      // Calculate commission rate as percentage of property amount
      final commissionRate = (commissionAmount / propertyAmount) * 100;

      // Create transaction
      final transaction = TransactionModel(
        id: '',
        propertyId: propertyId,
        propertyTitle: property.title,
        agentId: agentId,
        agentName: agent.name,
        buyerName: buyerName,
        buyerContact: buyerContact,
        type: type,
        propertyAmount: propertyAmount,
        commissionAmount: commissionAmount,
        commissionRate: commissionRate,
        status: 'completed',
        completedAt: DateTime.now(),
        createdAt: DateTime.now(),
        notes: notes,
      );

      // Save transaction to Firestore
      final transactionRef = await _firestore
          .collection(AppConstants.transactionsCollection)
          .add(transaction.toFirestore());

      final savedTransaction = transaction.copyWith(id: transactionRef.id);

      // Update transaction with ID
      await transactionRef.update({'id': transactionRef.id});

      // Distribute commissions through MLM hierarchy
      await _distributeCommissions(savedTransaction, agent);

      // Update property status
      await _updatePropertyStatus(propertyId, type);

      // Award stars to selling agent and uplines using the new star service
      await _awardStarsUsingStarService(savedTransaction, agent);

      return CommissionResult.success(savedTransaction);
    } catch (e) {
      return CommissionResult.failure(
        'Failed to create transaction: ${e.toString()}',
      );
    }
  }

  /// Distribute commissions through MLM hierarchy (up to 5 levels)
  static Future<void> _distributeCommissions(
    TransactionModel transaction,
    UserModel sellingAgent,
  ) async {
    try {
      final commissions = <CommissionModel>[];

      // Get upline chain (up to 5 levels)
      final uplineChain = await MLMService.getUserUplineChain(sellingAgent.id);

      // Add selling agent as level 0 (direct sale)
      final allAgents = [sellingAgent, ...uplineChain];

      // Distribute commissions based on predefined rates
      for (
        int i = 0;
        i < allAgents.length && i < AppConstants.maxMLMLevels;
        i++
      ) {
        final agent = allAgents[i];
        final level = i;
        final rate = AppConstants.defaultCommissionRates[level] ?? 0.0;

        if (rate > 0) {
          final commissionAmount = transaction.commissionAmount * rate;

          final commission = CommissionModel(
            id: '',
            transactionId: transaction.id,
            agentId: agent.id,
            agentName: agent.name,
            amount: commissionAmount,
            level: level,
            rate: rate,
            status: 'pending',
            createdAt: DateTime.now(),
          );

          commissions.add(commission);
        }
      }

      // Save all commissions to Firestore
      final batch = _firestore.batch();

      for (final commission in commissions) {
        final commissionRef = _firestore
            .collection(AppConstants.commissionsCollection)
            .doc();

        final commissionWithId = commission.copyWith(id: commissionRef.id);
        batch.set(commissionRef, commissionWithId.toFirestore());
      }

      await batch.commit();

      // Update user total commissions
      await _updateUserCommissions(commissions);
    } catch (e) {
      print('Error distributing commissions: $e');
    }
  }

  /// Update user total commissions
  static Future<void> _updateUserCommissions(
    List<CommissionModel> commissions,
  ) async {
    try {
      final batch = _firestore.batch();

      for (final commission in commissions) {
        final userRef = _firestore
            .collection(AppConstants.usersCollection)
            .doc(commission.agentId);

        batch.update(userRef, {
          'totalCommissions': FieldValue.increment(commission.amount),
          'updatedAt': Timestamp.now(),
        });
      }

      await batch.commit();
    } catch (e) {
      print('Error updating user commissions: $e');
    }
  }

  /// Award stars using the new star service
  static Future<void> _awardStarsUsingStarService(
    TransactionModel transaction,
    UserModel sellingAgent,
  ) async {
    try {
      // Use the new star service to award stars according to the enhanced rules
      await StarService.awardStarsForTransaction(
        transactionId: transaction.id,
        sellingAgentId: sellingAgent.id,
        propertyId: transaction.propertyId,
        propertyTitle: transaction.propertyTitle,
      );
    } catch (e) {
      print('Error awarding stars using star service: $e');
    }
  }

  /// Award star to specific agent
  static Future<void> _awardStarToAgent(
    String agentId,
    String transactionId,
    String type,
  ) async {
    try {
      // Create star record
      await _firestore.collection(AppConstants.starsCollection).add({
        'agentId': agentId,
        'transactionId': transactionId,
        'count': 1,
        'type': type,
        'earnedAt': Timestamp.now(),
      });

      // Update user total stars
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .update({
            'totalStars': FieldValue.increment(1),
            'updatedAt': Timestamp.now(),
          });

      // Check for 12-star bonus eligibility
      await _checkStarBonus(agentId);
    } catch (e) {
      print('Error awarding star to agent: $e');
    }
  }

  /// Check and award 12-star bonus
  static Future<void> _checkStarBonus(String agentId) async {
    try {
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .get();

      if (userDoc.exists) {
        final user = UserModel.fromFirestore(userDoc);

        if (user.totalStars >= AppConstants.starBenchmark &&
            user.totalStars % AppConstants.starBenchmark == 0) {
          // Award bonus (amount can be configured by admin)
          const bonusAmount = 10000.0; // ₹10,000 bonus

          await _firestore
              .collection(AppConstants.usersCollection)
              .doc(agentId)
              .update({
                'totalBonuses': FieldValue.increment(bonusAmount),
                'updatedAt': Timestamp.now(),
              });

          // Create notification for bonus
          await _createNotification(
            agentId,
            'Star Bonus Achieved!',
            'Congratulations! You have earned ₹${bonusAmount.toStringAsFixed(0)} for reaching ${user.totalStars} stars!',
            'star_bonus',
          );
        }
      }
    } catch (e) {
      print('Error checking star bonus: $e');
    }
  }

  /// Update property status after sale/rental
  static Future<void> _updatePropertyStatus(
    String propertyId,
    String transactionType,
  ) async {
    try {
      String newStatus;
      switch (transactionType) {
        case 'sale':
          newStatus = 'sold';
          break;
        case 'rental':
          newStatus = 'rented';
          break;
        default:
          return;
      }

      await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyId)
          .update({'status': newStatus, 'updatedAt': Timestamp.now()});
    } catch (e) {
      print('Error updating property status: $e');
    }
  }

  /// Create notification
  static Future<void> _createNotification(
    String userId,
    String title,
    String message,
    String type,
  ) async {
    try {
      await _firestore.collection(AppConstants.notificationsCollection).add({
        'userId': userId,
        'type': type,
        'title': title,
        'message': message,
        'isRead': false,
        'createdAt': Timestamp.now(),
      });
    } catch (e) {
      print('Error creating notification: $e');
    }
  }

  /// Get commissions for agent
  static Future<List<CommissionModel>> getAgentCommissions(
    String agentId,
  ) async {
    try {
      // Always use mock data for now to avoid Firestore index issues
      if (true) {
        return _getMockCommissions(agentId);
      }

      final query = await _firestore
          .collection(AppConstants.commissionsCollection)
          .where('agentId', isEqualTo: agentId)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs
          .map((doc) => CommissionModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting agent commissions: $e');
      return [];
    }
  }

  /// Get all transactions (Admin only)
  static Future<List<TransactionModel>> getAllTransactions() async {
    try {
      final query = await _firestore
          .collection(AppConstants.transactionsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs
          .map((doc) => TransactionModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting transactions: $e');
      return [];
    }
  }

  /// Get all commissions (Admin only)
  static Future<List<CommissionModel>> getAllCommissions() async {
    try {
      final query = await _firestore
          .collection(AppConstants.commissionsCollection)
          .orderBy('createdAt', descending: true)
          .get();

      return query.docs
          .map((doc) => CommissionModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      print('Error getting commissions: $e');
      return [];
    }
  }

  /// Mark commission as paid (Admin only)
  static Future<CommissionResult> markCommissionAsPaid(
    String commissionId,
    String paymentMethod,
    String paymentReference,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.commissionsCollection)
          .doc(commissionId)
          .update({
            'status': 'paid',
            'paidAt': Timestamp.now(),
            'paymentMethod': paymentMethod,
            'paymentReference': paymentReference,
          });

      return CommissionResult.success(null);
    } catch (e) {
      return CommissionResult.failure(
        'Failed to mark commission as paid: ${e.toString()}',
      );
    }
  }

  /// Get commission statistics
  static Future<Map<String, dynamic>> getCommissionStatistics() async {
    try {
      final commissions = await getAllCommissions();
      final transactions = await getAllTransactions();

      final totalCommissions = commissions.fold<double>(
        0,
        (sum, c) => sum + c.amount,
      );
      final paidCommissions = commissions
          .where((c) => c.isPaid)
          .fold<double>(0, (sum, c) => sum + c.amount);
      final pendingCommissions = commissions
          .where((c) => c.isPending)
          .fold<double>(0, (sum, c) => sum + c.amount);

      final totalSales = transactions.fold<double>(
        0,
        (sum, t) => sum + t.propertyAmount,
      );
      final completedSales = transactions.where((t) => t.isCompleted).length;

      return {
        'totalCommissions': totalCommissions,
        'paidCommissions': paidCommissions,
        'pendingCommissions': pendingCommissions,
        'totalSales': totalSales,
        'completedSales': completedSales,
        'totalTransactions': transactions.length,
        'averageCommission': commissions.isNotEmpty
            ? totalCommissions / commissions.length
            : 0,
        'commissionRate': totalSales > 0
            ? (totalCommissions / totalSales) * 100
            : 0,
      };
    } catch (e) {
      print('Error getting commission statistics: $e');
      return {};
    }
  }
}

/// Commission operation result wrapper
class CommissionResult {
  final bool isSuccess;
  final TransactionModel? transaction;
  final String? message;

  CommissionResult._(this.isSuccess, this.transaction, this.message);

  factory CommissionResult.success(
    TransactionModel? transaction, {
    String? message,
  }) {
    return CommissionResult._(true, transaction, message);
  }

  factory CommissionResult.failure(String message) {
    return CommissionResult._(false, null, message);
  }
}

/// Extension for CommissionService to add mock data
extension CommissionServiceMock on CommissionService {
  /// Generate mock commissions for development
  static List<CommissionModel> _getMockCommissions(String agentId) {
    final now = DateTime.now();
    return [
      CommissionModel(
        id: 'comm-1-$agentId',
        transactionId: 'trans-1',
        agentId: agentId,
        agentName: 'Mock Agent',
        amount: 125000,
        level: 1,
        rate: 5.0,
        status: 'paid',
        createdAt: now.subtract(const Duration(days: 10)),
        paidAt: now.subtract(const Duration(days: 5)),
      ),
      CommissionModel(
        id: 'comm-2-$agentId',
        transactionId: 'trans-2',
        agentId: agentId,
        agentName: 'Mock Agent',
        amount: 75000,
        level: 2,
        rate: 3.0,
        status: 'pending',
        createdAt: now.subtract(const Duration(days: 20)),
      ),
    ];
  }
}
