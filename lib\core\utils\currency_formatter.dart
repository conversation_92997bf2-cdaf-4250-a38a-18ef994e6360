import 'dart:math';

/// Utility class for formatting currency amounts in Indian Rupee format
class CurrencyFormatter {
  static const String _rupeeSymbol = '₹';
  static const String _croreSymbol = 'Cr';
  static const String _lakhSymbol = 'L';
  static const String _thousandSymbol = 'K';

  /// Format amount in Indian Rupee format with appropriate units
  /// Examples: ₹1.5 Cr, ₹25.0 L, ₹15.0 K, ₹500
  static String formatAmount(double amount) {
    if (amount.isNaN) return '$_rupeeSymbol--';
    if (amount.isInfinite) {
      return amount.isNegative ? '-$_rupeeSymbol∞' : '$_rupeeSymbol∞';
    }

    final absAmount = amount.abs();
    final isNegative = amount < 0;
    final prefix = isNegative ? '-' : '';

    if (absAmount >= 10000000) {
      // Crore (1 Cr = 10,000,000)
      final crores = absAmount / 10000000;
      return '$prefix$_rupeeSymbol${crores.toStringAsFixed(1)} $_croreSymbol';
    } else if (absAmount >= 100000) {
      // Lakh (1 L = 100,000)
      final lakhs = absAmount / 100000;
      return '$prefix$_rupeeSymbol${lakhs.toStringAsFixed(1)} $_lakhSymbol';
    } else if (absAmount >= 1000) {
      // Thousand (1 K = 1,000)
      final thousands = absAmount / 1000;
      return '$prefix$_rupeeSymbol${thousands.toStringAsFixed(1)} $_thousandSymbol';
    } else {
      // Direct rupees
      return '$prefix$_rupeeSymbol${absAmount.round()}';
    }
  }

  /// Format amount in compact format without spaces
  /// Examples: ₹1.5Cr, ₹25.0L, ₹15.0K, ₹500
  static String formatAmountCompact(double amount) {
    if (amount.isNaN) return '$_rupeeSymbol--';
    if (amount.isInfinite) {
      return amount.isNegative ? '-$_rupeeSymbol∞' : '$_rupeeSymbol∞';
    }

    final absAmount = amount.abs();
    final isNegative = amount < 0;
    final prefix = isNegative ? '-' : '';

    if (absAmount >= 10000000) {
      final crores = absAmount / 10000000;
      return '$prefix$_rupeeSymbol${crores.toStringAsFixed(1)}$_croreSymbol';
    } else if (absAmount >= 100000) {
      final lakhs = absAmount / 100000;
      return '$prefix$_rupeeSymbol${lakhs.toStringAsFixed(1)}$_lakhSymbol';
    } else if (absAmount >= 1000) {
      final thousands = absAmount / 1000;
      return '$prefix$_rupeeSymbol${thousands.toStringAsFixed(1)}$_thousandSymbol';
    } else {
      return '$prefix$_rupeeSymbol${absAmount.round()}';
    }
  }

  /// Format amount with Indian number system (lakhs and crores)
  /// Examples: ₹1,23,45,678, ₹10,00,000, ₹1,00,000
  static String formatAmountDetailed(double amount) {
    if (amount.isNaN) return '$_rupeeSymbol--';
    if (amount.isInfinite) {
      return amount.isNegative ? '-$_rupeeSymbol∞' : '$_rupeeSymbol∞';
    }

    final absAmount = amount.abs().round();
    final isNegative = amount < 0;
    final prefix = isNegative ? '-' : '';
    
    final amountStr = absAmount.toString();
    final length = amountStr.length;
    
    if (length <= 3) {
      return '$prefix$_rupeeSymbol$amountStr';
    }
    
    String formatted = '';
    int digitCount = 0;
    
    for (int i = length - 1; i >= 0; i--) {
      if (digitCount == 3 || (digitCount > 3 && (digitCount - 3) % 2 == 0)) {
        formatted = ',$formatted';
      }
      formatted = amountStr[i] + formatted;
      digitCount++;
    }
    
    return '$prefix$_rupeeSymbol$formatted';
  }

  /// Format amount with custom precision
  static String formatAmountWithPrecision(double amount, int precision) {
    if (amount.isNaN) return '$_rupeeSymbol--';
    if (amount.isInfinite) {
      return amount.isNegative ? '-$_rupeeSymbol∞' : '$_rupeeSymbol∞';
    }

    final absAmount = amount.abs();
    final isNegative = amount < 0;
    final prefix = isNegative ? '-' : '';

    if (absAmount >= 10000000) {
      final crores = absAmount / 10000000;
      return '$prefix$_rupeeSymbol${crores.toStringAsFixed(precision)} $_croreSymbol';
    } else if (absAmount >= 100000) {
      final lakhs = absAmount / 100000;
      return '$prefix$_rupeeSymbol${lakhs.toStringAsFixed(precision)} $_lakhSymbol';
    } else if (absAmount >= 1000) {
      final thousands = absAmount / 1000;
      return '$prefix$_rupeeSymbol${thousands.toStringAsFixed(precision)} $_thousandSymbol';
    } else {
      return '$prefix$_rupeeSymbol${absAmount.toStringAsFixed(precision)}';
    }
  }

  /// Format amount with custom currency symbol
  static String formatAmountWithSymbol(double amount, String symbol) {
    final formatted = formatAmount(amount);
    return formatted.replaceFirst(_rupeeSymbol, symbol);
  }

  /// Format commission amounts specifically
  static String formatCommission(double amount) {
    return formatAmount(amount);
  }

  /// Format commission rate as percentage
  static String formatCommissionRate(double rate) {
    final percentage = rate * 100;
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Format property prices
  static String formatPropertyPrice(double price) {
    return formatAmount(price);
  }

  /// Format price range
  static String formatPriceRange(double minPrice, double maxPrice) {
    // Ensure min is actually minimum
    final actualMin = min(minPrice, maxPrice);
    final actualMax = max(minPrice, maxPrice);
    
    if (actualMin == actualMax) {
      return formatAmount(actualMin);
    }
    
    return '${formatAmount(actualMin)} - ${formatAmount(actualMax)}';
  }

  /// Format budget range with null handling
  static String formatBudgetRange(double? minBudget, double? maxBudget) {
    if (minBudget == null && maxBudget == null) {
      return 'Budget not specified';
    } else if (minBudget == null) {
      return 'Up to ${formatAmount(maxBudget!)}';
    } else if (maxBudget == null) {
      return 'Above ${formatAmount(minBudget)}';
    } else {
      return formatPriceRange(minBudget, maxBudget);
    }
  }

  /// Format star bonus amounts
  static String formatStarBonus(double amount) {
    return formatAmount(amount);
  }

  /// Format performance metrics as percentage
  static String formatPerformanceMetric(double metric) {
    final percentage = metric * 100;
    return '${percentage.toStringAsFixed(1)}%';
  }

  /// Parse formatted amount back to double
  static double parseAmount(String formattedAmount) {
    try {
      // Remove currency symbol and spaces
      String cleanAmount = formattedAmount
          .replaceAll(_rupeeSymbol, '')
          .replaceAll(',', '')
          .trim();

      // Handle different units
      if (cleanAmount.contains(_croreSymbol)) {
        final value = double.parse(cleanAmount.replaceAll(_croreSymbol, '').trim());
        return value * 10000000;
      } else if (cleanAmount.contains(_lakhSymbol)) {
        final value = double.parse(cleanAmount.replaceAll(_lakhSymbol, '').trim());
        return value * 100000;
      } else if (cleanAmount.contains(_thousandSymbol)) {
        final value = double.parse(cleanAmount.replaceAll(_thousandSymbol, '').trim());
        return value * 1000;
      } else {
        return double.parse(cleanAmount);
      }
    } catch (e) {
      return 0.0;
    }
  }

  /// Get appropriate unit for amount
  static String getAmountUnit(double amount) {
    final absAmount = amount.abs();
    
    if (absAmount >= 10000000) {
      return _croreSymbol;
    } else if (absAmount >= 100000) {
      return _lakhSymbol;
    } else if (absAmount >= 1000) {
      return _thousandSymbol;
    } else {
      return '';
    }
  }

  /// Get numeric value without unit
  static double getAmountValue(double amount) {
    final absAmount = amount.abs();
    final isNegative = amount < 0;
    
    double value;
    if (absAmount >= 10000000) {
      value = absAmount / 10000000;
    } else if (absAmount >= 100000) {
      value = absAmount / 100000;
    } else if (absAmount >= 1000) {
      value = absAmount / 1000;
    } else {
      value = absAmount;
    }
    
    return isNegative ? -value : value;
  }

  /// Format amount for display in tables (shorter format)
  static String formatAmountForTable(double amount) {
    if (amount.abs() >= 10000000) {
      final crores = amount / 10000000;
      return '${crores.toStringAsFixed(1)}Cr';
    } else if (amount.abs() >= 100000) {
      final lakhs = amount / 100000;
      return '${lakhs.toStringAsFixed(1)}L';
    } else if (amount.abs() >= 1000) {
      final thousands = amount / 1000;
      return '${thousands.toStringAsFixed(1)}K';
    } else {
      return amount.round().toString();
    }
  }

  /// Format amount for charts and graphs
  static String formatAmountForChart(double amount) {
    return formatAmountCompact(amount);
  }

  /// Validate if a string represents a valid currency amount
  static bool isValidCurrencyString(String value) {
    try {
      parseAmount(value);
      return true;
    } catch (e) {
      return false;
    }
  }

  /// Get currency symbol
  static String get currencySymbol => _rupeeSymbol;

  /// Get all unit symbols
  static List<String> get unitSymbols => [_croreSymbol, _lakhSymbol, _thousandSymbol];
}
