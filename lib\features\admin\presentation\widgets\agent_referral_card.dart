import 'package:flutter/material.dart';
import 'package:intl/intl.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../services/referral_management_service.dart';

/// Card displaying agent referral information with edit options
class AgentReferralCard extends StatelessWidget {
  final AgentReferralInfo agentInfo;
  final VoidCallback onEditReferralCode;
  final VoidCallback onEditUpline;

  const AgentReferralCard({
    super.key,
    required this.agentInfo,
    required this.onEditReferralCode,
    required this.onEditUpline,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      decoration: AppTheme.createCardDecoration(addGlow: true),
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header with agent info and status
            _buildHeader(),
            
            const SizedBox(height: 16),
            
            // Referral information
            _buildReferralInfo(),
            
            const SizedBox(height: 16),
            
            // Stats row
            _buildStatsRow(),
            
            const SizedBox(height: 16),
            
            // Action buttons
            _buildActionButtons(),
          ],
        ),
      ),
    );
  }

  /// Build header with agent info and status
  Widget _buildHeader() {
    return Row(
      children: [
        // Avatar
        Container(
          width: 50,
          height: 50,
          decoration: BoxDecoration(
            gradient: AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(25),
            border: Border.all(
              color: AppTheme.primaryColor.withValues(alpha: 0.3),
              width: 2,
            ),
          ),
          child: Center(
            child: Text(
              agentInfo.agent.name.isNotEmpty 
                  ? agentInfo.agent.name[0].toUpperCase()
                  : 'A',
              style: const TextStyle(
                color: Colors.white,
                fontSize: 20,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        ),
        
        const SizedBox(width: 16),
        
        // Agent details
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Expanded(
                    child: Text(
                      agentInfo.agent.name,
                      style: TextStyle(
                        color: AppTheme.darkPrimaryText,
                        fontSize: 16,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  // Level badge
                  GradientWidgets.levelBadge(level: agentInfo.agent.level),
                ],
              ),
              const SizedBox(height: 4),
              Text(
                agentInfo.agent.email,
                style: TextStyle(
                  color: AppTheme.darkSecondaryText,
                  fontSize: 14,
                ),
              ),
              const SizedBox(height: 2),
              Text(
                agentInfo.agent.phoneNumber,
                style: TextStyle(
                  color: AppTheme.darkSecondaryText,
                  fontSize: 12,
                ),
              ),
            ],
          ),
        ),
        
        // Status indicator
        Container(
          padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          decoration: BoxDecoration(
            color: agentInfo.isActive 
                ? AppTheme.successColor.withValues(alpha: 0.2)
                : AppTheme.errorColor.withValues(alpha: 0.2),
            borderRadius: BorderRadius.circular(12),
            border: Border.all(
              color: agentInfo.isActive 
                  ? AppTheme.successColor
                  : AppTheme.errorColor,
              width: 1,
            ),
          ),
          child: Text(
            agentInfo.isActive ? 'Active' : 'Inactive',
            style: TextStyle(
              color: agentInfo.isActive 
                  ? AppTheme.successColor
                  : AppTheme.errorColor,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ),
      ],
    );
  }

  /// Build referral information section
  Widget _buildReferralInfo() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: AppTheme.darkBorder.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          // Referral code
          _buildInfoRow(
            icon: Icons.code,
            label: 'Referral Code',
            value: agentInfo.referralCode,
            isHighlighted: true,
          ),
          
          const SizedBox(height: 12),
          
          // Upline referral code
          _buildInfoRow(
            icon: Icons.arrow_upward,
            label: 'Upline Code',
            value: agentInfo.uplineReferralCode,
          ),
          
          const SizedBox(height: 12),
          
          // Join date
          _buildInfoRow(
            icon: Icons.calendar_today,
            label: 'Joined',
            value: DateFormat('MMM dd, yyyy').format(agentInfo.agent.createdAt),
          ),
        ],
      ),
    );
  }

  /// Build info row
  Widget _buildInfoRow({
    required IconData icon,
    required String label,
    required String value,
    bool isHighlighted = false,
  }) {
    return Row(
      children: [
        Icon(
          icon,
          color: isHighlighted ? AppTheme.primaryColor : AppTheme.darkSecondaryText,
          size: 16,
        ),
        const SizedBox(width: 8),
        Text(
          '$label:',
          style: TextStyle(
            color: AppTheme.darkSecondaryText,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(width: 8),
        Expanded(
          child: Text(
            value,
            style: TextStyle(
              color: isHighlighted ? AppTheme.primaryColor : AppTheme.darkPrimaryText,
              fontSize: 12,
              fontWeight: isHighlighted ? FontWeight.w600 : FontWeight.w500,
            ),
          ),
        ),
      ],
    );
  }

  /// Build stats row
  Widget _buildStatsRow() {
    return Row(
      children: [
        Expanded(
          child: _buildStatItem(
            'Downline',
            agentInfo.downlineCount.toString(),
            Icons.group,
            AppTheme.primaryColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatItem(
            'Commissions',
            '₹${agentInfo.totalCommissions.toStringAsFixed(0)}',
            Icons.currency_rupee,
            AppTheme.successColor,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatItem(
            'Level',
            agentInfo.agent.level.toString(),
            Icons.stairs,
            AppTheme.secondaryColor,
          ),
        ),
      ],
    );
  }

  /// Build individual stat item
  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: color.withValues(alpha: 0.3),
          width: 1,
        ),
      ),
      child: Column(
        children: [
          Icon(
            icon,
            color: color,
            size: 16,
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: color,
              fontSize: 14,
              fontWeight: FontWeight.bold,
            ),
          ),
          Text(
            label,
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 10,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return Row(
      children: [
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onEditReferralCode,
            icon: const Icon(Icons.edit, size: 16),
            label: const Text('Edit Code'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.primaryColor,
              side: BorderSide(color: AppTheme.primaryColor),
              padding: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: OutlinedButton.icon(
            onPressed: onEditUpline,
            icon: const Icon(Icons.swap_vert, size: 16),
            label: const Text('Edit Upline'),
            style: OutlinedButton.styleFrom(
              foregroundColor: AppTheme.secondaryColor,
              side: BorderSide(color: AppTheme.secondaryColor),
              padding: const EdgeInsets.symmetric(vertical: 8),
              shape: RoundedRectangleBorder(
                borderRadius: BorderRadius.circular(8),
              ),
            ),
          ),
        ),
      ],
    );
  }
}
