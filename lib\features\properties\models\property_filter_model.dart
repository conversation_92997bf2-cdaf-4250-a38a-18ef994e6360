import 'property_enums.dart';
import '../../../core/models/property_model.dart';

/// Comprehensive property filter model
class PropertyFilterModel {
  final String? searchQuery;
  final List<PropertyType> selectedTypes;
  final List<PropertyStatus> selectedStatuses;
  final PriceRange? priceRange;
  final double? customMinPrice;
  final double? customMaxPrice;
  final double? minArea;
  final double? maxArea;
  final int? minBedrooms;
  final int? maxBedrooms;
  final int? minBathrooms;
  final int? maxBathrooms;
  final List<String> selectedAmenities;
  final List<String> selectedCities;
  final List<String> selectedStates;
  final bool? isFeatured;
  final bool? isApproved;
  final PropertySortOption sortOption;
  final bool sortAscending;
  final DateTime? dateFrom;
  final DateTime? dateTo;

  const PropertyFilterModel({
    this.searchQuery,
    this.selectedTypes = const [],
    this.selectedStatuses = const [],
    this.priceRange,
    this.customMinPrice,
    this.customMaxPrice,
    this.minArea,
    this.maxArea,
    this.minBedrooms,
    this.maxBedrooms,
    this.minBathrooms,
    this.maxBathrooms,
    this.selectedAmenities = const [],
    this.selectedCities = const [],
    this.selectedStates = const [],
    this.isFeatured,
    this.isApproved,
    this.sortOption = PropertySortOption.newest,
    this.sortAscending = false,
    this.dateFrom,
    this.dateTo,
  });

  PropertyFilterModel copyWith({
    String? searchQuery,
    List<PropertyType>? selectedTypes,
    List<PropertyStatus>? selectedStatuses,
    PriceRange? priceRange,
    double? customMinPrice,
    double? customMaxPrice,
    double? minArea,
    double? maxArea,
    int? minBedrooms,
    int? maxBedrooms,
    int? minBathrooms,
    int? maxBathrooms,
    List<String>? selectedAmenities,
    List<String>? selectedCities,
    List<String>? selectedStates,
    bool? isFeatured,
    bool? isApproved,
    PropertySortOption? sortOption,
    bool? sortAscending,
    DateTime? dateFrom,
    DateTime? dateTo,
  }) {
    return PropertyFilterModel(
      searchQuery: searchQuery ?? this.searchQuery,
      selectedTypes: selectedTypes ?? this.selectedTypes,
      selectedStatuses: selectedStatuses ?? this.selectedStatuses,
      priceRange: priceRange ?? this.priceRange,
      customMinPrice: customMinPrice ?? this.customMinPrice,
      customMaxPrice: customMaxPrice ?? this.customMaxPrice,
      minArea: minArea ?? this.minArea,
      maxArea: maxArea ?? this.maxArea,
      minBedrooms: minBedrooms ?? this.minBedrooms,
      maxBedrooms: maxBedrooms ?? this.maxBedrooms,
      minBathrooms: minBathrooms ?? this.minBathrooms,
      maxBathrooms: maxBathrooms ?? this.maxBathrooms,
      selectedAmenities: selectedAmenities ?? this.selectedAmenities,
      selectedCities: selectedCities ?? this.selectedCities,
      selectedStates: selectedStates ?? this.selectedStates,
      isFeatured: isFeatured ?? this.isFeatured,
      isApproved: isApproved ?? this.isApproved,
      sortOption: sortOption ?? this.sortOption,
      sortAscending: sortAscending ?? this.sortAscending,
      dateFrom: dateFrom ?? this.dateFrom,
      dateTo: dateTo ?? this.dateTo,
    );
  }

  /// Check if any filters are applied
  bool get hasActiveFilters {
    return searchQuery?.isNotEmpty == true ||
           selectedTypes.isNotEmpty ||
           selectedStatuses.isNotEmpty ||
           priceRange != null ||
           customMinPrice != null ||
           customMaxPrice != null ||
           minArea != null ||
           maxArea != null ||
           minBedrooms != null ||
           maxBedrooms != null ||
           minBathrooms != null ||
           maxBathrooms != null ||
           selectedAmenities.isNotEmpty ||
           selectedCities.isNotEmpty ||
           selectedStates.isNotEmpty ||
           isFeatured != null ||
           isApproved != null ||
           dateFrom != null ||
           dateTo != null;
  }

  /// Get active filter count
  int get activeFilterCount {
    int count = 0;
    if (searchQuery?.isNotEmpty == true) count++;
    if (selectedTypes.isNotEmpty) count++;
    if (selectedStatuses.isNotEmpty) count++;
    if (priceRange != null || customMinPrice != null || customMaxPrice != null) count++;
    if (minArea != null || maxArea != null) count++;
    if (minBedrooms != null || maxBedrooms != null) count++;
    if (minBathrooms != null || maxBathrooms != null) count++;
    if (selectedAmenities.isNotEmpty) count++;
    if (selectedCities.isNotEmpty) count++;
    if (selectedStates.isNotEmpty) count++;
    if (isFeatured != null) count++;
    if (isApproved != null) count++;
    if (dateFrom != null || dateTo != null) count++;
    return count;
  }

  /// Clear all filters
  PropertyFilterModel clearAll() {
    return const PropertyFilterModel();
  }

  /// Apply filters to a list of properties
  List<PropertyModel> applyFilters(List<PropertyModel> properties) {
    var filtered = properties;

    // Search query filter
    if (searchQuery?.isNotEmpty == true) {
      final query = searchQuery!.toLowerCase();
      filtered = filtered.where((property) {
        return property.title.toLowerCase().contains(query) ||
               property.description.toLowerCase().contains(query) ||
               property.location.toLowerCase().contains(query) ||
               property.city.toLowerCase().contains(query) ||
               property.state.toLowerCase().contains(query);
      }).toList();
    }

    // Property type filter
    if (selectedTypes.isNotEmpty) {
      final typeValues = selectedTypes.map((t) => t.value).toList();
      filtered = filtered.where((property) => typeValues.contains(property.type)).toList();
    }

    // Property status filter
    if (selectedStatuses.isNotEmpty) {
      final statusValues = selectedStatuses.map((s) => s.value).toList();
      filtered = filtered.where((property) => statusValues.contains(property.status)).toList();
    }

    // Price range filter
    if (priceRange != null && priceRange != PriceRange.custom) {
      final minPrice = priceRange!.minPrice;
      final maxPrice = priceRange!.maxPrice;
      filtered = filtered.where((property) {
        if (minPrice != null && property.price < minPrice) return false;
        if (maxPrice != null && property.price > maxPrice) return false;
        return true;
      }).toList();
    }

    // Custom price filter
    if (customMinPrice != null || customMaxPrice != null) {
      filtered = filtered.where((property) {
        if (customMinPrice != null && property.price < customMinPrice!) return false;
        if (customMaxPrice != null && property.price > customMaxPrice!) return false;
        return true;
      }).toList();
    }

    // Area filter
    if (minArea != null || maxArea != null) {
      filtered = filtered.where((property) {
        if (property.areaSquareFeet == null) return false;
        if (minArea != null && property.areaSquareFeet! < minArea!) return false;
        if (maxArea != null && property.areaSquareFeet! > maxArea!) return false;
        return true;
      }).toList();
    }

    // Bedrooms filter
    if (minBedrooms != null || maxBedrooms != null) {
      filtered = filtered.where((property) {
        if (property.bedrooms == null) return false;
        if (minBedrooms != null && property.bedrooms! < minBedrooms!) return false;
        if (maxBedrooms != null && property.bedrooms! > maxBedrooms!) return false;
        return true;
      }).toList();
    }

    // Bathrooms filter
    if (minBathrooms != null || maxBathrooms != null) {
      filtered = filtered.where((property) {
        if (property.bathrooms == null) return false;
        if (minBathrooms != null && property.bathrooms! < minBathrooms!) return false;
        if (maxBathrooms != null && property.bathrooms! > maxBathrooms!) return false;
        return true;
      }).toList();
    }

    // Amenities filter
    if (selectedAmenities.isNotEmpty) {
      filtered = filtered.where((property) {
        return selectedAmenities.every((amenity) => property.amenities.contains(amenity));
      }).toList();
    }

    // Cities filter
    if (selectedCities.isNotEmpty) {
      filtered = filtered.where((property) => selectedCities.contains(property.city)).toList();
    }

    // States filter
    if (selectedStates.isNotEmpty) {
      filtered = filtered.where((property) => selectedStates.contains(property.state)).toList();
    }

    // Featured filter
    if (isFeatured != null) {
      filtered = filtered.where((property) => property.isFeatured == isFeatured).toList();
    }

    // Approved filter
    if (isApproved != null) {
      filtered = filtered.where((property) => property.isApproved == isApproved).toList();
    }

    // Date range filter
    if (dateFrom != null || dateTo != null) {
      filtered = filtered.where((property) {
        if (dateFrom != null && property.createdAt.isBefore(dateFrom!)) return false;
        if (dateTo != null && property.createdAt.isAfter(dateTo!.add(const Duration(days: 1)))) return false;
        return true;
      }).toList();
    }

    // Apply sorting
    return _applySorting(filtered);
  }

  /// Apply sorting to filtered properties
  List<PropertyModel> _applySorting(List<PropertyModel> properties) {
    switch (sortOption) {
      case PropertySortOption.priceAsc:
        properties.sort((a, b) => a.price.compareTo(b.price));
        break;
      case PropertySortOption.priceDesc:
        properties.sort((a, b) => b.price.compareTo(a.price));
        break;
      case PropertySortOption.areaAsc:
        properties.sort((a, b) {
          final aArea = a.areaSquareFeet ?? 0;
          final bArea = b.areaSquareFeet ?? 0;
          return aArea.compareTo(bArea);
        });
        break;
      case PropertySortOption.areaDesc:
        properties.sort((a, b) {
          final aArea = a.areaSquareFeet ?? 0;
          final bArea = b.areaSquareFeet ?? 0;
          return bArea.compareTo(aArea);
        });
        break;
      case PropertySortOption.newest:
        properties.sort((a, b) => b.createdAt.compareTo(a.createdAt));
        break;
      case PropertySortOption.oldest:
        properties.sort((a, b) => a.createdAt.compareTo(b.createdAt));
        break;
      case PropertySortOption.featured:
        properties.sort((a, b) {
          if (a.isFeatured && !b.isFeatured) return -1;
          if (!a.isFeatured && b.isFeatured) return 1;
          return b.createdAt.compareTo(a.createdAt);
        });
        break;
      case PropertySortOption.alphabetical:
        properties.sort((a, b) => a.title.compareTo(b.title));
        break;
    }

    if (!sortAscending && sortOption != PropertySortOption.featured) {
      properties = properties.reversed.toList();
    }

    return properties;
  }

  @override
  String toString() {
    return 'PropertyFilterModel(searchQuery: $searchQuery, activeFilters: $activeFilterCount)';
  }
}
