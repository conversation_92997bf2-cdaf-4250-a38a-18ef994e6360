import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../../core/models/user_model.dart';
import '../../services/onboarding_service.dart';

/// Widget that shows mentor confirmation when referral code is validated
class MentorConfirmationWidget extends ConsumerStatefulWidget {
  final String referralCode;
  final VoidCallback? onValidated;
  final VoidCallback? onError;

  const MentorConfirmationWidget({
    super.key,
    required this.referralCode,
    this.onValidated,
    this.onError,
  });

  @override
  ConsumerState<MentorConfirmationWidget> createState() =>
      _MentorConfirmationWidgetState();
}

class _MentorConfirmationWidgetState
    extends ConsumerState<MentorConfirmationWidget>
    with SingleTickerProviderStateMixin {
  UserModel? _mentorInfo;
  bool _isLoading = true;
  String? _errorMessage;
  late AnimationController _animationController;
  late Animation<double> _fadeAnimation;
  late Animation<Offset> _slideAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 600),
      vsync: this,
    );

    _fadeAnimation = Tween<double>(begin: 0.0, end: 1.0).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );

    _slideAnimation =
        Tween<Offset>(begin: const Offset(0, 0.3), end: Offset.zero).animate(
          CurvedAnimation(
            parent: _animationController,
            curve: Curves.easeOutBack,
          ),
        );

    _validateReferralCode();
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  Future<void> _validateReferralCode() async {
    try {
      setState(() {
        _isLoading = true;
        _errorMessage = null;
      });

      final result = await OnboardingService.verifyReferralCode(
        widget.referralCode,
      );

      if (result.isSuccess && result.ownerId != null) {
        // Fetch mentor details
        final mentorInfo = await OnboardingService.getUserById(result.ownerId!);

        if (mentorInfo != null) {
          setState(() {
            _mentorInfo = mentorInfo;
            _isLoading = false;
          });

          _animationController.forward();
          widget.onValidated?.call();
        } else {
          setState(() {
            _errorMessage = 'Mentor information not found';
            _isLoading = false;
          });
          widget.onError?.call();
        }
      } else {
        setState(() {
          _errorMessage = result.message ?? 'Invalid referral code';
          _isLoading = false;
        });
        widget.onError?.call();
      }
    } catch (e) {
      setState(() {
        _errorMessage = 'Error validating referral code: $e';
        _isLoading = false;
      });
      widget.onError?.call();
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return _buildLoadingWidget();
    }

    if (_errorMessage != null) {
      return _buildErrorWidget();
    }

    if (_mentorInfo != null) {
      return _buildMentorConfirmationWidget();
    }

    return const SizedBox.shrink();
  }

  Widget _buildLoadingWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.blue.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.blue.shade200),
      ),
      child: Row(
        children: [
          const SizedBox(
            width: 20,
            height: 20,
            child: CircularProgressIndicator(strokeWidth: 2),
          ),
          const SizedBox(width: 12),
          Text(
            'Validating referral code...',
            style: TextStyle(
              color: Colors.blue.shade700,
              fontWeight: FontWeight.w500,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildErrorWidget() {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: Colors.red.shade50,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: Colors.red.shade200),
      ),
      child: Row(
        children: [
          Icon(Icons.error_outline, color: Colors.red.shade600, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Text(
              _errorMessage!,
              style: TextStyle(
                color: Colors.red.shade700,
                fontWeight: FontWeight.w500,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMentorConfirmationWidget() {
    return FadeTransition(
      opacity: _fadeAnimation,
      child: SlideTransition(
        position: _slideAnimation,
        child: Container(
          padding: const EdgeInsets.all(20),
          decoration: BoxDecoration(
            gradient: LinearGradient(
              colors: [Colors.green.shade50, Colors.green.shade100],
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
            ),
            borderRadius: BorderRadius.circular(16),
            border: Border.all(color: Colors.green.shade300),
            boxShadow: [
              BoxShadow(
                color: Colors.green.withOpacity(0.1),
                blurRadius: 10,
                offset: const Offset(0, 5),
              ),
            ],
          ),
          child: Column(
            children: [
              // Success Icon
              Container(
                width: 60,
                height: 60,
                decoration: BoxDecoration(
                  color: Colors.green.shade500,
                  shape: BoxShape.circle,
                ),
                child: const Icon(Icons.check, color: Colors.white, size: 30),
              ),

              const SizedBox(height: 16),

              // Confirmation Text
              Text(
                'Great! You\'re joining Rama Samriddhi under the mentorship of:',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 16,
                  color: Colors.green.shade800,
                  fontWeight: FontWeight.w500,
                ),
              ),

              const SizedBox(height: 20),

              // Mentor Info Card
              Container(
                padding: const EdgeInsets.all(16),
                decoration: BoxDecoration(
                  color: Colors.white,
                  borderRadius: BorderRadius.circular(12),
                  boxShadow: [
                    BoxShadow(
                      color: Colors.black.withOpacity(0.05),
                      blurRadius: 5,
                      offset: const Offset(0, 2),
                    ),
                  ],
                ),
                child: Row(
                  children: [
                    // Mentor Avatar
                    CircleAvatar(
                      radius: 30,
                      backgroundImage: _mentorInfo!.profileImageUrl != null
                          ? NetworkImage(_mentorInfo!.profileImageUrl!)
                          : null,
                      backgroundColor: AppTheme.primaryColor.withOpacity(0.1),
                      child: _mentorInfo!.profileImageUrl == null
                          ? Icon(
                              Icons.person,
                              color: AppTheme.primaryColor,
                              size: 30,
                            )
                          : null,
                    ),

                    const SizedBox(width: 16),

                    // Mentor Details
                    Expanded(
                      child: Column(
                        crossAxisAlignment: CrossAxisAlignment.start,
                        children: [
                          Text(
                            _mentorInfo!.name,
                            style: const TextStyle(
                              fontSize: 18,
                              fontWeight: FontWeight.bold,
                              color: Color(0xFF1B150E),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Text(
                            'Senior Agent • Level ${_mentorInfo!.level}',
                            style: const TextStyle(
                              fontSize: 14,
                              color: Color(0xFF6B7280),
                            ),
                          ),
                          const SizedBox(height: 4),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 8,
                              vertical: 4,
                            ),
                            decoration: BoxDecoration(
                              color: AppTheme.primaryColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(6),
                            ),
                            child: Text(
                              'Code: ${widget.referralCode}',
                              style: TextStyle(
                                fontSize: 12,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ),
                        ],
                      ),
                    ),

                    // Verified Badge
                    Container(
                      padding: const EdgeInsets.all(8),
                      decoration: BoxDecoration(
                        color: Colors.green.shade500,
                        shape: BoxShape.circle,
                      ),
                      child: const Icon(
                        Icons.verified,
                        color: Colors.white,
                        size: 20,
                      ),
                    ),
                  ],
                ),
              ),

              const SizedBox(height: 16),

              // Benefits Text
              Text(
                '🎉 You\'ll receive professional guidance and support from your mentor throughout your journey!',
                textAlign: TextAlign.center,
                style: TextStyle(
                  fontSize: 14,
                  color: Colors.green.shade700,
                  fontStyle: FontStyle.italic,
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}
