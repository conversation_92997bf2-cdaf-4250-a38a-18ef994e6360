import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../models/report_models.dart';
import '../../services/report_service.dart';
import 'report_builder_widget.dart';

/// Report dashboard widget for viewing and managing reports
class ReportDashboardWidget extends ConsumerStatefulWidget {
  final String? agentId;

  const ReportDashboardWidget({
    super.key,
    this.agentId,
  });

  @override
  ConsumerState<ReportDashboardWidget> createState() => _ReportDashboardWidgetState();
}

class _ReportDashboardWidgetState extends ConsumerState<ReportDashboardWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  List<GeneratedReport> _reportHistory = [];
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadReportHistory();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadReportHistory() async {
    setState(() => _isLoading = true);
    
    try {
      final reports = await ReportService.getReportHistory(agentId: widget.agentId);
      setState(() {
        _reportHistory = reports;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        _buildHeader(),
        
        const SizedBox(height: 16),
        
        // Tab Bar
        _buildTabBar(),
        
        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildReportsTab(),
              _buildBuilderTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// Build header
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            child: const Icon(
              Icons.analytics,
              color: Colors.white,
              size: 24,
            ),
          ),
          const SizedBox(width: 12),
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                'Reports & Analytics',
                style: TextStyle(
                  color: AppTheme.darkPrimaryText,
                  fontSize: 20,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Text(
                'Generate and manage your business reports',
                style: TextStyle(
                  color: AppTheme.darkSecondaryText,
                  fontSize: 12,
                ),
              ),
            ],
          ),
          const Spacer(),
          GradientWidgets.gradientButton(
            text: 'Quick Report',
            onPressed: () => _tabController.animateTo(2),
            icon: Icons.add,
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 8),
          ),
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.darkSecondaryText,
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Reports'),
          Tab(text: 'Builder'),
        ],
      ),
    );
  }

  /// Build overview tab
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Report Statistics
          _buildReportStatistics(),
          
          const SizedBox(height: 24),
          
          // Quick Actions
          _buildQuickActions(),
          
          const SizedBox(height: 24),
          
          // Recent Reports
          _buildRecentReports(),
        ],
      ),
    );
  }

  /// Build reports tab
  Widget _buildReportsTab() {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_reportHistory.isEmpty) {
      return _buildEmptyReportsState();
    }

    return RefreshIndicator(
      onRefresh: _loadReportHistory,
      child: ListView.builder(
        padding: const EdgeInsets.all(16),
        itemCount: _reportHistory.length,
        itemBuilder: (context, index) {
          final report = _reportHistory[index];
          return _buildReportCard(report);
        },
      ),
    );
  }

  /// Build builder tab
  Widget _buildBuilderTab() {
    return ReportBuilderWidget(
      agentId: widget.agentId,
      onReportGenerated: (report) {
        setState(() {
          _reportHistory.insert(0, report);
        });
        _tabController.animateTo(1);
      },
    );
  }

  /// Build report statistics
  Widget _buildReportStatistics() {
    final totalReports = _reportHistory.length;
    final completedReports = _reportHistory.where((r) => r.status == ReportStatus.completed).length;
    final failedReports = _reportHistory.where((r) => r.status == ReportStatus.failed).length;
    final completionRate = totalReports > 0 ? (completedReports / totalReports * 100) : 0.0;

    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildStatCard(
          'Total Reports',
          '$totalReports',
          Icons.description,
          AppTheme.primaryColor,
          subtitle: 'All time',
        ),
        _buildStatCard(
          'Completed',
          '$completedReports',
          Icons.check_circle,
          AppTheme.successColor,
          subtitle: '${completionRate.toStringAsFixed(0)}% success rate',
        ),
        _buildStatCard(
          'Failed',
          '$failedReports',
          Icons.error,
          AppTheme.errorColor,
          subtitle: 'Need attention',
        ),
        _buildStatCard(
          'This Month',
          '${_reportHistory.where((r) => r.generatedAt.month == DateTime.now().month).length}',
          Icons.calendar_today,
          AppTheme.warningColor,
          subtitle: 'Generated',
        ),
      ],
    );
  }

  /// Build stat card
  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Icon(
                Icons.trending_up,
                color: AppTheme.successColor,
                size: 16,
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: AppTheme.darkHintText,
                fontSize: 10,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build quick actions
  Widget _buildQuickActions() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Quick Actions',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 2.5,
            children: [
              _buildQuickActionButton(
                'Sales Report',
                Icons.trending_up,
                ReportType.sales.color,
                () => _generateQuickReport(ReportType.sales),
              ),
              _buildQuickActionButton(
                'Commission Report',
                Icons.account_balance_wallet,
                ReportType.commissions.color,
                () => _generateQuickReport(ReportType.commissions),
              ),
              _buildQuickActionButton(
                'Team Report',
                Icons.group,
                ReportType.team.color,
                () => _generateQuickReport(ReportType.team),
              ),
              _buildQuickActionButton(
                'Network Analysis',
                Icons.account_tree,
                ReportType.network.color,
                () => _generateQuickReport(ReportType.network),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build quick action button
  Widget _buildQuickActionButton(String title, IconData icon, Color color, VoidCallback onPressed) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: color.withValues(alpha: 0.3),
          ),
        ),
        child: Row(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(width: 8),
            Expanded(
              child: Text(
                title,
                style: TextStyle(
                  color: AppTheme.darkPrimaryText,
                  fontSize: 12,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build recent reports
  Widget _buildRecentReports() {
    final recentReports = _reportHistory.take(3).toList();
    
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Text(
                'Recent Reports',
                style: TextStyle(
                  color: AppTheme.darkPrimaryText,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton(
                onPressed: () => _tabController.animateTo(1),
                child: Text(
                  'View All',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          if (recentReports.isEmpty)
            Text(
              'No reports generated yet',
              style: TextStyle(
                color: AppTheme.darkSecondaryText,
                fontSize: 14,
              ),
            )
          else
            ...recentReports.map((report) => _buildRecentReportItem(report)),
        ],
      ),
    );
  }

  /// Build recent report item
  Widget _buildRecentReportItem(GeneratedReport report) {
    return Container(
      margin: const EdgeInsets.only(bottom: 8),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(6),
            decoration: BoxDecoration(
              color: report.config.type.color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(6),
            ),
            child: Icon(
              report.config.type.icon,
              color: report.config.type.color,
              size: 16,
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  report.config.name,
                  style: TextStyle(
                    color: AppTheme.darkPrimaryText,
                    fontSize: 12,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${report.config.type.displayName} • ${report.formattedFileSize}',
                  style: TextStyle(
                    color: AppTheme.darkSecondaryText,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
          ),
          
          Container(
            padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
            decoration: BoxDecoration(
              color: report.status.color.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Text(
              report.status.displayName,
              style: TextStyle(
                color: report.status.color,
                fontSize: 10,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build empty reports state
  Widget _buildEmptyReportsState() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.description_outlined,
            size: 64,
            color: AppTheme.darkSecondaryText,
          ),
          const SizedBox(height: 16),
          Text(
            'No Reports Generated',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Create your first report to get started',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
          ),
          const SizedBox(height: 24),
          GradientWidgets.gradientButton(
            text: 'Create Report',
            onPressed: () => _tabController.animateTo(2),
            icon: Icons.add,
          ),
        ],
      ),
    );
  }

  /// Build report card
  Widget _buildReportCard(GeneratedReport report) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      decoration: AppTheme.createCardDecoration(),
      child: Column(
        children: [
          ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: report.config.type.color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                report.config.type.icon,
                color: report.config.type.color,
                size: 20,
              ),
            ),
            title: Text(
              report.config.name,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  '${report.config.type.displayName} • ${report.config.format.displayName}',
                  style: TextStyle(
                    color: AppTheme.darkSecondaryText,
                    fontSize: 12,
                  ),
                ),
                Text(
                  'Generated ${_formatDate(report.generatedAt)}',
                  style: TextStyle(
                    color: AppTheme.darkHintText,
                    fontSize: 10,
                  ),
                ),
              ],
            ),
            trailing: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: report.status.color.withValues(alpha: 0.2),
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        report.status.icon,
                        color: report.status.color,
                        size: 12,
                      ),
                      const SizedBox(width: 4),
                      Text(
                        report.status.displayName,
                        style: TextStyle(
                          color: report.status.color,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
                const SizedBox(width: 8),
                PopupMenuButton<String>(
                  icon: Icon(Icons.more_vert, color: AppTheme.darkSecondaryText),
                  onSelected: (value) => _handleReportAction(report, value),
                  itemBuilder: (context) => [
                    if (report.status == ReportStatus.completed) ...[
                      const PopupMenuItem(
                        value: 'download',
                        child: Row(
                          children: [
                            Icon(Icons.download, size: 16),
                            SizedBox(width: 8),
                            Text('Download'),
                          ],
                        ),
                      ),
                      const PopupMenuItem(
                        value: 'share',
                        child: Row(
                          children: [
                            Icon(Icons.share, size: 16),
                            SizedBox(width: 8),
                            Text('Share'),
                          ],
                        ),
                      ),
                    ],
                    const PopupMenuItem(
                      value: 'delete',
                      child: Row(
                        children: [
                          Icon(Icons.delete, size: 16, color: Colors.red),
                          SizedBox(width: 8),
                          Text('Delete', style: TextStyle(color: Colors.red)),
                        ],
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
          
          if (report.status == ReportStatus.completed && report.fileSize != null)
            Padding(
              padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
              child: Row(
                children: [
                  Icon(
                    Icons.file_present,
                    color: AppTheme.darkSecondaryText,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'File size: ${report.formattedFileSize}',
                    style: TextStyle(
                      color: AppTheme.darkSecondaryText,
                      fontSize: 12,
                    ),
                  ),
                  const Spacer(),
                  Text(
                    report.config.formattedDateRange,
                    style: TextStyle(
                      color: AppTheme.darkHintText,
                      fontSize: 10,
                    ),
                  ),
                ],
              ),
            ),
        ],
      ),
    );
  }

  /// Generate quick report
  void _generateQuickReport(ReportType type) {
    // Navigate to builder tab with pre-selected type
    _tabController.animateTo(2);
    // You could also auto-fill the builder with the selected type
  }

  /// Handle report action
  void _handleReportAction(GeneratedReport report, String action) {
    switch (action) {
      case 'download':
        _downloadReport(report);
        break;
      case 'share':
        _shareReport(report);
        break;
      case 'delete':
        _deleteReport(report);
        break;
    }
  }

  /// Download report
  void _downloadReport(GeneratedReport report) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Downloading ${report.config.name}...'),
        backgroundColor: AppTheme.primaryColor,
      ),
    );
  }

  /// Share report
  void _shareReport(GeneratedReport report) {
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text('Sharing ${report.config.name}...'),
        backgroundColor: AppTheme.secondaryColor,
      ),
    );
  }

  /// Delete report
  void _deleteReport(GeneratedReport report) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.darkCard,
        title: Text(
          'Delete Report',
          style: TextStyle(color: AppTheme.darkPrimaryText),
        ),
        content: Text(
          'Are you sure you want to delete "${report.config.name}"?',
          style: TextStyle(color: AppTheme.darkSecondaryText),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppTheme.darkSecondaryText),
            ),
          ),
          TextButton(
            onPressed: () {
              setState(() {
                _reportHistory.remove(report);
              });
              Navigator.of(context).pop();
              ScaffoldMessenger.of(context).showSnackBar(
                SnackBar(
                  content: Text('Report deleted'),
                  backgroundColor: AppTheme.errorColor,
                ),
              );
            },
            child: Text(
              'Delete',
              style: TextStyle(color: AppTheme.errorColor),
            ),
          ),
        ],
      ),
    );
  }

  /// Format date
  String _formatDate(DateTime date) {
    final now = DateTime.now();
    final difference = now.difference(date);

    if (difference.inDays == 0) {
      return 'Today';
    } else if (difference.inDays == 1) {
      return 'Yesterday';
    } else if (difference.inDays < 7) {
      return '${difference.inDays} days ago';
    } else {
      return '${date.day}/${date.month}/${date.year}';
    }
  }
}
