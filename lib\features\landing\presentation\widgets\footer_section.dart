import 'package:flutter/material.dart';
import '../../../../shared/themes/app_theme.dart';

/// Footer section with company info and links
class FooterSection extends StatelessWidget {
  const FooterSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 60, horizontal: 20),
      decoration: BoxDecoration(
        color: AppTheme.cardColor.withOpacity(0.8),
        border: Border(
          top: BorderSide(
            color: AppTheme.primaryColor.withOpacity(0.3),
            width: 1,
          ),
        ),
      ),
      child: Column(
        children: [
          // Main Footer Content
          LayoutBuilder(
            builder: (context, constraints) {
              bool isWide = constraints.maxWidth > 800;
              
              if (isWide) {
                return Row(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Expanded(flex: 2, child: _buildCompanyInfo()),
                    const SizedBox(width: 40),
                    Expanded(child: _buildQuickLinks()),
                    const SizedBox(width: 40),
                    Expanded(child: _buildContactInfo()),
                  ],
                );
              } else {
                return Column(
                  children: [
                    _buildCompanyInfo(),
                    const SizedBox(height: 40),
                    Row(
                      children: [
                        Expanded(child: _buildQuickLinks()),
                        const SizedBox(width: 40),
                        Expanded(child: _buildContactInfo()),
                      ],
                    ),
                  ],
                );
              }
            },
          ),
          
          const SizedBox(height: 40),
          
          // Divider
          Container(
            height: 1,
            color: Colors.white.withOpacity(0.2),
          ),
          
          const SizedBox(height: 20),
          
          // Copyright
          Row(
            mainAxisAlignment: MainAxisAlignment.spaceBetween,
            children: [
              Text(
                '© 2024 Rama Samriddhi Private Limited. All rights reserved.',
                style: TextStyle(
                  color: Colors.white.withOpacity(0.6),
                  fontSize: 14,
                ),
              ),
              
              // Social Links
              Row(
                children: [
                  _buildSocialIcon(Icons.facebook, () {}),
                  const SizedBox(width: 12),
                  _buildSocialIcon(Icons.phone, () {}),
                  const SizedBox(width: 12),
                  _buildSocialIcon(Icons.email, () {}),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildCompanyInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        // Logo and Brand
        Row(
          children: [
            Container(
              width: 40,
              height: 40,
              decoration: BoxDecoration(
                gradient: LinearGradient(
                  colors: [
                    AppTheme.primaryColor,
                    AppTheme.accentColor,
                  ],
                ),
                borderRadius: BorderRadius.circular(8),
              ),
              child: const Icon(
                Icons.home_work,
                color: Colors.white,
                size: 24,
              ),
            ),
            const SizedBox(width: 12),
            const Text(
              'Rama Samriddhi',
              style: TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
          ],
        ),
        
        const SizedBox(height: 16),
        
        Text(
          'Your trusted partner in premium real estate investments. We specialize in providing high-quality properties with guaranteed returns and exceptional service.',
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 14,
            height: 1.5,
          ),
        ),
        
        const SizedBox(height: 20),
        
        // CTA Button
        ElevatedButton(
          onPressed: () {
            // Navigate to registration
          },
          style: ElevatedButton.styleFrom(
            backgroundColor: AppTheme.primaryColor,
            shape: RoundedRectangleBorder(
              borderRadius: BorderRadius.circular(8),
            ),
          ),
          child: const Text('Get Started Today'),
        ),
      ],
    );
  }

  Widget _buildQuickLinks() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Quick Links',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: 16),
        
        _buildFooterLink('Properties', () {}),
        _buildFooterLink('About Us', () {}),
        _buildFooterLink('Services', () {}),
        _buildFooterLink('Contact', () {}),
        _buildFooterLink('Privacy Policy', () {}),
        _buildFooterLink('Terms & Conditions', () {}),
      ],
    );
  }

  Widget _buildContactInfo() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        const Text(
          'Contact Info',
          style: TextStyle(
            fontSize: 18,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        
        const SizedBox(height: 16),
        
        _buildContactItem(Icons.phone, '+91 98765 43210'),
        _buildContactItem(Icons.email, '<EMAIL>'),
        _buildContactItem(Icons.location_on, 'Mumbai, Maharashtra'),
        _buildContactItem(Icons.access_time, 'Mon-Sat: 9AM-7PM'),
      ],
    );
  }

  Widget _buildFooterLink(String text, VoidCallback onTap) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: InkWell(
        onTap: onTap,
        child: Text(
          text,
          style: TextStyle(
            color: Colors.white.withOpacity(0.8),
            fontSize: 14,
          ),
        ),
      ),
    );
  }

  Widget _buildContactItem(IconData icon, String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        children: [
          Icon(
            icon,
            color: AppTheme.primaryColor,
            size: 16,
          ),
          const SizedBox(width: 8),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: Colors.white.withOpacity(0.8),
                fontSize: 14,
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSocialIcon(IconData icon, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      child: Container(
        width: 36,
        height: 36,
        decoration: BoxDecoration(
          color: AppTheme.primaryColor.withOpacity(0.2),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(
            color: AppTheme.primaryColor.withOpacity(0.5),
          ),
        ),
        child: Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 18,
        ),
      ),
    );
  }
}
