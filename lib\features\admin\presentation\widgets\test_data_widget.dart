import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../../core/utils/test_data_generator.dart';

/// Widget for managing test data in development/demo mode
class TestDataWidget extends ConsumerStatefulWidget {
  const TestDataWidget({super.key});

  @override
  ConsumerState<TestDataWidget> createState() => _TestDataWidgetState();
}

class _TestDataWidgetState extends ConsumerState<TestDataWidget> {
  bool _isGenerating = false;
  bool _isClearing = false;
  String _statusMessage = '';

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.darkBorder.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Icon(
                Icons.science,
                color: AppTheme.primaryColor,
                size: 24,
              ),
              const SizedBox(width: 8),
              Text(
                'Test Data Management',
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          // Description
          Text(
            'Generate comprehensive test data for MLM hierarchy testing including:',
            style: TextStyle(
              color: Colors.white.withOpacity(0.8),
              fontSize: 14,
            ),
          ),
          
          const SizedBox(height: 8),
          
          // Features list
          ...[ 
            '• 5-level agent hierarchy (Admin → Regional → Team → Senior → Junior)',
            '• 20+ agents with realistic profiles and performance data',
            '• Diverse property portfolio (Apartments, Villas, Commercial)',
            '• Transaction history with commission distribution',
            '• MLM network relationships and referral tracking',
            '• Star achievements and bonus records',
          ].map((feature) => Padding(
            padding: const EdgeInsets.only(left: 16, bottom: 4),
            child: Text(
              feature,
              style: TextStyle(
                color: Colors.white.withOpacity(0.7),
                fontSize: 12,
              ),
            ),
          )),
          
          const SizedBox(height: 20),
          
          // Status message
          if (_statusMessage.isNotEmpty) ...[
            Container(
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: _statusMessage.contains('Error') 
                    ? AppTheme.errorColor.withOpacity(0.1)
                    : AppTheme.successColor.withOpacity(0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _statusMessage.contains('Error')
                      ? AppTheme.errorColor.withOpacity(0.3)
                      : AppTheme.successColor.withOpacity(0.3),
                ),
              ),
              child: Row(
                children: [
                  Icon(
                    _statusMessage.contains('Error') 
                        ? Icons.error_outline 
                        : Icons.check_circle_outline,
                    color: _statusMessage.contains('Error')
                        ? AppTheme.errorColor
                        : AppTheme.successColor,
                    size: 16,
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: Text(
                      _statusMessage,
                      style: TextStyle(
                        color: _statusMessage.contains('Error')
                            ? AppTheme.errorColor
                            : AppTheme.successColor,
                        fontSize: 12,
                      ),
                    ),
                  ),
                ],
              ),
            ),
            const SizedBox(height: 16),
          ],
          
          // Action buttons
          Row(
            children: [
              Expanded(
                child: ElevatedButton.icon(
                  onPressed: _isGenerating || _isClearing ? null : _generateTestData,
                  icon: _isGenerating 
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(Colors.white),
                          ),
                        )
                      : Icon(Icons.add_circle_outline),
                  label: Text(_isGenerating ? 'Generating...' : 'Generate Test Data'),
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                    foregroundColor: Colors.white,
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
              
              const SizedBox(width: 12),
              
              Expanded(
                child: OutlinedButton.icon(
                  onPressed: _isGenerating || _isClearing ? null : _clearTestData,
                  icon: _isClearing 
                      ? SizedBox(
                          width: 16,
                          height: 16,
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(AppTheme.errorColor),
                          ),
                        )
                      : Icon(Icons.delete_outline),
                  label: Text(_isClearing ? 'Clearing...' : 'Clear All Data'),
                  style: OutlinedButton.styleFrom(
                    foregroundColor: AppTheme.errorColor,
                    side: BorderSide(color: AppTheme.errorColor),
                    padding: const EdgeInsets.symmetric(vertical: 12),
                    shape: RoundedRectangleBorder(
                      borderRadius: BorderRadius.circular(8),
                    ),
                  ),
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 12),
          
          // Warning
          Container(
            padding: const EdgeInsets.all(8),
            decoration: BoxDecoration(
              color: AppTheme.warningColor.withOpacity(0.1),
              borderRadius: BorderRadius.circular(6),
              border: Border.all(
                color: AppTheme.warningColor.withOpacity(0.3),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.warning_amber_outlined,
                  color: AppTheme.warningColor,
                  size: 16,
                ),
                const SizedBox(width: 8),
                Expanded(
                  child: Text(
                    'Warning: This will modify your Firestore database. Use only in development/demo environment.',
                    style: TextStyle(
                      color: AppTheme.warningColor,
                      fontSize: 11,
                    ),
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Future<void> _generateTestData() async {
    setState(() {
      _isGenerating = true;
      _statusMessage = '';
    });

    try {
      await TestDataGenerator.generateCompleteTestData();
      
      setState(() {
        _statusMessage = '✅ Test data generated successfully! You can now test all MLM functionalities.';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '❌ Error generating test data: $e';
      });
    } finally {
      setState(() {
        _isGenerating = false;
      });
    }
  }

  Future<void> _clearTestData() async {
    // Show confirmation dialog
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.darkSurface,
        title: Text(
          'Clear All Test Data',
          style: TextStyle(color: AppTheme.errorColor),
        ),
        content: const Text(
          'This will permanently delete all test data from the database. This action cannot be undone.',
          style: TextStyle(color: Colors.white),
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context, false),
            child: Text(
              'Cancel',
              style: TextStyle(color: Colors.white.withOpacity(0.7)),
            ),
          ),
          ElevatedButton(
            onPressed: () => Navigator.pop(context, true),
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.errorColor,
            ),
            child: const Text('Delete All'),
          ),
        ],
      ),
    );

    if (confirmed != true) return;

    setState(() {
      _isClearing = true;
      _statusMessage = '';
    });

    try {
      await TestDataGenerator.clearAllTestData();
      
      setState(() {
        _statusMessage = '✅ All test data cleared successfully!';
      });
    } catch (e) {
      setState(() {
        _statusMessage = '❌ Error clearing test data: $e';
      });
    } finally {
      setState(() {
        _isClearing = false;
      });
    }
  }
}
