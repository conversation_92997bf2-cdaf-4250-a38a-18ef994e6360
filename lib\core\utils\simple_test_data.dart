import 'package:flutter/foundation.dart';

/// Simple test data generator for MLM app
class SimpleTestData {
  
  /// Generate complete test data set
  static Future<Map<String, dynamic>> generateAllTestData() async {
    if (kDebugMode) {
      print('🚀 Generating simple test data...');
    }

    final testData = <String, dynamic>{};
    
    // Generate users (MLM hierarchy)
    testData['users'] = _generateUsers();
    
    // Generate properties
    testData['properties'] = _generateProperties();
    
    // Generate transactions
    testData['transactions'] = _generateTransactions();
    
    // Generate commissions
    testData['commissions'] = _generateCommissions();
    
    // Generate stars
    testData['stars'] = _generateStars();
    
    // Generate leads
    testData['leads'] = _generateLeads();

    if (kDebugMode) {
      print('✅ Test data generated successfully!');
      print('📊 Data Summary:');
      print('   Users: ${testData['users'].length}');
      print('   Properties: ${testData['properties'].length}');
      print('   Transactions: ${testData['transactions'].length}');
      print('   Commissions: ${testData['commissions'].length}');
      print('   Stars: ${testData['stars'].length}');
      print('   Leads: ${testData['leads'].length}');
    }

    return testData;
  }

  /// Generate MLM user hierarchy
  static List<Map<String, dynamic>> _generateUsers() {
    final users = <Map<String, dynamic>>[];
    
    // Admin (Level 0)
    users.add({
      'id': 'admin_001',
      'email': '<EMAIL>',
      'name': 'System Administrator',
      'phoneNumber': '+91-9999999999',
      'role': 'admin',
      'level': 0,
      'uplineId': null,
      'downlineIds': ['regional_001', 'regional_002'],
      'referralCode': 'ADMIN2024',
      'referredBy': null,
      'totalStars': 0,
      'totalCommissions': 0.0,
      'isActive': true,
      'joinedAt': DateTime.now().subtract(const Duration(days: 365)).toIso8601String(),
      'lastActiveAt': DateTime.now().toIso8601String(),
      'profileImageUrl': 'https://via.placeholder.com/150/FF6B35/FFFFFF?text=AD',
      'address': 'Corporate Office, Gurgaon',
      'achievements': [],
      'metadata': {
        'isVerified': true,
        'kycStatus': 'approved',
        'tier': 'admin',
        'signupSource': 'system',
      },
    });

    // Regional Managers (Level 1)
    users.add({
      'id': 'regional_001',
      'email': '<EMAIL>',
      'name': 'Rajesh Kumar',
      'phoneNumber': '+91-9876543210',
      'role': 'regional_manager',
      'level': 1,
      'uplineId': 'admin_001',
      'downlineIds': ['team_001', 'team_002'],
      'referralCode': 'RAJESH2024',
      'referredBy': 'ADMIN2024',
      'totalStars': 85,
      'totalCommissions': 450000.0,
      'isActive': true,
      'joinedAt': DateTime.now().subtract(const Duration(days: 300)).toIso8601String(),
      'lastActiveAt': DateTime.now().subtract(const Duration(hours: 1)).toIso8601String(),
      'profileImageUrl': 'https://via.placeholder.com/150/8B5CF6/FFFFFF?text=RK',
      'address': 'North Delhi, Delhi',
      'achievements': ['top_performer', 'mentor_excellence'],
      'metadata': {
        'isVerified': true,
        'kycStatus': 'approved',
        'tier': 'platinum',
        'signupSource': 'referral',
        'specialization': 'luxury_properties',
      },
    });

    users.add({
      'id': 'regional_002',
      'email': '<EMAIL>',
      'name': 'Priya Sharma',
      'phoneNumber': '+91-9876543211',
      'role': 'regional_manager',
      'level': 1,
      'uplineId': 'admin_001',
      'downlineIds': ['team_003', 'team_004'],
      'referralCode': 'PRIYA2024',
      'referredBy': 'ADMIN2024',
      'totalStars': 95,
      'totalCommissions': 500000.0,
      'isActive': true,
      'joinedAt': DateTime.now().subtract(const Duration(days: 270)).toIso8601String(),
      'lastActiveAt': DateTime.now().subtract(const Duration(hours: 2)).toIso8601String(),
      'profileImageUrl': 'https://via.placeholder.com/150/8B5CF6/FFFFFF?text=PS',
      'address': 'South Delhi, Delhi',
      'achievements': ['top_performer', 'mentor_excellence'],
      'metadata': {
        'isVerified': true,
        'kycStatus': 'approved',
        'tier': 'platinum',
        'signupSource': 'referral',
        'specialization': 'luxury_properties',
      },
    });

    // Team Leaders (Level 2)
    final teamLeaders = [
      {'id': 'team_001', 'name': 'Amit Singh', 'email': '<EMAIL>', 'upline': 'regional_001'},
      {'id': 'team_002', 'name': 'Sneha Gupta', 'email': '<EMAIL>', 'upline': 'regional_001'},
      {'id': 'team_003', 'name': 'Vikram Patel', 'email': '<EMAIL>', 'upline': 'regional_002'},
      {'id': 'team_004', 'name': 'Kavya Reddy', 'email': '<EMAIL>', 'upline': 'regional_002'},
    ];

    for (int i = 0; i < teamLeaders.length; i++) {
      final leader = teamLeaders[i];
      users.add({
        'id': leader['id'],
        'email': leader['email'],
        'name': leader['name'],
        'phoneNumber': '+91-987654${3220 + i}',
        'role': 'team_leader',
        'level': 2,
        'uplineId': leader['upline'],
        'downlineIds': ['senior_${i * 3 + 1}', 'senior_${i * 3 + 2}', 'senior_${i * 3 + 3}'],
        'referralCode': '${leader['name']!.split(' ')[0].toUpperCase()}2024',
        'referredBy': leader['upline'] == 'regional_001' ? 'RAJESH2024' : 'PRIYA2024',
        'totalStars': 60 + (i * 5),
        'totalCommissions': 280000.0 + (i * 20000),
        'isActive': true,
        'joinedAt': DateTime.now().subtract(Duration(days: 250 - (i * 20))).toIso8601String(),
        'lastActiveAt': DateTime.now().subtract(Duration(hours: i + 2)).toIso8601String(),
        'profileImageUrl': 'https://via.placeholder.com/150/FF6B35/FFFFFF?text=${leader['name']![0]}',
        'address': 'Sector ${40 + i}, Gurgaon',
        'achievements': ['consistent_performer', 'team_builder'],
        'metadata': {
          'isVerified': true,
          'kycStatus': 'approved',
          'tier': 'gold',
          'signupSource': 'referral',
          'specialization': 'residential',
        },
      });
    }

    // Senior Agents (Level 3) - 12 agents
    for (int i = 1; i <= 12; i++) {
      final teamIndex = ((i - 1) ~/ 3);
      users.add({
        'id': 'senior_$i',
        'email': 'senior.agent$<EMAIL>',
        'name': 'Senior Agent $i',
        'phoneNumber': '+91-876543${2100 + i}',
        'role': 'senior_agent',
        'level': 3,
        'uplineId': 'team_${teamIndex + 1:03d}',
        'downlineIds': ['junior_${i * 2 - 1}', 'junior_${i * 2}'],
        'referralCode': 'SENIOR$i',
        'referredBy': teamLeaders[teamIndex]['name']!.split(' ')[0].toUpperCase() + '2024',
        'totalStars': 35 + (i % 10),
        'totalCommissions': 150000.0 + (i * 5000),
        'isActive': i % 8 != 0,
        'joinedAt': DateTime.now().subtract(Duration(days: 180 - (i * 5))).toIso8601String(),
        'lastActiveAt': DateTime.now().subtract(Duration(hours: i)).toIso8601String(),
        'profileImageUrl': 'https://via.placeholder.com/150/10B981/FFFFFF?text=S$i',
        'address': 'Block $i, Noida',
        'achievements': i % 3 == 0 ? ['rising_star'] : [],
        'metadata': {
          'isVerified': true,
          'kycStatus': i % 7 == 0 ? 'pending' : 'approved',
          'tier': i % 4 == 0 ? 'silver' : 'bronze',
          'signupSource': 'referral',
          'specialization': i % 2 == 0 ? 'commercial' : 'residential',
        },
      });
    }

    // Junior Agents (Level 4) - 24 agents
    for (int i = 1; i <= 24; i++) {
      final seniorIndex = ((i - 1) ~/ 2) + 1;
      users.add({
        'id': 'junior_$i',
        'email': 'junior.agent$<EMAIL>',
        'name': 'Junior Agent $i',
        'phoneNumber': '+91-765432${1000 + i}',
        'role': 'junior_agent',
        'level': 4,
        'uplineId': 'senior_$seniorIndex',
        'downlineIds': [],
        'referralCode': 'JUNIOR$i',
        'referredBy': 'SENIOR$seniorIndex',
        'totalStars': 5 + (i % 15),
        'totalCommissions': 25000.0 + (i * 2000),
        'isActive': i % 6 != 0,
        'joinedAt': DateTime.now().subtract(Duration(days: 90 - (i * 2))).toIso8601String(),
        'lastActiveAt': DateTime.now().subtract(Duration(hours: i + 10)).toIso8601String(),
        'profileImageUrl': 'https://via.placeholder.com/150/F59E0B/FFFFFF?text=J$i',
        'address': 'Area $i, Faridabad',
        'achievements': i % 5 == 0 ? ['newcomer'] : [],
        'metadata': {
          'isVerified': i % 3 != 0,
          'kycStatus': i % 5 == 0 ? 'pending' : 'approved',
          'tier': 'bronze',
          'signupSource': 'referral',
          'specialization': 'residential',
        },
      });
    }

    return users;
  }

  /// Generate property listings
  static List<Map<String, dynamic>> _generateProperties() {
    final properties = <Map<String, dynamic>>[];
    
    final propertyTypes = ['Apartment', 'Villa', 'Plot', 'Commercial', 'Penthouse'];
    final locations = [
      'Sector 47, Gurgaon',
      'Dwarka, Delhi',
      'Noida Extension',
      'Greater Kailash, Delhi',
      'Cyber City, Gurgaon',
      'Connaught Place, Delhi',
      'Sector 62, Noida',
      'Vasant Kunj, Delhi',
    ];

    for (int i = 1; i <= 50; i++) {
      final propertyType = propertyTypes[i % propertyTypes.length];
      final location = locations[i % locations.length];
      final isCommercial = propertyType == 'Commercial';
      
      properties.add({
        'id': 'property_$i',
        'title': '$propertyType in ${location.split(',')[0]}',
        'description': _getPropertyDescription(propertyType, i),
        'price': _getPropertyPrice(propertyType, i),
        'location': location,
        'type': propertyType.toLowerCase(),
        'bedrooms': isCommercial ? 0 : (2 + (i % 4)),
        'bathrooms': isCommercial ? 0 : (1 + (i % 3)),
        'squareFeet': isCommercial ? (1000 + (i * 100)) : (800 + (i * 50)),
        'isActive': i % 8 != 0,
        'isFeatured': i % 7 == 0,
        'agentId': 'senior_${(i % 12) + 1}',
        'images': [
          'https://via.placeholder.com/800x600/FF6B35/FFFFFF?text=$propertyType+$i',
          'https://via.placeholder.com/800x600/8B5CF6/FFFFFF?text=Interior+$i',
          'https://via.placeholder.com/800x600/10B981/FFFFFF?text=Exterior+$i',
        ],
        'amenities': _getPropertyAmenities(propertyType, i),
        'status': _getPropertyStatus(i),
        'createdAt': DateTime.now().subtract(Duration(days: i * 2)).toIso8601String(),
        'updatedAt': DateTime.now().subtract(Duration(days: i)).toIso8601String(),
        'metadata': {
          'propertyAge': i % 10,
          'parking': isCommercial ? (i % 5) + 5 : (i % 3) + 1,
          'floor': isCommercial ? (i % 20) + 1 : (i % 10) + 1,
          'totalFloors': isCommercial ? (i % 30) + 10 : (i % 15) + 5,
          'facing': ['North', 'South', 'East', 'West'][i % 4],
          'furnishing': ['Unfurnished', 'Semi-Furnished', 'Fully-Furnished'][i % 3],
        },
      });
    }

    return properties;
  }

  static String _getPropertyDescription(String type, int index) {
    final descriptions = {
      'Apartment': 'Spacious ${2 + (index % 4)}BHK apartment with modern amenities and excellent connectivity.',
      'Villa': 'Luxurious independent villa with private garden and premium finishes.',
      'Plot': 'Prime residential plot in developing area with clear title and approved layout.',
      'Commercial': 'Premium commercial space in business district with high footfall.',
      'Penthouse': 'Exclusive penthouse with panoramic city views and luxury amenities.',
    };
    return descriptions[type] ?? 'Premium property with excellent features.';
  }

  static double _getPropertyPrice(String type, int index) {
    final basePrices = {
      'Apartment': 5000000.0,
      'Villa': 15000000.0,
      'Plot': 3000000.0,
      'Commercial': 8000000.0,
      'Penthouse': 25000000.0,
    };
    final basePrice = basePrices[type] ?? 5000000.0;
    return basePrice + (index * 100000);
  }

  static List<String> _getPropertyAmenities(String type, int index) {
    final commonAmenities = ['Security', 'Power Backup', 'Water Supply'];
    final residentialAmenities = ['Swimming Pool', 'Gym', 'Garden', 'Parking', 'Elevator'];
    final commercialAmenities = ['Conference Room', 'Reception', 'Cafeteria', 'High-Speed Internet'];
    
    if (type == 'Commercial') {
      return [...commonAmenities, ...commercialAmenities.take(index % 4 + 1)];
    } else {
      return [...commonAmenities, ...residentialAmenities.take(index % 5 + 2)];
    }
  }

  static String _getPropertyStatus(int index) {
    final statuses = ['available', 'sold', 'under_negotiation', 'reserved'];
    return statuses[index % statuses.length];
  }

  /// Generate transaction records
  static List<Map<String, dynamic>> _generateTransactions() {
    final transactions = <Map<String, dynamic>>[];

    // Generate 30 transactions over the past 6 months
    for (int i = 1; i <= 30; i++) {
      final propertyId = 'property_${(i % 50) + 1}';
      final agentId = 'senior_${(i % 12) + 1}';
      final saleAmount = 5000000.0 + (i * 200000);

      transactions.add({
        'id': 'transaction_$i',
        'propertyId': propertyId,
        'agentId': agentId,
        'buyerName': 'Buyer $i',
        'buyerEmail': 'buyer$<EMAIL>',
        'buyerPhone': '+91-9${i.toString().padLeft(9, '0')}',
        'saleAmount': saleAmount,
        'commissionAmount': saleAmount * 0.02, // 2% commission
        'status': i % 10 == 0 ? 'pending' : 'completed',
        'saleDate': DateTime.now().subtract(Duration(days: i * 6)).toIso8601String(),
        'createdAt': DateTime.now().subtract(Duration(days: i * 6 + 1)).toIso8601String(),
        'updatedAt': DateTime.now().subtract(Duration(days: i * 6)).toIso8601String(),
        'metadata': {
          'paymentMethod': ['cash', 'loan', 'installment'][i % 3],
          'loanAmount': i % 3 == 1 ? saleAmount * 0.8 : 0,
          'registrationFee': saleAmount * 0.01,
          'brokerageFee': saleAmount * 0.02,
          'documentStatus': i % 8 == 0 ? 'pending' : 'completed',
        },
      });
    }

    return transactions;
  }

  /// Generate commission records
  static List<Map<String, dynamic>> _generateCommissions() {
    final commissions = <Map<String, dynamic>>[];

    // Generate commissions for each transaction
    for (int transactionIndex = 1; transactionIndex <= 30; transactionIndex++) {
      final transactionId = 'transaction_$transactionIndex';
      final saleAmount = 5000000.0 + (transactionIndex * 200000);
      final totalCommission = saleAmount * 0.02;

      // Get the agent hierarchy for this transaction
      final agentId = 'senior_${(transactionIndex % 12) + 1}';
      final teamLeaderId = 'team_${((transactionIndex % 12) ~/ 3) + 1}';
      final regionalId = ((transactionIndex % 12) ~/ 6) == 0 ? 'regional_001' : 'regional_002';

      // Level 1: Direct agent (40% of commission)
      commissions.add({
        'id': 'commission_${transactionIndex}_1',
        'transactionId': transactionId,
        'agentId': agentId,
        'level': 1,
        'amount': totalCommission * 0.40,
        'percentage': 40.0,
        'status': transactionIndex % 10 == 0 ? 'pending' : 'paid',
        'paidAt': transactionIndex % 10 == 0 ? null :
                  DateTime.now().subtract(Duration(days: transactionIndex * 6 - 2)).toIso8601String(),
        'createdAt': DateTime.now().subtract(Duration(days: transactionIndex * 6)).toIso8601String(),
        'metadata': {
          'commissionType': 'direct_sale',
          'paymentMethod': 'bank_transfer',
          'taxDeducted': totalCommission * 0.40 * 0.1, // 10% TDS
        },
      });

      // Level 2: Team leader (25% of commission)
      commissions.add({
        'id': 'commission_${transactionIndex}_2',
        'transactionId': transactionId,
        'agentId': teamLeaderId,
        'level': 2,
        'amount': totalCommission * 0.25,
        'percentage': 25.0,
        'status': transactionIndex % 10 == 0 ? 'pending' : 'paid',
        'paidAt': transactionIndex % 10 == 0 ? null :
                  DateTime.now().subtract(Duration(days: transactionIndex * 6 - 3)).toIso8601String(),
        'createdAt': DateTime.now().subtract(Duration(days: transactionIndex * 6)).toIso8601String(),
        'metadata': {
          'commissionType': 'team_bonus',
          'paymentMethod': 'bank_transfer',
          'taxDeducted': totalCommission * 0.25 * 0.1,
        },
      });

      // Level 3: Regional manager (20% of commission)
      commissions.add({
        'id': 'commission_${transactionIndex}_3',
        'transactionId': transactionId,
        'agentId': regionalId,
        'level': 3,
        'amount': totalCommission * 0.20,
        'percentage': 20.0,
        'status': transactionIndex % 10 == 0 ? 'pending' : 'paid',
        'paidAt': transactionIndex % 10 == 0 ? null :
                  DateTime.now().subtract(Duration(days: transactionIndex * 6 - 4)).toIso8601String(),
        'createdAt': DateTime.now().subtract(Duration(days: transactionIndex * 6)).toIso8601String(),
        'metadata': {
          'commissionType': 'regional_bonus',
          'paymentMethod': 'bank_transfer',
          'taxDeducted': totalCommission * 0.20 * 0.1,
        },
      });

      // Level 4: Company (15% of commission)
      commissions.add({
        'id': 'commission_${transactionIndex}_4',
        'transactionId': transactionId,
        'agentId': 'admin_001',
        'level': 4,
        'amount': totalCommission * 0.15,
        'percentage': 15.0,
        'status': 'paid',
        'paidAt': DateTime.now().subtract(Duration(days: transactionIndex * 6 - 1)).toIso8601String(),
        'createdAt': DateTime.now().subtract(Duration(days: transactionIndex * 6)).toIso8601String(),
        'metadata': {
          'commissionType': 'company_share',
          'paymentMethod': 'internal_transfer',
          'taxDeducted': 0,
        },
      });
    }

    return commissions;
  }

  /// Generate star achievements
  static List<Map<String, dynamic>> _generateStars() {
    final stars = <Map<String, dynamic>>[];

    // Generate stars for various achievements
    for (int agentIndex = 1; agentIndex <= 36; agentIndex++) { // 12 senior + 24 junior agents
      final agentId = agentIndex <= 12 ? 'senior_$agentIndex' : 'junior_${agentIndex - 12}';
      final starCount = agentIndex <= 12 ? (5 + (agentIndex % 8)) : (1 + (agentIndex % 5));

      for (int starIndex = 1; starIndex <= starCount; starIndex++) {
        final starTypes = ['first_sale', 'monthly_target', 'team_building', 'customer_satisfaction', 'referral_bonus'];
        final starType = starTypes[starIndex % starTypes.length];

        stars.add({
          'id': 'star_${agentId}_$starIndex',
          'agentId': agentId,
          'type': starType,
          'reason': _getStarReason(starType, starIndex),
          'points': _getStarPoints(starType),
          'awardedAt': DateTime.now().subtract(Duration(days: starIndex * 15)).toIso8601String(),
          'transactionId': starType == 'first_sale' ? 'transaction_${(agentIndex % 30) + 1}' : null,
          'metadata': {
            'category': _getStarCategory(starType),
            'milestone': starType == 'monthly_target' ? 'Month $starIndex' : null,
            'teamSize': starType == 'team_building' ? starIndex + 2 : null,
            'rating': starType == 'customer_satisfaction' ? (4.0 + (starIndex % 2) * 0.5) : null,
          },
        });
      }
    }

    return stars;
  }

  static String _getStarReason(String type, int index) {
    final reasons = {
      'first_sale': 'Completed first property sale successfully',
      'monthly_target': 'Achieved monthly sales target of ₹${(index * 500000)}',
      'team_building': 'Successfully built team of ${index + 2} active agents',
      'customer_satisfaction': 'Received 5-star customer rating',
      'referral_bonus': 'Brought $index new agents to the network',
    };
    return reasons[type] ?? 'Outstanding performance';
  }

  static int _getStarPoints(String type) {
    final points = {
      'first_sale': 10,
      'monthly_target': 15,
      'team_building': 20,
      'customer_satisfaction': 5,
      'referral_bonus': 8,
    };
    return points[type] ?? 5;
  }

  static String _getStarCategory(String type) {
    final categories = {
      'first_sale': 'sales',
      'monthly_target': 'performance',
      'team_building': 'leadership',
      'customer_satisfaction': 'service',
      'referral_bonus': 'growth',
    };
    return categories[type] ?? 'general';
  }

  /// Generate lead records
  static List<Map<String, dynamic>> _generateLeads() {
    final leads = <Map<String, dynamic>>[];

    // Generate 100 leads
    for (int i = 1; i <= 100; i++) {
      final agentId = 'senior_${(i % 12) + 1}';
      final propertyId = 'property_${(i % 50) + 1}';
      final leadStatuses = ['new', 'contacted', 'interested', 'negotiating', 'converted', 'lost'];
      final leadSources = ['website', 'referral', 'social_media', 'advertisement', 'walk_in'];

      leads.add({
        'id': 'lead_$i',
        'customerName': 'Customer $i',
        'customerEmail': 'customer$<EMAIL>',
        'customerPhone': '+91-8${i.toString().padLeft(9, '0')}',
        'propertyId': propertyId,
        'agentId': agentId,
        'status': leadStatuses[i % leadStatuses.length],
        'source': leadSources[i % leadSources.length],
        'budget': (2000000 + (i * 100000)).toDouble(),
        'requirements': _getLeadRequirements(i),
        'notes': 'Lead generated from ${leadSources[i % leadSources.length]}. Customer interested in ${_getPropertyType(i)}.',
        'priority': ['low', 'medium', 'high'][i % 3],
        'followUpDate': DateTime.now().add(Duration(days: i % 7 + 1)).toIso8601String(),
        'createdAt': DateTime.now().subtract(Duration(days: i)).toIso8601String(),
        'updatedAt': DateTime.now().subtract(Duration(hours: i % 24)).toIso8601String(),
        'interactions': _generateLeadInteractions(i),
        'metadata': {
          'preferredLocation': ['Gurgaon', 'Delhi', 'Noida', 'Faridabad'][i % 4],
          'timeframe': ['immediate', '1-3 months', '3-6 months', '6+ months'][i % 4],
          'financingRequired': i % 3 == 0,
          'isFirstTimeBuyer': i % 4 == 0,
        },
      });
    }

    return leads;
  }

  static String _getLeadRequirements(int index) {
    final requirements = [
      '2-3 BHK apartment with parking',
      'Independent villa with garden',
      'Commercial space for office',
      'Plot for construction',
      'Luxury penthouse with amenities',
    ];
    return requirements[index % requirements.length];
  }

  static String _getPropertyType(int index) {
    final types = ['apartment', 'villa', 'commercial space', 'plot', 'penthouse'];
    return types[index % types.length];
  }

  static List<Map<String, dynamic>> _generateLeadInteractions(int leadIndex) {
    final interactions = <Map<String, dynamic>>[];
    final interactionCount = (leadIndex % 5) + 1;

    for (int i = 0; i < interactionCount; i++) {
      final interactionTypes = ['call', 'email', 'meeting', 'site_visit', 'follow_up'];
      interactions.add({
        'type': interactionTypes[i % interactionTypes.length],
        'notes': 'Interaction ${i + 1} with customer',
        'date': DateTime.now().subtract(Duration(days: leadIndex - i)).toIso8601String(),
        'duration': (15 + (i * 10)), // minutes
        'outcome': ['positive', 'neutral', 'negative'][i % 3],
      });
    }

    return interactions;
  }
}
