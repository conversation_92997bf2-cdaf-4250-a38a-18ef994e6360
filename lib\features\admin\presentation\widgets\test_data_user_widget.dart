import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../shared/themes/app_theme.dart';
import '../providers/test_data_providers.dart';

/// Widget to display test data users in admin dashboard
class TestDataUserWidget extends ConsumerWidget {
  const TestDataUserWidget({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final usersAsync = ref.watch(testDataUsersProvider);
    final testDataStatus = ref.watch(testDataStatusProvider);

    return testDataStatus.when(
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Text('Error: $error', style: const TextStyle(color: Colors.red)),
      ),
      data: (isLoaded) {
        if (!isLoaded) {
          return _buildNoDataWidget();
        }

        return usersAsync.when(
          loading: () => const Center(child: CircularProgressIndicator()),
          error: (error, stack) => Center(
            child: Text('Error loading users: $error', style: const TextStyle(color: Colors.red)),
          ),
          data: (users) => _buildUsersWidget(users),
        );
      },
    );
  }

  Widget _buildNoDataWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.people_outline,
            size: 64,
            color: Colors.grey[600],
          ),
          const SizedBox(height: 16),
          Text(
            'No Test Data Loaded',
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.w600,
              color: Colors.grey[400],
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'Load test data to see MLM user hierarchy',
            style: TextStyle(
              fontSize: 14,
              color: Colors.grey[600],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUsersWidget(List<Map<String, dynamic>> users) {
    // Group users by level
    final usersByLevel = <int, List<Map<String, dynamic>>>{};
    for (final user in users) {
      final level = user['level'] as int;
      usersByLevel[level] = (usersByLevel[level] ?? [])..add(user);
    }

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Summary Cards
          Row(
            children: [
              Expanded(
                child: _buildSummaryCard(
                  'Total Users',
                  users.length.toString(),
                  Icons.people,
                  AppTheme.primaryColor,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'Active Users',
                  users.where((u) => u['isActive'] == true).length.toString(),
                  Icons.person_check,
                  Colors.green,
                ),
              ),
              const SizedBox(width: 16),
              Expanded(
                child: _buildSummaryCard(
                  'Hierarchy Levels',
                  usersByLevel.keys.length.toString(),
                  Icons.account_tree,
                  AppTheme.accentColor,
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Users by Level
          Text(
            'MLM Hierarchy',
            style: TextStyle(
              fontSize: 20,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          const SizedBox(height: 16),

          ...usersByLevel.entries.map((entry) {
            final level = entry.key;
            final levelUsers = entry.value;
            return _buildLevelSection(level, levelUsers);
          }).toList(),
        ],
      ),
    );
  }

  Widget _buildSummaryCard(String title, String value, IconData icon, Color color) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withOpacity(0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 24,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
          Text(
            title,
            style: TextStyle(
              fontSize: 12,
              color: Colors.white.withOpacity(0.7),
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildLevelSection(int level, List<Map<String, dynamic>> users) {
    final levelNames = {
      0: 'Admin',
      1: 'Regional Managers',
      2: 'Team Leaders',
      3: 'Senior Agents',
      4: 'Junior Agents',
    };

    final levelColors = {
      0: Colors.red,
      1: Colors.purple,
      2: Colors.blue,
      3: Colors.green,
      4: Colors.orange,
    };

    return Container(
      margin: const EdgeInsets.only(bottom: 16),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: levelColors[level]!.withOpacity(0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: levelColors[level]!.withOpacity(0.1),
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(12),
                topRight: Radius.circular(12),
              ),
            ),
            child: Row(
              children: [
                Icon(
                  Icons.group,
                  color: levelColors[level],
                  size: 20,
                ),
                const SizedBox(width: 8),
                Text(
                  'Level $level - ${levelNames[level] ?? 'Unknown'}',
                  style: TextStyle(
                    fontSize: 16,
                    fontWeight: FontWeight.bold,
                    color: levelColors[level],
                  ),
                ),
                const Spacer(),
                Container(
                  padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                  decoration: BoxDecoration(
                    color: levelColors[level]!.withOpacity(0.2),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    '${users.length} users',
                    style: TextStyle(
                      fontSize: 12,
                      color: levelColors[level],
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              ],
            ),
          ),
          Padding(
            padding: const EdgeInsets.all(16),
            child: Wrap(
              spacing: 8,
              runSpacing: 8,
              children: users.map((user) => _buildUserChip(user, levelColors[level]!)).toList(),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildUserChip(Map<String, dynamic> user, Color color) {
    final isActive = user['isActive'] as bool? ?? false;
    final totalStars = user['totalStars'] as int? ?? 0;
    final totalCommissions = user['totalCommissions'] as double? ?? 0.0;

    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withOpacity(0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(
          color: isActive ? color.withOpacity(0.5) : Colors.grey.withOpacity(0.3),
        ),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        mainAxisSize: MainAxisSize.min,
        children: [
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              CircleAvatar(
                radius: 12,
                backgroundColor: color.withOpacity(0.2),
                child: Text(
                  user['name']?.toString().substring(0, 1).toUpperCase() ?? '?',
                  style: TextStyle(
                    fontSize: 10,
                    fontWeight: FontWeight.bold,
                    color: color,
                  ),
                ),
              ),
              const SizedBox(width: 8),
              Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Text(
                    user['name']?.toString() ?? 'Unknown',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                      color: isActive ? Colors.white : Colors.grey,
                    ),
                  ),
                  Text(
                    user['role']?.toString().replaceAll('_', ' ').toUpperCase() ?? '',
                    style: TextStyle(
                      fontSize: 10,
                      color: color.withOpacity(0.8),
                    ),
                  ),
                ],
              ),
            ],
          ),
          const SizedBox(height: 4),
          Row(
            mainAxisSize: MainAxisSize.min,
            children: [
              Icon(Icons.star, size: 12, color: Colors.amber),
              const SizedBox(width: 2),
              Text(
                '$totalStars',
                style: const TextStyle(fontSize: 10, color: Colors.white70),
              ),
              const SizedBox(width: 8),
              Icon(Icons.currency_rupee, size: 12, color: Colors.green),
              const SizedBox(width: 2),
              Text(
                '${(totalCommissions / 1000).toStringAsFixed(0)}K',
                style: const TextStyle(fontSize: 10, color: Colors.white70),
              ),
            ],
          ),
        ],
      ),
    );
  }
}
