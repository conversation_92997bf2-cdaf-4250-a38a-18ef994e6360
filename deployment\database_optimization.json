{"indexes": [{"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "email", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "referralCode", "order": "ASCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "uplineId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isActive", "order": "ASCENDING"}, {"fieldPath": "totalStars", "order": "DESCENDING"}]}, {"collectionGroup": "users", "queryScope": "COLLECTION", "fields": [{"fieldPath": "level", "order": "ASCENDING"}, {"fieldPath": "totalCommissions", "order": "DESCENDING"}]}, {"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "city", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "price", "order": "ASCENDING"}]}, {"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isApproved", "order": "ASCENDING"}, {"fieldPath": "isFeatured", "order": "DESCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "properties", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agentId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "commissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agentId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "commissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agentId", "order": "ASCENDING"}, {"fieldPath": "isPaid", "order": "ASCENDING"}]}, {"collectionGroup": "commissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "transactionId", "order": "ASCENDING"}, {"fieldPath": "level", "order": "ASCENDING"}]}, {"collectionGroup": "commissions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "isPaid", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agentId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "amount", "order": "DESCENDING"}]}, {"collectionGroup": "transactions", "queryScope": "COLLECTION", "fields": [{"fieldPath": "propertyId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "stars", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agentId", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "stars", "queryScope": "COLLECTION", "fields": [{"fieldPath": "transactionId", "order": "ASCENDING"}, {"fieldPath": "source", "order": "ASCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agentId", "order": "ASCENDING"}, {"fieldPath": "status", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "propertyId", "order": "ASCENDING"}, {"fieldPath": "priority", "order": "DESCENDING"}]}, {"collectionGroup": "leads", "queryScope": "COLLECTION", "fields": [{"fieldPath": "agentId", "order": "ASCENDING"}, {"fieldPath": "followUpDate", "order": "ASCENDING"}]}, {"collectionGroup": "notifications", "queryScope": "COLLECTION", "fields": [{"fieldPath": "userId", "order": "ASCENDING"}, {"fieldPath": "isRead", "order": "ASCENDING"}, {"fieldPath": "createdAt", "order": "DESCENDING"}]}, {"collectionGroup": "analytics", "queryScope": "COLLECTION", "fields": [{"fieldPath": "type", "order": "ASCENDING"}, {"fieldPath": "date", "order": "DESCENDING"}]}], "fieldOverrides": [{"collectionGroup": "users", "fieldPath": "downlineIds", "indexes": [{"order": "ASCENDING", "queryScope": "COLLECTION"}, {"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "properties", "fieldPath": "amenities", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "properties", "fieldPath": "imageUrls", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}, {"collectionGroup": "leads", "fieldPath": "interactions", "indexes": [{"arrayConfig": "CONTAINS", "queryScope": "COLLECTION"}]}]}