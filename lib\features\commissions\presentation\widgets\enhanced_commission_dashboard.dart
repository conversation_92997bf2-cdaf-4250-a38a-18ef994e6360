import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';
import 'package:intl/intl.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../models/commission_enums.dart';
import '../../models/enhanced_commission_model.dart';
import '../../services/commission_analytics_service.dart';

/// Enhanced commission dashboard with analytics and charts
class EnhancedCommissionDashboard extends ConsumerStatefulWidget {
  final String agentId;

  const EnhancedCommissionDashboard({
    super.key,
    required this.agentId,
  });

  @override
  ConsumerState<EnhancedCommissionDashboard> createState() => _EnhancedCommissionDashboardState();
}

class _EnhancedCommissionDashboardState extends ConsumerState<EnhancedCommissionDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  CommissionAnalytics? _analytics;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadAnalytics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadAnalytics() async {
    setState(() => _isLoading = true);
    
    try {
      final analytics = await CommissionAnalyticsService.getAgentAnalytics(widget.agentId);
      setState(() {
        _analytics = analytics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(
        child: CircularProgressIndicator(),
      );
    }

    if (_analytics == null) {
      return _buildErrorWidget();
    }

    return Column(
      children: [
        // Summary Cards
        _buildSummaryCards(),
        
        const SizedBox(height: 24),
        
        // Tab Bar
        _buildTabBar(),
        
        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildAnalyticsTab(),
              _buildLeaderboardTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// Build summary cards
  Widget _buildSummaryCards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildSummaryCard(
          'Total Earned',
          _formatCurrency(_analytics!.totalEarned),
          Icons.account_balance_wallet,
          AppTheme.successColor,
          subtitle: 'All time earnings',
        ),
        _buildSummaryCard(
          'Pending',
          _formatCurrency(_analytics!.totalPending),
          Icons.schedule,
          AppTheme.warningColor,
          subtitle: 'Awaiting payment',
        ),
        _buildSummaryCard(
          'Monthly Avg',
          _formatCurrency(_analytics!.monthlyAverage),
          Icons.trending_up,
          AppTheme.primaryColor,
          subtitle: 'Average per month',
        ),
        _buildSummaryCard(
          'Growth Rate',
          '${_analytics!.growthRate.toStringAsFixed(1)}%',
          Icons.show_chart,
          _analytics!.growthRate >= 0 ? AppTheme.successColor : AppTheme.errorColor,
          subtitle: 'Month over month',
        ),
      ],
    );
  }

  /// Build summary card
  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: _analytics!.currentTier.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  _analytics!.currentTier.displayName,
                  style: TextStyle(
                    color: _analytics!.currentTier.color,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: AppTheme.darkHintText,
                fontSize: 10,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.darkSecondaryText,
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Analytics'),
          Tab(text: 'Leaderboard'),
        ],
      ),
    );
  }

  /// Build overview tab
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Tier Progress
          _buildTierProgress(),
          
          const SizedBox(height: 24),
          
          // Commission Types Breakdown
          _buildCommissionTypesBreakdown(),
          
          const SizedBox(height: 24),
          
          // Recent Commissions
          _buildRecentCommissions(),
        ],
      ),
    );
  }

  /// Build analytics tab
  Widget _buildAnalyticsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Monthly Trend Chart
          _buildMonthlyTrendChart(),
          
          const SizedBox(height: 24),
          
          // Commission Types Pie Chart
          _buildCommissionTypesPieChart(),
        ],
      ),
    );
  }

  /// Build leaderboard tab
  Widget _buildLeaderboardTab() {
    return FutureBuilder<List<AgentCommissionSummary>>(
      future: CommissionAnalyticsService.getCommissionLeaderboard(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const Center(child: CircularProgressIndicator());
        }
        
        if (!snapshot.hasData || snapshot.data!.isEmpty) {
          return const Center(
            child: Text('No leaderboard data available'),
          );
        }
        
        return _buildLeaderboardList(snapshot.data!);
      },
    );
  }

  /// Build tier progress
  Widget _buildTierProgress() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(
                _analytics!.currentTier.icon,
                color: _analytics!.currentTier.color,
                size: 24,
              ),
              const SizedBox(width: 12),
              Text(
                'Current Tier: ${_analytics!.currentTier.displayName}',
                style: TextStyle(
                  color: AppTheme.darkPrimaryText,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          
          const SizedBox(height: 16),
          
          Text(
            'Progress to Next Tier',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
          ),
          
          const SizedBox(height: 8),
          
          GradientWidgets.gradientProgressBar(
            progress: _analytics!.nextTierProgress,
            height: 12,
            borderRadius: 6,
          ),
          
          const SizedBox(height: 8),
          
          Text(
            '${(_analytics!.nextTierProgress * 100).toStringAsFixed(1)}% Complete',
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build commission types breakdown
  Widget _buildCommissionTypesBreakdown() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Commission Breakdown',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          ...CommissionType.values.map((type) {
            final amount = _analytics!.typeBreakdown[type] ?? 0;
            if (amount <= 0) return const SizedBox.shrink();
            
            return Padding(
              padding: const EdgeInsets.only(bottom: 12),
              child: Row(
                children: [
                  Container(
                    width: 12,
                    height: 12,
                    decoration: BoxDecoration(
                      color: type.color,
                      borderRadius: BorderRadius.circular(6),
                    ),
                  ),
                  const SizedBox(width: 12),
                  Expanded(
                    child: Text(
                      type.displayName,
                      style: TextStyle(
                        color: AppTheme.darkPrimaryText,
                        fontSize: 14,
                      ),
                    ),
                  ),
                  Text(
                    _formatCurrency(amount),
                    style: TextStyle(
                      color: AppTheme.darkPrimaryText,
                      fontSize: 14,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ],
              ),
            );
          }).toList(),
        ],
      ),
    );
  }

  /// Build recent commissions placeholder
  Widget _buildRecentCommissions() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Recent Commissions',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Recent commission history will be displayed here.',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Build monthly trend chart
  Widget _buildMonthlyTrendChart() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Monthly Earnings Trend',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: LineChart(
              LineChartData(
                gridData: FlGridData(show: false),
                titlesData: FlTitlesData(show: false),
                borderData: FlBorderData(show: false),
                lineBarsData: [
                  LineChartBarData(
                    spots: _analytics!.monthlyData.asMap().entries.map((entry) {
                      return FlSpot(entry.key.toDouble(), entry.value.amount / 1000);
                    }).toList(),
                    isCurved: true,
                    gradient: AppTheme.primaryGradient,
                    barWidth: 3,
                    dotData: FlDotData(show: false),
                    belowBarData: BarAreaData(
                      show: true,
                      gradient: LinearGradient(
                        colors: [
                          AppTheme.primaryColor.withValues(alpha: 0.3),
                          AppTheme.primaryColor.withValues(alpha: 0.1),
                        ],
                        begin: Alignment.topCenter,
                        end: Alignment.bottomCenter,
                      ),
                    ),
                  ),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build commission types pie chart
  Widget _buildCommissionTypesPieChart() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Commission Distribution',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _analytics!.typeBreakdown.entries
                    .where((entry) => entry.value > 0)
                    .map((entry) {
                  return PieChartSectionData(
                    value: entry.value,
                    title: '${(entry.value / _analytics!.totalEarned * 100).toStringAsFixed(1)}%',
                    color: entry.key.color,
                    radius: 60,
                    titleStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList(),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build leaderboard list
  Widget _buildLeaderboardList(List<AgentCommissionSummary> leaderboard) {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: leaderboard.length,
      itemBuilder: (context, index) {
        final agent = leaderboard[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: AppTheme.createCardDecoration(),
          child: ListTile(
            leading: CircleAvatar(
              backgroundColor: agent.tier.color,
              child: Text(
                '${index + 1}',
                style: const TextStyle(
                  color: Colors.white,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            title: Text(
              agent.agentName,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(
              '${agent.commissionCount} commissions',
              style: TextStyle(
                color: AppTheme.darkSecondaryText,
                fontSize: 12,
              ),
            ),
            trailing: Text(
              _formatCurrency(agent.totalAmount),
              style: TextStyle(
                color: AppTheme.primaryColor,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load commission data',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          GradientWidgets.gradientButton(
            text: 'Retry',
            onPressed: _loadAnalytics,
            icon: Icons.refresh,
          ),
        ],
      ),
    );
  }

  /// Format currency
  String _formatCurrency(double amount) {
    final formatter = NumberFormat.currency(
      locale: 'en_IN',
      symbol: '₹',
      decimalDigits: 0,
    );
    return formatter.format(amount);
  }
}
