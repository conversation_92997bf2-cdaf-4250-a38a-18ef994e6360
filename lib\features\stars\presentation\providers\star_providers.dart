import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/star_model.dart';
import '../../../../core/services/star_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

/// Agent star history provider
final agentStarHistoryProvider = FutureProvider.autoDispose<List<StarModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  return await StarService.getAgentStarHistory(currentUser.id);
});

/// Agent star statistics provider
final agentStarStatsProvider = FutureProvider.autoDispose<StarStatsModel>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return const StarStatsModel();
  
  return await StarService.getAgentStarStats(currentUser.id);
});

/// Agent star bonuses provider
final agentStarBonusesProvider = FutureProvider.autoDispose<List<StarBonusModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  return await StarService.getAgentStarBonuses(currentUser.id);
});

/// All star bonuses provider (Admin only)
final allStarBonusesProvider = FutureProvider.autoDispose<List<StarBonusModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) return [];
  
  return await StarService.getAllStarBonuses();
});

/// Star leaderboard provider
final starLeaderboardProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  return await StarService.getStarLeaderboard(limit: 20);
});

/// Star filter provider
final starFilterProvider = StateNotifierProvider<StarFilterNotifier, StarFilter>((ref) {
  return StarFilterNotifier();
});

/// Filtered star history provider
final filteredStarHistoryProvider = FutureProvider.autoDispose<List<StarModel>>((ref) async {
  final filter = ref.watch(starFilterProvider);
  final currentUser = ref.watch(currentUserProvider);
  
  if (currentUser == null) return [];
  
  List<StarModel> stars = await StarService.getAgentStarHistory(currentUser.id);
  
  // Apply filters
  if (filter.type != null) {
    stars = stars.where((s) => s.type == filter.type).toList();
  }
  
  if (filter.source != null) {
    stars = stars.where((s) => s.source == filter.source).toList();
  }
  
  if (filter.startDate != null) {
    stars = stars.where((s) => s.earnedAt.isAfter(filter.startDate!)).toList();
  }
  
  if (filter.endDate != null) {
    stars = stars.where((s) => s.earnedAt.isBefore(filter.endDate!)).toList();
  }
  
  // Sort stars
  stars.sort((a, b) {
    switch (filter.sortBy) {
      case 'type':
        return filter.sortAscending ? a.type.compareTo(b.type) : b.type.compareTo(a.type);
      case 'source':
        return filter.sortAscending ? a.source.compareTo(b.source) : b.source.compareTo(a.source);
      case 'count':
        return filter.sortAscending ? a.count.compareTo(b.count) : b.count.compareTo(a.count);
      case 'date':
      default:
        return filter.sortAscending ? a.earnedAt.compareTo(b.earnedAt) : b.earnedAt.compareTo(a.earnedAt);
    }
  });
  
  return stars;
});

/// Star analytics provider
final starAnalyticsProvider = FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return {};
  
  final stats = await StarService.getAgentStarStats(currentUser.id);
  final stars = await StarService.getAgentStarHistory(currentUser.id);
  
  // Calculate additional analytics
  final thisMonthStars = stars.where((s) {
    final now = DateTime.now();
    return s.earnedAt.year == now.year && s.earnedAt.month == now.month;
  }).length;
  
  final lastMonthStars = stars.where((s) {
    final lastMonth = DateTime.now().subtract(const Duration(days: 30));
    return s.earnedAt.year == lastMonth.year && s.earnedAt.month == lastMonth.month;
  }).length;
  
  final growthRate = lastMonthStars > 0 
      ? ((thisMonthStars - lastMonthStars) / lastMonthStars) * 100 
      : 0.0;
  
  // Calculate average stars per month
  final monthsActive = stats.starsByMonth.keys.length;
  final avgStarsPerMonth = monthsActive > 0 ? stats.totalStars.toDouble() / monthsActive : 0.0;
  
  return {
    'stats': stats,
    'thisMonthStars': thisMonthStars,
    'lastMonthStars': lastMonthStars,
    'growthRate': growthRate,
    'avgStarsPerMonth': avgStarsPerMonth,
    'totalBonusesEarned': stats.bonusHistory.where((b) => b.isAwarded).length,
    'pendingBonuses': stats.bonusHistory.where((b) => b.isPending).length,
  };
});

/// Star filter
class StarFilter {
  final String? type;
  final String? source;
  final DateTime? startDate;
  final DateTime? endDate;
  final String sortBy;
  final bool sortAscending;

  const StarFilter({
    this.type,
    this.source,
    this.startDate,
    this.endDate,
    this.sortBy = 'date',
    this.sortAscending = false,
  });

  StarFilter copyWith({
    String? type,
    String? source,
    DateTime? startDate,
    DateTime? endDate,
    String? sortBy,
    bool? sortAscending,
  }) {
    return StarFilter(
      type: type ?? this.type,
      source: source ?? this.source,
      startDate: startDate ?? this.startDate,
      endDate: endDate ?? this.endDate,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is StarFilter &&
        other.type == type &&
        other.source == source &&
        other.startDate == startDate &&
        other.endDate == endDate &&
        other.sortBy == sortBy &&
        other.sortAscending == sortAscending;
  }

  @override
  int get hashCode => Object.hash(type, source, startDate, endDate, sortBy, sortAscending);
}

/// Star filter notifier
class StarFilterNotifier extends StateNotifier<StarFilter> {
  StarFilterNotifier() : super(const StarFilter());

  void setType(String? type) {
    state = state.copyWith(type: type);
  }

  void setSource(String? source) {
    state = state.copyWith(source: source);
  }

  void setDateRange(DateTime? startDate, DateTime? endDate) {
    state = state.copyWith(startDate: startDate, endDate: endDate);
  }

  void setSortBy(String sortBy) {
    state = state.copyWith(sortBy: sortBy);
  }

  void setSortAscending(bool ascending) {
    state = state.copyWith(sortAscending: ascending);
  }

  void resetFilters() {
    state = const StarFilter();
  }
}

/// Star progress provider for specific agent
final starProgressProvider = FutureProvider.autoDispose.family<Map<String, dynamic>, String>((ref, agentId) async {
  final stats = await StarService.getAgentStarStats(agentId);
  
  return {
    'totalStars': stats.totalStars,
    'progressToNextBonus': stats.progressToNextBonus,
    'starsToNextBonus': stats.starsToNextBonus,
    'nextBonusMilestone': stats.nextBonusMilestone,
    'isEligibleForBonus': stats.isEligibleForBonus,
    'bonusesEarned': stats.bonusHistory.where((b) => b.isAwarded).length,
  };
});

/// Monthly star trends provider
final monthlyStarTrendsProvider = FutureProvider.autoDispose<Map<String, int>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return {};
  
  final stats = await StarService.getAgentStarStats(currentUser.id);
  return stats.starsByMonth;
});

/// Star type distribution provider
final starTypeDistributionProvider = FutureProvider.autoDispose<Map<String, int>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return {};
  
  final stats = await StarService.getAgentStarStats(currentUser.id);
  return stats.starsByType;
});

/// Recent stars provider (last 10 stars)
final recentStarsProvider = FutureProvider.autoDispose<List<StarModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  final stars = await StarService.getAgentStarHistory(currentUser.id);
  return stars.take(10).toList();
});

/// Star achievements provider
final starAchievementsProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  final stats = await StarService.getAgentStarStats(currentUser.id);
  final achievements = <Map<String, dynamic>>[];
  
  // Define achievements
  final achievementDefinitions = [
    {'id': 'first_star', 'title': 'First Star', 'description': 'Earn your first star', 'threshold': 1, 'icon': '⭐'},
    {'id': 'five_stars', 'title': 'Rising Star', 'description': 'Earn 5 stars', 'threshold': 5, 'icon': '🌟'},
    {'id': 'ten_stars', 'title': 'Star Performer', 'description': 'Earn 10 stars', 'threshold': 10, 'icon': '✨'},
    {'id': 'twelve_stars', 'title': 'Star Champion', 'description': 'Reach 12-star milestone', 'threshold': 12, 'icon': '🏆'},
    {'id': 'twenty_four_stars', 'title': 'Star Master', 'description': 'Reach 24-star milestone', 'threshold': 24, 'icon': '👑'},
    {'id': 'fifty_stars', 'title': 'Star Legend', 'description': 'Earn 50 stars', 'threshold': 50, 'icon': '🎖️'},
  ];
  
  for (final achievement in achievementDefinitions) {
    final threshold = achievement['threshold'] as int;
    final isUnlocked = stats.totalStars >= threshold;
    final progress = isUnlocked ? 1.0 : stats.totalStars.toDouble() / threshold.toDouble();
    
    achievements.add({
      'id': achievement['id'],
      'title': achievement['title'],
      'description': achievement['description'],
      'icon': achievement['icon'],
      'threshold': achievement['threshold'],
      'isUnlocked': isUnlocked,
      'progress': progress,
      'currentValue': stats.totalStars,
    });
  }
  
  return achievements;
});

/// Star milestone progress provider
final starMilestoneProgressProvider = FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return {};
  
  final stats = await StarService.getAgentStarStats(currentUser.id);
  
  final currentMilestone = (stats.totalStars / 12).floor() * 12;
  final nextMilestone = currentMilestone + 12;
  final progressInCurrentMilestone = stats.totalStars - currentMilestone;
  final progressPercentage = (progressInCurrentMilestone / 12) * 100;
  
  return {
    'currentMilestone': currentMilestone,
    'nextMilestone': nextMilestone,
    'progressInCurrentMilestone': progressInCurrentMilestone,
    'progressPercentage': progressPercentage,
    'totalStars': stats.totalStars,
    'starsToNextMilestone': 12 - progressInCurrentMilestone,
    'milestonesCompleted': (stats.totalStars / 12).floor(),
  };
});
