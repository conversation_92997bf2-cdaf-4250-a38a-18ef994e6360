import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/material.dart';

/// Lead model for tracking property inquiries and agent interactions
class LeadModel {
  final String id;
  final String propertyId;
  final String propertyTitle;
  final String agentId;
  final String agentName;
  final String customerName;
  final String customerPhone;
  final String customerEmail;
  final String status; // 'new', 'contacted', 'interested', 'not_interested', 'converted', 'lost'
  final String source; // 'whatsapp', 'email', 'direct_link', 'phone_call', 'walk_in'
  final String priority; // 'low', 'medium', 'high', 'urgent'
  final double? budgetMin;
  final double? budgetMax;
  final String? notes;
  final List<String> interactions; // List of interaction IDs
  final DateTime createdAt;
  final DateTime updatedAt;
  final DateTime? lastContactedAt;
  final DateTime? followUpDate;
  final Map<String, dynamic>? additionalInfo;

  const LeadModel({
    required this.id,
    required this.propertyId,
    required this.propertyTitle,
    required this.agentId,
    required this.agentName,
    required this.customerName,
    required this.customerPhone,
    required this.customerEmail,
    this.status = 'new',
    this.source = 'direct_link',
    this.priority = 'medium',
    this.budgetMin,
    this.budgetMax,
    this.notes,
    this.interactions = const [],
    required this.createdAt,
    required this.updatedAt,
    this.lastContactedAt,
    this.followUpDate,
    this.additionalInfo,
  });

  /// Create LeadModel from Firestore document
  factory LeadModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return LeadModel(
      id: doc.id,
      propertyId: data['propertyId'] ?? '',
      propertyTitle: data['propertyTitle'] ?? '',
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      customerName: data['customerName'] ?? '',
      customerPhone: data['customerPhone'] ?? '',
      customerEmail: data['customerEmail'] ?? '',
      status: data['status'] ?? 'new',
      source: data['source'] ?? 'direct_link',
      priority: data['priority'] ?? 'medium',
      budgetMin: data['budgetMin']?.toDouble(),
      budgetMax: data['budgetMax']?.toDouble(),
      notes: data['notes'],
      interactions: List<String>.from(data['interactions'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      lastContactedAt: data['lastContactedAt'] != null 
          ? (data['lastContactedAt'] as Timestamp).toDate() 
          : null,
      followUpDate: data['followUpDate'] != null 
          ? (data['followUpDate'] as Timestamp).toDate() 
          : null,
      additionalInfo: data['additionalInfo'],
    );
  }

  /// Convert LeadModel to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'propertyId': propertyId,
      'propertyTitle': propertyTitle,
      'agentId': agentId,
      'agentName': agentName,
      'customerName': customerName,
      'customerPhone': customerPhone,
      'customerEmail': customerEmail,
      'status': status,
      'source': source,
      'priority': priority,
      'budgetMin': budgetMin,
      'budgetMax': budgetMax,
      'notes': notes,
      'interactions': interactions,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'lastContactedAt': lastContactedAt != null 
          ? Timestamp.fromDate(lastContactedAt!) 
          : null,
      'followUpDate': followUpDate != null 
          ? Timestamp.fromDate(followUpDate!) 
          : null,
      'additionalInfo': additionalInfo,
    };
  }

  /// Create a copy with updated fields
  LeadModel copyWith({
    String? id,
    String? propertyId,
    String? propertyTitle,
    String? agentId,
    String? agentName,
    String? customerName,
    String? customerPhone,
    String? customerEmail,
    String? status,
    String? source,
    String? priority,
    double? budgetMin,
    double? budgetMax,
    String? notes,
    List<String>? interactions,
    DateTime? createdAt,
    DateTime? updatedAt,
    DateTime? lastContactedAt,
    DateTime? followUpDate,
    Map<String, dynamic>? additionalInfo,
  }) {
    return LeadModel(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      propertyTitle: propertyTitle ?? this.propertyTitle,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      customerName: customerName ?? this.customerName,
      customerPhone: customerPhone ?? this.customerPhone,
      customerEmail: customerEmail ?? this.customerEmail,
      status: status ?? this.status,
      source: source ?? this.source,
      priority: priority ?? this.priority,
      budgetMin: budgetMin ?? this.budgetMin,
      budgetMax: budgetMax ?? this.budgetMax,
      notes: notes ?? this.notes,
      interactions: interactions ?? this.interactions,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      lastContactedAt: lastContactedAt ?? this.lastContactedAt,
      followUpDate: followUpDate ?? this.followUpDate,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  /// Get status display name
  String get statusDisplayName {
    switch (status) {
      case 'new': return 'New Lead';
      case 'contacted': return 'Contacted';
      case 'interested': return 'Interested';
      case 'not_interested': return 'Not Interested';
      case 'converted': return 'Converted';
      case 'lost': return 'Lost';
      default: return 'Unknown';
    }
  }

  /// Get status color
  Color get statusColor {
    switch (status) {
      case 'new': return Colors.blue;
      case 'contacted': return Colors.orange;
      case 'interested': return Colors.green;
      case 'not_interested': return Colors.red;
      case 'converted': return Colors.purple;
      case 'lost': return Colors.grey;
      default: return Colors.grey;
    }
  }

  /// Get priority display name
  String get priorityDisplayName {
    switch (priority) {
      case 'low': return 'Low Priority';
      case 'medium': return 'Medium Priority';
      case 'high': return 'High Priority';
      case 'urgent': return 'Urgent';
      default: return 'Medium Priority';
    }
  }

  /// Get priority color
  Color get priorityColor {
    switch (priority) {
      case 'low': return Colors.green;
      case 'medium': return Colors.orange;
      case 'high': return Colors.red;
      case 'urgent': return Colors.purple;
      default: return Colors.orange;
    }
  }

  /// Get source display name
  String get sourceDisplayName {
    switch (source) {
      case 'whatsapp': return 'WhatsApp';
      case 'email': return 'Email';
      case 'direct_link': return 'Direct Link';
      case 'phone_call': return 'Phone Call';
      case 'walk_in': return 'Walk-in';
      default: return 'Unknown';
    }
  }

  /// Get formatted budget range
  String get formattedBudgetRange {
    if (budgetMin == null && budgetMax == null) return 'Not specified';
    
    final min = budgetMin != null ? _formatAmount(budgetMin!) : 'No min';
    final max = budgetMax != null ? _formatAmount(budgetMax!) : 'No max';
    
    if (budgetMin != null && budgetMax != null) {
      return '$min - $max';
    } else if (budgetMin != null) {
      return 'Above $min';
    } else {
      return 'Below $max';
    }
  }

  /// Format amount in Indian Rupees
  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(1)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(1)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }

  /// Check if lead needs follow-up
  bool get needsFollowUp {
    if (followUpDate == null) return false;
    return DateTime.now().isAfter(followUpDate!);
  }

  /// Get days since last contact
  int get daysSinceLastContact {
    final lastContact = lastContactedAt ?? createdAt;
    return DateTime.now().difference(lastContact).inDays;
  }

  /// Check if lead is hot (high priority and recent)
  bool get isHotLead {
    return priority == 'high' || priority == 'urgent' || 
           (status == 'interested' && daysSinceLastContact <= 3);
  }
}

/// Lead interaction model for tracking communication history
class LeadInteractionModel {
  final String id;
  final String leadId;
  final String agentId;
  final String type; // 'call', 'email', 'whatsapp', 'meeting', 'note'
  final String subject;
  final String description;
  final DateTime createdAt;
  final Map<String, dynamic>? metadata;

  const LeadInteractionModel({
    required this.id,
    required this.leadId,
    required this.agentId,
    required this.type,
    required this.subject,
    required this.description,
    required this.createdAt,
    this.metadata,
  });

  /// Create from Firestore document
  factory LeadInteractionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return LeadInteractionModel(
      id: doc.id,
      leadId: data['leadId'] ?? '',
      agentId: data['agentId'] ?? '',
      type: data['type'] ?? 'note',
      subject: data['subject'] ?? '',
      description: data['description'] ?? '',
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      metadata: data['metadata'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'leadId': leadId,
      'agentId': agentId,
      'type': type,
      'subject': subject,
      'description': description,
      'createdAt': Timestamp.fromDate(createdAt),
      'metadata': metadata,
    };
  }

  /// Get type display name
  String get typeDisplayName {
    switch (type) {
      case 'call': return 'Phone Call';
      case 'email': return 'Email';
      case 'whatsapp': return 'WhatsApp';
      case 'meeting': return 'Meeting';
      case 'note': return 'Note';
      default: return 'Unknown';
    }
  }

  /// Get type icon
  IconData get typeIcon {
    switch (type) {
      case 'call': return Icons.phone;
      case 'email': return Icons.email;
      case 'whatsapp': return Icons.chat;
      case 'meeting': return Icons.meeting_room;
      case 'note': return Icons.note;
      default: return Icons.info;
    }
  }
}
