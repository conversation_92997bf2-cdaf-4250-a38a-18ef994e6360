import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/property_model.dart';
import '../../../agent_interactions/presentation/widgets/property_sharing_widgets.dart';
import '../../../agent_interactions/presentation/providers/agent_interaction_providers.dart';

/// Property card widget for displaying property information
class PropertyCard extends ConsumerWidget {
  final PropertyModel property;
  final VoidCallback? onTap;
  final bool showAdminActions;
  final bool showAgentActions;
  final Function(PropertyModel)? onEdit;
  final Function(PropertyModel)? onDelete;
  final Function(PropertyModel)? onApprove;
  final Function(PropertyModel)? onReject;
  final Function(PropertyModel)? onToggleFeatured;

  const PropertyCard({
    super.key,
    required this.property,
    this.onTap,
    this.showAdminActions = false,
    this.showAgentActions = true,
    this.onEdit,
    this.onDelete,
    this.onApprove,
    this.onReject,
    this.onToggleFeatured,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.all(8),
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Property Image
            _buildPropertyImage(context),

            // Property Details
            Padding(
              padding: const EdgeInsets.all(16),
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Title and Status
                  Row(
                    children: [
                      Expanded(
                        child: Text(
                          property.title,
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                          maxLines: 2,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                      _buildStatusChip(context),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Price in Indian Rupees
                  Text(
                    property.formattedPrice,
                    style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                      color: Theme.of(context).primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),

                  const SizedBox(height: 8),

                  // Location
                  Row(
                    children: [
                      const Icon(
                        Icons.location_on,
                        size: 16,
                        color: Colors.grey,
                      ),
                      const SizedBox(width: 4),
                      Expanded(
                        child: Text(
                          '${property.location}, ${property.city}',
                          style: Theme.of(context).textTheme.bodyMedium
                              ?.copyWith(color: Colors.grey[600]),
                          maxLines: 1,
                          overflow: TextOverflow.ellipsis,
                        ),
                      ),
                    ],
                  ),

                  const SizedBox(height: 8),

                  // Property Details
                  _buildPropertyDetails(context),

                  const SizedBox(height: 12),

                  // Tags and Features
                  _buildPropertyTags(context),

                  // Admin Actions
                  if (showAdminActions) ...[
                    const SizedBox(height: 12),
                    _buildAdminActions(context),
                  ],

                  // Agent Actions
                  if (showAgentActions && !showAdminActions) ...[
                    const SizedBox(height: 12),
                    _buildAgentActions(context, ref),
                  ],
                ],
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyImage(BuildContext context) {
    return Stack(
      children: [
        Container(
          height: 200,
          width: double.infinity,
          decoration: BoxDecoration(
            borderRadius: const BorderRadius.vertical(top: Radius.circular(12)),
            color: Colors.grey[300],
          ),
          child: property.primaryImageUrl != null
              ? ClipRRect(
                  borderRadius: const BorderRadius.vertical(
                    top: Radius.circular(12),
                  ),
                  child: CachedNetworkImage(
                    imageUrl: property.primaryImageUrl!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) =>
                        const Center(child: CircularProgressIndicator()),
                    errorWidget: (context, url, error) => const Center(
                      child: Icon(
                        Icons.home_work,
                        size: 48,
                        color: Colors.grey,
                      ),
                    ),
                  ),
                )
              : const Center(
                  child: Icon(Icons.home_work, size: 48, color: Colors.grey),
                ),
        ),

        // Featured Badge
        if (property.isFeatured)
          Positioned(
            top: 8,
            left: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.orange,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Text(
                'FEATURED',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

        // Approval Status
        if (showAdminActions)
          Positioned(
            top: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: property.isApproved ? Colors.green : Colors.red,
                borderRadius: BorderRadius.circular(12),
              ),
              child: Text(
                property.isApproved ? 'APPROVED' : 'PENDING',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
          ),

        // Image Count
        if (property.imageUrls.length > 1)
          Positioned(
            bottom: 8,
            right: 8,
            child: Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: Colors.black.withValues(alpha: 0.7),
                borderRadius: BorderRadius.circular(12),
              ),
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  const Icon(
                    Icons.photo_library,
                    size: 12,
                    color: Colors.white,
                  ),
                  const SizedBox(width: 4),
                  Text(
                    '${property.imageUrls.length}',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 10,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
            ),
          ),
      ],
    );
  }

  Widget _buildStatusChip(BuildContext context) {
    Color chipColor;
    String statusText;

    switch (property.status) {
      case 'for_sale':
        chipColor = Colors.green;
        statusText = 'For Sale';
        break;
      case 'for_rent':
        chipColor = Colors.blue;
        statusText = 'For Rent';
        break;
      case 'sold':
        chipColor = Colors.red;
        statusText = 'Sold';
        break;
      case 'rented':
        chipColor = Colors.orange;
        statusText = 'Rented';
        break;
      default:
        chipColor = Colors.grey;
        statusText = property.status;
    }

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: chipColor.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: chipColor),
      ),
      child: Text(
        statusText,
        style: TextStyle(
          color: chipColor,
          fontSize: 12,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  Widget _buildPropertyDetails(BuildContext context) {
    final details = <Widget>[];

    if (property.areaSquareFeet != null) {
      details.add(
        _buildDetailItem(
          Icons.square_foot,
          '${property.areaSquareFeet!.toStringAsFixed(0)} sq ft',
        ),
      );
    }

    if (property.bedrooms != null) {
      details.add(_buildDetailItem(Icons.bed, '${property.bedrooms} BHK'));
    }

    if (property.bathrooms != null) {
      details.add(
        _buildDetailItem(Icons.bathroom, '${property.bathrooms} Bath'),
      );
    }

    details.add(_buildDetailItem(Icons.category, property.type));

    return Wrap(spacing: 16, runSpacing: 4, children: details);
  }

  Widget _buildDetailItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, size: 14, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Text(text, style: TextStyle(fontSize: 12, color: Colors.grey[600])),
      ],
    );
  }

  Widget _buildPropertyTags(BuildContext context) {
    final tags = <String>[];

    if (property.amenities.isNotEmpty) {
      tags.addAll(property.amenities.take(3));
    }

    if (tags.isEmpty) return const SizedBox.shrink();

    return Wrap(
      spacing: 8,
      runSpacing: 4,
      children: tags
          .map(
            (tag) => Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: Colors.grey[200],
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(tag, style: const TextStyle(fontSize: 10)),
            ),
          )
          .toList(),
    );
  }

  Widget _buildAdminActions(BuildContext context) {
    return Row(
      children: [
        if (onEdit != null)
          IconButton(
            icon: const Icon(Icons.edit, size: 20),
            onPressed: () => onEdit!(property),
            tooltip: 'Edit',
          ),

        if (!property.isApproved && onApprove != null)
          IconButton(
            icon: const Icon(Icons.check, size: 20, color: Colors.green),
            onPressed: () => onApprove!(property),
            tooltip: 'Approve',
          ),

        if (property.isApproved && onReject != null)
          IconButton(
            icon: const Icon(Icons.close, size: 20, color: Colors.red),
            onPressed: () => onReject!(property),
            tooltip: 'Reject',
          ),

        if (onToggleFeatured != null)
          IconButton(
            icon: Icon(
              property.isFeatured ? Icons.star : Icons.star_border,
              size: 20,
              color: property.isFeatured ? Colors.orange : Colors.grey,
            ),
            onPressed: () => onToggleFeatured!(property),
            tooltip: property.isFeatured
                ? 'Remove from Featured'
                : 'Mark as Featured',
          ),

        const Spacer(),

        if (onDelete != null)
          IconButton(
            icon: const Icon(Icons.delete, size: 20, color: Colors.red),
            onPressed: () => onDelete!(property),
            tooltip: 'Delete',
          ),
      ],
    );
  }

  Widget _buildAgentActions(BuildContext context, WidgetRef ref) {
    final isFavoriteAsync = ref.watch(
      isPropertyInFavoritesProvider(property.id),
    );

    return Row(
      children: [
        // Favorite button
        isFavoriteAsync.when(
          data: (isFavorite) => IconButton(
            icon: Icon(
              isFavorite ? Icons.favorite : Icons.favorite_border,
              size: 20,
              color: isFavorite ? Colors.red : Colors.grey,
            ),
            onPressed: () => _toggleFavorite(context, ref, isFavorite),
            tooltip: isFavorite ? 'Remove from Favorites' : 'Add to Favorites',
          ),
          loading: () => const IconButton(
            icon: Icon(Icons.favorite_border, size: 20, color: Colors.grey),
            onPressed: null,
          ),
          error: (_, __) => const IconButton(
            icon: Icon(Icons.favorite_border, size: 20, color: Colors.grey),
            onPressed: null,
          ),
        ),

        // Share button
        IconButton(
          icon: const Icon(Icons.share, size: 20, color: Colors.blue),
          onPressed: () => _showSharingSheet(context),
          tooltip: 'Share Property',
        ),

        // WhatsApp quick share
        IconButton(
          icon: const Icon(Icons.chat, size: 20, color: Colors.green),
          onPressed: () => _quickShareWhatsApp(context),
          tooltip: 'Share on WhatsApp',
        ),

        const Spacer(),

        // View details button
        TextButton.icon(
          onPressed: onTap,
          icon: const Icon(Icons.visibility, size: 16),
          label: const Text('View'),
          style: TextButton.styleFrom(
            padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
          ),
        ),
      ],
    );
  }

  void _toggleFavorite(
    BuildContext context,
    WidgetRef ref,
    bool isFavorite,
  ) async {
    // TODO: Implement toggle favorite functionality
    ScaffoldMessenger.of(context).showSnackBar(
      SnackBar(
        content: Text(
          isFavorite ? 'Removed from favorites' : 'Added to favorites',
        ),
        backgroundColor: isFavorite ? Colors.orange : Colors.green,
      ),
    );

    // Refresh the favorites status
    ref.refresh(isPropertyInFavoritesProvider(property.id));
  }

  void _showSharingSheet(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: PropertySharingSheet(property: property),
      ),
    );
  }

  void _quickShareWhatsApp(BuildContext context) {
    final message =
        '''Check out this property:

${property.title}
📍 ${property.location}, ${property.city}
💰 ${property.formattedPrice}
🏠 ${property.type}

Interested? Let me know!''';

    // TODO: Implement quick WhatsApp sharing
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('WhatsApp sharing coming soon!')),
    );
  }
}

/// Compact property card for lists
class CompactPropertyCard extends StatelessWidget {
  final PropertyModel property;
  final VoidCallback? onTap;

  const CompactPropertyCard({super.key, required this.property, this.onTap});

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 4),
      child: ListTile(
        onTap: onTap,
        leading: Container(
          width: 60,
          height: 60,
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(8),
            color: Colors.grey[300],
          ),
          child: property.primaryImageUrl != null
              ? ClipRRect(
                  borderRadius: BorderRadius.circular(8),
                  child: CachedNetworkImage(
                    imageUrl: property.primaryImageUrl!,
                    fit: BoxFit.cover,
                    placeholder: (context, url) =>
                        const Center(child: CircularProgressIndicator()),
                    errorWidget: (context, url, error) =>
                        const Icon(Icons.home_work),
                  ),
                )
              : const Icon(Icons.home_work),
        ),
        title: Text(
          property.title,
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              property.formattedPrice,
              style: TextStyle(
                color: Theme.of(context).primaryColor,
                fontWeight: FontWeight.bold,
              ),
            ),
            Text(
              '${property.location}, ${property.city}',
              maxLines: 1,
              overflow: TextOverflow.ellipsis,
              style: TextStyle(color: Colors.grey[600]),
            ),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getStatusColor().withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(color: _getStatusColor()),
              ),
              child: Text(
                _getStatusText(),
                style: TextStyle(
                  color: _getStatusColor(),
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            if (property.isFeatured)
              const Icon(Icons.star, size: 16, color: Colors.orange),
          ],
        ),
      ),
    );
  }

  Color _getStatusColor() {
    switch (property.status) {
      case 'for_sale':
        return Colors.green;
      case 'for_rent':
        return Colors.blue;
      case 'sold':
        return Colors.red;
      case 'rented':
        return Colors.orange;
      default:
        return Colors.grey;
    }
  }

  String _getStatusText() {
    switch (property.status) {
      case 'for_sale':
        return 'Sale';
      case 'for_rent':
        return 'Rent';
      case 'sold':
        return 'Sold';
      case 'rented':
        return 'Rented';
      default:
        return property.status;
    }
  }
}
