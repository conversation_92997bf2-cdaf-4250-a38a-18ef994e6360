import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';

/// Service to test Firebase connectivity and permissions
class FirebaseTestService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Test basic Firebase connectivity
  static Future<Map<String, dynamic>> testFirebaseConnectivity() async {
    final results = <String, dynamic>{};
    
    try {
      // Test 1: Check if Firebase is initialized
      results['firebase_initialized'] = true;
      
      // Test 2: Check current authentication status
      final currentUser = _auth.currentUser;
      results['auth_status'] = {
        'is_signed_in': currentUser != null,
        'user_id': currentUser?.uid,
        'email': currentUser?.email,
      };
      
      // Test 3: Try to read from a simple collection
      try {
        final testDoc = await _firestore.collection('test').doc('connectivity').get();
        results['firestore_read'] = {
          'success': true,
          'document_exists': testDoc.exists,
        };
      } catch (e) {
        results['firestore_read'] = {
          'success': false,
          'error': e.toString(),
        };
      }
      
      // Test 4: Try to write to a simple collection
      try {
        await _firestore.collection('test').doc('connectivity').set({
          'timestamp': FieldValue.serverTimestamp(),
          'test_data': 'Firebase connectivity test',
        });
        results['firestore_write'] = {
          'success': true,
        };
      } catch (e) {
        results['firestore_write'] = {
          'success': false,
          'error': e.toString(),
        };
      }
      
      // Test 5: Try to read from users collection
      try {
        final usersSnapshot = await _firestore.collection('users').limit(1).get();
        results['users_collection'] = {
          'success': true,
          'document_count': usersSnapshot.docs.length,
        };
      } catch (e) {
        results['users_collection'] = {
          'success': false,
          'error': e.toString(),
        };
      }
      
      // Test 6: Check security rules by trying different operations
      try {
        // Try to create a test user document
        await _firestore.collection('users').doc('test_user').set({
          'name': 'Test User',
          'email': '<EMAIL>',
          'createdAt': FieldValue.serverTimestamp(),
        });
        results['user_write_test'] = {
          'success': true,
        };
      } catch (e) {
        results['user_write_test'] = {
          'success': false,
          'error': e.toString(),
        };
      }
      
    } catch (e) {
      results['firebase_initialized'] = false;
      results['error'] = e.toString();
    }
    
    return results;
  }

  /// Test authentication with demo credentials
  static Future<Map<String, dynamic>> testAuthentication() async {
    final results = <String, dynamic>{};
    
    try {
      // Test anonymous authentication first
      try {
        final userCredential = await _auth.signInAnonymously();
        results['anonymous_auth'] = {
          'success': true,
          'user_id': userCredential.user?.uid,
        };
      } catch (e) {
        results['anonymous_auth'] = {
          'success': false,
          'error': e.toString(),
        };
      }
      
      // Test email/password authentication with demo credentials
      try {
        final userCredential = await _auth.signInWithEmailAndPassword(
          email: '<EMAIL>',
          password: 'admin123',
        );
        results['email_auth'] = {
          'success': true,
          'user_id': userCredential.user?.uid,
          'email': userCredential.user?.email,
        };
      } catch (e) {
        results['email_auth'] = {
          'success': false,
          'error': e.toString(),
        };
      }
      
    } catch (e) {
      results['error'] = e.toString();
    }
    
    return results;
  }

  /// Get current Firebase project info
  static Future<Map<String, dynamic>> getProjectInfo() async {
    final results = <String, dynamic>{};
    
    try {
      // Get current user info
      final currentUser = _auth.currentUser;
      results['current_user'] = {
        'uid': currentUser?.uid,
        'email': currentUser?.email,
        'is_anonymous': currentUser?.isAnonymous,
        'creation_time': currentUser?.metadata.creationTime?.toIso8601String(),
        'last_sign_in': currentUser?.metadata.lastSignInTime?.toIso8601String(),
      };
      
      // Get Firestore app info
      results['firestore_app'] = {
        'app_name': _firestore.app.name,
        'project_id': _firestore.app.options.projectId,
      };
      
      // Test collection access
      final collections = ['users', 'properties', 'transactions', 'commissions', 'stars', 'leads'];
      final collectionTests = <String, dynamic>{};
      
      for (final collection in collections) {
        try {
          final snapshot = await _firestore.collection(collection).limit(1).get();
          collectionTests[collection] = {
            'accessible': true,
            'document_count': snapshot.docs.length,
          };
        } catch (e) {
          collectionTests[collection] = {
            'accessible': false,
            'error': e.toString(),
          };
        }
      }
      
      results['collection_access'] = collectionTests;
      
    } catch (e) {
      results['error'] = e.toString();
    }
    
    return results;
  }

  /// Clean up test data
  static Future<void> cleanupTestData() async {
    try {
      // Remove test documents
      await _firestore.collection('test').doc('connectivity').delete();
      await _firestore.collection('users').doc('test_user').delete();
    } catch (e) {
      print('Error cleaning up test data: $e');
    }
  }

  /// Print comprehensive Firebase status
  static Future<void> printFirebaseStatus() async {
    print('🔥 Firebase Connectivity Test');
    print('=' * 50);
    
    final connectivity = await testFirebaseConnectivity();
    print('📡 Connectivity Test:');
    connectivity.forEach((key, value) {
      print('  $key: $value');
    });
    
    print('\n🔐 Authentication Test:');
    final auth = await testAuthentication();
    auth.forEach((key, value) {
      print('  $key: $value');
    });
    
    print('\n📊 Project Info:');
    final info = await getProjectInfo();
    info.forEach((key, value) {
      print('  $key: $value');
    });
    
    print('\n🧹 Cleaning up test data...');
    await cleanupTestData();
    print('✅ Firebase test completed');
  }
}
