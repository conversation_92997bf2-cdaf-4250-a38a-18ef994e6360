import 'package:flutter/material.dart';
import 'package:fl_chart/fl_chart.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/models/commission_model.dart';
import '../../../../core/models/star_model.dart';

/// Enhanced dashboard stat card with trend indicators
class EnhancedStatCard extends StatelessWidget {
  final String title;
  final String value;
  final String? subtitle;
  final IconData icon;
  final Color color;
  final double? trend;
  final VoidCallback? onTap;

  const EnhancedStatCard({
    super.key,
    required this.title,
    required this.value,
    this.subtitle,
    required this.icon,
    required this.color,
    this.trend,
    this.onTap,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      elevation: 4,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Container(
          padding: const EdgeInsets.all(16),
          decoration: BoxDecoration(
            borderRadius: BorderRadius.circular(12),
            gradient: LinearGradient(
              begin: Alignment.topLeft,
              end: Alignment.bottomRight,
              colors: [
                color.withValues(alpha: 0.1),
                color.withValues(alpha: 0.05),
              ],
            ),
          ),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.all(8),
                    decoration: BoxDecoration(
                      color: color.withValues(alpha: 0.2),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Icon(icon, color: color, size: 24),
                  ),
                  const Spacer(),
                  if (trend != null) _buildTrendIndicator(),
                ],
              ),
              const SizedBox(height: 12),
              Text(
                value,
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                  color: color,
                ),
              ),
              const SizedBox(height: 4),
              Text(
                title,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
              if (subtitle != null) ...[
                const SizedBox(height: 4),
                Text(
                  subtitle!,
                  style: Theme.of(context).textTheme.bodySmall?.copyWith(
                    color: Colors.grey[500],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildTrendIndicator() {
    if (trend == null) return const SizedBox.shrink();
    
    final isPositive = trend! >= 0;
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
      decoration: BoxDecoration(
        color: isPositive ? Colors.green.withValues(alpha: 0.1) : Colors.red.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(
            isPositive ? Icons.trending_up : Icons.trending_down,
            size: 12,
            color: isPositive ? Colors.green : Colors.red,
          ),
          const SizedBox(width: 2),
          Text(
            '${trend!.abs().toStringAsFixed(1)}%',
            style: TextStyle(
              fontSize: 10,
              fontWeight: FontWeight.bold,
              color: isPositive ? Colors.green : Colors.red,
            ),
          ),
        ],
      ),
    );
  }
}

/// Performance overview widget with charts
class PerformanceOverviewWidget extends StatelessWidget {
  final List<CommissionModel> commissions;
  final List<StarModel> stars;
  final UserModel user;

  const PerformanceOverviewWidget({
    super.key,
    required this.commissions,
    required this.stars,
    required this.user,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Performance Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            
            // Monthly performance chart
            SizedBox(
              height: 200,
              child: _buildPerformanceChart(),
            ),
            
            const SizedBox(height: 16),
            
            // Performance metrics
            Row(
              children: [
                Expanded(
                  child: _buildMetricItem(
                    'This Month',
                    _getThisMonthCommissions(),
                    Icons.calendar_month,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    'Avg/Month',
                    _getAverageMonthlyCommissions(),
                    Icons.analytics,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildMetricItem(
                    'Best Month',
                    _getBestMonthCommissions(),
                    Icons.star,
                    Colors.amber,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceChart() {
    final monthlyData = _getMonthlyCommissionData();
    
    if (monthlyData.isEmpty) {
      return const Center(
        child: Text('No performance data available'),
      );
    }

    return LineChart(
      LineChartData(
        gridData: const FlGridData(show: true),
        titlesData: FlTitlesData(
          leftTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              reservedSize: 40,
              getTitlesWidget: (value, meta) {
                return Text(
                  '₹${(value / 1000).toStringAsFixed(0)}K',
                  style: const TextStyle(fontSize: 10),
                );
              },
            ),
          ),
          bottomTitles: AxisTitles(
            sideTitles: SideTitles(
              showTitles: true,
              getTitlesWidget: (value, meta) {
                final months = ['Jan', 'Feb', 'Mar', 'Apr', 'May', 'Jun'];
                final index = value.toInt();
                if (index >= 0 && index < months.length) {
                  return Text(months[index], style: const TextStyle(fontSize: 10));
                }
                return const Text('');
              },
            ),
          ),
          topTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
          rightTitles: const AxisTitles(sideTitles: SideTitles(showTitles: false)),
        ),
        borderData: FlBorderData(show: true),
        lineBarsData: [
          LineChartBarData(
            spots: monthlyData,
            isCurved: true,
            color: Colors.blue,
            barWidth: 3,
            dotData: const FlDotData(show: true),
            belowBarData: BarAreaData(
              show: true,
              color: Colors.blue.withValues(alpha: 0.1),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildMetricItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 20),
        const SizedBox(height: 4),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 14,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 10),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  List<FlSpot> _getMonthlyCommissionData() {
    final monthlyTotals = <int, double>{};
    
    for (final commission in commissions) {
      final month = commission.createdAt.month - 1; // 0-based for chart
      monthlyTotals[month] = (monthlyTotals[month] ?? 0) + commission.amount;
    }
    
    return monthlyTotals.entries
        .map((entry) => FlSpot(entry.key.toDouble(), entry.value))
        .toList();
  }

  String _getThisMonthCommissions() {
    final now = DateTime.now();
    final thisMonthCommissions = commissions.where((c) => 
        c.createdAt.year == now.year && c.createdAt.month == now.month);
    
    final total = thisMonthCommissions.fold<double>(0, (sum, c) => sum + c.amount);
    return _formatAmount(total);
  }

  String _getAverageMonthlyCommissions() {
    if (commissions.isEmpty) return '₹0';
    
    final monthlyTotals = <String, double>{};
    for (final commission in commissions) {
      final key = '${commission.createdAt.year}-${commission.createdAt.month}';
      monthlyTotals[key] = (monthlyTotals[key] ?? 0) + commission.amount;
    }
    
    if (monthlyTotals.isEmpty) return '₹0';
    
    final average = monthlyTotals.values.reduce((a, b) => a + b) / monthlyTotals.length;
    return _formatAmount(average);
  }

  String _getBestMonthCommissions() {
    if (commissions.isEmpty) return '₹0';
    
    final monthlyTotals = <String, double>{};
    for (final commission in commissions) {
      final key = '${commission.createdAt.year}-${commission.createdAt.month}';
      monthlyTotals[key] = (monthlyTotals[key] ?? 0) + commission.amount;
    }
    
    if (monthlyTotals.isEmpty) return '₹0';
    
    final best = monthlyTotals.values.reduce((a, b) => a > b ? a : b);
    return _formatAmount(best);
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(1)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(1)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }
}

/// Quick actions widget for common tasks
class QuickActionsWidget extends StatelessWidget {
  final VoidCallback? onViewProperties;
  final VoidCallback? onViewCommissions;
  final VoidCallback? onViewNetwork;
  final VoidCallback? onShareReferral;

  const QuickActionsWidget({
    super.key,
    this.onViewProperties,
    this.onViewCommissions,
    this.onViewNetwork,
    this.onShareReferral,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 12,
              mainAxisSpacing: 12,
              childAspectRatio: 2.5,
              children: [
                _buildActionButton(
                  'View Properties',
                  Icons.home_work,
                  Colors.blue,
                  onViewProperties,
                ),
                _buildActionButton(
                  'Commissions',
                  Icons.currency_rupee,
                  Colors.green,
                  onViewCommissions,
                ),
                _buildActionButton(
                  'My Network',
                  Icons.account_tree,
                  Colors.purple,
                  onViewNetwork,
                ),
                _buildActionButton(
                  'Share Referral',
                  Icons.share,
                  Colors.orange,
                  onShareReferral,
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(String label, IconData icon, Color color, VoidCallback? onTap) {
    return Material(
      color: color.withValues(alpha: 0.1),
      borderRadius: BorderRadius.circular(8),
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(8),
        child: Container(
          padding: const EdgeInsets.all(8),
          child: Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Expanded(
                child: Text(
                  label,
                  style: TextStyle(
                    color: color,
                    fontWeight: FontWeight.w500,
                    fontSize: 12,
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }
}

/// Recent activities widget
class RecentActivitiesWidget extends StatelessWidget {
  final List<ActivityItem> activities;

  const RecentActivitiesWidget({
    super.key,
    required this.activities,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(
                  'Recent Activities',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const Spacer(),
                TextButton(
                  onPressed: () {}, // TODO: Navigate to full activity log
                  child: const Text('View All'),
                ),
              ],
            ),
            const SizedBox(height: 16),
            if (activities.isEmpty)
              const Center(
                child: Padding(
                  padding: EdgeInsets.all(32),
                  child: Text('No recent activities'),
                ),
              )
            else
              ListView.separated(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                itemCount: activities.length,
                separatorBuilder: (context, index) => const Divider(),
                itemBuilder: (context, index) {
                  final activity = activities[index];
                  return ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: CircleAvatar(
                      backgroundColor: activity.color.withValues(alpha: 0.1),
                      child: Icon(activity.icon, color: activity.color, size: 20),
                    ),
                    title: Text(
                      activity.title,
                      style: const TextStyle(fontWeight: FontWeight.w500),
                    ),
                    subtitle: Text(activity.description),
                    trailing: Text(
                      activity.timeAgo,
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  );
                },
              ),
          ],
        ),
      ),
    );
  }
}

/// Activity item model
class ActivityItem {
  final String title;
  final String description;
  final String timeAgo;
  final IconData icon;
  final Color color;

  const ActivityItem({
    required this.title,
    required this.description,
    required this.timeAgo,
    required this.icon,
    required this.color,
  });
}
