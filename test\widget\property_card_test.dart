import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:rama_realty_mlm/features/properties/presentation/widgets/property_card.dart';
import 'package:rama_realty_mlm/core/models/property_model.dart';

void main() {
  group('PropertyCard Widget Tests', () {
    late PropertyModel testProperty;

    setUp(() {
      testProperty = PropertyModel(
        id: 'test-property-id',
        title: 'Luxury 3 BHK Apartment',
        description: 'Beautiful apartment with modern amenities',
        price: 5000000.0, // ₹50 Lakh
        type: 'Residential',
        status: 'For Sale',
        location: 'Bandra West',
        city: 'Mumbai',
        state: 'Maharashtra',
        pincode: '400050',
        bedrooms: 3,
        bathrooms: 2,
        area: 1200.0,
        imageUrls: [
          'https://example.com/image1.jpg',
          'https://example.com/image2.jpg',
        ],
        amenities: ['Parking', 'Gym', 'Swimming Pool'],
        agentId: 'agent-123',
        agentName: '<PERSON>',
        isApproved: true,
        isFeatured: false,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    });

    testWidgets('should display property information correctly', (WidgetTester tester) async {
      // Arrange & Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: testProperty,
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Luxury 3 BHK Apartment'), findsOneWidget);
      expect(find.text('₹50.0 L'), findsOneWidget);
      expect(find.text('Residential'), findsOneWidget);
      expect(find.text('Bandra West, Mumbai'), findsOneWidget);
      expect(find.text('3 BHK • 2 Bath • 1200 sq ft'), findsOneWidget);
      expect(find.text('John Doe'), findsOneWidget);
    });

    testWidgets('should display featured badge for featured properties', (WidgetTester tester) async {
      // Arrange
      final featuredProperty = testProperty.copyWith(isFeatured: true);

      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: featuredProperty,
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('FEATURED'), findsOneWidget);
    });

    testWidgets('should display status badge correctly', (WidgetTester tester) async {
      // Arrange
      final soldProperty = testProperty.copyWith(status: 'Sold');

      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: soldProperty,
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Sold'), findsOneWidget);
    });

    testWidgets('should show agent actions when enabled', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: testProperty,
                showAgentActions: true,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.favorite_border), findsOneWidget);
      expect(find.byIcon(Icons.share), findsOneWidget);
      expect(find.byIcon(Icons.chat), findsOneWidget);
      expect(find.text('View'), findsOneWidget);
    });

    testWidgets('should hide agent actions when disabled', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: testProperty,
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.favorite_border), findsNothing);
      expect(find.byIcon(Icons.share), findsNothing);
      expect(find.byIcon(Icons.chat), findsNothing);
    });

    testWidgets('should show admin actions when enabled', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: testProperty,
                showAdminActions: true,
                showAgentActions: false,
                onEdit: (property) {},
                onDelete: (property) {},
                onApprove: (property) {},
                onToggleFeatured: (property) {},
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.byIcon(Icons.edit), findsOneWidget);
      expect(find.byIcon(Icons.delete), findsOneWidget);
      expect(find.byIcon(Icons.check), findsOneWidget);
      expect(find.byIcon(Icons.star_border), findsOneWidget);
    });

    testWidgets('should handle tap events correctly', (WidgetTester tester) async {
      // Arrange
      bool wasTapped = false;

      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: testProperty,
                onTap: () => wasTapped = true,
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      await tester.tap(find.byType(PropertyCard));
      await tester.pumpAndSettle();

      // Assert
      expect(wasTapped, true);
    });

    testWidgets('should display amenities correctly', (WidgetTester tester) async {
      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: testProperty,
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Assert
      expect(find.text('Parking'), findsOneWidget);
      expect(find.text('Gym'), findsOneWidget);
      expect(find.text('Swimming Pool'), findsOneWidget);
    });

    testWidgets('should format price correctly for different amounts', (WidgetTester tester) async {
      // Test different price formats
      final testCases = [
        (1000000.0, '₹10.0 L'),
        (5000000.0, '₹50.0 L'),
        (10000000.0, '₹1.0 Cr'),
        (25000000.0, '₹2.5 Cr'),
      ];

      for (final testCase in testCases) {
        final property = testProperty.copyWith(price: testCase.$1);
        
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: PropertyCard(
                  property: property,
                  showAgentActions: false,
                ),
              ),
            ),
          ),
        );

        expect(find.text(testCase.$2), findsOneWidget);
        
        // Clear the widget tree for next test
        await tester.pumpWidget(Container());
      }
    });

    testWidgets('should handle missing image URLs gracefully', (WidgetTester tester) async {
      // Arrange
      final propertyWithoutImages = testProperty.copyWith(imageUrls: []);

      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: propertyWithoutImages,
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Assert - Should show placeholder icon instead of image
      expect(find.byIcon(Icons.home), findsOneWidget);
    });

    testWidgets('should display property type icon correctly', (WidgetTester tester) async {
      // Test different property types
      final testCases = [
        ('Residential', Icons.home),
        ('Commercial', Icons.business),
        ('Land', Icons.landscape),
        ('Industrial', Icons.factory),
      ];

      for (final testCase in testCases) {
        final property = testProperty.copyWith(type: testCase.$1);
        
        await tester.pumpWidget(
          ProviderScope(
            child: MaterialApp(
              home: Scaffold(
                body: PropertyCard(
                  property: property,
                  showAgentActions: false,
                ),
              ),
            ),
          ),
        );

        expect(find.byIcon(testCase.$2), findsOneWidget);
        
        // Clear the widget tree for next test
        await tester.pumpWidget(Container());
      }
    });

    testWidgets('should handle long property titles correctly', (WidgetTester tester) async {
      // Arrange
      final longTitleProperty = testProperty.copyWith(
        title: 'This is a very long property title that should be truncated properly to fit within the card layout without causing overflow issues',
      );

      // Act
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: longTitleProperty,
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Assert - Should not throw overflow errors
      expect(tester.takeException(), isNull);
    });

    testWidgets('should show correct approval status', (WidgetTester tester) async {
      // Test approved property
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: testProperty.copyWith(isApproved: true),
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Should not show pending approval indicator
      expect(find.text('Pending Approval'), findsNothing);

      // Test unapproved property
      await tester.pumpWidget(
        ProviderScope(
          child: MaterialApp(
            home: Scaffold(
              body: PropertyCard(
                property: testProperty.copyWith(isApproved: false),
                showAgentActions: false,
              ),
            ),
          ),
        ),
      );

      // Should show pending approval indicator
      expect(find.text('Pending Approval'), findsOneWidget);
    });
  });
}
