import 'dart:io';

/// Model for collecting onboarding data from new agents
class OnboardingData {
  final String? name;
  final String? phoneNumber;
  final String? referralCode;
  final File? profileImage;
  final String? profileImageUrl;
  final bool isNameValid;
  final bool isPhoneValid;
  final bool isReferralCodeValid;
  final String? nameError;
  final String? phoneError;
  final String? referralCodeError;

  const OnboardingData({
    this.name,
    this.phoneNumber,
    this.referralCode,
    this.profileImage,
    this.profileImageUrl,
    this.isNameValid = false,
    this.isPhoneValid = false,
    this.isReferralCodeValid = false,
    this.nameError,
    this.phoneError,
    this.referralCodeError,
  });

  OnboardingData copyWith({
    String? name,
    String? phoneNumber,
    String? referralCode,
    File? profileImage,
    String? profileImageUrl,
    bool? isNameValid,
    bool? isPhoneValid,
    bool? isReferralCodeValid,
    String? nameError,
    String? phoneError,
    String? referralCodeError,
  }) {
    return OnboardingData(
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      referralCode: referralCode ?? this.referralCode,
      profileImage: profileImage ?? this.profileImage,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      isNameValid: isNameValid ?? this.isNameValid,
      isPhoneValid: isPhoneValid ?? this.isPhoneValid,
      isReferralCodeValid: isReferralCodeValid ?? this.isReferralCodeValid,
      nameError: nameError ?? this.nameError,
      phoneError: phoneError ?? this.phoneError,
      referralCodeError: referralCodeError ?? this.referralCodeError,
    );
  }

  bool get isBasicInfoComplete {
    return isNameValid && isPhoneValid && isReferralCodeValid;
  }

  bool get hasErrors {
    return nameError != null || phoneError != null || referralCodeError != null;
  }

  Map<String, dynamic> toJson() {
    return {
      'name': name,
      'phoneNumber': phoneNumber,
      'referralCode': referralCode,
      'profileImageUrl': profileImageUrl,
    };
  }
}

/// Validation utilities for onboarding data
class OnboardingValidator {
  static const String adminReferralCode = 'ADMIN001';
  
  /// Validate agent name
  static ValidationResult validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        error: 'Name is required',
      );
    }
    
    if (name.trim().length < 2) {
      return ValidationResult(
        isValid: false,
        error: 'Name must be at least 2 characters',
      );
    }
    
    if (name.trim().length > 50) {
      return ValidationResult(
        isValid: false,
        error: 'Name must be less than 50 characters',
      );
    }
    
    // Check for valid characters (letters, spaces, hyphens, apostrophes)
    final nameRegex = RegExp(r"^[a-zA-Z\s\-'\.]+$");
    if (!nameRegex.hasMatch(name.trim())) {
      return ValidationResult(
        isValid: false,
        error: 'Name can only contain letters, spaces, hyphens, and apostrophes',
      );
    }
    
    return ValidationResult(isValid: true);
  }
  
  /// Validate phone number (Indian format)
  static ValidationResult validatePhoneNumber(String? phone) {
    if (phone == null || phone.trim().isEmpty) {
      return ValidationResult(
        isValid: false,
        error: 'Phone number is required',
      );
    }
    
    // Remove all non-digit characters
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    // Check for Indian mobile number format
    if (cleanPhone.length == 10) {
      // 10-digit format (without country code)
      if (!cleanPhone.startsWith(RegExp(r'[6-9]'))) {
        return ValidationResult(
          isValid: false,
          error: 'Invalid Indian mobile number',
        );
      }
    } else if (cleanPhone.length == 12) {
      // 12-digit format (with country code)
      if (!cleanPhone.startsWith('91')) {
        return ValidationResult(
          isValid: false,
          error: 'Country code must be +91 for Indian numbers',
        );
      }
      if (!cleanPhone.substring(2).startsWith(RegExp(r'[6-9]'))) {
        return ValidationResult(
          isValid: false,
          error: 'Invalid Indian mobile number',
        );
      }
    } else {
      return ValidationResult(
        isValid: false,
        error: 'Phone number must be 10 digits or +91 followed by 10 digits',
      );
    }
    
    return ValidationResult(isValid: true);
  }
  
  /// Validate referral code format
  static ValidationResult validateReferralCodeFormat(String? code) {
    if (code == null || code.trim().isEmpty) {
      // Empty referral code will be assigned to admin
      return ValidationResult(isValid: true);
    }
    
    final cleanCode = code.trim().toUpperCase();
    
    // Check format: 6-10 alphanumeric characters
    if (cleanCode.length < 6 || cleanCode.length > 10) {
      return ValidationResult(
        isValid: false,
        error: 'Referral code must be 6-10 characters',
      );
    }
    
    // Check for alphanumeric characters only
    final codeRegex = RegExp(r'^[A-Z0-9]+$');
    if (!codeRegex.hasMatch(cleanCode)) {
      return ValidationResult(
        isValid: false,
        error: 'Referral code can only contain letters and numbers',
      );
    }
    
    return ValidationResult(isValid: true);
  }
  
  /// Format phone number for storage
  static String formatPhoneNumber(String phone) {
    final cleanPhone = phone.replaceAll(RegExp(r'[^\d]'), '');
    
    if (cleanPhone.length == 10) {
      return '+91-${cleanPhone.substring(0, 5)}-${cleanPhone.substring(5)}';
    } else if (cleanPhone.length == 12) {
      final number = cleanPhone.substring(2);
      return '+91-${number.substring(0, 5)}-${number.substring(5)}';
    }
    
    return phone; // Return original if format is unexpected
  }
  
  /// Get referral code for assignment (admin code if empty)
  static String getAssignedReferralCode(String? inputCode) {
    if (inputCode == null || inputCode.trim().isEmpty) {
      return adminReferralCode;
    }
    return inputCode.trim().toUpperCase();
  }
}

/// Result of validation
class ValidationResult {
  final bool isValid;
  final String? error;
  
  const ValidationResult({
    required this.isValid,
    this.error,
  });
}

/// Onboarding steps enum
enum OnboardingStep {
  basicInfo,
  profileImage,
  complete,
}

/// Onboarding state
class OnboardingState {
  final OnboardingStep currentStep;
  final OnboardingData data;
  final bool isLoading;
  final String? error;
  final bool isReferralCodeVerifying;
  final bool isReferralCodeValid;
  final String? referralCodeOwnerName;

  const OnboardingState({
    this.currentStep = OnboardingStep.basicInfo,
    this.data = const OnboardingData(),
    this.isLoading = false,
    this.error,
    this.isReferralCodeVerifying = false,
    this.isReferralCodeValid = false,
    this.referralCodeOwnerName,
  });

  OnboardingState copyWith({
    OnboardingStep? currentStep,
    OnboardingData? data,
    bool? isLoading,
    String? error,
    bool? isReferralCodeVerifying,
    bool? isReferralCodeValid,
    String? referralCodeOwnerName,
  }) {
    return OnboardingState(
      currentStep: currentStep ?? this.currentStep,
      data: data ?? this.data,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
      isReferralCodeVerifying: isReferralCodeVerifying ?? this.isReferralCodeVerifying,
      isReferralCodeValid: isReferralCodeValid ?? this.isReferralCodeValid,
      referralCodeOwnerName: referralCodeOwnerName ?? this.referralCodeOwnerName,
    );
  }
}
