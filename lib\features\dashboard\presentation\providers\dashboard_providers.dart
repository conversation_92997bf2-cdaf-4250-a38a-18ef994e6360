import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/commission_model.dart';
import '../../../../core/models/star_model.dart';
import '../../../../core/models/property_model.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/services/commission_service.dart';
import '../../../../core/services/star_service.dart';
import '../../../../core/services/property_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';
import '../widgets/dashboard_widgets.dart';

/// Dashboard overview data provider
final dashboardOverviewProvider = FutureProvider.autoDispose<DashboardOverview>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return DashboardOverview();

  // Fetch all dashboard data in parallel
  final futures = await Future.wait([
    CommissionService.getAgentCommissions(currentUser.id),
    StarService.getAgentStarHistory(currentUser.id),
    StarService.getAgentStarStats(currentUser.id),
    PropertyService.getProperties(isApproved: true),
  ]);

  final commissions = futures[0] as List<CommissionModel>;
  final stars = futures[1] as List<StarModel>;
  final starStats = futures[2] as StarStatsModel;
  final properties = futures[3] as List<PropertyModel>;

  return DashboardOverview(
    user: currentUser,
    commissions: commissions,
    stars: stars,
    starStats: starStats,
    availableProperties: properties,
  );
});

/// Dashboard statistics provider
final dashboardStatsProvider = FutureProvider.autoDispose<DashboardStats>((ref) async {
  final overview = await ref.watch(dashboardOverviewProvider.future);
  
  return DashboardStats(
    totalCommissions: overview.commissions.fold<double>(0, (sum, c) => sum + c.amount),
    thisMonthCommissions: _getThisMonthCommissions(overview.commissions),
    totalStars: overview.user.totalStars,
    starsToNextBonus: overview.starStats.starsToNextBonus,
    networkSize: overview.user.downlineIds.length,
    availableProperties: overview.availableProperties.length,
    commissionTrend: _calculateCommissionTrend(overview.commissions),
    starTrend: _calculateStarTrend(overview.stars),
  );
});

/// Recent activities provider
final recentActivitiesProvider = FutureProvider.autoDispose<List<ActivityItem>>((ref) async {
  final overview = await ref.watch(dashboardOverviewProvider.future);
  final activities = <ActivityItem>[];

  // Add recent commission activities
  final recentCommissions = overview.commissions.take(3);
  for (final commission in recentCommissions) {
    activities.add(ActivityItem(
      title: 'Commission Earned',
      description: '${commission.formattedAmount} from ${commission.levelDescription}',
      timeAgo: _formatTimeAgo(commission.createdAt),
      icon: Icons.currency_rupee,
      color: Colors.green,
    ));
  }

  // Add recent star activities
  final recentStars = overview.stars.take(3);
  for (final star in recentStars) {
    activities.add(ActivityItem(
      title: 'Star Earned',
      description: '${star.typeDescription} - ${star.sourceDescription}',
      timeAgo: _formatTimeAgo(star.earnedAt),
      icon: Icons.star,
      color: Colors.amber,
    ));
  }

  // Sort by time and return latest 5
  activities.sort((a, b) => b.timeAgo.compareTo(a.timeAgo));
  return activities.take(5).toList();
});

/// Performance trends provider
final performanceTrendsProvider = FutureProvider.autoDispose<PerformanceTrends>((ref) async {
  final overview = await ref.watch(dashboardOverviewProvider.future);
  
  return PerformanceTrends(
    monthlyCommissions: _getMonthlyCommissionTrends(overview.commissions),
    monthlyStars: _getMonthlyStarTrends(overview.stars),
    weeklyActivity: _getWeeklyActivityTrends(overview.commissions, overview.stars),
  );
});

/// Agent ranking provider
final agentRankingProvider = FutureProvider.autoDispose<AgentRanking>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return const AgentRanking();

  // Get leaderboard data
  final leaderboard = await StarService.getStarLeaderboard(limit: 100);
  
  // Find current user's rank
  int rank = 0;
  for (int i = 0; i < leaderboard.length; i++) {
    final entry = leaderboard[i];
    if (entry['agent'].id == currentUser.id) {
      rank = i + 1;
      break;
    }
  }

  return AgentRanking(
    currentRank: rank,
    totalAgents: leaderboard.length,
    percentile: rank > 0 ? ((leaderboard.length - rank + 1) / leaderboard.length) * 100 : 0,
    topPerformers: leaderboard.take(5).toList(),
  );
});

/// Goal tracking provider
final goalTrackingProvider = FutureProvider.autoDispose<GoalTracking>((ref) async {
  final overview = await ref.watch(dashboardOverviewProvider.future);
  
  // Calculate monthly goals (can be made configurable)
  const monthlyCommissionGoal = 50000.0; // ₹50K
  const monthlyStarGoal = 3;
  
  final thisMonthCommissions = _getThisMonthCommissions(overview.commissions);
  final thisMonthStars = _getThisMonthStars(overview.stars);
  
  return GoalTracking(
    monthlyCommissionGoal: monthlyCommissionGoal,
    currentMonthCommissions: thisMonthCommissions,
    monthlyStarGoal: monthlyStarGoal,
    currentMonthStars: thisMonthStars,
    commissionProgress: (thisMonthCommissions / monthlyCommissionGoal) * 100,
    starProgress: (thisMonthStars / monthlyStarGoal) * 100,
  );
});

// Helper functions
double _getThisMonthCommissions(List<CommissionModel> commissions) {
  final now = DateTime.now();
  return commissions
      .where((c) => c.createdAt.year == now.year && c.createdAt.month == now.month)
      .fold<double>(0, (sum, c) => sum + c.amount);
}

int _getThisMonthStars(List<StarModel> stars) {
  final now = DateTime.now();
  return stars
      .where((s) => s.earnedAt.year == now.year && s.earnedAt.month == now.month)
      .fold<int>(0, (sum, s) => sum + s.count);
}

double _calculateCommissionTrend(List<CommissionModel> commissions) {
  final now = DateTime.now();
  final thisMonth = _getThisMonthCommissions(commissions);
  
  final lastMonth = commissions
      .where((c) => c.createdAt.year == now.year && c.createdAt.month == now.month - 1)
      .fold<double>(0, (sum, c) => sum + c.amount);
  
  if (lastMonth == 0) return 0;
  return ((thisMonth - lastMonth) / lastMonth) * 100;
}

double _calculateStarTrend(List<StarModel> stars) {
  final now = DateTime.now();
  final thisMonth = _getThisMonthStars(stars);
  
  final lastMonth = stars
      .where((s) => s.earnedAt.year == now.year && s.earnedAt.month == now.month - 1)
      .fold<int>(0, (sum, s) => sum + s.count);
  
  if (lastMonth == 0) return 0;
  return ((thisMonth - lastMonth) / lastMonth) * 100;
}

Map<String, double> _getMonthlyCommissionTrends(List<CommissionModel> commissions) {
  final trends = <String, double>{};
  
  for (final commission in commissions) {
    final key = '${commission.createdAt.year}-${commission.createdAt.month.toString().padLeft(2, '0')}';
    trends[key] = (trends[key] ?? 0) + commission.amount;
  }
  
  return trends;
}

Map<String, int> _getMonthlyStarTrends(List<StarModel> stars) {
  final trends = <String, int>{};
  
  for (final star in stars) {
    final key = '${star.earnedAt.year}-${star.earnedAt.month.toString().padLeft(2, '0')}';
    trends[key] = (trends[key] ?? 0) + star.count;
  }
  
  return trends;
}

Map<String, int> _getWeeklyActivityTrends(List<CommissionModel> commissions, List<StarModel> stars) {
  final trends = <String, int>{};
  final now = DateTime.now();
  
  // Get last 7 days
  for (int i = 6; i >= 0; i--) {
    final date = now.subtract(Duration(days: i));
    final key = '${date.day}/${date.month}';
    
    final dayCommissions = commissions.where((c) => 
        c.createdAt.year == date.year &&
        c.createdAt.month == date.month &&
        c.createdAt.day == date.day).length;
    
    final dayStars = stars.where((s) => 
        s.earnedAt.year == date.year &&
        s.earnedAt.month == date.month &&
        s.earnedAt.day == date.day).length;
    
    trends[key] = dayCommissions + dayStars;
  }
  
  return trends;
}

String _formatTimeAgo(DateTime dateTime) {
  final now = DateTime.now();
  final difference = now.difference(dateTime);
  
  if (difference.inDays > 0) {
    return '${difference.inDays}d ago';
  } else if (difference.inHours > 0) {
    return '${difference.inHours}h ago';
  } else if (difference.inMinutes > 0) {
    return '${difference.inMinutes}m ago';
  } else {
    return 'Just now';
  }
}

// Data models
class DashboardOverview {
  final UserModel user;
  final List<CommissionModel> commissions;
  final List<StarModel> stars;
  final StarStatsModel starStats;
  final List<PropertyModel> availableProperties;

  DashboardOverview({
    UserModel? user,
    this.commissions = const [],
    this.stars = const [],
    this.starStats = const StarStatsModel(),
    this.availableProperties = const [],
  }) : user = user ?? UserModel(
      id: '',
      name: '',
      email: '',
      phoneNumber: '',
      role: 'agent',
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );
}

class DashboardStats {
  final double totalCommissions;
  final double thisMonthCommissions;
  final int totalStars;
  final int starsToNextBonus;
  final int networkSize;
  final int availableProperties;
  final double commissionTrend;
  final double starTrend;

  const DashboardStats({
    this.totalCommissions = 0,
    this.thisMonthCommissions = 0,
    this.totalStars = 0,
    this.starsToNextBonus = 12,
    this.networkSize = 0,
    this.availableProperties = 0,
    this.commissionTrend = 0,
    this.starTrend = 0,
  });
}

class PerformanceTrends {
  final Map<String, double> monthlyCommissions;
  final Map<String, int> monthlyStars;
  final Map<String, int> weeklyActivity;

  const PerformanceTrends({
    this.monthlyCommissions = const {},
    this.monthlyStars = const {},
    this.weeklyActivity = const {},
  });
}

class AgentRanking {
  final int currentRank;
  final int totalAgents;
  final double percentile;
  final List<Map<String, dynamic>> topPerformers;

  const AgentRanking({
    this.currentRank = 0,
    this.totalAgents = 0,
    this.percentile = 0,
    this.topPerformers = const [],
  });
}

class GoalTracking {
  final double monthlyCommissionGoal;
  final double currentMonthCommissions;
  final int monthlyStarGoal;
  final int currentMonthStars;
  final double commissionProgress;
  final double starProgress;

  const GoalTracking({
    this.monthlyCommissionGoal = 0,
    this.currentMonthCommissions = 0,
    this.monthlyStarGoal = 0,
    this.currentMonthStars = 0,
    this.commissionProgress = 0,
    this.starProgress = 0,
  });
}
