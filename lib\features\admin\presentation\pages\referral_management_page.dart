import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../../shared/widgets/loading_widget.dart';
import '../../providers/referral_management_provider.dart';
import '../../services/referral_management_service.dart';
import '../widgets/referral_analytics_card.dart';
import '../widgets/agent_referral_card.dart';
import '../widgets/referral_search_bar.dart';
import '../widgets/referral_sort_options.dart';

/// Admin page for managing referral codes and agent hierarchy
class ReferralManagementPage extends ConsumerStatefulWidget {
  const ReferralManagementPage({super.key});

  @override
  ConsumerState<ReferralManagementPage> createState() => _ReferralManagementPageState();
}

class _ReferralManagementPageState extends ConsumerState<ReferralManagementPage> {
  @override
  void initState() {
    super.initState();
    // Load data when page initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(referralManagementProvider.notifier).loadData();
    });
  }

  @override
  Widget build(BuildContext context) {
    final state = ref.watch(referralManagementProvider);
    final filteredAgents = ref.watch(filteredAgentsProvider);

    return Scaffold(
      body: Container(
        decoration: const BoxDecoration(
          gradient: AppTheme.darkBackgroundGradient,
        ),
        child: SafeArea(
          child: Column(
            children: [
              // Header
              _buildHeader(context),
              
              // Analytics Section
              if (state.analytics != null)
                Padding(
                  padding: const EdgeInsets.all(16),
                  child: ReferralAnalyticsCard(analytics: state.analytics!),
                ),
              
              // Search and Sort Controls
              Padding(
                padding: const EdgeInsets.symmetric(horizontal: 16),
                child: Column(
                  children: [
                    ReferralSearchBar(
                      searchQuery: state.searchQuery,
                      onSearchChanged: (query) {
                        ref.read(referralManagementProvider.notifier).updateSearchQuery(query);
                      },
                    ),
                    const SizedBox(height: 12),
                    ReferralSortOptions(
                      currentOption: state.sortOption,
                      isAscending: state.sortAscending,
                      onSortChanged: (option) {
                        ref.read(referralManagementProvider.notifier).updateSortOption(option);
                      },
                    ),
                  ],
                ),
              ),
              
              const SizedBox(height: 16),
              
              // Agents List
              Expanded(
                child: _buildAgentsList(state, filteredAgents),
              ),
            ],
          ),
        ),
      ),
      floatingActionButton: _buildFloatingActionButton(),
    );
  }

  /// Build header with title and refresh button
  Widget _buildHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(24),
      child: Row(
        children: [
          // Back Button
          IconButton(
            onPressed: () => Navigator.of(context).pop(),
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppTheme.primaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.arrow_back,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
          
          const SizedBox(width: 16),
          
          // Title
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Referral Management',
                  style: TextStyle(
                    color: AppTheme.darkPrimaryText,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                Text(
                  'Manage agent referral codes and hierarchy',
                  style: TextStyle(
                    color: AppTheme.darkSecondaryText,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
          
          // Refresh Button
          IconButton(
            onPressed: () {
              ref.read(referralManagementProvider.notifier).refresh();
            },
            icon: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                gradient: AppTheme.secondaryGradient,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Icon(
                Icons.refresh,
                color: Colors.white,
                size: 20,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build agents list
  Widget _buildAgentsList(ReferralManagementState state, List<AgentReferralInfo> agents) {
    if (state.isLoading) {
      return const LoadingWidget(message: 'Loading agents...');
    }

    if (state.error != null) {
      return _buildErrorWidget(state.error!);
    }

    if (agents.isEmpty) {
      return _buildEmptyWidget();
    }

    return RefreshIndicator(
      onRefresh: () async {
        await ref.read(referralManagementProvider.notifier).refresh();
      },
      child: ListView.builder(
        padding: const EdgeInsets.symmetric(horizontal: 16),
        itemCount: agents.length,
        itemBuilder: (context, index) {
          final agent = agents[index];
          return Padding(
            padding: const EdgeInsets.only(bottom: 12),
            child: AgentReferralCard(
              agentInfo: agent,
              onEditReferralCode: () => _showEditReferralCodeDialog(agent),
              onEditUpline: () => _showEditUplineDialog(agent),
            ),
          );
        },
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget(String error) {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Error Loading Data',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            error,
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 24),
          GradientWidgets.gradientButton(
            text: 'Retry',
            onPressed: () {
              ref.read(referralManagementProvider.notifier).refresh();
            },
            icon: Icons.refresh,
          ),
        ],
      ),
    );
  }

  /// Build empty widget
  Widget _buildEmptyWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.group_off,
            size: 64,
            color: AppTheme.darkSecondaryText,
          ),
          const SizedBox(height: 16),
          Text(
            'No Agents Found',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 8),
          Text(
            'No agents match your search criteria',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Build floating action button
  Widget _buildFloatingActionButton() {
    return GradientWidgets.gradientFAB(
      onPressed: () {
        // TODO: Add new agent functionality
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(
            content: Text('Add new agent functionality coming soon!'),
          ),
        );
      },
      icon: Icons.person_add,
    );
  }

  /// Show edit referral code dialog
  void _showEditReferralCodeDialog(AgentReferralInfo agentInfo) {
    final controller = TextEditingController(text: agentInfo.referralCode);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.darkCard,
        title: Text(
          'Edit Referral Code',
          style: TextStyle(color: AppTheme.darkPrimaryText),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Agent: ${agentInfo.agent.name}',
              style: TextStyle(
                color: AppTheme.darkSecondaryText,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              style: TextStyle(color: AppTheme.darkPrimaryText),
              decoration: InputDecoration(
                labelText: 'Referral Code',
                labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.darkBorder),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.darkBorder),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.primaryColor),
                ),
              ),
              textCapitalization: TextCapitalization.characters,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppTheme.darkSecondaryText),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final newCode = controller.text.trim();
              if (newCode.isNotEmpty && newCode != agentInfo.referralCode) {
                ref.read(referralManagementProvider.notifier)
                    .updateReferralCode(agentInfo.agent.id, newCode);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }

  /// Show edit upline dialog
  void _showEditUplineDialog(AgentReferralInfo agentInfo) {
    final controller = TextEditingController(text: agentInfo.uplineReferralCode);
    
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.darkCard,
        title: Text(
          'Edit Upline',
          style: TextStyle(color: AppTheme.darkPrimaryText),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Agent: ${agentInfo.agent.name}',
              style: TextStyle(
                color: AppTheme.darkSecondaryText,
                fontSize: 14,
              ),
            ),
            const SizedBox(height: 16),
            TextField(
              controller: controller,
              style: TextStyle(color: AppTheme.darkPrimaryText),
              decoration: InputDecoration(
                labelText: 'Upline Referral Code',
                labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
                border: OutlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.darkBorder),
                ),
                enabledBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.darkBorder),
                ),
                focusedBorder: OutlineInputBorder(
                  borderSide: BorderSide(color: AppTheme.primaryColor),
                ),
              ),
              textCapitalization: TextCapitalization.characters,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: Text(
              'Cancel',
              style: TextStyle(color: AppTheme.darkSecondaryText),
            ),
          ),
          ElevatedButton(
            onPressed: () {
              final newUplineCode = controller.text.trim();
              if (newUplineCode.isNotEmpty && newUplineCode != agentInfo.uplineReferralCode) {
                ref.read(referralManagementProvider.notifier)
                    .updateUpline(agentInfo.agent.id, newUplineCode);
              }
              Navigator.of(context).pop();
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Update'),
          ),
        ],
      ),
    );
  }
}
