import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/property_providers.dart';
import '../../../../core/models/property_model.dart';

/// Enhanced property search bar with suggestions
class PropertySearchBar extends ConsumerStatefulWidget {
  final Function(PropertyModel)? onPropertySelected;
  final String? hintText;
  final bool showFilters;

  const PropertySearchBar({
    super.key,
    this.onPropertySelected,
    this.hintText,
    this.showFilters = true,
  });

  @override
  ConsumerState<PropertySearchBar> createState() => _PropertySearchBarState();
}

class _PropertySearchBarState extends ConsumerState<PropertySearchBar> {
  final TextEditingController _controller = TextEditingController();
  final FocusNode _focusNode = FocusNode();
  bool _showSuggestions = false;

  @override
  void dispose() {
    _controller.dispose();
    _focusNode.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(propertySearchProvider);
    final searchSuggestions = ref.watch(searchSuggestionsProvider);

    return Column(
      children: [
        // Search Input
        Container(
          decoration: BoxDecoration(
            color: Colors.white,
            borderRadius: BorderRadius.circular(12),
            boxShadow: [
              BoxShadow(
                color: Colors.grey.withValues(alpha: 0.1),
                blurRadius: 8,
                offset: const Offset(0, 2),
              ),
            ],
          ),
          child: TextField(
            controller: _controller,
            focusNode: _focusNode,
            decoration: InputDecoration(
              hintText: widget.hintText ?? 'Search properties by location, type, or price...',
              prefixIcon: const Icon(Icons.search, color: Colors.grey),
              suffixIcon: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  if (_controller.text.isNotEmpty)
                    IconButton(
                      icon: const Icon(Icons.clear, color: Colors.grey),
                      onPressed: () {
                        _controller.clear();
                        ref.read(propertySearchProvider.notifier).clearSearch();
                        setState(() => _showSuggestions = false);
                      },
                    ),
                  if (widget.showFilters)
                    IconButton(
                      icon: const Icon(Icons.tune, color: Colors.grey),
                      onPressed: () => _showAdvancedFilters(context),
                    ),
                ],
              ),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(12),
                borderSide: BorderSide.none,
              ),
              filled: true,
              fillColor: Colors.grey[50],
              contentPadding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
            ),
            onChanged: (query) {
              if (query.isNotEmpty) {
                ref.read(propertySearchProvider.notifier).searchProperties(query);
                ref.read(searchSuggestionsProvider.notifier).updateSuggestions(query);
                setState(() => _showSuggestions = true);
              } else {
                setState(() => _showSuggestions = false);
              }
            },
            onTap: () {
              if (_controller.text.isNotEmpty) {
                setState(() => _showSuggestions = true);
              }
            },
          ),
        ),

        // Search Suggestions
        if (_showSuggestions) ...[
          const SizedBox(height: 8),
          _buildSuggestions(searchState, searchSuggestions),
        ],
      ],
    );
  }

  Widget _buildSuggestions(PropertySearchState searchState, SearchSuggestionsState suggestions) {
    return Container(
      constraints: const BoxConstraints(maxHeight: 300),
      decoration: BoxDecoration(
        color: Colors.white,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: Colors.grey.withValues(alpha: 0.1),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Quick Suggestions
          if (suggestions.locationSuggestions.isNotEmpty ||
              suggestions.typeSuggestions.isNotEmpty ||
              suggestions.priceSuggestions.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(12),
              child: Text(
                'Suggestions',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            _buildQuickSuggestions(suggestions),
            const Divider(height: 1),
          ],

          // Property Results
          if (searchState.results.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(12),
              child: Text(
                'Properties (${searchState.results.length})',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                  fontWeight: FontWeight.w500,
                ),
              ),
            ),
            Expanded(
              child: ListView.builder(
                shrinkWrap: true,
                itemCount: searchState.results.take(5).length,
                itemBuilder: (context, index) {
                  final property = searchState.results[index];
                  return _buildPropertySuggestion(property);
                },
              ),
            ),
          ] else if (searchState.isLoading) ...[
            const Padding(
              padding: EdgeInsets.all(16),
              child: Center(child: CircularProgressIndicator()),
            ),
          ] else if (searchState.query.isNotEmpty) ...[
            Padding(
              padding: const EdgeInsets.all(16),
              child: Text(
                'No properties found for "${searchState.query}"',
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  Widget _buildQuickSuggestions(SearchSuggestionsState suggestions) {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 12),
      child: Wrap(
        spacing: 8,
        runSpacing: 4,
        children: [
          ...suggestions.locationSuggestions.take(3).map((location) =>
            _buildSuggestionChip(location, Icons.location_on, () {
              _controller.text = location;
              ref.read(propertySearchProvider.notifier).searchProperties(location);
            }),
          ),
          ...suggestions.typeSuggestions.take(2).map((type) =>
            _buildSuggestionChip(type, Icons.home, () {
              _controller.text = type;
              ref.read(propertySearchProvider.notifier).searchProperties(type);
            }),
          ),
          ...suggestions.priceSuggestions.take(2).map((price) =>
            _buildSuggestionChip(price, Icons.currency_rupee, () {
              _controller.text = price;
              ref.read(propertySearchProvider.notifier).searchProperties(price);
            }),
          ),
        ],
      ),
    );
  }

  Widget _buildSuggestionChip(String text, IconData icon, VoidCallback onTap) {
    return GestureDetector(
      onTap: onTap,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
        decoration: BoxDecoration(
          color: Colors.blue.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(12),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, size: 14, color: Colors.blue),
            const SizedBox(width: 4),
            Text(
              text,
              style: const TextStyle(fontSize: 12, color: Colors.blue),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertySuggestion(PropertyModel property) {
    return ListTile(
      dense: true,
      leading: Container(
        width: 40,
        height: 40,
        decoration: BoxDecoration(
          color: Colors.grey[200],
          borderRadius: BorderRadius.circular(8),
        ),
        child: property.imageUrls.isNotEmpty
            ? ClipRRect(
                borderRadius: BorderRadius.circular(8),
                child: Image.network(
                  property.imageUrls.first,
                  fit: BoxFit.cover,
                  errorBuilder: (context, error, stackTrace) =>
                      const Icon(Icons.home, color: Colors.grey),
                ),
              )
            : const Icon(Icons.home, color: Colors.grey),
      ),
      title: Text(
        property.title,
        style: const TextStyle(fontWeight: FontWeight.w500),
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      subtitle: Text(
        '${property.formattedPrice} • ${property.city}',
        maxLines: 1,
        overflow: TextOverflow.ellipsis,
      ),
      trailing: const Icon(Icons.arrow_forward_ios, size: 16),
      onTap: () {
        setState(() => _showSuggestions = false);
        _focusNode.unfocus();
        widget.onPropertySelected?.call(property);
      },
    );
  }

  void _showAdvancedFilters(BuildContext context) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      backgroundColor: Colors.transparent,
      builder: (context) => const AdvancedFilterSheet(),
    );
  }
}

/// Advanced filter bottom sheet
class AdvancedFilterSheet extends ConsumerStatefulWidget {
  const AdvancedFilterSheet({super.key});

  @override
  ConsumerState<AdvancedFilterSheet> createState() => _AdvancedFilterSheetState();
}

class _AdvancedFilterSheetState extends ConsumerState<AdvancedFilterSheet> {
  late PropertyFilter _tempFilter;

  @override
  void initState() {
    super.initState();
    _tempFilter = ref.read(propertyFilterProvider);
  }

  @override
  Widget build(BuildContext context) {
    return DraggableScrollableSheet(
      initialChildSize: 0.7,
      maxChildSize: 0.9,
      minChildSize: 0.5,
      builder: (context, scrollController) => Container(
        decoration: const BoxDecoration(
          color: Colors.white,
          borderRadius: BorderRadius.vertical(top: Radius.circular(20)),
        ),
        child: Column(
          children: [
            // Handle
            Container(
              margin: const EdgeInsets.only(top: 8),
              width: 40,
              height: 4,
              decoration: BoxDecoration(
                color: Colors.grey[300],
                borderRadius: BorderRadius.circular(2),
              ),
            ),

            // Header
            Padding(
              padding: const EdgeInsets.all(16),
              child: Row(
                children: [
                  Text(
                    'Advanced Filters',
                    style: Theme.of(context).textTheme.titleLarge?.copyWith(
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                  const Spacer(),
                  TextButton(
                    onPressed: () {
                      setState(() {
                        _tempFilter = const PropertyFilter();
                      });
                    },
                    child: const Text('Reset'),
                  ),
                ],
              ),
            ),

            const Divider(height: 1),

            // Filter Content
            Expanded(
              child: SingleChildScrollView(
                controller: scrollController,
                padding: const EdgeInsets.all(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    _buildPropertyTypeFilter(),
                    const SizedBox(height: 24),
                    _buildStatusFilter(),
                    const SizedBox(height: 24),
                    _buildLocationFilter(),
                    const SizedBox(height: 24),
                    _buildPriceRangeFilter(),
                    const SizedBox(height: 24),
                    _buildPropertyDetailsFilter(),
                    const SizedBox(height: 24),
                    _buildSortingOptions(),
                  ],
                ),
              ),
            ),

            // Apply Button
            Container(
              padding: const EdgeInsets.all(16),
              child: SizedBox(
                width: double.infinity,
                child: ElevatedButton(
                  onPressed: () {
                    ref.read(propertyFilterProvider.notifier).updateFilter(_tempFilter);
                    Navigator.of(context).pop();
                  },
                  child: const Text('Apply Filters'),
                ),
              ),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPropertyTypeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Property Type',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: ['Residential', 'Commercial', 'Land', 'Industrial', 'Agricultural']
              .map((type) => FilterChip(
                    label: Text(type),
                    selected: _tempFilter.type == type,
                    onSelected: (selected) {
                      setState(() {
                        _tempFilter = _tempFilter.copyWith(
                          type: selected ? type : null,
                        );
                      });
                    },
                  ))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildStatusFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Status',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: [
            {'value': 'for_sale', 'label': 'For Sale'},
            {'value': 'for_rent', 'label': 'For Rent'},
            {'value': 'sold', 'label': 'Sold'},
            {'value': 'rented', 'label': 'Rented'},
          ]
              .map((status) => FilterChip(
                    label: Text(status['label']!),
                    selected: _tempFilter.status == status['value'],
                    onSelected: (selected) {
                      setState(() {
                        _tempFilter = _tempFilter.copyWith(
                          status: selected ? status['value'] : null,
                        );
                      });
                    },
                  ))
              .toList(),
        ),
      ],
    );
  }

  Widget _buildLocationFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Location',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: DropdownButtonFormField<String?>(
                value: _tempFilter.state,
                decoration: const InputDecoration(
                  labelText: 'State',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem<String?>(value: null, child: Text('All States')),
                  ...['Maharashtra', 'Delhi', 'Karnataka', 'Tamil Nadu', 'Gujarat', 'Rajasthan']
                      .map((state) => DropdownMenuItem(value: state, child: Text(state))),
                ],
                onChanged: (value) {
                  setState(() {
                    _tempFilter = _tempFilter.copyWith(state: value);
                  });
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: DropdownButtonFormField<String?>(
                value: _tempFilter.city,
                decoration: const InputDecoration(
                  labelText: 'City',
                  border: OutlineInputBorder(),
                ),
                items: [
                  const DropdownMenuItem<String?>(value: null, child: Text('All Cities')),
                  ...['Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Pune', 'Hyderabad']
                      .map((city) => DropdownMenuItem(value: city, child: Text(city))),
                ],
                onChanged: (value) {
                  setState(() {
                    _tempFilter = _tempFilter.copyWith(city: value);
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPriceRangeFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Price Range',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Min Price (₹)',
                  border: OutlineInputBorder(),
                  prefixText: '₹ ',
                ),
                keyboardType: TextInputType.number,
                initialValue: _tempFilter.minPrice?.toString(),
                onChanged: (value) {
                  final price = double.tryParse(value);
                  setState(() {
                    _tempFilter = _tempFilter.copyWith(minPrice: price);
                  });
                },
              ),
            ),
            const SizedBox(width: 16),
            Expanded(
              child: TextFormField(
                decoration: const InputDecoration(
                  labelText: 'Max Price (₹)',
                  border: OutlineInputBorder(),
                  prefixText: '₹ ',
                ),
                keyboardType: TextInputType.number,
                initialValue: _tempFilter.maxPrice?.toString(),
                onChanged: (value) {
                  final price = double.tryParse(value);
                  setState(() {
                    _tempFilter = _tempFilter.copyWith(maxPrice: price);
                  });
                },
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildPropertyDetailsFilter() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Property Details',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        Row(
          children: [
            Expanded(
              child: CheckboxListTile(
                title: const Text('Featured Only'),
                value: _tempFilter.isFeatured ?? false,
                onChanged: (value) {
                  setState(() {
                    _tempFilter = _tempFilter.copyWith(isFeatured: value);
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),
            ),
            Expanded(
              child: CheckboxListTile(
                title: const Text('Approved Only'),
                value: _tempFilter.isApproved ?? true,
                onChanged: (value) {
                  setState(() {
                    _tempFilter = _tempFilter.copyWith(isApproved: value);
                  });
                },
                controlAffinity: ListTileControlAffinity.leading,
                contentPadding: EdgeInsets.zero,
              ),
            ),
          ],
        ),
      ],
    );
  }

  Widget _buildSortingOptions() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Sort By',
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.w600,
          ),
        ),
        const SizedBox(height: 12),
        DropdownButtonFormField<String>(
          value: _tempFilter.sortBy,
          decoration: const InputDecoration(
            border: OutlineInputBorder(),
          ),
          items: [
            {'value': 'createdAt', 'label': 'Date Added'},
            {'value': 'price', 'label': 'Price'},
            {'value': 'title', 'label': 'Title'},
            {'value': 'city', 'label': 'City'},
          ]
              .map((sort) => DropdownMenuItem(
                    value: sort['value'],
                    child: Text(sort['label']!),
                  ))
              .toList(),
          onChanged: (value) {
            setState(() {
              _tempFilter = _tempFilter.copyWith(sortBy: value ?? 'createdAt');
            });
          },
        ),
        const SizedBox(height: 8),
        SwitchListTile(
          title: const Text('Ascending Order'),
          value: _tempFilter.sortAscending,
          onChanged: (value) {
            setState(() {
              _tempFilter = _tempFilter.copyWith(sortAscending: value);
            });
          },
          contentPadding: EdgeInsets.zero,
        ),
      ],
    );
  }
}
