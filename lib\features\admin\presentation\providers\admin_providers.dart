import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/admin_analytics_model.dart';
import '../../../../core/models/user_model.dart';
import '../../../../core/services/admin_analytics_service.dart';
import '../../../../core/services/auth_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

/// Admin analytics provider - with permission error handling
final adminAnalyticsProvider = FutureProvider.autoDispose<AdminAnalyticsModel>((
  ref,
) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) {
    throw Exception('Access denied: Admin privileges required');
  }

  try {
    return await AdminAnalyticsService.generateAnalytics();
  } catch (e) {
    // If Firebase permission error, throw a more helpful message
    if (e.toString().contains('permission') ||
        e.toString().contains('insufficient')) {
      throw Exception(
        'Analytics require admin privileges. Check Users tab for test data.',
      );
    }
    rethrow;
  }
});

/// Admin configuration provider
final adminConfigProvider = FutureProvider.autoDispose<AdminConfigModel?>((
  ref,
) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) return null;

  return await AdminAnalyticsService.getAdminConfig();
});

/// All users provider (Admin only) - with test data support
final allUsersProvider = FutureProvider.autoDispose<List<UserModel>>((
  ref,
) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) return [];

  try {
    return await AuthService.getAllUsers();
  } catch (e) {
    // If Firebase permission error, return empty list
    print('Firebase permission error in getAllUsers: $e');
    return [];
  }
});

/// User management provider
final userManagementProvider =
    StateNotifierProvider<UserManagementNotifier, UserManagementState>((ref) {
      return UserManagementNotifier();
    });

/// User management state
class UserManagementState {
  final List<UserModel> users;
  final List<UserModel> filteredUsers;
  final UserFilter filter;
  final bool isLoading;
  final String? error;

  const UserManagementState({
    this.users = const [],
    this.filteredUsers = const [],
    this.filter = const UserFilter(),
    this.isLoading = false,
    this.error,
  });

  UserManagementState copyWith({
    List<UserModel>? users,
    List<UserModel>? filteredUsers,
    UserFilter? filter,
    bool? isLoading,
    String? error,
  }) {
    return UserManagementState(
      users: users ?? this.users,
      filteredUsers: filteredUsers ?? this.filteredUsers,
      filter: filter ?? this.filter,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// User filter
class UserFilter {
  final String? role;
  final String? status; // active, inactive
  final int? level;
  final String? searchQuery;
  final String sortBy;
  final bool sortAscending;

  const UserFilter({
    this.role,
    this.status,
    this.level,
    this.searchQuery,
    this.sortBy = 'createdAt',
    this.sortAscending = false,
  });

  UserFilter copyWith({
    String? role,
    String? status,
    int? level,
    String? searchQuery,
    String? sortBy,
    bool? sortAscending,
  }) {
    return UserFilter(
      role: role ?? this.role,
      status: status ?? this.status,
      level: level ?? this.level,
      searchQuery: searchQuery ?? this.searchQuery,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }
}

/// User management notifier
class UserManagementNotifier extends StateNotifier<UserManagementState> {
  UserManagementNotifier() : super(const UserManagementState());

  /// Load all users
  Future<void> loadUsers() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final users = await AuthService.getAllUsers();
      state = state.copyWith(
        users: users,
        filteredUsers: _applyFilters(users, state.filter),
        isLoading: false,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load users: $e',
      );
    }
  }

  /// Update filter
  void updateFilter(UserFilter filter) {
    final filteredUsers = _applyFilters(state.users, filter);
    state = state.copyWith(filter: filter, filteredUsers: filteredUsers);
  }

  /// Apply filters to user list
  List<UserModel> _applyFilters(List<UserModel> users, UserFilter filter) {
    var filtered = users.where((user) {
      // Role filter
      if (filter.role != null && user.role != filter.role) return false;

      // Status filter
      if (filter.status != null) {
        final isActive = filter.status == 'active';
        if (user.isActive != isActive) return false;
      }

      // Level filter
      if (filter.level != null && user.level != filter.level) return false;

      // Search query
      if (filter.searchQuery != null && filter.searchQuery!.isNotEmpty) {
        final query = filter.searchQuery!.toLowerCase();
        if (!user.name.toLowerCase().contains(query) &&
            !user.email.toLowerCase().contains(query) &&
            !user.phoneNumber.contains(query)) {
          return false;
        }
      }

      return true;
    }).toList();

    // Apply sorting
    filtered.sort((a, b) {
      int comparison = 0;

      switch (filter.sortBy) {
        case 'name':
          comparison = a.name.compareTo(b.name);
          break;
        case 'email':
          comparison = a.email.compareTo(b.email);
          break;
        case 'level':
          comparison = a.level.compareTo(b.level);
          break;
        case 'totalStars':
          comparison = a.totalStars.compareTo(b.totalStars);
          break;
        case 'totalCommissions':
          comparison = a.totalCommissions.compareTo(b.totalCommissions);
          break;
        case 'createdAt':
        default:
          comparison = a.createdAt.compareTo(b.createdAt);
          break;
      }

      return filter.sortAscending ? comparison : -comparison;
    });

    return filtered;
  }

  /// Toggle user active status
  Future<void> toggleUserStatus(String userId) async {
    try {
      final userIndex = state.users.indexWhere((u) => u.id == userId);
      if (userIndex == -1) return;

      final user = state.users[userIndex];
      final updatedUser = user.copyWith(
        isActive: !user.isActive,
        updatedAt: DateTime.now(),
      );

      // Update in Firestore
      await AuthService.updateUser(updatedUser);

      // Update local state
      final updatedUsers = List<UserModel>.from(state.users);
      updatedUsers[userIndex] = updatedUser;

      state = state.copyWith(
        users: updatedUsers,
        filteredUsers: _applyFilters(updatedUsers, state.filter),
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to update user status: $e');
    }
  }

  /// Update user role
  Future<void> updateUserRole(String userId, String newRole) async {
    try {
      final userIndex = state.users.indexWhere((u) => u.id == userId);
      if (userIndex == -1) return;

      final user = state.users[userIndex];
      final updatedUser = user.copyWith(
        role: newRole,
        updatedAt: DateTime.now(),
      );

      // Update in Firestore
      await AuthService.updateUser(updatedUser);

      // Update local state
      final updatedUsers = List<UserModel>.from(state.users);
      updatedUsers[userIndex] = updatedUser;

      state = state.copyWith(
        users: updatedUsers,
        filteredUsers: _applyFilters(updatedUsers, state.filter),
      );
    } catch (e) {
      state = state.copyWith(error: 'Failed to update user role: $e');
    }
  }
}

/// System configuration provider
final systemConfigProvider =
    StateNotifierProvider<SystemConfigNotifier, SystemConfigState>((ref) {
      return SystemConfigNotifier();
    });

/// System configuration state
class SystemConfigState {
  final Map<int, double> commissionRates;
  final double starBonusAmount;
  final Map<String, bool> featureFlags;
  final bool isLoading;
  final String? error;

  const SystemConfigState({
    this.commissionRates = const {},
    this.starBonusAmount = 0.0,
    this.featureFlags = const {},
    this.isLoading = false,
    this.error,
  });

  SystemConfigState copyWith({
    Map<int, double>? commissionRates,
    double? starBonusAmount,
    Map<String, bool>? featureFlags,
    bool? isLoading,
    String? error,
  }) {
    return SystemConfigState(
      commissionRates: commissionRates ?? this.commissionRates,
      starBonusAmount: starBonusAmount ?? this.starBonusAmount,
      featureFlags: featureFlags ?? this.featureFlags,
      isLoading: isLoading ?? this.isLoading,
      error: error ?? this.error,
    );
  }
}

/// System configuration notifier
class SystemConfigNotifier extends StateNotifier<SystemConfigState> {
  SystemConfigNotifier() : super(const SystemConfigState());

  /// Load configuration
  Future<void> loadConfig() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final config = await AdminAnalyticsService.getAdminConfig();

      if (config != null) {
        state = state.copyWith(
          commissionRates: config.commissionRates,
          starBonusAmount: config.starBonusAmount,
          featureFlags: config.featureFlags,
          isLoading: false,
        );
      } else {
        // Use default configuration
        state = state.copyWith(
          commissionRates: const {
            0: 0.05, // 5%
            1: 0.02, // 2%
            2: 0.01, // 1%
            3: 0.005, // 0.5%
            4: 0.002, // 0.2%
          },
          starBonusAmount: 50000.0, // ₹50,000
          featureFlags: const {
            'property_approval_required': true,
            'auto_commission_distribution': true,
            'star_bonus_enabled': true,
            'lead_tracking_enabled': true,
          },
          isLoading: false,
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to load configuration: $e',
      );
    }
  }

  /// Update commission rate for a specific level
  void updateCommissionRate(int level, double rate) {
    final updatedRates = Map<int, double>.from(state.commissionRates);
    updatedRates[level] = rate;
    state = state.copyWith(commissionRates: updatedRates);
  }

  /// Update star bonus amount
  void updateStarBonusAmount(double amount) {
    state = state.copyWith(starBonusAmount: amount);
  }

  /// Toggle feature flag
  void toggleFeatureFlag(String feature, bool enabled) {
    final updatedFlags = Map<String, bool>.from(state.featureFlags);
    updatedFlags[feature] = enabled;
    state = state.copyWith(featureFlags: updatedFlags);
  }

  /// Save configuration
  Future<void> saveConfig(String adminId) async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final config = AdminConfigModel(
        id: 'system_config',
        commissionRates: state.commissionRates,
        starBonusAmount: state.starBonusAmount,
        systemSettings: {},
        featureFlags: state.featureFlags,
        updatedAt: DateTime.now(),
        updatedBy: adminId,
      );

      final success = await AdminAnalyticsService.updateAdminConfig(config);

      if (success) {
        state = state.copyWith(isLoading: false);
      } else {
        state = state.copyWith(
          isLoading: false,
          error: 'Failed to save configuration',
        );
      }
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Failed to save configuration: $e',
      );
    }
  }
}
