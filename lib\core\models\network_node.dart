import 'package:cloud_firestore/cloud_firestore.dart';
import 'user_model.dart';

/// Represents a node in the MLM network tree
class NetworkNode {
  final UserModel user;
  final List<NetworkNode> children;
  final NetworkNode? parent;
  final int depth;
  final DateTime joinedAt;
  final NetworkStats stats;

  const NetworkNode({
    required this.user,
    this.children = const [],
    this.parent,
    this.depth = 0,
    required this.joinedAt,
    required this.stats,
  });

  /// Create a copy with updated fields
  NetworkNode copyWith({
    UserModel? user,
    List<NetworkNode>? children,
    NetworkNode? parent,
    int? depth,
    DateTime? joinedAt,
    NetworkStats? stats,
  }) {
    return NetworkNode(
      user: user ?? this.user,
      children: children ?? this.children,
      parent: parent ?? this.parent,
      depth: depth ?? this.depth,
      joinedAt: joinedAt ?? this.joinedAt,
      stats: stats ?? this.stats,
    );
  }

  /// Get total network size (including this node)
  int get totalNetworkSize {
    int size = 1; // Count this node
    for (final child in children) {
      size += child.totalNetworkSize;
    }
    return size;
  }

  /// Get direct downline count
  int get directDownlineCount => children.length;

  /// Get all descendants up to specified level
  List<NetworkNode> getDescendants({int? maxLevel}) {
    final descendants = <NetworkNode>[];
    
    void addDescendants(NetworkNode node, int currentLevel) {
      if (maxLevel != null && currentLevel >= maxLevel) return;
      
      for (final child in node.children) {
        descendants.add(child);
        addDescendants(child, currentLevel + 1);
      }
    }
    
    addDescendants(this, 0);
    return descendants;
  }

  /// Get nodes at specific level
  List<NetworkNode> getNodesAtLevel(int targetLevel) {
    if (targetLevel == 0) return [this];
    
    final nodes = <NetworkNode>[];
    
    void findNodesAtLevel(NetworkNode node, int currentLevel) {
      if (currentLevel == targetLevel) {
        nodes.add(node);
        return;
      }
      
      for (final child in node.children) {
        findNodesAtLevel(child, currentLevel + 1);
      }
    }
    
    findNodesAtLevel(this, 0);
    return nodes;
  }

  /// Check if this node is an ancestor of another node
  bool isAncestorOf(NetworkNode other) {
    NetworkNode? current = other.parent;
    while (current != null) {
      if (current.user.id == user.id) return true;
      current = current.parent;
    }
    return false;
  }

  /// Get path from root to this node
  List<NetworkNode> getPathFromRoot() {
    final path = <NetworkNode>[];
    NetworkNode? current = this;
    
    while (current != null) {
      path.insert(0, current);
      current = current.parent;
    }
    
    return path;
  }

  @override
  String toString() {
    return 'NetworkNode(user: ${user.name}, level: $depth, children: ${children.length})';
  }
}

/// Statistics for a network node
class NetworkStats {
  final int totalDownlines;
  final int activeDownlines;
  final double totalCommissions;
  final int totalStars;
  final int totalSales;
  final Map<int, int> levelCounts; // level -> count
  final DateTime lastActivity;

  const NetworkStats({
    this.totalDownlines = 0,
    this.activeDownlines = 0,
    this.totalCommissions = 0.0,
    this.totalStars = 0,
    this.totalSales = 0,
    this.levelCounts = const {},
    required this.lastActivity,
  });

  /// Create NetworkStats from Firestore document
  factory NetworkStats.fromFirestore(Map<String, dynamic> data) {
    return NetworkStats(
      totalDownlines: data['totalDownlines'] ?? 0,
      activeDownlines: data['activeDownlines'] ?? 0,
      totalCommissions: (data['totalCommissions'] ?? 0.0).toDouble(),
      totalStars: data['totalStars'] ?? 0,
      totalSales: data['totalSales'] ?? 0,
      levelCounts: Map<int, int>.from(data['levelCounts'] ?? {}),
      lastActivity: (data['lastActivity'] as Timestamp?)?.toDate() ?? DateTime.now(),
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'totalDownlines': totalDownlines,
      'activeDownlines': activeDownlines,
      'totalCommissions': totalCommissions,
      'totalStars': totalStars,
      'totalSales': totalSales,
      'levelCounts': levelCounts,
      'lastActivity': Timestamp.fromDate(lastActivity),
    };
  }

  /// Create a copy with updated fields
  NetworkStats copyWith({
    int? totalDownlines,
    int? activeDownlines,
    double? totalCommissions,
    int? totalStars,
    int? totalSales,
    Map<int, int>? levelCounts,
    DateTime? lastActivity,
  }) {
    return NetworkStats(
      totalDownlines: totalDownlines ?? this.totalDownlines,
      activeDownlines: activeDownlines ?? this.activeDownlines,
      totalCommissions: totalCommissions ?? this.totalCommissions,
      totalStars: totalStars ?? this.totalStars,
      totalSales: totalSales ?? this.totalSales,
      levelCounts: levelCounts ?? this.levelCounts,
      lastActivity: lastActivity ?? this.lastActivity,
    );
  }

  /// Get growth rate compared to previous period
  double getGrowthRate(NetworkStats previous) {
    if (previous.totalDownlines == 0) return 0.0;
    return ((totalDownlines - previous.totalDownlines) / previous.totalDownlines) * 100;
  }

  /// Get activity score (0-100)
  int get activityScore {
    final daysSinceActivity = DateTime.now().difference(lastActivity).inDays;
    if (daysSinceActivity <= 1) return 100;
    if (daysSinceActivity <= 7) return 80;
    if (daysSinceActivity <= 30) return 60;
    if (daysSinceActivity <= 90) return 40;
    return 20;
  }

  @override
  String toString() {
    return 'NetworkStats(downlines: $totalDownlines, active: $activeDownlines, commissions: $totalCommissions)';
  }
}

/// MLM Network relationship model
class MLMRelationship {
  final String id;
  final String uplineId;
  final String downlineId;
  final int level;
  final DateTime createdAt;
  final bool isActive;

  const MLMRelationship({
    required this.id,
    required this.uplineId,
    required this.downlineId,
    required this.level,
    required this.createdAt,
    this.isActive = true,
  });

  /// Create MLMRelationship from Firestore document
  factory MLMRelationship.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return MLMRelationship(
      id: doc.id,
      uplineId: data['uplineId'] ?? '',
      downlineId: data['downlineId'] ?? '',
      level: data['level'] ?? 0,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      isActive: data['isActive'] ?? true,
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'uplineId': uplineId,
      'downlineId': downlineId,
      'level': level,
      'createdAt': Timestamp.fromDate(createdAt),
      'isActive': isActive,
    };
  }

  /// Create a copy with updated fields
  MLMRelationship copyWith({
    String? id,
    String? uplineId,
    String? downlineId,
    int? level,
    DateTime? createdAt,
    bool? isActive,
  }) {
    return MLMRelationship(
      id: id ?? this.id,
      uplineId: uplineId ?? this.uplineId,
      downlineId: downlineId ?? this.downlineId,
      level: level ?? this.level,
      createdAt: createdAt ?? this.createdAt,
      isActive: isActive ?? this.isActive,
    );
  }

  @override
  String toString() {
    return 'MLMRelationship(upline: $uplineId, downline: $downlineId, level: $level)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is MLMRelationship && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
