import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../commissions/models/commission_enums.dart';
import '../../models/network_models.dart';
import '../../services/network_service.dart';

/// Network statistics dashboard with charts and metrics
class NetworkStatisticsDashboard extends ConsumerStatefulWidget {
  final String agentId;

  const NetworkStatisticsDashboard({
    super.key,
    required this.agentId,
  });

  @override
  ConsumerState<NetworkStatisticsDashboard> createState() => _NetworkStatisticsDashboardState();
}

class _NetworkStatisticsDashboardState extends ConsumerState<NetworkStatisticsDashboard>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  NetworkStatistics? _statistics;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadStatistics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  Future<void> _loadStatistics() async {
    setState(() => _isLoading = true);
    
    try {
      final statistics = await NetworkService.getNetworkStatistics(widget.agentId);
      setState(() {
        _statistics = statistics;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_statistics == null) {
      return _buildErrorWidget();
    }

    return Column(
      children: [
        // Summary Cards
        _buildSummaryCards(),
        
        const SizedBox(height: 24),
        
        // Tab Bar
        _buildTabBar(),
        
        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildOverviewTab(),
              _buildDistributionTab(),
              _buildPerformanceTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// Build summary cards
  Widget _buildSummaryCards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.2,
      children: [
        _buildSummaryCard(
          'Total Agents',
          '${_statistics!.totalAgents}',
          Icons.group,
          AppTheme.primaryColor,
          subtitle: '${_statistics!.activeAgents} active',
        ),
        _buildSummaryCard(
          'Network Sales',
          _statistics!.formattedTotalSales,
          Icons.trending_up,
          AppTheme.successColor,
          subtitle: 'Total revenue',
        ),
        _buildSummaryCard(
          'Commissions',
          _statistics!.formattedTotalCommissions,
          Icons.account_balance_wallet,
          AppTheme.warningColor,
          subtitle: 'Total earned',
        ),
        _buildSummaryCard(
          'Growth Rate',
          '${_statistics!.networkGrowthRate.toStringAsFixed(1)}%',
          Icons.show_chart,
          _statistics!.networkGrowthRate >= 0 ? AppTheme.successColor : AppTheme.errorColor,
          subtitle: 'Network growth',
        ),
      ],
    );
  }

  /// Build summary card
  Widget _buildSummaryCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: AppTheme.primaryColor.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  '${_statistics!.activityRate.toStringAsFixed(0)}%',
                  style: TextStyle(
                    color: AppTheme.primaryColor,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(
                color: AppTheme.darkHintText,
                fontSize: 10,
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.darkSecondaryText,
        labelStyle: const TextStyle(
          fontSize: 14,
          fontWeight: FontWeight.w600,
        ),
        tabs: const [
          Tab(text: 'Overview'),
          Tab(text: 'Distribution'),
          Tab(text: 'Performance'),
        ],
      ),
    );
  }

  /// Build overview tab
  Widget _buildOverviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Activity Overview
          _buildActivityOverview(),
          
          const SizedBox(height: 24),
          
          // Top Performers
          _buildTopPerformers(),
        ],
      ),
    );
  }

  /// Build distribution tab
  Widget _buildDistributionTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Level Distribution Chart
          _buildLevelDistributionChart(),
          
          const SizedBox(height: 24),
          
          // Tier Distribution Chart
          _buildTierDistributionChart(),
        ],
      ),
    );
  }

  /// Build performance tab
  Widget _buildPerformanceTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Performance Metrics
          _buildPerformanceMetrics(),
          
          const SizedBox(height: 24),
          
          // Growth Trends
          _buildGrowthTrends(),
        ],
      ),
    );
  }

  /// Build activity overview
  Widget _buildActivityOverview() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Network Activity',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          Row(
            children: [
              Expanded(
                child: _buildActivityItem(
                  'Active Agents',
                  '${_statistics!.activeAgents}',
                  AppTheme.successColor,
                ),
              ),
              Expanded(
                child: _buildActivityItem(
                  'Inactive Agents',
                  '${_statistics!.inactiveAgents}',
                  AppTheme.errorColor,
                ),
              ),
              Expanded(
                child: _buildActivityItem(
                  'Avg Team Size',
                  _statistics!.averageTeamSize.toStringAsFixed(1),
                  AppTheme.primaryColor,
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build activity item
  Widget _buildActivityItem(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        const SizedBox(height: 4),
        Text(
          label,
          style: TextStyle(
            color: AppTheme.darkSecondaryText,
            fontSize: 12,
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  /// Build top performers
  Widget _buildTopPerformers() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Top Performers',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          ..._statistics!.topPerformers.take(5).map((performer) => 
            _buildTopPerformerItem(performer)
          ).toList(),
        ],
      ),
    );
  }

  /// Build top performer item
  Widget _buildTopPerformerItem(TopPerformer performer) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          CircleAvatar(
            radius: 20,
            backgroundColor: performer.tier.color,
            child: Text(
              performer.name.isNotEmpty ? performer.name[0] : 'A',
              style: const TextStyle(
                color: Colors.white,
                fontWeight: FontWeight.bold,
              ),
            ),
          ),
          
          const SizedBox(width: 12),
          
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  performer.name,
                  style: TextStyle(
                    color: AppTheme.darkPrimaryText,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  '${performer.teamSize} team members',
                  style: TextStyle(
                    color: AppTheme.darkSecondaryText,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          
          Column(
            crossAxisAlignment: CrossAxisAlignment.end,
            children: [
              Text(
                performer.sales.toStringAsFixed(0),
                style: TextStyle(
                  color: AppTheme.primaryColor,
                  fontSize: 14,
                  fontWeight: FontWeight.bold,
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
                decoration: BoxDecoration(
                  color: performer.tier.color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Text(
                  performer.tier.displayName,
                  style: TextStyle(
                    color: performer.tier.color,
                    fontSize: 10,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build level distribution chart
  Widget _buildLevelDistributionChart() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Level Distribution',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          SizedBox(
            height: 200,
            child: BarChart(
              BarChartData(
                alignment: BarChartAlignment.spaceAround,
                maxY: _statistics!.levelDistribution.values.isNotEmpty
                    ? _statistics!.levelDistribution.values.reduce((a, b) => a > b ? a : b).toDouble()
                    : 10,
                barTouchData: BarTouchData(enabled: false),
                titlesData: FlTitlesData(
                  show: true,
                  bottomTitles: AxisTitles(
                    sideTitles: SideTitles(
                      showTitles: true,
                      getTitlesWidget: (value, meta) {
                        return Text(
                          'L${value.toInt()}',
                          style: TextStyle(
                            color: AppTheme.darkSecondaryText,
                            fontSize: 12,
                          ),
                        );
                      },
                    ),
                  ),
                  leftTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  topTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                  rightTitles: AxisTitles(
                    sideTitles: SideTitles(showTitles: false),
                  ),
                ),
                borderData: FlBorderData(show: false),
                barGroups: _statistics!.levelDistribution.entries.map((entry) {
                  return BarChartGroupData(
                    x: entry.key,
                    barRods: [
                      BarChartRodData(
                        toY: entry.value.toDouble(),
                        gradient: AppTheme.primaryGradient,
                        width: 20,
                        borderRadius: BorderRadius.circular(4),
                      ),
                    ],
                  );
                }).toList(),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build tier distribution chart
  Widget _buildTierDistributionChart() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Tier Distribution',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          
          const SizedBox(height: 16),
          
          SizedBox(
            height: 200,
            child: PieChart(
              PieChartData(
                sections: _statistics!.tierDistribution.entries
                    .where((entry) => entry.value > 0)
                    .map((entry) {
                  final percentage = (entry.value / _statistics!.totalAgents * 100);
                  return PieChartSectionData(
                    value: entry.value.toDouble(),
                    title: '${percentage.toStringAsFixed(1)}%',
                    color: entry.key.color,
                    radius: 60,
                    titleStyle: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.bold,
                    ),
                  );
                }).toList(),
                centerSpaceRadius: 40,
                sectionsSpace: 2,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build performance metrics
  Widget _buildPerformanceMetrics() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Performance Metrics',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Detailed performance analytics will be available in the next update.',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Build growth trends
  Widget _buildGrowthTrends() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Growth Trends',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),
          Text(
            'Growth trend analysis will be available in the next update.',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
            ),
          ),
        ],
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            Icons.error_outline,
            size: 64,
            color: AppTheme.errorColor,
          ),
          const SizedBox(height: 16),
          Text(
            'Failed to load network statistics',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          GradientWidgets.gradientButton(
            text: 'Retry',
            onPressed: _loadStatistics,
            icon: Icons.refresh,
          ),
        ],
      ),
    );
  }
}
