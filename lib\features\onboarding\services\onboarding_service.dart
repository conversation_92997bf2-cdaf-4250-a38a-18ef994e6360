import 'dart:io';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'package:flutter/foundation.dart';

import '../../../core/models/user_model.dart';
import '../../../core/constants/app_constants.dart';
import '../models/onboarding_data.dart';

/// Service for handling agent onboarding process
class OnboardingService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;
  static final FirebaseAuth _auth = FirebaseAuth.instance;

  /// Verify if referral code exists and get owner details
  static Future<ReferralCodeResult> verifyReferralCode(
    String referralCode,
  ) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - simulate referral code verification
        await Future.delayed(const Duration(seconds: 1));

        if (referralCode.toUpperCase() == 'ADMIN001') {
          return ReferralCodeResult.success(
            ownerName: 'Admin User',
            ownerId: 'admin-001',
            level: 0,
          );
        } else if (referralCode.toUpperCase() == 'AGENT001') {
          return ReferralCodeResult.success(
            ownerName: 'Senior Agent',
            ownerId: 'agent-001',
            level: 1,
          );
        } else if (referralCode.toUpperCase().startsWith('TEST')) {
          return ReferralCodeResult.success(
            ownerName: 'Test Agent',
            ownerId: 'test-001',
            level: 2,
          );
        } else {
          return ReferralCodeResult.failure('Referral code not found');
        }
      }

      // Production mode - query Firestore
      final querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('referralCode', isEqualTo: referralCode.toUpperCase())
          .limit(1)
          .get();

      if (querySnapshot.docs.isEmpty) {
        return ReferralCodeResult.failure('Referral code not found');
      }

      final userData = querySnapshot.docs.first.data();
      return ReferralCodeResult.success(
        ownerName: userData['name'] ?? 'Unknown',
        ownerId: querySnapshot.docs.first.id,
        level: userData['level'] ?? 0,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error verifying referral code: $e');
      }
      return ReferralCodeResult.failure('Failed to verify referral code');
    }
  }

  /// Upload profile image to Firebase Storage
  static Future<String?> uploadProfileImage(
    File imageFile,
    String userId,
  ) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock URL
        await Future.delayed(const Duration(seconds: 2));
        return 'https://via.placeholder.com/150x150.png?text=Profile';
      }

      final ref = _storage.ref().child('profile_images').child('$userId.jpg');

      final uploadTask = ref.putFile(imageFile);
      final snapshot = await uploadTask;
      return await snapshot.ref.getDownloadURL();
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading profile image: $e');
      }
      return null;
    }
  }

  /// Complete agent onboarding and create user profile
  static Future<OnboardingResult> completeOnboarding({
    required String email,
    required OnboardingData data,
  }) async {
    try {
      final currentUser = _auth.currentUser;
      if (currentUser == null) {
        return OnboardingResult.failure('User not authenticated');
      }

      // Get referral code (assign admin code if empty)
      final assignedReferralCode = OnboardingValidator.getAssignedReferralCode(
        data.referralCode,
      );

      // Find upline user details
      String? uplineId;
      int level = 0;

      if (assignedReferralCode != OnboardingValidator.adminReferralCode) {
        final referralResult = await verifyReferralCode(assignedReferralCode);
        if (referralResult.isSuccess) {
          uplineId = referralResult.ownerId;
          level = (referralResult.level ?? 0) + 1;
        }
      }

      // Upload profile image if provided
      String? profileImageUrl = data.profileImageUrl;
      if (data.profileImage != null) {
        profileImageUrl = await uploadProfileImage(
          data.profileImage!,
          currentUser.uid,
        );
      }

      // Generate unique referral code for new agent
      final newReferralCode = await _generateUniqueReferralCode(data.name!);

      // Create user model
      final userModel = UserModel(
        id: currentUser.uid,
        email: email,
        name: data.name!.trim(),
        phoneNumber: OnboardingValidator.formatPhoneNumber(data.phoneNumber!),
        role: AppConstants.agentRole,
        profileImageUrl: profileImageUrl,
        uplineId: uplineId,
        level: level,
        isActive: true,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        additionalInfo: {
          'referralCode': newReferralCode,
          'uplineReferralCode': assignedReferralCode,
          'onboardingCompleted': true,
          'onboardingDate': DateTime.now().toIso8601String(),
        },
      );

      if (kIsWeb && kDebugMode) {
        // Development mode - simulate success
        await Future.delayed(const Duration(seconds: 2));
        if (kDebugMode) {
          print('Development onboarding completed for: ${userModel.name}');
          print('Assigned referral code: $newReferralCode');
          print('Upline referral code: $assignedReferralCode');
        }
        return OnboardingResult.success(userModel);
      }

      // Production mode - save to Firestore
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(currentUser.uid)
          .set(userModel.toFirestore());

      // Update upline's downline list if applicable
      if (uplineId != null) {
        await _addToUplineDownline(uplineId, currentUser.uid);
      }

      // Update Firebase Auth display name
      await currentUser.updateDisplayName(data.name!.trim());

      return OnboardingResult.success(userModel);
    } catch (e) {
      if (kDebugMode) {
        print('Error completing onboarding: $e');
      }
      return OnboardingResult.failure(
        'Failed to complete onboarding: ${e.toString()}',
      );
    }
  }

  /// Generate unique referral code based on name
  static Future<String> _generateUniqueReferralCode(String name) async {
    // Create base code from name
    final cleanName = name.replaceAll(RegExp(r'[^a-zA-Z]'), '').toUpperCase();
    final baseCode = cleanName.length >= 4
        ? cleanName.substring(0, 4)
        : cleanName.padRight(4, 'X');

    // Add random numbers
    final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
    final suffix = timestamp.substring(timestamp.length - 3);

    return '$baseCode$suffix';
  }

  /// Add new agent to upline's downline list
  static Future<void> _addToUplineDownline(
    String uplineId,
    String newAgentId,
  ) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - just log
        if (kDebugMode) {
          print('Adding $newAgentId to upline $uplineId downline list');
        }
        return;
      }

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(uplineId)
          .update({
            'downlineIds': FieldValue.arrayUnion([newAgentId]),
            'updatedAt': Timestamp.now(),
          });
    } catch (e) {
      if (kDebugMode) {
        print('Error updating upline downline: $e');
      }
    }
  }

  /// Check if user has completed onboarding
  static Future<bool> hasCompletedOnboarding(String userId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - check if user exists in our mock system
        return false; // Always show onboarding in development
      }

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (!userDoc.exists) return false;

      final userData = userDoc.data()!;
      return userData['additionalInfo']?['onboardingCompleted'] == true;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking onboarding status: $e');
      }
      return false;
    }
  }
}

/// Result of referral code verification
class ReferralCodeResult {
  final bool isSuccess;
  final String? ownerName;
  final String? ownerId;
  final int? level;
  final String? error;

  const ReferralCodeResult._({
    required this.isSuccess,
    this.ownerName,
    this.ownerId,
    this.level,
    this.error,
  });

  factory ReferralCodeResult.success({
    required String ownerName,
    required String ownerId,
    required int level,
  }) {
    return ReferralCodeResult._(
      isSuccess: true,
      ownerName: ownerName,
      ownerId: ownerId,
      level: level,
    );
  }

  factory ReferralCodeResult.failure(String error) {
    return ReferralCodeResult._(isSuccess: false, error: error);
  }
}

/// Result of onboarding completion
class OnboardingResult {
  final bool isSuccess;
  final UserModel? user;
  final String? error;

  const OnboardingResult._({required this.isSuccess, this.user, this.error});

  factory OnboardingResult.success(UserModel user) {
    return OnboardingResult._(isSuccess: true, user: user);
  }

  factory OnboardingResult.failure(String error) {
    return OnboardingResult._(isSuccess: false, error: error);
  }
}
