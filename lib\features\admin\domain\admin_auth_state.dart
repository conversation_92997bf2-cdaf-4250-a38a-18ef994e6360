/// Admin authentication states
sealed class AdminAuthState {
  const AdminAuthState();
}

/// Admin is not authenticated
class AdminUnauthenticated extends AdminAuthState {
  const AdminUnauthenticated();
}

/// Admin authentication in progress
class AdminLoading extends AdminAuthState {
  const AdminLoading();
}

/// Admin is authenticated
class AdminAuthenticated extends AdminAuthState {
  final AdminUser admin;
  
  const AdminAuthenticated(this.admin);
}

/// Admin authentication error
class AdminError extends AdminAuthState {
  final String message;
  
  const AdminError(this.message);
}

/// Admin user model
class AdminUser {
  final String id;
  final String email;
  final String name;
  final String role;
  final List<String> permissions;
  final DateTime lastLogin;
  
  const AdminUser({
    required this.id,
    required this.email,
    required this.name,
    required this.role,
    required this.permissions,
    required this.lastLogin,
  });
  
  factory AdminUser.fromMap(Map<String, dynamic> map) {
    return AdminUser(
      id: map['id'] ?? '',
      email: map['email'] ?? '',
      name: map['name'] ?? '',
      role: map['role'] ?? 'admin',
      permissions: List<String>.from(map['permissions'] ?? []),
      lastLogin: DateTime.parse(map['lastLogin'] ?? DateTime.now().toIso8601String()),
    );
  }
  
  Map<String, dynamic> toMap() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'role': role,
      'permissions': permissions,
      'lastLogin': lastLogin.toIso8601String(),
    };
  }
  
  bool hasPermission(String permission) {
    return permissions.contains(permission) || permissions.contains('all');
  }
}
