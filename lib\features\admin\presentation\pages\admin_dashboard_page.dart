import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/admin_providers.dart';
import '../providers/test_data_providers.dart';
import '../widgets/admin_analytics_widgets.dart';
import '../widgets/admin_management_widgets.dart';
import '../widgets/test_data_user_widget.dart';
import '../../../../core/models/admin_analytics_model.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../../shared/themes/app_theme.dart';
import 'referral_management_page.dart';
import 'test_data_page.dart';

class AdminDashboardPage extends ConsumerStatefulWidget {
  const AdminDashboardPage({super.key});

  @override
  ConsumerState<AdminDashboardPage> createState() => _AdminDashboardPageState();
}

class _AdminDashboardPageState extends ConsumerState<AdminDashboardPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 5, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final analyticsAsync = ref.watch(adminAnalyticsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Admin Dashboard'),
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.refresh),
            onPressed: () {
              ref.invalidate(adminAnalyticsProvider);
              ref.invalidate(testDataUsersProvider);
              ref.invalidate(testDataSummaryProvider);
              ref.invalidate(allUsersProvider);
              ref.invalidate(adminConfigProvider);
            },
            tooltip: 'Refresh Data',
          ),
          IconButton(
            icon: const Icon(Icons.settings),
            onPressed: () => _showSystemSettings(context),
            tooltip: 'System Settings',
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: const [
            Tab(icon: Icon(Icons.dashboard), text: 'Overview'),
            Tab(icon: Icon(Icons.analytics), text: 'Analytics'),
            Tab(icon: Icon(Icons.people), text: 'Users'),
            Tab(icon: Icon(Icons.data_usage), text: 'Test Data'),
            Tab(icon: Icon(Icons.settings), text: 'Config'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          _buildOverviewTab(analyticsAsync),
          _buildAnalyticsTab(analyticsAsync),
          _buildUsersTab(),
          _buildTestDataTab(),
          _buildConfigTab(),
        ],
      ),
    );
  }

  Widget _buildOverviewTab(AsyncValue<AdminAnalyticsModel> analyticsAsync) {
    return analyticsAsync.when(
      data: (analytics) => RefreshIndicator(
        onRefresh: () async {
          ref.invalidate(adminAnalyticsProvider);
        },
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Welcome header
              _buildWelcomeHeader(context),

              const SizedBox(height: 24),

              // System overview cards
              SystemOverviewWidget(overview: analytics.systemOverview),

              const SizedBox(height: 24),

              // Quick stats grid
              QuickStatsGrid(analytics: analytics),

              const SizedBox(height: 24),

              // Quick Actions
              _buildQuickActions(context),

              const SizedBox(height: 24),

              // Recent activities and alerts
              Row(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Expanded(child: RecentActivitiesWidget(analytics: analytics)),
                  const SizedBox(width: 16),
                  Expanded(child: SystemAlertsWidget(analytics: analytics)),
                ],
              ),
            ],
          ),
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading analytics: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(adminAnalyticsProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAnalyticsTab(AsyncValue<AdminAnalyticsModel> analyticsAsync) {
    return analyticsAsync.when(
      data: (analytics) => SingleChildScrollView(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Detailed Analytics',
              style: Theme.of(
                context,
              ).textTheme.headlineSmall?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 24),

            // User analytics
            UserAnalyticsWidget(userAnalytics: analytics.userAnalytics),

            const SizedBox(height: 24),

            // Property analytics
            _buildPropertyAnalyticsWidget(analytics.propertyAnalytics),

            const SizedBox(height: 24),

            // Commission analytics
            _buildCommissionAnalyticsWidget(analytics.commissionAnalytics),

            const SizedBox(height: 24),

            // Star analytics
            _buildStarAnalyticsWidget(analytics.starAnalytics),

            const SizedBox(height: 24),

            // Performance metrics
            _buildPerformanceMetricsWidget(analytics.performanceMetrics),
          ],
        ),
      ),
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => _buildAnalyticsErrorWidget(error.toString()),
    );
  }

  Widget _buildAnalyticsErrorWidget(String error) {
    final isTestDataLoaded = ref.watch(testDataStatusProvider);

    return Center(
      child: Padding(
        padding: const EdgeInsets.all(24),
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.analytics_outlined, size: 64, color: Colors.grey[600]),
            const SizedBox(height: 16),
            Text(
              'Analytics Unavailable',
              style: TextStyle(
                fontSize: 18,
                fontWeight: FontWeight.w600,
                color: Colors.grey[400],
              ),
            ),
            const SizedBox(height: 8),
            if (error.contains('admin privileges'))
              Column(
                children: [
                  Text(
                    'Admin analytics require special privileges.',
                    style: TextStyle(fontSize: 14, color: Colors.grey[600]),
                    textAlign: TextAlign.center,
                  ),
                  const SizedBox(height: 16),
                  isTestDataLoaded.when(
                    data: (isLoaded) => isLoaded
                        ? Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: Colors.green.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: Colors.green.withOpacity(0.3),
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(Icons.check_circle, color: Colors.green),
                                const SizedBox(height: 8),
                                Text(
                                  'Test data is loaded!\nCheck the Users tab to see the MLM hierarchy.',
                                  style: TextStyle(color: Colors.green),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          )
                        : Container(
                            padding: const EdgeInsets.all(16),
                            decoration: BoxDecoration(
                              color: AppTheme.warningColor.withOpacity(0.1),
                              borderRadius: BorderRadius.circular(8),
                              border: Border.all(
                                color: AppTheme.warningColor.withOpacity(0.3),
                              ),
                            ),
                            child: Column(
                              children: [
                                Icon(Icons.info, color: AppTheme.warningColor),
                                const SizedBox(height: 8),
                                Text(
                                  'Load test data using the floating action button to see sample analytics.',
                                  style: TextStyle(
                                    color: AppTheme.warningColor,
                                  ),
                                  textAlign: TextAlign.center,
                                ),
                              ],
                            ),
                          ),
                    loading: () => const CircularProgressIndicator(),
                    error: (_, __) => const SizedBox(),
                  ),
                ],
              )
            else
              Text(
                'Error: $error',
                style: TextStyle(fontSize: 12, color: Colors.red[400]),
                textAlign: TextAlign.center,
              ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersTab() {
    return const TestDataUserWidget();
  }

  Widget _buildTestDataTab() {
    return const TestDataPage();
  }

  Widget _buildConfigTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Test Data Management - Temporarily disabled
          // const TestDataWidget(),
          // const SizedBox(height: 24),

          // System Configuration
          const SystemConfigurationWidget(),
        ],
      ),
    );
  }

  Widget _buildWelcomeHeader(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          colors: [
            Theme.of(context).primaryColor.withValues(alpha: 0.1),
            Theme.of(context).primaryColor.withValues(alpha: 0.05),
          ],
          begin: Alignment.topLeft,
          end: Alignment.bottomRight,
        ),
        borderRadius: BorderRadius.circular(12),
      ),
      child: Row(
        children: [
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(12),
            ),
            child: Icon(
              Icons.admin_panel_settings,
              size: 32,
              color: Theme.of(context).primaryColor,
            ),
          ),

          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Admin Dashboard',
                  style: Theme.of(
                    context,
                  ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
                ),
                const SizedBox(height: 4),
                Text(
                  'Comprehensive system management and analytics',
                  style: Theme.of(
                    context,
                  ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
                ),
              ],
            ),
          ),

          Container(
            padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            decoration: BoxDecoration(
              color: Colors.green.withValues(alpha: 0.1),
              borderRadius: BorderRadius.circular(20),
            ),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              children: [
                Container(
                  width: 8,
                  height: 8,
                  decoration: const BoxDecoration(
                    color: Colors.green,
                    shape: BoxShape.circle,
                  ),
                ),
                const SizedBox(width: 6),
                const Text(
                  'System Online',
                  style: TextStyle(
                    color: Colors.green,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  void _showSystemSettings(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('System Settings'),
        content: const Text('Advanced system settings coming soon!'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  Widget _buildPropertyAnalyticsWidget(dynamic propertyAnalytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Property Analytics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Total Properties: ${propertyAnalytics?.totalProperties ?? 0}',
            ),
            Text('Approved: ${propertyAnalytics?.approvedProperties ?? 0}'),
            Text('Pending: ${propertyAnalytics?.pendingProperties ?? 0}'),
          ],
        ),
      ),
    );
  }

  Widget _buildCommissionAnalyticsWidget(dynamic commissionAnalytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Commission Analytics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Total Commissions: ₹${commissionAnalytics?.totalCommissions ?? 0}',
            ),
            Text('Paid: ₹${commissionAnalytics?.paidCommissions ?? 0}'),
            Text('Pending: ₹${commissionAnalytics?.pendingCommissions ?? 0}'),
          ],
        ),
      ),
    );
  }

  Widget _buildStarAnalyticsWidget(dynamic starAnalytics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Star Analytics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text('Total Stars: ${starAnalytics?.totalStars ?? 0}'),
            Text('Avg Stars/User: ${starAnalytics?.averageStarsPerUser ?? 0}'),
            Text(
              '12-Star Achievers: ${starAnalytics?.twelveStarAchievers ?? 0}',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildPerformanceMetricsWidget(dynamic performanceMetrics) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            const Text(
              'Performance Metrics',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Text(
              'Conversion Rate: ${performanceMetrics?.conversionRate ?? 0}%',
            ),
            Text('Response Time: ${performanceMetrics?.responseTime ?? 0}h'),
            Text(
              'System Efficiency: ${performanceMetrics?.systemEfficiency ?? 0}%',
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildQuickActions(BuildContext context) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(20),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Quick Actions',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            Wrap(
              spacing: 12,
              runSpacing: 12,
              children: [
                _buildActionButton(
                  context,
                  'Referral Management',
                  Icons.group_add,
                  AppTheme.primaryColor,
                  () {
                    Navigator.of(context).push(
                      MaterialPageRoute(
                        builder: (context) => const ReferralManagementPage(),
                      ),
                    );
                  },
                ),
                _buildActionButton(
                  context,
                  'User Management',
                  Icons.people,
                  AppTheme.secondaryColor,
                  () {
                    // Switch to users tab
                    _tabController.animateTo(2);
                  },
                ),
                _buildActionButton(
                  context,
                  'System Config',
                  Icons.settings,
                  AppTheme.warningColor,
                  () {
                    // Switch to config tab
                    _tabController.animateTo(4);
                  },
                ),
                _buildActionButton(
                  context,
                  'Analytics',
                  Icons.analytics,
                  AppTheme.infoColor,
                  () {
                    // Switch to analytics tab
                    _tabController.animateTo(1);
                  },
                ),
                _buildActionButton(
                  context,
                  'Test Data',
                  Icons.data_usage,
                  Colors.green,
                  () {
                    // Switch to test data tab
                    _tabController.animateTo(3);
                  },
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildActionButton(
    BuildContext context,
    String label,
    IconData icon,
    Color color,
    VoidCallback onPressed,
  ) {
    return GradientWidgets.gradientButton(
      text: label,
      onPressed: onPressed,
      icon: icon,
      gradient: LinearGradient(colors: [color, color.withValues(alpha: 0.8)]),
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
    );
  }
}
