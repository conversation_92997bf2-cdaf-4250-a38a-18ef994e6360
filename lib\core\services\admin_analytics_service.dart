import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/admin_analytics_model.dart';
import '../models/user_model.dart';
import '../models/property_model.dart';
import '../models/commission_model.dart';
import '../models/star_model.dart';
import '../models/lead_model.dart';
import '../constants/app_constants.dart';

/// Service for generating comprehensive admin analytics
class AdminAnalyticsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Generate comprehensive admin analytics
  static Future<AdminAnalyticsModel> generateAnalytics() async {
    try {
      // Fetch all required data in parallel
      final futures = await Future.wait([
        _generateSystemOverview(),
        _generateUserAnalytics(),
        _generatePropertyAnalytics(),
        _generateCommissionAnalytics(),
        _generateStarAnalytics(),
        _generatePerformanceMetrics(),
      ]);

      return AdminAnalyticsModel(
        systemOverview: futures[0] as SystemOverview,
        userAnalytics: futures[1] as UserAnalytics,
        propertyAnalytics: futures[2] as PropertyAnalytics,
        commissionAnalytics: futures[3] as CommissionAnalytics,
        starAnalytics: futures[4] as StarAnalytics,
        performanceMetrics: futures[5] as PerformanceMetrics,
        generatedAt: DateTime.now(),
      );
    } catch (e) {
      print('Error generating admin analytics: $e');
      rethrow;
    }
  }

  /// Generate system overview metrics
  static Future<SystemOverview> _generateSystemOverview() async {
    final now = DateTime.now();
    final startOfYear = DateTime(now.year, 1, 1);

    // Get basic counts
    final usersSnapshot = await _firestore.collection(AppConstants.usersCollection).get();
    final propertiesSnapshot = await _firestore.collection(AppConstants.propertiesCollection).get();
    final commissionsSnapshot = await _firestore.collection(AppConstants.commissionsCollection).get();
    final starsSnapshot = await _firestore.collection(AppConstants.starsCollection).get();
    final transactionsSnapshot = await _firestore.collection(AppConstants.transactionsCollection).get();

    final totalUsers = usersSnapshot.docs.length;
    final activeUsers = usersSnapshot.docs.where((doc) {
      final data = doc.data();
      return data['isActive'] == true;
    }).length;

    final totalProperties = propertiesSnapshot.docs.length;
    final approvedProperties = propertiesSnapshot.docs.where((doc) {
      final data = doc.data();
      return data['isApproved'] == true;
    }).length;

    final totalCommissionsPaid = commissionsSnapshot.docs.fold<double>(0.0, (sum, doc) {
      final data = doc.data();
      return sum + ((data['amount'] ?? 0.0) as num).toDouble();
    });

    final totalStarsAwarded = starsSnapshot.docs.length;
    final totalTransactions = transactionsSnapshot.docs.length;

    // Calculate system revenue (total property values sold)
    final systemRevenue = transactionsSnapshot.docs.fold<double>(0.0, (sum, doc) {
      final data = doc.data();
      return sum + ((data['propertyAmount'] ?? 0.0) as num).toDouble();
    });

    // Generate growth trends (last 12 months)
    final userGrowthTrend = await _generateMonthlyUserGrowth(startOfYear);
    final revenueGrowthTrend = await _generateMonthlyRevenueGrowth(startOfYear);

    return SystemOverview(
      totalUsers: totalUsers,
      activeUsers: activeUsers,
      totalProperties: totalProperties,
      approvedProperties: approvedProperties,
      totalCommissionsPaid: totalCommissionsPaid,
      totalStarsAwarded: totalStarsAwarded,
      totalTransactions: totalTransactions,
      systemRevenue: systemRevenue,
      userGrowthTrend: userGrowthTrend,
      revenueGrowthTrend: revenueGrowthTrend,
    );
  }

  /// Generate user analytics
  static Future<UserAnalytics> _generateUserAnalytics() async {
    final usersSnapshot = await _firestore.collection(AppConstants.usersCollection).get();
    final users = usersSnapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();

    // User distribution by MLM level
    final usersByLevel = <int, int>{};
    for (final user in users) {
      usersByLevel[user.level] = (usersByLevel[user.level] ?? 0) + 1;
    }

    // Geographic distribution (placeholder - would need state field in user model)
    final usersByState = <String, int>{
      'Maharashtra': users.length ~/ 4,
      'Delhi': users.length ~/ 5,
      'Karnataka': users.length ~/ 6,
      'Tamil Nadu': users.length ~/ 7,
      'Others': users.length - (users.length ~/ 4 + users.length ~/ 5 + users.length ~/ 6 + users.length ~/ 7),
    };

    // Registration trend (last 12 months)
    final userRegistrationTrend = await _generateMonthlyUserRegistrations();

    // Top performers
    final topPerformers = users
        .where((user) => !user.isAdmin)
        .map((user) => TopPerformer(
              userId: user.id,
              name: user.name,
              totalStars: user.totalStars,
              totalCommissions: user.totalCommissions,
              networkSize: user.downlineIds.length,
              performanceScore: _calculatePerformanceScore(user),
              profileImageUrl: user.profileImageUrl ?? '',
            ))
        .toList()
      ..sort((a, b) => b.performanceScore.compareTo(a.performanceScore));

    final now = DateTime.now();
    final startOfMonth = DateTime(now.year, now.month, 1);

    final activeThisMonth = users.where((user) => 
        user.updatedAt.isAfter(startOfMonth) && user.isActive).length;
    
    final newRegistrationsThisMonth = users.where((user) => 
        user.createdAt.isAfter(startOfMonth)).length;

    final averageStarsPerUser = users.isNotEmpty 
        ? users.map((u) => u.totalStars).reduce((a, b) => a + b) / users.length 
        : 0.0;

    final averageCommissionPerUser = users.isNotEmpty 
        ? users.map((u) => u.totalCommissions).reduce((a, b) => a + b) / users.length 
        : 0.0;

    return UserAnalytics(
      usersByLevel: usersByLevel,
      usersByState: usersByState,
      userRegistrationTrend: userRegistrationTrend,
      topPerformers: topPerformers.take(10).toList(),
      activeThisMonth: activeThisMonth,
      newRegistrationsThisMonth: newRegistrationsThisMonth,
      averageStarsPerUser: averageStarsPerUser,
      averageCommissionPerUser: averageCommissionPerUser,
    );
  }

  /// Generate property analytics
  static Future<PropertyAnalytics> _generatePropertyAnalytics() async {
    final propertiesSnapshot = await _firestore.collection(AppConstants.propertiesCollection).get();
    final properties = propertiesSnapshot.docs.map((doc) => PropertyModel.fromFirestore(doc)).toList();

    // Properties by type
    final propertiesByType = <String, int>{};
    for (final property in properties) {
      propertiesByType[property.type] = (propertiesByType[property.type] ?? 0) + 1;
    }

    // Properties by state
    final propertiesByState = <String, int>{};
    for (final property in properties) {
      propertiesByState[property.state] = (propertiesByState[property.state] ?? 0) + 1;
    }

    // Properties by status
    final propertiesByStatus = <String, int>{};
    for (final property in properties) {
      propertiesByStatus[property.status] = (propertiesByStatus[property.status] ?? 0) + 1;
    }

    // Average price by type
    final averagePriceByType = <String, double>{};
    final pricesByType = <String, List<double>>{};
    
    for (final property in properties) {
      pricesByType.putIfAbsent(property.type, () => []).add(property.price);
    }
    
    for (final entry in pricesByType.entries) {
      averagePriceByType[entry.key] = entry.value.reduce((a, b) => a + b) / entry.value.length;
    }

    // Property addition trend
    final propertyAdditionTrend = await _generateMonthlyPropertyAdditions();

    final pendingApprovals = properties.where((p) => !p.isApproved).length;
    final featuredProperties = properties.where((p) => p.isFeatured).length;
    final averagePropertyPrice = properties.isNotEmpty 
        ? properties.map((p) => p.price).reduce((a, b) => a + b) / properties.length 
        : 0.0;

    // Get properties with leads
    final leadsSnapshot = await _firestore.collection(AppConstants.leadsCollection).get();
    final propertyIdsWithLeads = leadsSnapshot.docs
        .map((doc) => doc.data()['propertyId'] as String)
        .toSet();
    final propertiesWithLeads = propertyIdsWithLeads.length;

    return PropertyAnalytics(
      propertiesByType: propertiesByType,
      propertiesByState: propertiesByState,
      propertiesByStatus: propertiesByStatus,
      averagePriceByType: averagePriceByType,
      propertyAdditionTrend: propertyAdditionTrend,
      pendingApprovals: pendingApprovals,
      featuredProperties: featuredProperties,
      averagePropertyPrice: averagePropertyPrice,
      propertiesWithLeads: propertiesWithLeads,
    );
  }

  /// Generate commission analytics
  static Future<CommissionAnalytics> _generateCommissionAnalytics() async {
    final commissionsSnapshot = await _firestore.collection(AppConstants.commissionsCollection).get();
    final commissions = commissionsSnapshot.docs.map((doc) => CommissionModel.fromFirestore(doc)).toList();

    // Commissions by month
    final commissionsByMonth = await _generateMonthlyCommissions();

    // Commissions by MLM level
    final commissionsByLevel = <int, double>{};
    for (final commission in commissions) {
      commissionsByLevel[commission.level] = 
          (commissionsByLevel[commission.level] ?? 0.0) + commission.amount;
    }

    // Top earning agents
    final commissionsByAgent = <String, double>{};
    for (final commission in commissions) {
      commissionsByAgent[commission.agentName] = 
          (commissionsByAgent[commission.agentName] ?? 0.0) + commission.amount;
    }

    final totalPaidCommissions = commissions
        .where((c) => c.isPaid)
        .fold<double>(0.0, (sum, c) => sum + c.amount);

    final totalPendingCommissions = commissions
        .where((c) => !c.isPaid)
        .fold<double>(0.0, (sum, c) => sum + c.amount);

    final transactionsSnapshot = await _firestore.collection(AppConstants.transactionsCollection).get();
    final totalTransactions = transactionsSnapshot.docs.length;

    final averageCommissionPerTransaction = totalTransactions > 0 
        ? (totalPaidCommissions + totalPendingCommissions) / totalTransactions 
        : 0.0;

    final transactionTrend = await _generateMonthlyTransactions();

    return CommissionAnalytics(
      commissionsByMonth: commissionsByMonth,
      commissionsByLevel: commissionsByLevel,
      commissionsByAgent: commissionsByAgent,
      totalPaidCommissions: totalPaidCommissions,
      totalPendingCommissions: totalPendingCommissions,
      averageCommissionPerTransaction: averageCommissionPerTransaction,
      totalTransactions: totalTransactions,
      transactionTrend: transactionTrend,
    );
  }

  /// Generate star analytics
  static Future<StarAnalytics> _generateStarAnalytics() async {
    final starsSnapshot = await _firestore.collection(AppConstants.starsCollection).get();
    final stars = starsSnapshot.docs.map((doc) => StarModel.fromFirestore(doc)).toList();

    // Stars by month
    final starsByMonth = await _generateMonthlyStars();

    // Star distribution
    final usersSnapshot = await _firestore.collection(AppConstants.usersCollection).get();
    final users = usersSnapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();

    final starDistribution = <int, int>{
      0: 0, // 0 stars
      1: 0, // 1-5 stars
      6: 0, // 6-11 stars
      12: 0, // 12+ stars
    };

    for (final user in users) {
      if (user.totalStars == 0) {
        starDistribution[0] = starDistribution[0]! + 1;
      } else if (user.totalStars <= 5) {
        starDistribution[1] = starDistribution[1]! + 1;
      } else if (user.totalStars <= 11) {
        starDistribution[6] = starDistribution[6]! + 1;
      } else {
        starDistribution[12] = starDistribution[12]! + 1;
      }
    }

    // Top star earners
    final topStarEarners = users
        .where((user) => !user.isAdmin && user.totalStars > 0)
        .map((user) => StarAchiever(
              userId: user.id,
              name: user.name,
              totalStars: user.totalStars,
              starsThisMonth: 0, // Would need to calculate from star records
              lastStarEarned: user.updatedAt, // Approximation
              profileImageUrl: user.profileImageUrl ?? '',
            ))
        .toList()
      ..sort((a, b) => b.totalStars.compareTo(a.totalStars));

    final usersNearingBonus = users.where((user) => 
        user.totalStars >= 10 && user.totalStars < 12).length;

    // Get bonus information
    final bonusesSnapshot = await _firestore.collection(AppConstants.starBonusesCollection).get();
    final bonusesAwarded = bonusesSnapshot.docs.length;
    final totalBonusAmount = bonusesSnapshot.docs.fold<double>(0.0, (sum, doc) {
      final data = doc.data();
      return sum + ((data['amount'] ?? 0.0) as num).toDouble();
    });

    // Star source distribution
    final starSourceDistribution = <String, int>{
      'direct_sale': stars.where((s) => s.source == 'direct_sale').length,
      'upline_bonus': stars.where((s) => s.source == 'upline_bonus').length,
    };

    return StarAnalytics(
      starsByMonth: starsByMonth,
      starDistribution: starDistribution,
      topStarEarners: topStarEarners.take(10).toList(),
      usersNearingBonus: usersNearingBonus,
      bonusesAwarded: bonusesAwarded,
      totalBonusAmount: totalBonusAmount,
      starSourceDistribution: starSourceDistribution,
    );
  }

  /// Generate performance metrics
  static Future<PerformanceMetrics> _generatePerformanceMetrics() async {
    final leadsSnapshot = await _firestore.collection(AppConstants.leadsCollection).get();
    final leads = leadsSnapshot.docs.map((doc) => LeadModel.fromFirestore(doc)).toList();

    final activeLeads = leads.where((lead) => 
        lead.status != 'converted' && lead.status != 'lost').length;
    
    final convertedLeads = leads.where((lead) => lead.status == 'converted').length;
    
    final conversionRate = leads.isNotEmpty ? (convertedLeads / leads.length) * 100 : 0.0;

    // Calculate average response time (placeholder)
    final averageResponseTime = 2.5; // Hours

    // Customer satisfaction score (placeholder)
    final customerSatisfactionScore = 4.2; // Out of 5

    // Performance trends (placeholder)
    final performanceTrends = <String, double>{
      'conversion_rate': conversionRate,
      'response_time': averageResponseTime,
      'satisfaction': customerSatisfactionScore,
    };

    // System efficiency score (composite metric)
    final systemEfficiency = (conversionRate + (5 - averageResponseTime) * 20 + customerSatisfactionScore * 20) / 3;

    return PerformanceMetrics(
      conversionRate: conversionRate,
      averageResponseTime: averageResponseTime,
      customerSatisfactionScore: customerSatisfactionScore,
      activeLeads: activeLeads,
      convertedLeads: convertedLeads,
      performanceTrends: performanceTrends,
      systemEfficiency: systemEfficiency.clamp(0.0, 100.0),
    );
  }

  /// Helper methods for generating trends
  static Future<Map<String, int>> _generateMonthlyUserGrowth(DateTime startDate) async {
    // Implementation would query users by creation date and group by month
    // Placeholder implementation
    final now = DateTime.now();
    final trend = <String, int>{};
    
    for (int i = 11; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      trend[monthKey] = 10 + (i * 2); // Placeholder growth
    }
    
    return trend;
  }

  static Future<Map<String, double>> _generateMonthlyRevenueGrowth(DateTime startDate) async {
    // Similar implementation for revenue trends
    final now = DateTime.now();
    final trend = <String, double>{};
    
    for (int i = 11; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      trend[monthKey] = 1000000.0 + (i * 200000.0); // Placeholder revenue
    }
    
    return trend;
  }

  static Future<Map<String, int>> _generateMonthlyUserRegistrations() async {
    // Placeholder implementation
    return _generateMonthlyUserGrowth(DateTime.now().subtract(const Duration(days: 365)));
  }

  static Future<Map<String, int>> _generateMonthlyPropertyAdditions() async {
    // Placeholder implementation
    final now = DateTime.now();
    final trend = <String, int>{};
    
    for (int i = 11; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      trend[monthKey] = 5 + i; // Placeholder property additions
    }
    
    return trend;
  }

  static Future<Map<String, double>> _generateMonthlyCommissions() async {
    // Placeholder implementation
    final now = DateTime.now();
    final trend = <String, double>{};
    
    for (int i = 11; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      trend[monthKey] = 50000.0 + (i * 10000.0); // Placeholder commissions
    }
    
    return trend;
  }

  static Future<Map<String, int>> _generateMonthlyTransactions() async {
    // Placeholder implementation
    final now = DateTime.now();
    final trend = <String, int>{};
    
    for (int i = 11; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      trend[monthKey] = 3 + i; // Placeholder transactions
    }
    
    return trend;
  }

  static Future<Map<String, int>> _generateMonthlyStars() async {
    // Placeholder implementation
    final now = DateTime.now();
    final trend = <String, int>{};
    
    for (int i = 11; i >= 0; i--) {
      final month = DateTime(now.year, now.month - i, 1);
      final monthKey = '${month.year}-${month.month.toString().padLeft(2, '0')}';
      trend[monthKey] = 15 + (i * 3); // Placeholder stars
    }
    
    return trend;
  }

  /// Calculate performance score for a user
  static double _calculatePerformanceScore(UserModel user) {
    double score = 0.0;
    
    // Stars contribute 40% of score
    score += (user.totalStars * 3.33).clamp(0.0, 40.0);
    
    // Commissions contribute 30% of score
    score += (user.totalCommissions / 10000).clamp(0.0, 30.0);
    
    // Network size contributes 20% of score
    score += (user.downlineIds.length * 2.0).clamp(0.0, 20.0);
    
    // Activity contributes 10% of score
    final daysSinceUpdate = DateTime.now().difference(user.updatedAt).inDays;
    if (daysSinceUpdate <= 7) {
      score += 10.0;
    } else if (daysSinceUpdate <= 30) {
      score += 5.0;
    }
    
    return score.clamp(0.0, 100.0);
  }

  /// Get admin configuration
  static Future<AdminConfigModel?> getAdminConfig() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.adminConfigCollection)
          .limit(1)
          .get();

      if (snapshot.docs.isNotEmpty) {
        return AdminConfigModel.fromFirestore(snapshot.docs.first);
      }
      return null;
    } catch (e) {
      print('Error getting admin config: $e');
      return null;
    }
  }

  /// Update admin configuration
  static Future<bool> updateAdminConfig(AdminConfigModel config) async {
    try {
      await _firestore
          .collection(AppConstants.adminConfigCollection)
          .doc(config.id)
          .set(config.toFirestore());
      return true;
    } catch (e) {
      print('Error updating admin config: $e');
      return false;
    }
  }
}
