import 'dart:convert';
import 'dart:typed_data';
import 'package:crypto/crypto.dart';
import 'package:flutter/foundation.dart';
import 'package:http/http.dart' as http;

/// Cloudinary service for free image storage
class CloudinaryService {
  // Cloudinary credentials for Rama Realty MLM
  static const String cloudName = 'dvojmiyo7';
  static const String apiKey = '868174296632956';
  static const String apiSecret = '4TDqfAQNu6hjn02nxEcaMnBVEzY';

  static const String uploadUrl =
      'https://api.cloudinary.com/v1_1/$cloudName/image/upload';
  static const String baseUrl =
      'https://res.cloudinary.com/$cloudName/image/upload';

  /// Upload image to Cloudinary
  static Future<String?> uploadImage({
    required Uint8List imageBytes,
    required String fileName,
    String? folder,
    Map<String, String>? tags,
  }) async {
    try {
      if (kDebugMode) {
        print('🔄 CloudinaryService: Starting image upload');
        print('📁 CloudinaryService: Folder: ${folder ?? 'root'}');
        print('📄 CloudinaryService: File name: $fileName');
        print('📊 CloudinaryService: File size: ${imageBytes.length} bytes');
      }
      // Generate timestamp
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();

      // Create public_id (filename without extension)
      final publicId = folder != null
          ? '$folder/${fileName.split('.').first}_$timestamp'
          : '${fileName.split('.').first}_$timestamp';

      // Create signature
      final signature = _generateSignature(publicId, timestamp);

      // Prepare form data
      final request = http.MultipartRequest('POST', Uri.parse(uploadUrl));

      // Add file
      request.files.add(
        http.MultipartFile.fromBytes('file', imageBytes, filename: fileName),
      );

      // Add parameters
      request.fields.addAll({
        'api_key': apiKey,
        'timestamp': timestamp,
        'public_id': publicId,
        'signature': signature,
        'folder': folder ?? '',
        'resource_type': 'image',
        'quality': 'auto:good',
        'fetch_format': 'auto',
      });

      // Add tags if provided
      if (tags != null && tags.isNotEmpty) {
        request.fields['tags'] = tags.values.join(',');
      }

      // Send request
      if (kDebugMode) {
        print('🚀 CloudinaryService: Sending upload request...');
      }

      final response = await request.send();
      final responseBody = await response.stream.bytesToString();

      if (kDebugMode) {
        print('📡 CloudinaryService: Response status: ${response.statusCode}');
        print('📄 CloudinaryService: Response body: $responseBody');
      }

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(responseBody);
        final secureUrl = jsonResponse['secure_url'] as String;

        if (kDebugMode) {
          print('✅ CloudinaryService: Image uploaded successfully: $secureUrl');
        }

        return secureUrl;
      } else {
        if (kDebugMode) {
          print(
            '❌ CloudinaryService: Upload failed: ${response.statusCode} - $responseBody',
          );
        }
        return null;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading to Cloudinary: $e');
      }
      return null;
    }
  }

  /// Generate signature for Cloudinary API
  static String _generateSignature(String publicId, String timestamp) {
    final params = 'public_id=$publicId&timestamp=$timestamp$apiSecret';
    final bytes = utf8.encode(params);
    final digest = sha1.convert(bytes);
    return digest.toString();
  }

  /// Get optimized image URL with transformations
  static String getOptimizedImageUrl(
    String imageUrl, {
    int? width,
    int? height,
    String quality = 'auto:good',
    String format = 'auto',
    String crop = 'fill',
  }) {
    if (!imageUrl.contains('cloudinary.com')) {
      return imageUrl; // Return original if not a Cloudinary URL
    }

    // Extract public_id from URL
    final uri = Uri.parse(imageUrl);
    final pathSegments = uri.pathSegments;
    final uploadIndex = pathSegments.indexOf('upload');

    if (uploadIndex == -1 || uploadIndex >= pathSegments.length - 1) {
      return imageUrl; // Return original if can't parse
    }

    final publicId = pathSegments.sublist(uploadIndex + 1).join('/');

    // Build transformation string
    final transformations = <String>[];

    if (width != null || height != null) {
      final dimensions = <String>[];
      if (width != null) dimensions.add('w_$width');
      if (height != null) dimensions.add('h_$height');
      dimensions.add('c_$crop');
      transformations.add(dimensions.join(','));
    }

    transformations.addAll(['q_$quality', 'f_$format']);

    final transformationString = transformations.join('/');

    return '$baseUrl/$transformationString/$publicId';
  }

  /// Delete image from Cloudinary
  static Future<bool> deleteImage(String imageUrl) async {
    try {
      // Extract public_id from URL
      final uri = Uri.parse(imageUrl);
      final pathSegments = uri.pathSegments;
      final uploadIndex = pathSegments.indexOf('upload');

      if (uploadIndex == -1 || uploadIndex >= pathSegments.length - 1) {
        return false;
      }

      final publicId = pathSegments.sublist(uploadIndex + 1).join('/');
      final publicIdWithoutExtension = publicId.split('.').first;

      // Generate timestamp and signature
      final timestamp = DateTime.now().millisecondsSinceEpoch.toString();
      final signature = _generateDeleteSignature(
        publicIdWithoutExtension,
        timestamp,
      );

      // Prepare request
      final response = await http.post(
        Uri.parse('https://api.cloudinary.com/v1_1/$cloudName/image/destroy'),
        body: {
          'api_key': apiKey,
          'timestamp': timestamp,
          'public_id': publicIdWithoutExtension,
          'signature': signature,
        },
      );

      if (response.statusCode == 200) {
        final jsonResponse = json.decode(response.body);
        return jsonResponse['result'] == 'ok';
      }

      return false;
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting from Cloudinary: $e');
      }
      return false;
    }
  }

  /// Generate signature for delete operation
  static String _generateDeleteSignature(String publicId, String timestamp) {
    final params = 'public_id=$publicId&timestamp=$timestamp$apiSecret';
    final bytes = utf8.encode(params);
    final digest = sha1.convert(bytes);
    return digest.toString();
  }

  /// Upload multiple images
  static Future<List<String>> uploadMultipleImages({
    required List<Uint8List> imageBytesList,
    required List<String> fileNames,
    String? folder,
    Map<String, String>? tags,
  }) async {
    final uploadedUrls = <String>[];

    for (int i = 0; i < imageBytesList.length; i++) {
      final url = await uploadImage(
        imageBytes: imageBytesList[i],
        fileName: fileNames.length > i ? fileNames[i] : 'image_$i.jpg',
        folder: folder,
        tags: tags,
      );

      if (url != null) {
        uploadedUrls.add(url);
      }
    }

    return uploadedUrls;
  }

  /// Check if service is configured
  static bool get isConfigured {
    return cloudName != 'your-cloud-name' &&
        apiKey != 'your-api-key' &&
        apiSecret != 'your-api-secret';
  }

  /// Get thumbnail URL
  static String getThumbnailUrl(String imageUrl, {int size = 150}) {
    return getOptimizedImageUrl(
      imageUrl,
      width: size,
      height: size,
      crop: 'thumb',
      quality: 'auto:low',
    );
  }

  /// Get medium size URL
  static String getMediumUrl(String imageUrl) {
    return getOptimizedImageUrl(
      imageUrl,
      width: 800,
      height: 600,
      quality: 'auto:good',
    );
  }
}
