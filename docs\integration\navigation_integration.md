# Navigation Integration Guide

## Overview
This guide explains how to integrate the enhanced MLM features into your main app navigation structure.

## Main Navigation Structure

```dart
// lib/core/navigation/app_router.dart
class AppRouter {
  static const String dashboard = '/dashboard';
  static const String properties = '/properties';
  static const String commissions = '/commissions';
  static const String network = '/network';
  static const String reports = '/reports';
  static const String goals = '/goals';
  static const String profile = '/profile';
}

// Navigation Items Configuration
final List<NavigationItem> navigationItems = [
  NavigationItem(
    route: AppRouter.dashboard,
    icon: Icons.dashboard,
    label: 'Dashboard',
    widget: EnhancedDashboardOverview(agentId: currentUser.id),
  ),
  NavigationItem(
    route: AppRouter.properties,
    icon: Icons.home,
    label: 'Properties',
    widget: EnhancedPropertyListView(),
  ),
  NavigationItem(
    route: AppRouter.commissions,
    icon: Icons.account_balance_wallet,
    label: 'Commissions',
    widget: EnhancedCommissionDashboard(agentId: currentUser.id),
  ),
  NavigationItem(
    route: AppRouter.network,
    icon: Icons.account_tree,
    label: 'Network',
    widget: NetworkVisualizationPage(agentId: currentUser.id),
  ),
  NavigationItem(
    route: AppRouter.reports,
    icon: Icons.analytics,
    label: 'Reports',
    widget: ReportDashboardWidget(agentId: currentUser.id),
  ),
];
```

## Bottom Navigation Integration

```dart
// lib/shared/widgets/main_navigation.dart
class MainNavigationWidget extends StatefulWidget {
  final String currentUserId;
  
  const MainNavigationWidget({
    super.key,
    required this.currentUserId,
  });
}

class _MainNavigationWidgetState extends State<MainNavigationWidget> {
  int _currentIndex = 0;
  
  late final List<Widget> _pages;
  
  @override
  void initState() {
    super.initState();
    _pages = [
      EnhancedDashboardOverview(agentId: widget.currentUserId),
      EnhancedPropertyListView(),
      EnhancedCommissionDashboard(agentId: widget.currentUserId),
      NetworkVisualizationPage(agentId: widget.currentUserId),
      ReportDashboardWidget(agentId: widget.currentUserId),
    ];
  }
  
  @override
  Widget build(BuildContext context) {
    return Scaffold(
      body: IndexedStack(
        index: _currentIndex,
        children: _pages,
      ),
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        type: BottomNavigationBarType.fixed,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.home),
            label: 'Properties',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_balance_wallet),
            label: 'Commissions',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_tree),
            label: 'Network',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Reports',
          ),
        ],
      ),
    );
  }
}
```

## Drawer Navigation Integration

```dart
// lib/shared/widgets/app_drawer.dart
class AppDrawer extends StatelessWidget {
  final String currentUserId;
  
  const AppDrawer({
    super.key,
    required this.currentUserId,
  });
  
  @override
  Widget build(BuildContext context) {
    return Drawer(
      child: Column(
        children: [
          // User Header
          UserAccountsDrawerHeader(
            accountName: Text(currentUser.name),
            accountEmail: Text(currentUser.email),
            currentAccountPicture: CircleAvatar(
              backgroundImage: currentUser.profileImageUrl != null
                  ? NetworkImage(currentUser.profileImageUrl!)
                  : null,
              child: currentUser.profileImageUrl == null
                  ? Text(currentUser.initials)
                  : null,
            ),
          ),
          
          // Navigation Items
          _buildDrawerItem(
            icon: Icons.dashboard,
            title: 'Dashboard',
            onTap: () => _navigateTo(context, AppRouter.dashboard),
          ),
          _buildDrawerItem(
            icon: Icons.home,
            title: 'Properties',
            onTap: () => _navigateTo(context, AppRouter.properties),
          ),
          _buildDrawerItem(
            icon: Icons.account_balance_wallet,
            title: 'Commissions',
            onTap: () => _navigateTo(context, AppRouter.commissions),
          ),
          _buildDrawerItem(
            icon: Icons.account_tree,
            title: 'My Network',
            onTap: () => _navigateTo(context, AppRouter.network),
          ),
          _buildDrawerItem(
            icon: Icons.analytics,
            title: 'Reports',
            onTap: () => _navigateTo(context, AppRouter.reports),
          ),
          _buildDrawerItem(
            icon: Icons.flag,
            title: 'Goals',
            onTap: () => _navigateTo(context, AppRouter.goals),
          ),
          
          const Divider(),
          
          // Settings and Logout
          _buildDrawerItem(
            icon: Icons.settings,
            title: 'Settings',
            onTap: () => _navigateTo(context, '/settings'),
          ),
          _buildDrawerItem(
            icon: Icons.logout,
            title: 'Logout',
            onTap: () => _handleLogout(context),
          ),
        ],
      ),
    );
  }
  
  Widget _buildDrawerItem({
    required IconData icon,
    required String title,
    required VoidCallback onTap,
  }) {
    return ListTile(
      leading: Icon(icon),
      title: Text(title),
      onTap: onTap,
    );
  }
  
  void _navigateTo(BuildContext context, String route) {
    Navigator.of(context).pop();
    Navigator.of(context).pushNamed(route);
  }
  
  void _handleLogout(BuildContext context) {
    // Implement logout logic
  }
}
```

## Route Configuration

```dart
// lib/core/navigation/route_generator.dart
class RouteGenerator {
  static Route<dynamic> generateRoute(RouteSettings settings) {
    switch (settings.name) {
      case AppRouter.dashboard:
        return MaterialPageRoute(
          builder: (_) => EnhancedDashboardOverview(
            agentId: AuthService.currentUser?.id ?? '',
          ),
        );
        
      case AppRouter.properties:
        return MaterialPageRoute(
          builder: (_) => const EnhancedPropertyListView(),
        );
        
      case AppRouter.commissions:
        return MaterialPageRoute(
          builder: (_) => EnhancedCommissionDashboard(
            agentId: AuthService.currentUser?.id ?? '',
          ),
        );
        
      case AppRouter.network:
        return MaterialPageRoute(
          builder: (_) => NetworkVisualizationPage(
            agentId: AuthService.currentUser?.id ?? '',
          ),
        );
        
      case AppRouter.reports:
        return MaterialPageRoute(
          builder: (_) => ReportDashboardWidget(
            agentId: AuthService.currentUser?.id ?? '',
          ),
        );
        
      default:
        return MaterialPageRoute(
          builder: (_) => const NotFoundPage(),
        );
    }
  }
}
```

## Deep Linking Integration

```dart
// lib/core/navigation/deep_link_handler.dart
class DeepLinkHandler {
  static void handleDeepLink(String link) {
    final uri = Uri.parse(link);
    
    switch (uri.pathSegments.first) {
      case 'property':
        if (uri.pathSegments.length > 1) {
          final propertyId = uri.pathSegments[1];
          NavigationService.navigateToProperty(propertyId);
        }
        break;
        
      case 'commission':
        if (uri.pathSegments.length > 1) {
          final commissionId = uri.pathSegments[1];
          NavigationService.navigateToCommission(commissionId);
        }
        break;
        
      case 'network':
        if (uri.pathSegments.length > 1) {
          final agentId = uri.pathSegments[1];
          NavigationService.navigateToNetworkNode(agentId);
        }
        break;
        
      case 'report':
        if (uri.pathSegments.length > 1) {
          final reportId = uri.pathSegments[1];
          NavigationService.navigateToReport(reportId);
        }
        break;
    }
  }
}
```

## Permission-Based Navigation

```dart
// lib/core/navigation/permission_guard.dart
class PermissionGuard {
  static bool canAccessRoute(String route, UserModel user) {
    switch (route) {
      case AppRouter.dashboard:
        return true; // All users can access dashboard
        
      case AppRouter.properties:
        return user.hasPermission('view_properties');
        
      case AppRouter.commissions:
        return user.hasPermission('view_commissions');
        
      case AppRouter.network:
        return user.hasPermission('view_network');
        
      case AppRouter.reports:
        return user.hasPermission('generate_reports');
        
      default:
        return false;
    }
  }
  
  static List<NavigationItem> getAccessibleNavigation(UserModel user) {
    return navigationItems.where((item) => 
      canAccessRoute(item.route, user)
    ).toList();
  }
}
```
