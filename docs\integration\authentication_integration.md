# Authentication Integration Guide

## Overview
This guide explains how to integrate the MLM features with Firebase Authentication and implement role-based access control.

## Authentication Service

```dart
// lib/core/services/auth_service.dart
class AuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  
  static User? get currentFirebaseUser => _auth.currentUser;
  static UserModel? _currentUser;
  static UserModel? get currentUser => _currentUser;

  // Initialize authentication
  static Future<void> initialize() async {
    _auth.authStateChanges().listen(_onAuthStateChanged);
    
    // Check if user is already signed in
    if (_auth.currentUser != null) {
      await _loadUserData(_auth.currentUser!.uid);
    }
  }

  // Sign in with email and password
  static Future<AuthResult> signInWithEmailAndPassword(String email, String password) async {
    try {
      final credential = await _auth.signInWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        await _loadUserData(credential.user!.uid);
        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Sign in failed');
      }
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  // Sign up with email and password
  static Future<AuthResult> signUpWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    String? referralCode,
  }) async {
    try {
      final credential = await _auth.createUserWithEmailAndPassword(
        email: email,
        password: password,
      );

      if (credential.user != null) {
        // Create user profile
        await _createUserProfile(
          uid: credential.user!.uid,
          email: email,
          name: name,
          phoneNumber: phoneNumber,
          referralCode: referralCode,
        );

        await _loadUserData(credential.user!.uid);
        return AuthResult.success(_currentUser!);
      } else {
        return AuthResult.failure('Sign up failed');
      }
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  // Sign out
  static Future<void> signOut() async {
    await _auth.signOut();
    _currentUser = null;
    SyncService.stopAllSync();
  }

  // Reset password
  static Future<AuthResult> resetPassword(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return AuthResult.success(null, message: 'Password reset email sent');
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('An unexpected error occurred');
    }
  }

  // Update profile
  static Future<AuthResult> updateProfile({
    String? name,
    String? phoneNumber,
    String? profileImageUrl,
  }) async {
    try {
      if (_currentUser == null) {
        return AuthResult.failure('User not authenticated');
      }

      final updates = <String, dynamic>{};
      if (name != null) updates['name'] = name;
      if (phoneNumber != null) updates['phoneNumber'] = phoneNumber;
      if (profileImageUrl != null) updates['profileImageUrl'] = profileImageUrl;

      if (updates.isNotEmpty) {
        await _firestore.collection('users').doc(_currentUser!.id).update(updates);
        await _loadUserData(_currentUser!.id);
      }

      return AuthResult.success(_currentUser!);
    } catch (e) {
      return AuthResult.failure('Failed to update profile');
    }
  }

  // Change password
  static Future<AuthResult> changePassword(String currentPassword, String newPassword) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return AuthResult.failure('User not authenticated');
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: currentPassword,
      );
      await user.reauthenticateWithCredential(credential);

      // Update password
      await user.updatePassword(newPassword);
      return AuthResult.success(_currentUser!, message: 'Password updated successfully');
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('Failed to change password');
    }
  }

  // Delete account
  static Future<AuthResult> deleteAccount(String password) async {
    try {
      final user = _auth.currentUser;
      if (user == null) {
        return AuthResult.failure('User not authenticated');
      }

      // Re-authenticate user
      final credential = EmailAuthProvider.credential(
        email: user.email!,
        password: password,
      );
      await user.reauthenticateWithCredential(credential);

      // Delete user data
      await _deleteUserData(user.uid);

      // Delete authentication account
      await user.delete();
      _currentUser = null;

      return AuthResult.success(null, message: 'Account deleted successfully');
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('Failed to delete account');
    }
  }

  // Private methods
  static Future<void> _onAuthStateChanged(User? user) async {
    if (user != null) {
      await _loadUserData(user.uid);
      
      // Start syncing user data
      SyncService.syncUserData(user.uid);
      SyncService.syncCommissionData(user.uid);
      SyncService.syncNetworkData(user.uid);
      SyncService.syncGoalsData(user.uid);
    } else {
      _currentUser = null;
      SyncService.stopAllSync();
    }
  }

  static Future<void> _loadUserData(String uid) async {
    try {
      final doc = await _firestore.collection('users').doc(uid).get();
      if (doc.exists) {
        _currentUser = UserModel.fromFirestore(doc);
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
  }

  static Future<void> _createUserProfile({
    required String uid,
    required String email,
    required String name,
    required String phoneNumber,
    String? referralCode,
  }) async {
    // Find upline agent if referral code provided
    String? uplineId;
    if (referralCode != null && referralCode.isNotEmpty) {
      uplineId = await _findUplineByReferralCode(referralCode);
    }

    // Create user document
    await _firestore.collection('users').doc(uid).set({
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'role': AppConstants.agentRole,
      'level': uplineId != null ? 1 : 0, // Level 0 for direct signups, 1 for referred
      'uplineId': uplineId,
      'downlineIds': [],
      'totalSales': 0.0,
      'totalCommissions': 0.0,
      'isActive': true,
      'referralCode': _generateReferralCode(),
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });

    // Update upline's downline list if applicable
    if (uplineId != null) {
      await _firestore.collection('users').doc(uplineId).update({
        'downlineIds': FieldValue.arrayUnion([uid]),
      });

      // Create referral bonus commission
      await _createReferralBonus(uplineId, uid, name);
    }

    // Create welcome notification
    await _createWelcomeNotification(uid, name);
  }

  static Future<String?> _findUplineByReferralCode(String referralCode) async {
    try {
      final query = await _firestore
          .collection('users')
          .where('referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (query.docs.isNotEmpty) {
        return query.docs.first.id;
      }
    } catch (e) {
      print('Error finding upline: $e');
    }
    return null;
  }

  static String _generateReferralCode() {
    const chars = 'ABCDEFGHIJKLMNOPQRSTUVWXYZ0123456789';
    final random = Random();
    return String.fromCharCodes(Iterable.generate(
      8, (_) => chars.codeUnitAt(random.nextInt(chars.length))
    ));
  }

  static Future<void> _createReferralBonus(String uplineId, String newAgentId, String newAgentName) async {
    await _firestore.collection('commissions').add({
      'agentId': uplineId,
      'agentName': '', // Will be filled by trigger
      'type': CommissionType.referralBonus.value,
      'status': CommissionStatus.pending.value,
      'amount': 5000.0, // ₹5,000 referral bonus
      'baseAmount': 5000.0,
      'percentage': 0.0,
      'tier': CommissionTier.bronze.value,
      'tierMultiplier': 1.0,
      'sourceAgentId': newAgentId,
      'sourceAgentName': newAgentName,
      'level': 0,
      'metadata': {
        'referralType': 'new_agent_signup',
        'newAgentId': newAgentId,
        'newAgentName': newAgentName,
      },
      'calculatedAt': FieldValue.serverTimestamp(),
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  static Future<void> _createWelcomeNotification(String userId, String name) async {
    await _firestore.collection('notifications').add({
      'agentId': userId,
      'type': NotificationType.system.name,
      'title': 'Welcome to Rama Realty MLM!',
      'message': 'Welcome $name! Your account has been created successfully. Start exploring properties and building your network.',
      'timestamp': FieldValue.serverTimestamp(),
      'isRead': false,
      'priority': NotificationPriority.normal.name,
      'actionData': {},
    });
  }

  static Future<void> _deleteUserData(String uid) async {
    final batch = _firestore.batch();

    // Delete user document
    batch.delete(_firestore.collection('users').doc(uid));

    // Delete user's commissions
    final commissionsQuery = await _firestore
        .collection('commissions')
        .where('agentId', isEqualTo: uid)
        .get();
    for (final doc in commissionsQuery.docs) {
      batch.delete(doc.reference);
    }

    // Delete user's goals
    final goalsQuery = await _firestore
        .collection('goals')
        .where('agentId', isEqualTo: uid)
        .get();
    for (final doc in goalsQuery.docs) {
      batch.delete(doc.reference);
    }

    // Delete user's notifications
    final notificationsQuery = await _firestore
        .collection('notifications')
        .where('agentId', isEqualTo: uid)
        .get();
    for (final doc in notificationsQuery.docs) {
      batch.delete(doc.reference);
    }

    await batch.commit();
  }

  static String _getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'user-not-found':
        return 'No user found with this email address';
      case 'wrong-password':
        return 'Incorrect password';
      case 'email-already-in-use':
        return 'An account already exists with this email address';
      case 'weak-password':
        return 'Password is too weak';
      case 'invalid-email':
        return 'Invalid email address';
      case 'user-disabled':
        return 'This account has been disabled';
      case 'too-many-requests':
        return 'Too many failed attempts. Please try again later';
      default:
        return e.message ?? 'An authentication error occurred';
    }
  }
}

// Auth result class
class AuthResult {
  final bool isSuccess;
  final UserModel? user;
  final String? message;
  final String? error;

  const AuthResult._({
    required this.isSuccess,
    this.user,
    this.message,
    this.error,
  });

  factory AuthResult.success(UserModel? user, {String? message}) {
    return AuthResult._(
      isSuccess: true,
      user: user,
      message: message,
    );
  }

  factory AuthResult.failure(String error) {
    return AuthResult._(
      isSuccess: false,
      error: error,
    );
  }
}
```

## Role-Based Access Control

```dart
// lib/core/services/permission_service.dart
class PermissionService {
  // Define permissions
  static const Map<String, List<String>> rolePermissions = {
    AppConstants.adminRole: [
      'view_all_properties',
      'manage_properties',
      'view_all_commissions',
      'manage_commissions',
      'view_all_networks',
      'manage_users',
      'generate_all_reports',
      'manage_system_settings',
    ],
    AppConstants.managerRole: [
      'view_team_properties',
      'manage_team_properties',
      'view_team_commissions',
      'approve_commissions',
      'view_team_networks',
      'generate_team_reports',
    ],
    AppConstants.agentRole: [
      'view_own_properties',
      'manage_own_properties',
      'view_own_commissions',
      'view_own_network',
      'generate_own_reports',
      'manage_own_goals',
    ],
  };

  // Check if user has permission
  static bool hasPermission(UserModel user, String permission) {
    final userPermissions = rolePermissions[user.role] ?? [];
    return userPermissions.contains(permission);
  }

  // Check multiple permissions
  static bool hasAnyPermission(UserModel user, List<String> permissions) {
    return permissions.any((permission) => hasPermission(user, permission));
  }

  static bool hasAllPermissions(UserModel user, List<String> permissions) {
    return permissions.every((permission) => hasPermission(user, permission));
  }

  // Get user's permissions
  static List<String> getUserPermissions(UserModel user) {
    return rolePermissions[user.role] ?? [];
  }

  // Permission-based widget wrapper
  static Widget requirePermission({
    required String permission,
    required Widget child,
    Widget? fallback,
  }) {
    return Consumer(
      builder: (context, ref, _) {
        final user = ref.watch(currentUserProvider);
        
        if (user != null && hasPermission(user, permission)) {
          return child;
        }
        
        return fallback ?? const SizedBox.shrink();
      },
    );
  }

  // Permission-based navigation guard
  static bool canNavigateToRoute(String route, UserModel user) {
    switch (route) {
      case '/admin':
        return user.role == AppConstants.adminRole;
      case '/manager':
        return user.role == AppConstants.managerRole || user.role == AppConstants.adminRole;
      case '/properties/manage':
        return hasPermission(user, 'manage_properties') || 
               hasPermission(user, 'manage_own_properties');
      case '/commissions/approve':
        return hasPermission(user, 'approve_commissions');
      case '/reports/all':
        return hasPermission(user, 'generate_all_reports');
      default:
        return true; // Allow access to general routes
    }
  }
}
```

## Authentication Widgets

```dart
// lib/features/auth/presentation/widgets/auth_wrapper.dart
class AuthWrapper extends ConsumerWidget {
  final Widget child;

  const AuthWrapper({super.key, required this.child});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return StreamBuilder<User?>(
      stream: FirebaseAuth.instance.authStateChanges(),
      builder: (context, snapshot) {
        if (snapshot.connectionState == ConnectionState.waiting) {
          return const SplashScreen();
        }

        if (snapshot.hasData) {
          return FutureBuilder<UserModel?>(
            future: _loadUserData(snapshot.data!.uid),
            builder: (context, userSnapshot) {
              if (userSnapshot.connectionState == ConnectionState.waiting) {
                return const LoadingScreen();
              }

              if (userSnapshot.hasData) {
                return ProviderScope(
                  overrides: [
                    currentUserProvider.overrideWithValue(userSnapshot.data),
                  ],
                  child: child,
                );
              }

              return const OnboardingScreen();
            },
          );
        }

        return const LoginScreen();
      },
    );
  }

  Future<UserModel?> _loadUserData(String uid) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('users')
          .doc(uid)
          .get();
      
      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
    } catch (e) {
      print('Error loading user data: $e');
    }
    return null;
  }
}
```
