import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../providers/auth_providers.dart';
import '../../domain/auth_state.dart';

class RegisterPage extends ConsumerWidget {
  const RegisterPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final registrationState = ref.watch(registrationStateProvider);
    final authState = ref.watch(authStateProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authStateProvider, (previous, next) {
      if (next is AuthAuthenticated) {
        context.go('/dashboard');
      } else if (next is AuthError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(next.message),
            backgroundColor: Colors.red,
          ),
        );
        ref.read(authStateProvider.notifier).clearError();
      }
    });

    return Scaffold(
      appBar: AppBar(
        title: const Text('Register'),
        backgroundColor: Colors.transparent,
        elevation: 0,
      ),
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              Text(
                'Join Rama Realty Network',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
                textAlign: TextAlign.center,
              ),

              const SizedBox(height: 32),

              // Error Message
              if (registrationState.errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                  ),
                  child: Text(
                    registrationState.errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),

              TextField(
                onChanged: (value) => ref.read(registrationStateProvider.notifier).updateName(value),
                decoration: const InputDecoration(
                  labelText: 'Full Name',
                  prefixIcon: Icon(Icons.person),
                ),
                enabled: !registrationState.isLoading,
              ),

              const SizedBox(height: 16),

              TextField(
                onChanged: (value) => ref.read(registrationStateProvider.notifier).updateEmail(value),
                decoration: const InputDecoration(
                  labelText: 'Email',
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                enabled: !registrationState.isLoading,
              ),

              const SizedBox(height: 16),

              TextField(
                onChanged: (value) => ref.read(registrationStateProvider.notifier).updatePhoneNumber(value),
                decoration: const InputDecoration(
                  labelText: 'Phone Number',
                  prefixIcon: Icon(Icons.phone),
                  hintText: '10-digit mobile number',
                ),
                keyboardType: TextInputType.phone,
                enabled: !registrationState.isLoading,
              ),

              const SizedBox(height: 16),

              TextField(
                onChanged: (value) => ref.read(registrationStateProvider.notifier).updatePassword(value),
                decoration: InputDecoration(
                  labelText: 'Password',
                  prefixIcon: const Icon(Icons.lock),
                  suffixIcon: IconButton(
                    icon: Icon(
                      registrationState.obscurePassword ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () => ref.read(registrationStateProvider.notifier).togglePasswordVisibility(),
                  ),
                  helperText: 'Minimum 8 characters',
                ),
                obscureText: registrationState.obscurePassword,
                enabled: !registrationState.isLoading,
              ),

              const SizedBox(height: 16),

              TextField(
                onChanged: (value) => ref.read(registrationStateProvider.notifier).updateConfirmPassword(value),
                decoration: InputDecoration(
                  labelText: 'Confirm Password',
                  prefixIcon: const Icon(Icons.lock_outline),
                  suffixIcon: IconButton(
                    icon: Icon(
                      registrationState.obscureConfirmPassword ? Icons.visibility_off : Icons.visibility,
                    ),
                    onPressed: () => ref.read(registrationStateProvider.notifier).toggleConfirmPasswordVisibility(),
                  ),
                ),
                obscureText: registrationState.obscureConfirmPassword,
                enabled: !registrationState.isLoading,
              ),

              const SizedBox(height: 16),

              TextField(
                onChanged: (value) => ref.read(registrationStateProvider.notifier).updateReferralCode(value),
                decoration: const InputDecoration(
                  labelText: 'Referral Code (Optional)',
                  prefixIcon: Icon(Icons.group_add),
                  hintText: 'Enter referral code from your upline',
                ),
                enabled: !registrationState.isLoading,
              ),

              const SizedBox(height: 24),

              ElevatedButton(
                onPressed: registrationState.isLoading || authState is AuthLoading
                    ? null
                    : () => _handleRegistration(context, ref),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: registrationState.isLoading || authState is AuthLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Register'),
                ),
              ),

              const SizedBox(height: 16),

              TextButton(
                onPressed: registrationState.isLoading ? null : () => context.pop(),
                child: const Text('Already have an account? Login'),
              ),

              const SizedBox(height: 16),

              // Terms and Privacy
              Text(
                'By registering, you agree to our Terms of Service and Privacy Policy',
                style: Theme.of(context).textTheme.bodySmall?.copyWith(
                  color: Colors.grey[600],
                ),
                textAlign: TextAlign.center,
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleRegistration(BuildContext context, WidgetRef ref) {
    final registrationNotifier = ref.read(registrationStateProvider.notifier);
    final authNotifier = ref.read(authStateProvider.notifier);
    final registrationState = ref.read(registrationStateProvider);

    // Validate form
    final error = registrationNotifier.validateForm();
    if (error != null) {
      registrationNotifier.setError(error);
      return;
    }

    // Perform registration
    authNotifier.register(
      email: registrationState.email,
      password: registrationState.password,
      name: registrationState.name,
      phoneNumber: registrationState.phoneNumber,
      referralCode: registrationState.referralCode.isNotEmpty
          ? registrationState.referralCode
          : null,
    );
  }
}
