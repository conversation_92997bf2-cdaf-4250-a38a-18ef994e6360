import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../models/property_model.dart';
import '../constants/app_constants.dart';
// import '../../features/commissions/models/commission_model.dart';
// import '../../features/transactions/models/transaction_model.dart';

/// Comprehensive test data generator for MLM hierarchy testing
class TestDataGenerator {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Generate complete test data hierarchy
  static Future<void> generateCompleteTestData() async {
    if (kDebugMode) {
      print('🚀 Starting comprehensive test data generation...');
    }

    try {
      // 1. Create hierarchical user structure
      await _createUserHierarchy();
      await _createAgentLevels();
      await _createJuniorAgents();
      
      // 2. Create diverse property portfolio
      await _createPropertyPortfolio();
      
      // 3. Generate transaction history
      await _createTransactionHistory();
      
      // 4. Create commission records
      await _createCommissionRecords();
      
      // 5. Generate MLM network data
      await _createMLMNetworkData();

      if (kDebugMode) {
        print('✅ Complete test data generation successful!');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error generating test data: $e');
      }
      rethrow;
    }
  }

  /// Create 5-level user hierarchy with realistic MLM structure
  static Future<void> _createUserHierarchy() async {
    if (kDebugMode) {
      print('👥 Creating user hierarchy...');
    }

    // Level 0: Admin/Founder
    final admin = UserModel(
      id: 'admin_001',
      name: 'Rama Samriddhi Admin',
      email: '<EMAIL>',
      phoneNumber: '+91-9876543210',
      uplineId: null, // Top level
      downlineIds: ['regional_001', 'regional_002'],
      level: 0,
      totalStars: 100,
      totalCommissions: 500000.0,
      totalSales: 5000000.0,
      totalBonuses: 100000,
      profileImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
      createdAt: DateTime.now().subtract(const Duration(days: 365)),
      updatedAt: DateTime.now(),
    );

    // Level 1: Regional Managers
    final regional1 = UserModel(
      id: 'regional_001',
      name: 'Rajesh Kumar',
      email: '<EMAIL>',
      phoneNumber: '+91-9876543211',
      uplineId: 'admin_001',
      downlineIds: ['team_001', 'team_002'],
      referralCode: 'RAJESH2024',
      referredBy: 'ADMIN2024',
      level: 1,
      totalStars: 75,
      totalCommissions: 250000.0,
      totalSales: 2500000.0,
      totalBonuses: 50000,
      profileImageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
      createdAt: DateTime.now().subtract(const Duration(days: 300)),
      updatedAt: DateTime.now(),
    );

    final regional2 = UserModel(
      id: 'regional_002',
      name: 'Priya Sharma',
      email: '<EMAIL>',
      phoneNumber: '+91-9876543212',
      uplineId: 'admin_001',
      downlineIds: ['team_003', 'team_004'],
      referralCode: 'PRIYA2024',
      referredBy: 'ADMIN2024',
      level: 1,
      totalStars: 80,
      totalCommissions: 300000.0,
      totalSales: 3000000.0,
      totalBonuses: 60000,
      profileImageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
      createdAt: DateTime.now().subtract(const Duration(days: 280)),
      updatedAt: DateTime.now(),
    );

    // Level 2: Team Leaders
    final teamLeaders = [
      UserModel(
        id: 'team_001',
        name: 'Amit Patel',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543213',
        uplineId: 'regional_001',
        downlineIds: ['agent_001', 'agent_002', 'agent_003'],
        referralCode: 'AMIT2024',
        referredBy: 'RAJESH2024',
        level: 2,
        totalStars: 50,
        totalCommissions: 150000.0,
        totalSales: 1500000.0,
        totalBonuses: 30000,
        profileImageUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 200)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'team_002',
        name: 'Sunita Gupta',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543214',
        uplineId: 'regional_001',
        downlineIds: ['agent_004', 'agent_005'],
        referralCode: 'SUNITA2024',
        referredBy: 'RAJESH2024',
        level: 2,
        totalStars: 45,
        totalCommissions: 120000.0,
        totalSales: 1200000.0,
        totalBonuses: 25000,
        profileImageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 180)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'team_003',
        name: 'Vikram Singh',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543215',
        uplineId: 'regional_002',
        downlineIds: ['agent_006', 'agent_007', 'agent_008'],
        referralCode: 'VIKRAM2024',
        referredBy: 'PRIYA2024',
        level: 2,
        totalStars: 55,
        totalCommissions: 180000.0,
        totalSales: 1800000.0,
        totalBonuses: 35000,
        profileImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 160)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'team_004',
        name: 'Kavita Reddy',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543216',
        uplineId: 'regional_002',
        downlineIds: ['agent_009', 'agent_010'],
        referralCode: 'KAVITA2024',
        referredBy: 'PRIYA2024',
        level: 2,
        totalStars: 40,
        totalCommissions: 100000.0,
        totalSales: 1000000.0,
        totalBonuses: 20000,
        profileImageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 140)),
        updatedAt: DateTime.now(),
      ),
    ];

    // Save all users
    final allUsers = [admin, regional1, regional2, ...teamLeaders];
    
    for (final user in allUsers) {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .set(user.toFirestore());
      
      if (kDebugMode) {
        print('✅ Created user: ${user.name} (Level ${user.level})');
      }
    }
  }

  /// Create Level 3 & 4 agents
  static Future<void> _createAgentLevels() async {
    if (kDebugMode) {
      print('👨‍💼 Creating agent levels...');
    }

    // Level 3: Senior Agents
    final seniorAgents = [
      UserModel(
        id: 'agent_001',
        name: 'Rohit Mehta',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543217',
        uplineId: 'team_001',
        downlineIds: ['junior_001', 'junior_002'],
        referralCode: 'ROHIT2024',
        referredBy: 'AMIT2024',
        level: 3,
        totalStars: 25,
        totalCommissions: 75000.0,
        totalSales: 750000.0,
        totalBonuses: 15000,
        profileImageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 120)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_002',
        name: 'Deepika Joshi',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543218',
        uplineId: 'team_001',
        downlineIds: ['junior_003'],
        referralCode: 'DEEPIKA2024',
        referredBy: 'AMIT2024',
        level: 3,
        totalStars: 20,
        totalCommissions: 60000.0,
        totalSales: 600000.0,
        totalBonuses: 12000,
        profileImageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 100)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_003',
        name: 'Arjun Kapoor',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543219',
        uplineId: 'team_001',
        downlineIds: [],
        referralCode: 'ARJUN2024',
        referredBy: 'AMIT2024',
        level: 3,
        totalStars: 15,
        totalCommissions: 45000.0,
        totalSales: 450000.0,
        totalBonuses: 9000,
        profileImageUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 90)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_004',
        name: 'Sanya Malhotra',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543220',
        uplineId: 'team_002',
        downlineIds: ['junior_004', 'junior_005'],
        referralCode: 'SANYA2024',
        referredBy: 'SUNITA2024',
        level: 3,
        totalStars: 30,
        totalCommissions: 90000.0,
        totalSales: 900000.0,
        totalBonuses: 18000,
        profileImageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 110)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_005',
        name: 'Karan Johar',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543221',
        uplineId: 'team_002',
        downlineIds: [],
        referralCode: 'KARAN2024',
        referredBy: 'SUNITA2024',
        level: 3,
        totalStars: 18,
        totalCommissions: 54000.0,
        totalSales: 540000.0,
        totalBonuses: 10800,
        profileImageUrl: 'https://images.unsplash.com/photo-1507003211169-0a1dd7228f2d?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 85)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_006',
        name: 'Ananya Pandey',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543222',
        uplineId: 'team_003',
        downlineIds: ['junior_006'],
        referralCode: 'ANANYA2024',
        referredBy: 'VIKRAM2024',
        level: 3,
        totalStars: 22,
        totalCommissions: 66000.0,
        totalSales: 660000.0,
        totalBonuses: 13200,
        profileImageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 95)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_007',
        name: 'Varun Dhawan',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543223',
        uplineId: 'team_003',
        downlineIds: ['junior_007', 'junior_008'],
        referralCode: 'VARUN2024',
        referredBy: 'VIKRAM2024',
        level: 3,
        totalStars: 28,
        totalCommissions: 84000.0,
        totalSales: 840000.0,
        totalBonuses: 16800,
        profileImageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 105)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_008',
        name: 'Shraddha Kapoor',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543224',
        uplineId: 'team_003',
        downlineIds: [],
        referralCode: 'SHRADDHA2024',
        referredBy: 'VIKRAM2024',
        level: 3,
        totalStars: 16,
        totalCommissions: 48000.0,
        totalSales: 480000.0,
        totalBonuses: 9600,
        profileImageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 80)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_009',
        name: 'Sidharth Malhotra',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543225',
        uplineId: 'team_004',
        downlineIds: ['junior_009'],
        referralCode: 'SIDHARTH2024',
        referredBy: 'KAVITA2024',
        level: 3,
        totalStars: 24,
        totalCommissions: 72000.0,
        totalSales: 720000.0,
        totalBonuses: 14400,
        profileImageUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 75)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'agent_010',
        name: 'Kiara Advani',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543226',
        uplineId: 'team_004',
        downlineIds: ['junior_010'],
        referralCode: 'KIARA2024',
        referredBy: 'KAVITA2024',
        level: 3,
        totalStars: 19,
        totalCommissions: 57000.0,
        totalSales: 570000.0,
        totalBonuses: 11400,
        profileImageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 70)),
        updatedAt: DateTime.now(),
      ),
    ];

    // Save senior agents
    for (final agent in seniorAgents) {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agent.id)
          .set(agent.toFirestore());

      if (kDebugMode) {
        print('✅ Created senior agent: ${agent.name} (Level ${agent.level})');
      }
    }
  }

  /// Clear all test data
  static Future<void> clearAllTestData() async {
    if (kDebugMode) {
      print('🧹 Clearing all test data...');
    }

    try {
      // Clear users
      final usersSnapshot = await _firestore.collection(AppConstants.usersCollection).get();
      for (final doc in usersSnapshot.docs) {
        await doc.reference.delete();
      }

      // Clear properties
      final propertiesSnapshot = await _firestore.collection(AppConstants.propertiesCollection).get();
      for (final doc in propertiesSnapshot.docs) {
        await doc.reference.delete();
      }

      // Clear other collections as needed
      
      if (kDebugMode) {
        print('✅ All test data cleared successfully!');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing test data: $e');
      }
      rethrow;
    }
  }

  /// Create junior agents (Level 4)
  static Future<void> _createJuniorAgents() async {
    if (kDebugMode) {
      print('👶 Creating junior agents...');
    }

    // Level 4: Junior Agents
    final juniorAgents = [
      UserModel(
        id: 'junior_001',
        name: 'Neha Agarwal',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543230',
        uplineId: 'agent_001',
        downlineIds: [],
        referralCode: 'NEHA2024',
        referredBy: 'ROHIT2024',
        level: 4,
        totalStars: 10,
        totalCommissions: 25000.0,
        totalSales: 250000.0,
        totalBonuses: 5000,
        profileImageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 60)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_002',
        name: 'Rahul Tiwari',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543231',
        uplineId: 'agent_001',
        downlineIds: [],
        referralCode: 'RAHUL2024',
        referredBy: 'ROHIT2024',
        level: 4,
        totalStars: 8,
        totalCommissions: 20000.0,
        totalSales: 200000.0,
        totalBonuses: 4000,
        profileImageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 50)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_003',
        name: 'Pooja Sharma',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543232',
        uplineId: 'agent_002',
        downlineIds: [],
        referralCode: 'POOJA2024',
        referredBy: 'DEEPIKA2024',
        level: 4,
        totalStars: 12,
        totalCommissions: 30000.0,
        totalSales: 300000.0,
        totalBonuses: 6000,
        profileImageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 45)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_004',
        name: 'Akash Kumar',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543233',
        uplineId: 'agent_004',
        downlineIds: [],
        referralCode: 'AKASH2024',
        referredBy: 'SANYA2024',
        level: 4,
        totalStars: 14,
        totalCommissions: 35000.0,
        totalSales: 350000.0,
        totalBonuses: 7000,
        profileImageUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 40)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_005',
        name: 'Riya Patel',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543234',
        uplineId: 'agent_004',
        downlineIds: [],
        referralCode: 'RIYA2024',
        referredBy: 'SANYA2024',
        level: 4,
        totalStars: 9,
        totalCommissions: 22500.0,
        totalSales: 225000.0,
        totalBonuses: 4500,
        profileImageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 35)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_006',
        name: 'Manish Gupta',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543235',
        uplineId: 'agent_006',
        downlineIds: [],
        referralCode: 'MANISH2024',
        referredBy: 'ANANYA2024',
        level: 4,
        totalStars: 11,
        totalCommissions: 27500.0,
        totalSales: 275000.0,
        totalBonuses: 5500,
        profileImageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 30)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_007',
        name: 'Sneha Reddy',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543236',
        uplineId: 'agent_007',
        downlineIds: [],
        referralCode: 'SNEHA2024',
        referredBy: 'VARUN2024',
        level: 4,
        totalStars: 13,
        totalCommissions: 32500.0,
        totalSales: 325000.0,
        totalBonuses: 6500,
        profileImageUrl: 'https://images.unsplash.com/photo-1438761681033-6461ffad8d80?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 25)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_008',
        name: 'Vishal Singh',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543237',
        uplineId: 'agent_007',
        downlineIds: [],
        referralCode: 'VISHAL2024',
        referredBy: 'VARUN2024',
        level: 4,
        totalStars: 7,
        totalCommissions: 17500.0,
        totalSales: 175000.0,
        totalBonuses: 3500,
        profileImageUrl: 'https://images.unsplash.com/photo-1500648767791-00dcc994a43e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 20)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_009',
        name: 'Kavya Nair',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543238',
        uplineId: 'agent_009',
        downlineIds: [],
        referralCode: 'KAVYA2024',
        referredBy: 'SIDHARTH2024',
        level: 4,
        totalStars: 15,
        totalCommissions: 37500.0,
        totalSales: 375000.0,
        totalBonuses: 7500,
        profileImageUrl: 'https://images.unsplash.com/photo-1494790108755-2616b612b786?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 15)),
        updatedAt: DateTime.now(),
      ),
      UserModel(
        id: 'junior_010',
        name: 'Aryan Jain',
        email: '<EMAIL>',
        phoneNumber: '+91-9876543239',
        uplineId: 'agent_010',
        downlineIds: [],
        referralCode: 'ARYAN2024',
        referredBy: 'KIARA2024',
        level: 4,
        totalStars: 6,
        totalCommissions: 15000.0,
        totalSales: 150000.0,
        totalBonuses: 3000,
        profileImageUrl: 'https://images.unsplash.com/photo-1472099645785-5658abf4ff4e?w=150',
        createdAt: DateTime.now().subtract(const Duration(days: 10)),
        updatedAt: DateTime.now(),
      ),
    ];

    // Save junior agents
    for (final agent in juniorAgents) {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agent.id)
          .set(agent.toFirestore());

      if (kDebugMode) {
        print('✅ Created junior agent: ${agent.name} (Level ${agent.level})');
      }
    }
  }

  /// Create diverse property portfolio
  static Future<void> _createPropertyPortfolio() async {
    if (kDebugMode) {
      print('🏠 Creating property portfolio...');
    }

    final properties = [
      // Luxury Apartments
      {
        'id': 'prop_001',
        'title': 'Luxury 3BHK Apartment in Bandra',
        'description': 'Premium apartment with sea view, modern amenities, and prime location.',
        'price': 25000000.0,
        'location': 'Bandra West, Mumbai',
        'type': 'Apartment',
        'bedrooms': 3,
        'bathrooms': 3,
        'area': 1800.0,
        'images': [
          'https://images.unsplash.com/photo-1560448204-e02f11c3d0e2?w=800',
          'https://images.unsplash.com/photo-1502672260266-1c1ef2d93688?w=800',
        ],
        'amenities': ['Swimming Pool', 'Gym', 'Security', 'Parking', 'Garden'],
        'agentId': 'agent_001',
        'status': 'available',
        'featured': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 30)),
      },
      {
        'id': 'prop_002',
        'title': 'Modern 2BHK in Koramangala',
        'description': 'Contemporary design with smart home features in tech hub.',
        'price': 15000000.0,
        'location': 'Koramangala, Bangalore',
        'type': 'Apartment',
        'bedrooms': 2,
        'bathrooms': 2,
        'area': 1200.0,
        'images': [
          'https://images.unsplash.com/photo-1512917774080-9991f1c4c750?w=800',
          'https://images.unsplash.com/photo-1493809842364-78817add7ffb?w=800',
        ],
        'amenities': ['Smart Home', 'Gym', 'Security', 'Parking'],
        'agentId': 'agent_002',
        'status': 'available',
        'featured': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 25)),
      },
      // Villas
      {
        'id': 'prop_003',
        'title': 'Luxury Villa in Gurgaon',
        'description': 'Independent villa with private garden and premium finishes.',
        'price': 45000000.0,
        'location': 'DLF Phase 2, Gurgaon',
        'type': 'Villa',
        'bedrooms': 4,
        'bathrooms': 5,
        'area': 3500.0,
        'images': [
          'https://images.unsplash.com/photo-1564013799919-ab600027ffc6?w=800',
          'https://images.unsplash.com/photo-1600596542815-ffad4c1539a9?w=800',
        ],
        'amenities': ['Private Garden', 'Swimming Pool', 'Gym', 'Security', 'Parking', 'Servant Quarter'],
        'agentId': 'agent_003',
        'status': 'available',
        'featured': true,
        'createdAt': DateTime.now().subtract(const Duration(days: 20)),
      },
    ];

    for (final propertyData in properties) {
      await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyData['id'] as String)
          .set(propertyData);

      if (kDebugMode) {
        print('✅ Created property: ${propertyData['title']}');
      }
    }
  }

  /// Create transaction history
  static Future<void> _createTransactionHistory() async {
    if (kDebugMode) {
      print('💰 Creating transaction history...');
    }

    final transactions = [
      {
        'id': 'txn_001',
        'propertyId': 'prop_001',
        'buyerName': 'Rajesh Khanna',
        'buyerEmail': '<EMAIL>',
        'buyerPhone': '+91-9876543240',
        'sellingAgentId': 'agent_001',
        'saleAmount': 25000000.0,
        'commissionAmount': 1250000.0, // 5% commission
        'status': 'completed',
        'saleDate': DateTime.now().subtract(const Duration(days: 15)),
        'createdAt': DateTime.now().subtract(const Duration(days: 15)),
      },
      {
        'id': 'txn_002',
        'propertyId': 'prop_002',
        'buyerName': 'Priya Nair',
        'buyerEmail': '<EMAIL>',
        'buyerPhone': '+91-9876543241',
        'sellingAgentId': 'agent_002',
        'saleAmount': 15000000.0,
        'commissionAmount': 750000.0,
        'status': 'completed',
        'saleDate': DateTime.now().subtract(const Duration(days: 10)),
        'createdAt': DateTime.now().subtract(const Duration(days: 10)),
      },
      {
        'id': 'txn_003',
        'propertyId': 'prop_003',
        'buyerName': 'Amit Sharma',
        'buyerEmail': '<EMAIL>',
        'buyerPhone': '+91-9876543242',
        'sellingAgentId': 'agent_003',
        'saleAmount': 45000000.0,
        'commissionAmount': 2250000.0,
        'status': 'pending',
        'saleDate': DateTime.now().subtract(const Duration(days: 5)),
        'createdAt': DateTime.now().subtract(const Duration(days: 5)),
      },
    ];

    for (final transaction in transactions) {
      await _firestore
          .collection('transactions')
          .doc(transaction['id'] as String)
          .set(transaction);

      if (kDebugMode) {
        print('✅ Created transaction: ${transaction['id']}');
      }
    }
  }

  /// Create commission records
  static Future<void> _createCommissionRecords() async {
    if (kDebugMode) {
      print('💸 Creating commission records...');
    }

    final commissions = [
      // Commission for txn_001 (agent_001 sale)
      {
        'id': 'comm_001_1',
        'transactionId': 'txn_001',
        'agentId': 'agent_001', // Direct seller
        'level': 0,
        'amount': 1250000.0, // 5% of sale
        'percentage': 5.0,
        'type': 'direct_sale',
        'status': 'paid',
        'createdAt': DateTime.now().subtract(const Duration(days: 15)),
      },
      {
        'id': 'comm_001_2',
        'transactionId': 'txn_001',
        'agentId': 'team_001', // Level 1 upline
        'level': 1,
        'amount': 375000.0, // 1.5% of sale
        'percentage': 1.5,
        'type': 'upline_bonus',
        'status': 'paid',
        'createdAt': DateTime.now().subtract(const Duration(days: 15)),
      },
      {
        'id': 'comm_001_3',
        'transactionId': 'txn_001',
        'agentId': 'regional_001', // Level 2 upline
        'level': 2,
        'amount': 250000.0, // 1% of sale
        'percentage': 1.0,
        'type': 'upline_bonus',
        'status': 'paid',
        'createdAt': DateTime.now().subtract(const Duration(days: 15)),
      },
      {
        'id': 'comm_001_4',
        'transactionId': 'txn_001',
        'agentId': 'admin_001', // Level 3 upline
        'level': 3,
        'amount': 125000.0, // 0.5% of sale
        'percentage': 0.5,
        'type': 'upline_bonus',
        'status': 'paid',
        'createdAt': DateTime.now().subtract(const Duration(days: 15)),
      },
      // Commission for txn_002 (agent_002 sale)
      {
        'id': 'comm_002_1',
        'transactionId': 'txn_002',
        'agentId': 'agent_002',
        'level': 0,
        'amount': 750000.0,
        'percentage': 5.0,
        'type': 'direct_sale',
        'status': 'paid',
        'createdAt': DateTime.now().subtract(const Duration(days: 10)),
      },
      {
        'id': 'comm_002_2',
        'transactionId': 'txn_002',
        'agentId': 'team_002',
        'level': 1,
        'amount': 225000.0,
        'percentage': 1.5,
        'type': 'upline_bonus',
        'status': 'paid',
        'createdAt': DateTime.now().subtract(const Duration(days: 10)),
      },
    ];

    for (final commission in commissions) {
      await _firestore
          .collection('commissions')
          .doc(commission['id'] as String)
          .set(commission);

      if (kDebugMode) {
        print('✅ Created commission: ${commission['id']}');
      }
    }
  }

  /// Create MLM network data
  static Future<void> _createMLMNetworkData() async {
    if (kDebugMode) {
      print('🌐 Creating MLM network data...');
    }

    // Create star records for achievements
    final starRecords = [
      {
        'id': 'star_001',
        'agentId': 'agent_001',
        'stars': 5,
        'type': 'direct_sale',
        'transactionId': 'txn_001',
        'description': 'Sale of Luxury 3BHK Apartment',
        'createdAt': DateTime.now().subtract(const Duration(days: 15)),
      },
      {
        'id': 'star_002',
        'agentId': 'team_001',
        'stars': 2,
        'type': 'upline_bonus',
        'transactionId': 'txn_001',
        'description': 'Upline bonus from team member sale',
        'createdAt': DateTime.now().subtract(const Duration(days: 15)),
      },
      {
        'id': 'star_003',
        'agentId': 'agent_002',
        'stars': 4,
        'type': 'direct_sale',
        'transactionId': 'txn_002',
        'description': 'Sale of Modern 2BHK Apartment',
        'createdAt': DateTime.now().subtract(const Duration(days: 10)),
      },
    ];

    for (final star in starRecords) {
      await _firestore
          .collection('stars')
          .doc(star['id'] as String)
          .set(star);

      if (kDebugMode) {
        print('✅ Created star record: ${star['id']}');
      }
    }

    // Create referral tracking
    final referrals = [
      {
        'id': 'ref_001',
        'referrerId': 'team_001',
        'referredId': 'agent_001',
        'referralCode': 'AMIT2024',
        'status': 'active',
        'joinDate': DateTime.now().subtract(const Duration(days: 120)),
      },
      {
        'id': 'ref_002',
        'referrerId': 'team_001',
        'referredId': 'agent_002',
        'referralCode': 'AMIT2024',
        'status': 'active',
        'joinDate': DateTime.now().subtract(const Duration(days: 100)),
      },
      {
        'id': 'ref_003',
        'referrerId': 'agent_001',
        'referredId': 'junior_001',
        'referralCode': 'ROHIT2024',
        'status': 'active',
        'joinDate': DateTime.now().subtract(const Duration(days: 60)),
      },
    ];

    for (final referral in referrals) {
      await _firestore
          .collection('referrals')
          .doc(referral['id'] as String)
          .set(referral);

      if (kDebugMode) {
        print('✅ Created referral: ${referral['id']}');
      }
    }
  }
}
