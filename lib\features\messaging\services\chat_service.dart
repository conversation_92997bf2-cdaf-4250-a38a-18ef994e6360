import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/chat_models.dart';

/// Service for handling real-time messaging
class ChatService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Send a message
  static Future<void> sendMessage({
    required String chatId,
    required String senderId,
    required String content,
    MessageType type = MessageType.text,
    Map<String, dynamic>? metadata,
  }) async {
    try {
      // Add message to chat
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .add({
        'senderId': senderId,
        'content': content,
        'type': type.name,
        'metadata': metadata ?? {},
        'timestamp': FieldValue.serverTimestamp(),
        'isRead': false,
        'isEdited': false,
        'reactions': {},
      });

      // Update chat last message
      await _firestore.collection('chats').doc(chatId).update({
        'lastMessage': content,
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastMessageSender': senderId,
        'updatedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('Message sent successfully to chat: $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error sending message: $e');
      }
      rethrow;
    }
  }

  /// Create a direct chat between two users
  static Future<String> createDirectChat({
    required String user1Id,
    required String user2Id,
  }) async {
    try {
      // Check if chat already exists
      final existingChat = await _findExistingDirectChat(user1Id, user2Id);
      if (existingChat != null) {
        return existingChat;
      }

      // Create new chat
      final chatDoc = await _firestore.collection('chats').add({
        'type': ChatType.direct.name,
        'memberIds': [user1Id, user2Id],
        'adminIds': [user1Id, user2Id],
        'createdBy': user1Id,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'lastMessage': '',
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastMessageSender': '',
        'isActive': true,
        'name': '', // Direct chats don't have names
        'description': '',
        'imageUrl': '',
      });

      if (kDebugMode) {
        print('Direct chat created: ${chatDoc.id}');
      }

      return chatDoc.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating direct chat: $e');
      }
      rethrow;
    }
  }

  /// Create a group chat
  static Future<String> createGroupChat({
    required String name,
    required List<String> memberIds,
    required String createdBy,
    String? description,
    String? imageUrl,
  }) async {
    try {
      final chatDoc = await _firestore.collection('chats').add({
        'name': name,
        'description': description ?? '',
        'imageUrl': imageUrl ?? '',
        'type': ChatType.group.name,
        'memberIds': memberIds,
        'adminIds': [createdBy],
        'createdBy': createdBy,
        'createdAt': FieldValue.serverTimestamp(),
        'updatedAt': FieldValue.serverTimestamp(),
        'lastMessage': '',
        'lastMessageTime': FieldValue.serverTimestamp(),
        'lastMessageSender': '',
        'isActive': true,
      });

      // Send welcome message
      await sendMessage(
        chatId: chatDoc.id,
        senderId: 'system',
        content: '$name group created by $createdBy',
        type: MessageType.system,
      );

      if (kDebugMode) {
        print('Group chat created: ${chatDoc.id}');
      }

      return chatDoc.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error creating group chat: $e');
      }
      rethrow;
    }
  }

  /// Get chat messages stream
  static Stream<List<ChatMessage>> getChatMessages(String chatId) {
    return _firestore
        .collection('chats')
        .doc(chatId)
        .collection('messages')
        .orderBy('timestamp', descending: true)
        .limit(50)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => ChatMessage.fromFirestore(doc))
            .toList());
  }

  /// Get user chats stream
  static Stream<List<Chat>> getUserChats(String userId) {
    return _firestore
        .collection('chats')
        .where('memberIds', arrayContains: userId)
        .where('isActive', isEqualTo: true)
        .orderBy('lastMessageTime', descending: true)
        .snapshots()
        .map((snapshot) => snapshot.docs
            .map((doc) => Chat.fromFirestore(doc))
            .toList());
  }

  /// Mark messages as read
  static Future<void> markMessagesAsRead(String chatId, String userId) async {
    try {
      final unreadMessages = await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .where('senderId', isNotEqualTo: userId)
          .where('isRead', isEqualTo: false)
          .get();

      if (unreadMessages.docs.isNotEmpty) {
        final batch = _firestore.batch();
        for (final doc in unreadMessages.docs) {
          batch.update(doc.reference, {'isRead': true});
        }
        await batch.commit();

        if (kDebugMode) {
          print('Marked ${unreadMessages.docs.length} messages as read');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error marking messages as read: $e');
      }
    }
  }

  /// Add member to group chat
  static Future<void> addMemberToGroup({
    required String chatId,
    required String memberId,
    required String addedBy,
  }) async {
    try {
      await _firestore.collection('chats').doc(chatId).update({
        'memberIds': FieldValue.arrayUnion([memberId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Send system message
      await sendMessage(
        chatId: chatId,
        senderId: 'system',
        content: 'Member added to the group',
        type: MessageType.system,
        metadata: {
          'action': 'member_added',
          'memberId': memberId,
          'addedBy': addedBy,
        },
      );

      if (kDebugMode) {
        print('Member $memberId added to chat $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error adding member to group: $e');
      }
      rethrow;
    }
  }

  /// Remove member from group chat
  static Future<void> removeMemberFromGroup({
    required String chatId,
    required String memberId,
    required String removedBy,
  }) async {
    try {
      await _firestore.collection('chats').doc(chatId).update({
        'memberIds': FieldValue.arrayRemove([memberId]),
        'updatedAt': FieldValue.serverTimestamp(),
      });

      // Send system message
      await sendMessage(
        chatId: chatId,
        senderId: 'system',
        content: 'Member removed from the group',
        type: MessageType.system,
        metadata: {
          'action': 'member_removed',
          'memberId': memberId,
          'removedBy': removedBy,
        },
      );

      if (kDebugMode) {
        print('Member $memberId removed from chat $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error removing member from group: $e');
      }
      rethrow;
    }
  }

  /// Share property in chat
  static Future<void> shareProperty({
    required String chatId,
    required String senderId,
    required String propertyId,
    required String propertyTitle,
    required double propertyPrice,
    String? propertyImage,
    String? message,
  }) async {
    try {
      await sendMessage(
        chatId: chatId,
        senderId: senderId,
        content: message ?? 'Check out this property!',
        type: MessageType.property,
        metadata: {
          'propertyId': propertyId,
          'propertyTitle': propertyTitle,
          'propertyPrice': propertyPrice,
          'propertyImage': propertyImage,
        },
      );

      if (kDebugMode) {
        print('Property $propertyId shared in chat $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing property: $e');
      }
      rethrow;
    }
  }

  /// Share location in chat
  static Future<void> shareLocation({
    required String chatId,
    required String senderId,
    required double latitude,
    required double longitude,
    String? address,
  }) async {
    try {
      await sendMessage(
        chatId: chatId,
        senderId: senderId,
        content: 'Shared location',
        type: MessageType.location,
        metadata: {
          'latitude': latitude,
          'longitude': longitude,
          'address': address,
        },
      );

      if (kDebugMode) {
        print('Location shared in chat $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing location: $e');
      }
      rethrow;
    }
  }

  /// Delete message
  static Future<void> deleteMessage({
    required String chatId,
    required String messageId,
    required String userId,
  }) async {
    try {
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .delete();

      if (kDebugMode) {
        print('Message $messageId deleted from chat $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting message: $e');
      }
      rethrow;
    }
  }

  /// Edit message
  static Future<void> editMessage({
    required String chatId,
    required String messageId,
    required String newContent,
  }) async {
    try {
      await _firestore
          .collection('chats')
          .doc(chatId)
          .collection('messages')
          .doc(messageId)
          .update({
        'content': newContent,
        'isEdited': true,
        'editedAt': FieldValue.serverTimestamp(),
      });

      if (kDebugMode) {
        print('Message $messageId edited in chat $chatId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error editing message: $e');
      }
      rethrow;
    }
  }

  /// Find existing direct chat between two users
  static Future<String?> _findExistingDirectChat(String user1Id, String user2Id) async {
    try {
      final query = await _firestore
          .collection('chats')
          .where('type', isEqualTo: ChatType.direct.name)
          .where('memberIds', arrayContains: user1Id)
          .get();

      for (final doc in query.docs) {
        final memberIds = List<String>.from(doc.data()['memberIds'] ?? []);
        if (memberIds.contains(user2Id)) {
          return doc.id;
        }
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding existing direct chat: $e');
      }
      return null;
    }
  }

  /// Get unread message count for user
  static Future<int> getUnreadMessageCount(String userId) async {
    try {
      int totalUnread = 0;
      
      // Get all user's chats
      final chatsSnapshot = await _firestore
          .collection('chats')
          .where('memberIds', arrayContains: userId)
          .where('isActive', isEqualTo: true)
          .get();

      for (final chatDoc in chatsSnapshot.docs) {
        // Count unread messages in each chat
        final unreadSnapshot = await _firestore
            .collection('chats')
            .doc(chatDoc.id)
            .collection('messages')
            .where('senderId', isNotEqualTo: userId)
            .where('isRead', isEqualTo: false)
            .get();
        
        totalUnread += unreadSnapshot.docs.length;
      }

      return totalUnread;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting unread message count: $e');
      }
      return 0;
    }
  }
}
