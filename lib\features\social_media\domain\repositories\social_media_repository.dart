import '../models/social_media_image.dart';

abstract class SocialMediaRepository {
  Future<List<SocialMediaImage>> getSocialMediaImages();
}

class MockSocialMediaRepository implements SocialMediaRepository {
  @override
  Future<List<SocialMediaImage>> getSocialMediaImages() async {
    // In a real implementation, you would fetch this data from Firebase.
    return [
      SocialMediaImage(
        id: '1',
        name: 'Placeholder Image 1',
        assetPath: 'assets/social_media/placeholder.png',
      ),
      SocialMediaImage(
        id: '2',
        name: 'Placeholder Image 2',
        assetPath: 'assets/social_media/placeholder.png',
      ),
      SocialMediaImage(
        id: '3',
        name: 'Placeholder Image 3',
        assetPath: 'assets/social_media/placeholder.png',
      ),
    ];
  }
}
