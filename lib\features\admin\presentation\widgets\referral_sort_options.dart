import 'package:flutter/material.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../providers/referral_management_provider.dart';

/// Sort options widget for referral management
class ReferralSortOptions extends StatelessWidget {
  final ReferralSortOption currentOption;
  final bool isAscending;
  final Function(ReferralSortOption) onSortChanged;

  const ReferralSortOptions({
    super.key,
    required this.currentOption,
    required this.isAscending,
    required this.onSortChanged,
  });

  @override
  Widget build(BuildContext context) {
    return Container(
      height: 50,
      child: ListView.builder(
        scrollDirection: Axis.horizontal,
        itemCount: ReferralSortOption.values.length,
        itemBuilder: (context, index) {
          final option = ReferralSortOption.values[index];
          final isSelected = option == currentOption;
          
          return Padding(
            padding: EdgeInsets.only(
              left: index == 0 ? 0 : 8,
              right: index == ReferralSortOption.values.length - 1 ? 0 : 0,
            ),
            child: _buildSortChip(option, isSelected),
          );
        },
      ),
    );
  }

  /// Build individual sort chip
  Widget _buildSortChip(ReferralSortOption option, bool isSelected) {
    return GestureDetector(
      onTap: () => onSortChanged(option),
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
        decoration: BoxDecoration(
          gradient: isSelected ? AppTheme.primaryGradient : null,
          color: isSelected ? null : AppTheme.darkCard,
          borderRadius: BorderRadius.circular(25),
          border: Border.all(
            color: isSelected 
                ? AppTheme.primaryColor
                : AppTheme.darkBorder.withValues(alpha: 0.3),
            width: 1,
          ),
          boxShadow: isSelected
              ? [
                  BoxShadow(
                    color: AppTheme.primaryColor.withValues(alpha: 0.3),
                    blurRadius: 8,
                    offset: const Offset(0, 2),
                  ),
                ]
              : null,
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(
              option.icon,
              color: isSelected 
                  ? Colors.white
                  : AppTheme.darkSecondaryText,
              size: 16,
            ),
            const SizedBox(width: 6),
            Text(
              option.displayName,
              style: TextStyle(
                color: isSelected 
                    ? Colors.white
                    : AppTheme.darkPrimaryText,
                fontSize: 12,
                fontWeight: isSelected ? FontWeight.w600 : FontWeight.w500,
              ),
            ),
            if (isSelected) ...[
              const SizedBox(width: 4),
              Icon(
                isAscending ? Icons.arrow_upward : Icons.arrow_downward,
                color: Colors.white,
                size: 12,
              ),
            ],
          ],
        ),
      ),
    );
  }
}
