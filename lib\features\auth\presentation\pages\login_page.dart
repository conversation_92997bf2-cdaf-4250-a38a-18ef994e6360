import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';
import '../../../../shared/themes/app_theme.dart';
import '../providers/auth_providers.dart';
import '../../domain/auth_state.dart';

class LoginPage extends ConsumerWidget {
  const LoginPage({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final loginState = ref.watch(loginStateProvider);
    final authState = ref.watch(authStateProvider);

    // Listen to auth state changes
    ref.listen<AuthState>(authStateProvider, (previous, next) {
      if (next is AuthAuthenticated) {
        context.go('/dashboard');
      } else if (next is AuthError) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(content: Text(next.message), backgroundColor: Colors.red),
        );
        ref.read(authStateProvider.notifier).clearError();
      }
    });

    return Scaffold(
      body: SafeArea(
        child: Padding(
          padding: const EdgeInsets.all(24.0),
          child: Column(
            mainAxisAlignment: MainAxisAlignment.center,
            crossAxisAlignment: CrossAxisAlignment.stretch,
            children: [
              // Logo and Title
              Column(
                children: [
                  Container(
                    width: 120,
                    height: 120,
                    decoration: BoxDecoration(
                      color: Theme.of(context).primaryColor,
                      borderRadius: BorderRadius.circular(60),
                    ),
                    child: const Icon(
                      Icons.home_work,
                      size: 60,
                      color: Colors.white,
                    ),
                  ),
                  const SizedBox(height: 24),
                  Text(
                    'Rama Samriddhi',
                    style: Theme.of(context).textTheme.headlineMedium?.copyWith(
                      fontWeight: FontWeight.w900, // Extra bold
                      color: const Color(0xFFFF4500), // Bright Bold Orange
                      letterSpacing: 1.0,
                    ),
                  ),
                  const SizedBox(height: 8),
                  Text(
                    'Real Estate Marketing Network',
                    style: Theme.of(
                      context,
                    ).textTheme.bodyLarge?.copyWith(color: Colors.grey[600]),
                  ),
                ],
              ),

              const SizedBox(height: 48),

              // Error Message
              if (loginState.errorMessage != null)
                Container(
                  padding: const EdgeInsets.all(12),
                  margin: const EdgeInsets.only(bottom: 16),
                  decoration: BoxDecoration(
                    color: Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: Colors.red.withValues(alpha: 0.3),
                    ),
                  ),
                  child: Text(
                    loginState.errorMessage!,
                    style: const TextStyle(color: Colors.red),
                  ),
                ),

              // Login Form
              TextField(
                onChanged: (value) =>
                    ref.read(loginStateProvider.notifier).updateEmail(value),
                decoration: const InputDecoration(
                  labelText: 'Email',
                  prefixIcon: Icon(Icons.email),
                ),
                keyboardType: TextInputType.emailAddress,
                enabled: !loginState.isLoading,
              ),

              const SizedBox(height: 16),

              TextField(
                onChanged: (value) =>
                    ref.read(loginStateProvider.notifier).updatePassword(value),
                decoration: InputDecoration(
                  labelText: 'Password',
                  prefixIcon: const Icon(Icons.lock),
                  suffixIcon: IconButton(
                    icon: Icon(
                      loginState.obscurePassword
                          ? Icons.visibility_off
                          : Icons.visibility,
                    ),
                    onPressed: () => ref
                        .read(loginStateProvider.notifier)
                        .togglePasswordVisibility(),
                  ),
                ),
                obscureText: loginState.obscurePassword,
                enabled: !loginState.isLoading,
              ),

              const SizedBox(height: 16),

              // Remember Me
              Row(
                children: [
                  Checkbox(
                    value: loginState.rememberMe,
                    onChanged: loginState.isLoading
                        ? null
                        : (value) => ref
                              .read(loginStateProvider.notifier)
                              .toggleRememberMe(),
                  ),
                  const Text('Remember me'),
                ],
              ),

              const SizedBox(height: 24),

              ElevatedButton(
                onPressed: loginState.isLoading || authState is AuthLoading
                    ? null
                    : () => _handleLogin(context, ref),
                child: Padding(
                  padding: const EdgeInsets.symmetric(vertical: 16),
                  child: loginState.isLoading || authState is AuthLoading
                      ? const SizedBox(
                          height: 20,
                          width: 20,
                          child: CircularProgressIndicator(strokeWidth: 2),
                        )
                      : const Text('Login'),
                ),
              ),

              const SizedBox(height: 16),

              // Demo Login Button (for testing)
              if (kDebugMode) ...[
                SizedBox(
                  width: double.infinity,
                  child: OutlinedButton(
                    onPressed: () => _demoLogin(context, ref),
                    style: OutlinedButton.styleFrom(
                      side: BorderSide(color: AppTheme.primaryColor),
                      padding: const EdgeInsets.symmetric(vertical: 16),
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: Text(
                      'Demo Login (Admin)',
                      style: TextStyle(
                        fontSize: 16,
                        fontWeight: FontWeight.w600,
                        color: AppTheme.primaryColor,
                      ),
                    ),
                  ),
                ),
                const SizedBox(height: 16),
              ],

              TextButton(
                onPressed: loginState.isLoading
                    ? null
                    : () => context.go('/register'),
                child: const Text('Don\'t have an account? Register'),
              ),

              const SizedBox(height: 16),

              TextButton(
                onPressed: loginState.isLoading
                    ? null
                    : () => _showForgotPasswordDialog(context, ref),
                child: const Text('Forgot Password?'),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _handleLogin(BuildContext context, WidgetRef ref) {
    final loginNotifier = ref.read(loginStateProvider.notifier);
    final authNotifier = ref.read(authStateProvider.notifier);
    final loginState = ref.read(loginStateProvider);

    if (kDebugMode) {
      print('Login attempt: ${loginState.email}');
    }

    // Validate form
    final error = loginNotifier.validateForm();
    if (error != null) {
      if (kDebugMode) {
        print('Login validation error: $error');
      }
      loginNotifier.setError(error);
      return;
    }

    if (kDebugMode) {
      print('Login validation passed, attempting sign in...');
    }

    // Perform login
    authNotifier.signIn(loginState.email, loginState.password);
  }

  void _demoLogin(BuildContext context, WidgetRef ref) {
    final authNotifier = ref.read(authStateProvider.notifier);

    if (kDebugMode) {
      print('Demo login: Logging in as admin');
    }

    // Directly sign in with admin credentials
    authNotifier.signIn('<EMAIL>', 'admin123');
  }

  void _showForgotPasswordDialog(BuildContext context, WidgetRef ref) {
    final emailController = TextEditingController();

    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Reset Password'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Enter your email address to receive a password reset link.',
            ),
            const SizedBox(height: 16),
            TextField(
              controller: emailController,
              decoration: const InputDecoration(
                labelText: 'Email',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () async {
              if (emailController.text.isNotEmpty) {
                final success = await ref
                    .read(authStateProvider.notifier)
                    .sendPasswordResetEmail(emailController.text);

                if (context.mounted) {
                  Navigator.of(context).pop();
                  ScaffoldMessenger.of(context).showSnackBar(
                    SnackBar(
                      content: Text(
                        success
                            ? 'Password reset email sent!'
                            : 'Failed to send reset email',
                      ),
                      backgroundColor: success ? Colors.green : Colors.red,
                    ),
                  );
                }
              }
            },
            child: const Text('Send'),
          ),
        ],
      ),
    );
  }
}
