import 'package:flutter/foundation.dart';
import 'package:url_launcher/url_launcher.dart';

/// Service for handling QR code generation and deep link management
class QRCodeService {
  static const String _baseUrl = kDebugMode 
      ? 'http://localhost:8080' 
      : 'https://rama-samriddhi.web.app';
  
  static const String _onboardingPath = '/onboarding';
  
  /// Generate referral URL for QR code
  static String generateReferralUrl(String referralCode) {
    return '$_baseUrl$_onboardingPath?ref=$referralCode';
  }
  
  /// Generate QR code data (URL) for a referral code
  static String generateQRCodeData(String referralCode) {
    return generateReferralUrl(referralCode);
  }
  
  /// Parse referral code from URL
  static String? parseReferralCodeFromUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.queryParameters['ref'];
    } catch (e) {
      if (kDebugMode) {
        print('Error parsing referral code from URL: $e');
      }
      return null;
    }
  }
  
  /// Launch referral URL
  static Future<bool> launchReferralUrl(String referralCode) async {
    final url = generateReferralUrl(referralCode);
    try {
      final uri = Uri.parse(url);
      return await launchUrl(uri, mode: LaunchMode.externalApplication);
    } catch (e) {
      if (kDebugMode) {
        print('Error launching referral URL: $e');
      }
      return false;
    }
  }
  
  /// Share referral link via platform sharing
  static Future<void> shareReferralLink(String referralCode, String agentName) async {
    final url = generateReferralUrl(referralCode);
    final message = '''
🎯 Join Rama Samriddhi Real Estate!

Hi! I'm $agentName, and I'd like to invite you to join our amazing real estate team.

✨ Use my referral code: $referralCode
🔗 Or click this link: $url

Benefits of joining:
• Earn commissions on property sales
• Build your own team
• Professional training & support
• Flexible working hours

Ready to start your real estate journey? Let's grow together! 🚀

#RamaSamriddhi #RealEstate #MLM #Opportunity
''';

    try {
      // For web, we'll copy to clipboard and show instructions
      if (kIsWeb) {
        // Web sharing will be handled by the UI component
        return;
      }
      
      // For mobile, use platform sharing
      // This would require share_plus package integration
      if (kDebugMode) {
        print('Sharing message: $message');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error sharing referral link: $e');
      }
    }
  }
  
  /// Validate if URL is a valid referral link
  static bool isValidReferralUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.host.contains('rama-samriddhi') || 
             uri.host.contains('localhost') &&
             uri.path == _onboardingPath &&
             uri.queryParameters.containsKey('ref');
    } catch (e) {
      return false;
    }
  }
  
  /// Extract domain from current environment
  static String getCurrentDomain() {
    return kDebugMode ? 'localhost:8080' : 'rama-samriddhi.web.app';
  }
  
  /// Generate shareable text for social media
  static String generateSocialShareText(String referralCode, String agentName) {
    return '''
🏠 Join Rama Samriddhi Real Estate Team! 

I'm $agentName and I'm looking for motivated individuals to join our growing real estate network.

💼 What you'll get:
✅ Commission on every sale
✅ Build your own team
✅ Professional training
✅ Flexible schedule

🎯 Use referral code: $referralCode
🔗 Join here: ${generateReferralUrl(referralCode)}

#RealEstate #MLM #Opportunity #RamaSamriddhi
''';
  }
  
  /// Generate WhatsApp share URL
  static String generateWhatsAppShareUrl(String referralCode, String agentName) {
    final message = generateSocialShareText(referralCode, agentName);
    final encodedMessage = Uri.encodeComponent(message);
    return 'https://wa.me/?text=$encodedMessage';
  }
  
  /// Generate Telegram share URL
  static String generateTelegramShareUrl(String referralCode, String agentName) {
    final message = generateSocialShareText(referralCode, agentName);
    final encodedMessage = Uri.encodeComponent(message);
    return 'https://t.me/share/url?url=${generateReferralUrl(referralCode)}&text=$encodedMessage';
  }
}
