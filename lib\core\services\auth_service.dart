import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import '../models/user_model.dart';
import '../constants/app_constants.dart';
import '../../features/onboarding/services/onboarding_service.dart';

/// Authentication service for Firebase Auth operations
class AuthService {
  static final FirebaseAuth _auth = FirebaseAuth.instance;
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get current user
  static User? get currentUser => _auth.currentUser;

  /// Get current user stream
  static Stream<User?> get authStateChanges => _auth.authStateChanges();

  /// Register new user with email and password
  static Future<AuthResult> registerWithEmailAndPassword({
    required String email,
    required String password,
    required String name,
    required String phoneNumber,
    String? referralCode,
  }) async {
    try {
      // Create user with Firebase Auth
      final UserCredential userCredential = await _auth
          .createUserWithEmailAndPassword(email: email, password: password);

      final User? user = userCredential.user;
      if (user == null) {
        return AuthResult.failure('Failed to create user account');
      }

      // Update display name
      await user.updateDisplayName(name);

      // Find upline user if referral code provided
      String? uplineId;
      int level = 0;

      if (referralCode != null && referralCode.isNotEmpty) {
        final uplineResult = await _findUserByReferralCode(referralCode);
        if (uplineResult != null) {
          uplineId = uplineResult.id;
          level = uplineResult.level + 1;
        }
      }

      // Create user document in Firestore
      final userModel = UserModel(
        id: user.uid,
        email: email,
        name: name,
        phoneNumber: phoneNumber,
        role: AppConstants.agentRole,
        uplineId: uplineId,
        level: level,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .set(userModel.toFirestore());

      // Update upline's downline list if applicable
      if (uplineId != null) {
        await _addToUplineDownline(uplineId, user.uid);
      }

      // Send email verification
      await user.sendEmailVerification();

      return AuthResult.success(userModel);
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('Registration failed: ${e.toString()}');
    }
  }

  /// Sign in with email and password
  static Future<AuthResult> signInWithEmailAndPassword({
    required String email,
    required String password,
  }) async {
    // Development mode bypass for web
    if (kIsWeb && kDebugMode) {
      return _handleDevelopmentLogin(email, password);
    }

    try {
      final UserCredential userCredential = await _auth
          .signInWithEmailAndPassword(email: email, password: password);

      final User? user = userCredential.user;
      if (user == null) {
        return AuthResult.failure('Failed to sign in');
      }

      // Get user data from Firestore
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .get();

      if (!userDoc.exists) {
        return AuthResult.failure('User profile not found');
      }

      final userModel = UserModel.fromFirestore(userDoc);

      // Check if user is active
      if (!userModel.isActive) {
        await signOut();
        return AuthResult.failure('Account is deactivated. Contact admin.');
      }

      return AuthResult.success(userModel);
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('Sign in failed: ${e.toString()}');
    }
  }

  /// Handle development mode login with dummy credentials
  static Future<AuthResult> _handleDevelopmentLogin(
    String email,
    String password,
  ) async {
    // Development credentials from RUN_APP_INSTRUCTIONS.md
    final Map<String, Map<String, dynamic>> devCredentials = {
      '<EMAIL>': {
        'password': 'admin123',
        'role': AppConstants.adminRole,
        'name': 'Admin User',
        'id': 'dev-admin-001',
      },
      '<EMAIL>': {
        'password': 'agent123',
        'role': AppConstants.agentRole,
        'name': 'Agent User',
        'id': 'dev-agent-001',
      },
    };

    final userCreds = devCredentials[email];
    if (userCreds == null || userCreds['password'] != password) {
      return AuthResult.failure('Invalid development credentials');
    }

    // Create mock user model for development
    final userModel = UserModel(
      id: userCreds['id'],
      email: email,
      name: userCreds['name'],
      role: userCreds['role'],
      isActive: true,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
      phoneNumber: '+91-9876543210',
      level: 0,
      totalCommissions: userCreds['role'] == AppConstants.adminRole
          ? 50000.0
          : 25000.0,
      totalStars: userCreds['role'] == AppConstants.adminRole ? 100 : 50,
      totalBonuses: userCreds['role'] == AppConstants.adminRole ? 10 : 5,
    );

    if (kDebugMode) {
      print(
        'Development login successful for: ${userModel.name} (${userModel.role})',
      );
    }

    return AuthResult.success(userModel);
  }

  /// Sign out current user
  static Future<void> signOut() async {
    await _auth.signOut();
  }

  /// Send password reset email
  static Future<AuthResult> sendPasswordResetEmail(String email) async {
    try {
      await _auth.sendPasswordResetEmail(email: email);
      return AuthResult.success(null, message: 'Password reset email sent');
    } on FirebaseAuthException catch (e) {
      return AuthResult.failure(_getAuthErrorMessage(e));
    } catch (e) {
      return AuthResult.failure('Failed to send reset email: ${e.toString()}');
    }
  }

  /// Get current user profile
  static Future<UserModel?> getCurrentUserProfile() async {
    final user = currentUser;
    if (user == null) return null;

    try {
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.uid)
          .get();

      if (userDoc.exists) {
        return UserModel.fromFirestore(userDoc);
      }
    } catch (e) {
      print('Error getting user profile: $e');
    }
    return null;
  }

  /// Update user profile
  static Future<AuthResult> updateUserProfile(UserModel updatedUser) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(updatedUser.id)
          .update(
            updatedUser.copyWith(updatedAt: DateTime.now()).toFirestore(),
          );

      return AuthResult.success(updatedUser);
    } catch (e) {
      return AuthResult.failure('Failed to update profile: ${e.toString()}');
    }
  }

  /// Generate referral code for user
  static String generateReferralCode(String userId) {
    // Simple referral code generation - can be enhanced
    return userId.substring(0, 8).toUpperCase();
  }

  /// Find user by referral code
  static Future<UserModel?> _findUserByReferralCode(String referralCode) async {
    try {
      // For now, we'll use the first 8 characters of user ID as referral code
      // In production, you might want a separate referral codes collection
      final userId = referralCode.toLowerCase();

      final querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('id', isGreaterThanOrEqualTo: userId)
          .where('id', isLessThan: '$userId\uf8ff')
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return UserModel.fromFirestore(querySnapshot.docs.first);
      }
    } catch (e) {
      print('Error finding user by referral code: $e');
    }
    return null;
  }

  /// Add user to upline's downline list
  static Future<void> _addToUplineDownline(
    String uplineId,
    String newUserId,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(uplineId)
          .update({
            'downlineIds': FieldValue.arrayUnion([newUserId]),
            'updatedAt': Timestamp.now(),
          });
    } catch (e) {
      print('Error updating upline downline: $e');
    }
  }

  /// Check if user needs onboarding
  static Future<bool> needsOnboarding(String userId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // In development, always check if user has completed onboarding
        return !(await OnboardingService.hasCompletedOnboarding(userId));
      }

      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (!userDoc.exists) return true;

      final userData = userDoc.data()!;
      return userData['additionalInfo']?['onboardingCompleted'] != true;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking onboarding status: $e');
      }
      return true; // Default to needing onboarding if error
    }
  }

  /// Get user-friendly error messages
  static String _getAuthErrorMessage(FirebaseAuthException e) {
    switch (e.code) {
      case 'weak-password':
        return 'The password provided is too weak.';
      case 'email-already-in-use':
        return 'An account already exists for this email.';
      case 'user-not-found':
        return 'No user found for this email.';
      case 'wrong-password':
        return 'Wrong password provided.';
      case 'invalid-email':
        return 'The email address is not valid.';
      case 'user-disabled':
        return 'This user account has been disabled.';
      case 'too-many-requests':
        return 'Too many requests. Try again later.';
      case 'operation-not-allowed':
        return 'Email/password accounts are not enabled.';
      default:
        return e.message ?? 'An authentication error occurred.';
    }
  }

  /// Get all users (Admin only)
  static Future<List<UserModel>> getAllUsers() async {
    try {
      final snapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs.map((doc) => UserModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting all users: $e');
      return [];
    }
  }

  /// Update user (Admin only)
  static Future<void> updateUser(UserModel user) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .update(user.toFirestore());
    } catch (e) {
      print('Error updating user: $e');
      throw Exception('Failed to update user: $e');
    }
  }
}

/// Authentication result wrapper
class AuthResult {
  final bool isSuccess;
  final UserModel? user;
  final String? message;

  AuthResult._(this.isSuccess, this.user, this.message);

  factory AuthResult.success(UserModel? user, {String? message}) {
    return AuthResult._(true, user, message);
  }

  factory AuthResult.failure(String message) {
    return AuthResult._(false, null, message);
  }
}
