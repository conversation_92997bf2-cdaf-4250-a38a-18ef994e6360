import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/models/property_model.dart';
import '../../../core/models/user_model.dart';
import '../models/commission_enums.dart';
import '../models/enhanced_commission_model.dart';

/// Advanced commission calculation service
class CommissionCalculationService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Commission configuration
  static const Map<CommissionType, double> _basePercentages = {
    CommissionType.directSale: 2.5, // 2.5% of property value
    CommissionType.referralBonus: 0.5, // 0.5% of referred agent's first sale
    CommissionType.levelBonus: 0.3, // 0.3% per level
    CommissionType.performanceBonus: 1.0, // 1% for performance targets
    CommissionType.teamBonus: 0.8, // 0.8% for team performance
    CommissionType.leadershipBonus: 1.5, // 1.5% for leadership
    CommissionType.achievementBonus: 2.0, // 2% for special achievements
  };

  static const int _maxLevels = 5; // Maximum levels for MLM commission

  /// Calculate commission for property sale
  static Future<List<EnhancedCommissionModel>> calculatePropertySaleCommissions({
    required PropertyModel property,
    required UserModel sellingAgent,
  }) async {
    try {
      final commissions = <EnhancedCommissionModel>[];
      final now = DateTime.now();

      // 1. Direct sale commission for selling agent
      final directCommission = await _calculateDirectSaleCommission(
        property: property,
        agent: sellingAgent,
        calculatedAt: now,
      );
      commissions.add(directCommission);

      // 2. Level-based commissions for upline agents
      final levelCommissions = await _calculateLevelCommissions(
        property: property,
        sellingAgent: sellingAgent,
        calculatedAt: now,
      );
      commissions.addAll(levelCommissions);

      // 3. Performance bonuses if applicable
      final performanceBonuses = await _calculatePerformanceBonuses(
        property: property,
        sellingAgent: sellingAgent,
        calculatedAt: now,
      );
      commissions.addAll(performanceBonuses);

      if (kIsWeb && kDebugMode) {
        // Development mode - log calculations
        print('Calculated ${commissions.length} commissions for property ${property.id}');
        for (final commission in commissions) {
          print('  ${commission.type.displayName}: ${commission.formattedAmount} for ${commission.agentName}');
        }
      }

      return commissions;
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating property sale commissions: $e');
      }
      return [];
    }
  }

  /// Calculate direct sale commission
  static Future<EnhancedCommissionModel> _calculateDirectSaleCommission({
    required PropertyModel property,
    required UserModel agent,
    required DateTime calculatedAt,
  }) async {
    final tier = await _getAgentTier(agent.id);
    final basePercentage = _basePercentages[CommissionType.directSale]!;
    final tierMultiplier = tier.multiplier;
    final finalPercentage = basePercentage * tierMultiplier;
    final baseAmount = property.price * (basePercentage / 100);
    final finalAmount = property.price * (finalPercentage / 100);

    return EnhancedCommissionModel(
      id: '', // Will be set when saved to Firestore
      agentId: agent.id,
      agentName: agent.name,
      propertyId: property.id,
      propertyTitle: property.title,
      type: CommissionType.directSale,
      status: CommissionStatus.pending,
      amount: finalAmount,
      baseAmount: baseAmount,
      percentage: finalPercentage,
      tier: tier,
      tierMultiplier: tierMultiplier,
      level: 0,
      calculatedAt: calculatedAt,
      metadata: {
        'propertyPrice': property.price,
        'propertyType': property.type,
        'propertyLocation': property.location,
      },
      createdAt: calculatedAt,
      updatedAt: calculatedAt,
    );
  }

  /// Calculate level-based commissions for upline agents
  static Future<List<EnhancedCommissionModel>> _calculateLevelCommissions({
    required PropertyModel property,
    required UserModel sellingAgent,
    required DateTime calculatedAt,
  }) async {
    final commissions = <EnhancedCommissionModel>[];
    
    try {
      String? currentUplineId = sellingAgent.uplineId;
      int level = 1;

      while (currentUplineId != null && level <= _maxLevels) {
        final uplineAgent = await _getAgentById(currentUplineId);
        if (uplineAgent == null) break;

        final tier = await _getAgentTier(uplineAgent.id);
        final basePercentage = _basePercentages[CommissionType.levelBonus]!;
        final levelMultiplier = _getLevelMultiplier(level);
        final tierMultiplier = tier.multiplier;
        final finalPercentage = basePercentage * levelMultiplier * tierMultiplier;
        final baseAmount = property.price * (basePercentage * levelMultiplier / 100);
        final finalAmount = property.price * (finalPercentage / 100);

        final commission = EnhancedCommissionModel(
          id: '',
          agentId: uplineAgent.id,
          agentName: uplineAgent.name,
          propertyId: property.id,
          propertyTitle: property.title,
          type: CommissionType.levelBonus,
          status: CommissionStatus.pending,
          amount: finalAmount,
          baseAmount: baseAmount,
          percentage: finalPercentage,
          tier: tier,
          tierMultiplier: tierMultiplier,
          sourceAgentId: sellingAgent.id,
          sourceAgentName: sellingAgent.name,
          level: level,
          calculatedAt: calculatedAt,
          metadata: {
            'propertyPrice': property.price,
            'sellingAgentId': sellingAgent.id,
            'levelMultiplier': levelMultiplier,
          },
          createdAt: calculatedAt,
          updatedAt: calculatedAt,
        );

        commissions.add(commission);

        // Move to next level
        currentUplineId = uplineAgent.uplineId;
        level++;
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating level commissions: $e');
      }
    }

    return commissions;
  }

  /// Calculate performance bonuses
  static Future<List<EnhancedCommissionModel>> _calculatePerformanceBonuses({
    required PropertyModel property,
    required UserModel sellingAgent,
    required DateTime calculatedAt,
  }) async {
    final bonuses = <EnhancedCommissionModel>[];

    try {
      // Check if agent qualifies for performance bonus
      final monthlyTarget = await _getMonthlyTarget(sellingAgent.id);
      final monthlySales = await _getMonthlySales(sellingAgent.id);

      if (monthlySales >= monthlyTarget) {
        final tier = await _getAgentTier(sellingAgent.id);
        final basePercentage = _basePercentages[CommissionType.performanceBonus]!;
        final tierMultiplier = tier.multiplier;
        final finalPercentage = basePercentage * tierMultiplier;
        final baseAmount = property.price * (basePercentage / 100);
        final finalAmount = property.price * (finalPercentage / 100);

        final bonus = EnhancedCommissionModel(
          id: '',
          agentId: sellingAgent.id,
          agentName: sellingAgent.name,
          propertyId: property.id,
          propertyTitle: property.title,
          type: CommissionType.performanceBonus,
          status: CommissionStatus.pending,
          amount: finalAmount,
          baseAmount: baseAmount,
          percentage: finalPercentage,
          tier: tier,
          tierMultiplier: tierMultiplier,
          level: 0,
          calculatedAt: calculatedAt,
          metadata: {
            'monthlyTarget': monthlyTarget,
            'monthlySales': monthlySales,
            'targetAchievement': (monthlySales / monthlyTarget * 100).round(),
          },
          createdAt: calculatedAt,
          updatedAt: calculatedAt,
        );

        bonuses.add(bonus);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating performance bonuses: $e');
      }
    }

    return bonuses;
  }

  /// Get agent tier based on performance
  static Future<CommissionTier> _getAgentTier(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock tier
        return CommissionTier.silver;
      }

      // Get agent's total sales for tier calculation
      final totalSales = await _getAgentTotalSales(agentId);
      
      for (final tier in CommissionTier.values.reversed) {
        if (totalSales >= tier.minimumSales) {
          return tier;
        }
      }
      
      return CommissionTier.bronze;
    } catch (e) {
      return CommissionTier.bronze;
    }
  }

  /// Get level multiplier for commission calculation
  static double _getLevelMultiplier(int level) {
    switch (level) {
      case 1:
        return 1.0;
      case 2:
        return 0.8;
      case 3:
        return 0.6;
      case 4:
        return 0.4;
      case 5:
        return 0.2;
      default:
        return 0.0;
    }
  }

  /// Get agent by ID
  static Future<UserModel?> _getAgentById(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock agent
        return UserModel(
          id: agentId,
          email: '<EMAIL>',
          name: 'Mock Agent',
          phoneNumber: '+91-98765-43210',
          role: AppConstants.agentRole,
          level: 1,
          isActive: true,
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );
      }

      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .get();

      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get agent's total sales
  static Future<double> _getAgentTotalSales(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock sales
        return 2500000; // 25L for silver tier
      }

      // Query commissions to calculate total sales
      final snapshot = await _firestore
          .collection('commissions')
          .where('agentId', isEqualTo: agentId)
          .where('type', isEqualTo: CommissionType.directSale.value)
          .where('status', isEqualTo: CommissionStatus.paid.value)
          .get();

      double totalSales = 0;
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final propertyPrice = data['metadata']?['propertyPrice'] ?? 0;
        totalSales += propertyPrice.toDouble();
      }

      return totalSales;
    } catch (e) {
      return 0;
    }
  }

  /// Get monthly target for agent
  static Future<double> _getMonthlyTarget(String agentId) async {
    // Mock implementation - in real app, this would come from agent settings
    return 1000000; // 10L monthly target
  }

  /// Get monthly sales for agent
  static Future<double> _getMonthlySales(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock sales
        return 1200000; // 12L (above target)
      }

      final now = DateTime.now();
      final monthStart = DateTime(now.year, now.month, 1);
      
      final snapshot = await _firestore
          .collection('commissions')
          .where('agentId', isEqualTo: agentId)
          .where('type', isEqualTo: CommissionType.directSale.value)
          .where('calculatedAt', isGreaterThanOrEqualTo: Timestamp.fromDate(monthStart))
          .get();

      double monthlySales = 0;
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final propertyPrice = data['metadata']?['propertyPrice'] ?? 0;
        monthlySales += propertyPrice.toDouble();
      }

      return monthlySales;
    } catch (e) {
      return 0;
    }
  }

  /// Save commissions to Firestore
  static Future<List<String>> saveCommissions(List<EnhancedCommissionModel> commissions) async {
    final savedIds = <String>[];

    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - simulate saving
        for (int i = 0; i < commissions.length; i++) {
          final mockId = 'commission_${DateTime.now().millisecondsSinceEpoch}_$i';
          savedIds.add(mockId);
        }
        return savedIds;
      }

      final batch = _firestore.batch();
      
      for (final commission in commissions) {
        final docRef = _firestore.collection('commissions').doc();
        batch.set(docRef, commission.toFirestore());
        savedIds.add(docRef.id);
      }

      await batch.commit();
      return savedIds;
    } catch (e) {
      if (kDebugMode) {
        print('Error saving commissions: $e');
      }
      return [];
    }
  }

  /// Calculate referral bonus for new agent signup
  static Future<EnhancedCommissionModel?> calculateReferralBonus({
    required UserModel referringAgent,
    required UserModel newAgent,
  }) async {
    try {
      final tier = await _getAgentTier(referringAgent.id);
      final baseAmount = 5000.0; // Fixed referral bonus
      final tierMultiplier = tier.multiplier;
      final finalAmount = baseAmount * tierMultiplier;

      return EnhancedCommissionModel(
        id: '',
        agentId: referringAgent.id,
        agentName: referringAgent.name,
        type: CommissionType.referralBonus,
        status: CommissionStatus.pending,
        amount: finalAmount,
        baseAmount: baseAmount,
        percentage: 0, // Fixed amount, not percentage
        tier: tier,
        tierMultiplier: tierMultiplier,
        sourceAgentId: newAgent.id,
        sourceAgentName: newAgent.name,
        level: 0,
        calculatedAt: DateTime.now(),
        metadata: {
          'newAgentId': newAgent.id,
          'newAgentEmail': newAgent.email,
          'referralDate': DateTime.now().toIso8601String(),
        },
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating referral bonus: $e');
      }
      return null;
    }
  }
}
