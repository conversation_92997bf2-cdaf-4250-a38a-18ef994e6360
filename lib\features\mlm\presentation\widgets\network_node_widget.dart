import 'package:flutter/material.dart';
import 'package:cached_network_image/cached_network_image.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../models/network_models.dart';

/// Individual network node widget for tree visualization
class NetworkNodeWidget extends StatefulWidget {
  final NetworkNode node;
  final NetworkVisualizationConfig config;
  final bool isSelected;
  final bool isHovered;
  final VoidCallback? onTap;
  final VoidCallback? onLongPress;
  final Function(bool)? onHover;

  const NetworkNodeWidget({
    super.key,
    required this.node,
    required this.config,
    this.isSelected = false,
    this.isHovered = false,
    this.onTap,
    this.onLongPress,
    this.onHover,
  });

  @override
  State<NetworkNodeWidget> createState() => _NetworkNodeWidgetState();
}

class _NetworkNodeWidgetState extends State<NetworkNodeWidget>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  late Animation<double> _glowAnimation;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    
    _scaleAnimation = Tween<double>(
      begin: 1.0,
      end: 1.1,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
    
    _glowAnimation = Tween<double>(
      begin: 0.0,
      end: 1.0,
    ).animate(CurvedAnimation(
      parent: _animationController,
      curve: Curves.easeInOut,
    ));
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  void didUpdateWidget(NetworkNodeWidget oldWidget) {
    super.didUpdateWidget(oldWidget);
    
    if (widget.isSelected || widget.isHovered) {
      _animationController.forward();
    } else {
      _animationController.reverse();
    }
  }

  @override
  Widget build(BuildContext context) {
    return MouseRegion(
      onEnter: (_) => widget.onHover?.call(true),
      onExit: (_) => widget.onHover?.call(false),
      child: GestureDetector(
        onTap: widget.onTap,
        onLongPress: widget.onLongPress,
        child: AnimatedBuilder(
          animation: _animationController,
          builder: (context, child) {
            return Transform.scale(
              scale: _scaleAnimation.value,
              child: Container(
                width: widget.config.nodeSize,
                height: widget.config.nodeSize,
                child: Stack(
                  children: [
                    // Glow effect
                    if (widget.isSelected || widget.isHovered)
                      _buildGlowEffect(),
                    
                    // Main node
                    _buildMainNode(),
                    
                    // Tier badge
                    if (widget.config.showTierBadges)
                      _buildTierBadge(),
                    
                    // Performance indicator
                    if (widget.config.showPerformanceColors)
                      _buildPerformanceIndicator(),
                    
                    // Children count badge
                    if (widget.node.hasChildren)
                      _buildChildrenBadge(),
                  ],
                ),
              ),
            );
          },
        ),
      ),
    );
  }

  /// Build glow effect for selected/hovered nodes
  Widget _buildGlowEffect() {
    return Positioned.fill(
      child: AnimatedBuilder(
        animation: _glowAnimation,
        builder: (context, child) {
          return Container(
            decoration: BoxDecoration(
              shape: BoxShape.circle,
              boxShadow: [
                BoxShadow(
                  color: widget.isSelected
                      ? AppTheme.primaryColor.withValues(alpha: 0.6 * _glowAnimation.value)
                      : AppTheme.secondaryColor.withValues(alpha: 0.4 * _glowAnimation.value),
                  blurRadius: 20 * _glowAnimation.value,
                  spreadRadius: 5 * _glowAnimation.value,
                ),
              ],
            ),
          );
        },
      ),
    );
  }

  /// Build main node container
  Widget _buildMainNode() {
    return Container(
      width: widget.config.nodeSize,
      height: widget.config.nodeSize,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: _getNodeGradient(),
        border: Border.all(
          color: widget.isSelected
              ? AppTheme.primaryColor
              : widget.isHovered
                  ? AppTheme.secondaryColor
                  : widget.node.tier.color,
          width: widget.isSelected ? 3 : 2,
        ),
        boxShadow: [
          BoxShadow(
            color: widget.node.tier.color.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: _buildNodeContent(),
    );
  }

  /// Get node gradient based on configuration
  Gradient _getNodeGradient() {
    if (widget.config.showPerformanceColors) {
      final performanceColor = widget.node.performance.performanceColor;
      return LinearGradient(
        colors: [
          performanceColor,
          performanceColor.withValues(alpha: 0.8),
        ],
        begin: Alignment.topLeft,
        end: Alignment.bottomRight,
      );
    }
    
    return LinearGradient(
      colors: [
        widget.node.tier.color,
        widget.node.tier.color.withValues(alpha: 0.8),
      ],
      begin: Alignment.topLeft,
      end: Alignment.bottomRight,
    );
  }

  /// Build node content (avatar or initials)
  Widget _buildNodeContent() {
    return ClipOval(
      child: widget.node.profileImageUrl != null
          ? CachedNetworkImage(
              imageUrl: widget.node.profileImageUrl!,
              width: widget.config.nodeSize,
              height: widget.config.nodeSize,
              fit: BoxFit.cover,
              placeholder: (context, url) => _buildInitialsAvatar(),
              errorWidget: (context, url, error) => _buildInitialsAvatar(),
            )
          : _buildInitialsAvatar(),
    );
  }

  /// Build initials avatar
  Widget _buildInitialsAvatar() {
    return Container(
      width: widget.config.nodeSize,
      height: widget.config.nodeSize,
      child: Center(
        child: Text(
          widget.node.initials,
          style: TextStyle(
            color: Colors.white,
            fontSize: widget.config.nodeSize * 0.3,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }

  /// Build tier badge
  Widget _buildTierBadge() {
    return Positioned(
      top: 0,
      right: 0,
      child: Container(
        width: 20,
        height: 20,
        decoration: BoxDecoration(
          color: widget.node.tier.color,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
          boxShadow: [
            BoxShadow(
              color: widget.node.tier.color.withValues(alpha: 0.3),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Center(
          child: Icon(
            widget.node.tier.icon,
            color: Colors.white,
            size: 10,
          ),
        ),
      ),
    );
  }

  /// Build performance indicator
  Widget _buildPerformanceIndicator() {
    return Positioned(
      bottom: 0,
      left: 0,
      child: Container(
        width: 16,
        height: 16,
        decoration: BoxDecoration(
          color: widget.node.performance.performanceColor,
          shape: BoxShape.circle,
          border: Border.all(color: Colors.white, width: 2),
        ),
        child: Center(
          child: Icon(
            widget.node.performance.performanceIcon,
            color: Colors.white,
            size: 8,
          ),
        ),
      ),
    );
  }

  /// Build children count badge
  Widget _buildChildrenBadge() {
    return Positioned(
      bottom: 0,
      right: 0,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 4, vertical: 2),
        decoration: BoxDecoration(
          color: AppTheme.primaryColor,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: Colors.white, width: 1),
        ),
        child: Text(
          '${widget.node.children.length}',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 8,
            fontWeight: FontWeight.bold,
          ),
        ),
      ),
    );
  }
}

/// Tooltip widget for network nodes
class NetworkNodeTooltip extends StatelessWidget {
  final NetworkNode node;
  final Widget child;

  const NetworkNodeTooltip({
    super.key,
    required this.node,
    required this.child,
  });

  @override
  Widget build(BuildContext context) {
    return Tooltip(
      message: _buildTooltipMessage(),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(8),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withValues(alpha: 0.2),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      textStyle: TextStyle(
        color: AppTheme.darkPrimaryText,
        fontSize: 12,
      ),
      child: child,
    );
  }

  String _buildTooltipMessage() {
    return '''${node.name}
${node.email}
Level: ${node.level}
Tier: ${node.tier.displayName}
Sales: ${node.formattedSales}
Team: ${node.totalNetworkSize - 1} members
Performance: ${node.performance.performanceDescription}''';
  }
}
