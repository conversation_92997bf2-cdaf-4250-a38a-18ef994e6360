import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_core/firebase_core.dart';
import 'package:fake_cloud_firestore/fake_cloud_firestore.dart';
import 'package:firebase_auth_mocks/firebase_auth_mocks.dart';

/// Test configuration and utilities
class TestConfig {
  static const String testUserEmail = '<EMAIL>';
  static const String testUserPassword = 'password123';
  static const String testUserName = 'Test User';
  static const String testUserPhone = '+919876543210';
  static const String testReferralCode = 'TEST1234';

  static const String adminEmail = '<EMAIL>';
  static const String adminPassword = 'admin123';
  static const String adminName = 'Admin User';

  /// Initialize Firebase for testing
  static Future<void> initializeFirebaseForTesting() async {
    TestWidgetsFlutterBinding.ensureInitialized();

    // Initialize Firebase with test configuration
    await Firebase.initializeApp(
      options: const FirebaseOptions(
        apiKey: 'test-api-key',
        appId: 'test-app-id',
        messagingSenderId: 'test-sender-id',
        projectId: 'test-project-id',
      ),
    );
  }

  /// Create a test widget wrapper with providers
  static Widget createTestWidget({
    required Widget child,
    List<Override>? overrides,
  }) {
    return ProviderScope(
      overrides: overrides ?? [],
      child: MaterialApp(
        home: Scaffold(body: child),
        theme: ThemeData(
          primarySwatch: Colors.blue,
          visualDensity: VisualDensity.adaptivePlatformDensity,
        ),
      ),
    );
  }

  /// Create mock Firestore with test data
  static FakeFirebaseFirestore createMockFirestore() {
    final firestore = FakeFirebaseFirestore();

    // Add test users
    firestore.collection('users').doc('test-user-id').set({
      'id': 'test-user-id',
      'name': testUserName,
      'email': testUserEmail,
      'phoneNumber': testUserPhone,
      'role': 'agent',
      'level': 0,
      'uplineId': null,
      'downlineIds': [],
      'totalStars': 5,
      'totalCommissions': 25000.0,
      'isActive': true,
      'isAdmin': false,
      'referralCode': testReferralCode,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
    });

    firestore.collection('users').doc('admin-user-id').set({
      'id': 'admin-user-id',
      'name': adminName,
      'email': adminEmail,
      'phoneNumber': '+919876543211',
      'role': 'admin',
      'level': 0,
      'uplineId': null,
      'downlineIds': [],
      'totalStars': 0,
      'totalCommissions': 0.0,
      'isActive': true,
      'isAdmin': true,
      'referralCode': 'ADMIN123',
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
    });

    // Add test properties
    firestore.collection('properties').doc('test-property-1').set({
      'id': 'test-property-1',
      'title': 'Luxury 3 BHK Apartment',
      'description': 'Beautiful apartment with modern amenities',
      'price': 5000000.0,
      'type': 'Residential',
      'status': 'For Sale',
      'location': 'Bandra West',
      'city': 'Mumbai',
      'state': 'Maharashtra',
      'pincode': '400050',
      'bedrooms': 3,
      'bathrooms': 2,
      'area': 1200.0,
      'imageUrls': ['https://example.com/image1.jpg'],
      'amenities': ['Parking', 'Gym', 'Swimming Pool'],
      'agentId': 'test-user-id',
      'agentName': testUserName,
      'isApproved': true,
      'isFeatured': false,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
    });

    firestore.collection('properties').doc('test-property-2').set({
      'id': 'test-property-2',
      'title': 'Commercial Office Space',
      'description': 'Prime commercial space in business district',
      'price': 15000000.0,
      'type': 'Commercial',
      'status': 'For Sale',
      'location': 'BKC',
      'city': 'Mumbai',
      'state': 'Maharashtra',
      'pincode': '400051',
      'bedrooms': 0,
      'bathrooms': 4,
      'area': 2500.0,
      'imageUrls': ['https://example.com/office1.jpg'],
      'amenities': ['Parking', 'Security', 'Elevator'],
      'agentId': 'test-user-id',
      'agentName': testUserName,
      'isApproved': true,
      'isFeatured': true,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
    });

    // Add test commissions
    firestore.collection('commissions').doc('test-commission-1').set({
      'id': 'test-commission-1',
      'agentId': 'test-user-id',
      'agentName': testUserName,
      'transactionId': 'test-transaction-1',
      'amount': 25000.0,
      'level': 0,
      'isPaid': true,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
    });

    // Add test stars
    firestore.collection('stars').doc('test-star-1').set({
      'id': 'test-star-1',
      'agentId': 'test-user-id',
      'agentName': testUserName,
      'transactionId': 'test-transaction-1',
      'source': 'direct_sale',
      'createdAt': DateTime.now(),
    });

    // Add test transactions
    firestore.collection('transactions').doc('test-transaction-1').set({
      'id': 'test-transaction-1',
      'agentId': 'test-user-id',
      'agentName': testUserName,
      'propertyId': 'test-property-1',
      'propertyTitle': 'Luxury 3 BHK Apartment',
      'amount': 5000000.0,
      'type': 'sale',
      'status': 'completed',
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
    });

    // Add test leads
    firestore.collection('leads').doc('test-lead-1').set({
      'id': 'test-lead-1',
      'agentId': 'test-user-id',
      'agentName': testUserName,
      'propertyId': 'test-property-1',
      'propertyTitle': 'Luxury 3 BHK Apartment',
      'customerName': 'John Customer',
      'customerPhone': '+919876543220',
      'customerEmail': '<EMAIL>',
      'status': 'new',
      'priority': 'medium',
      'source': 'whatsapp',
      'budgetMin': 4000000.0,
      'budgetMax': 6000000.0,
      'notes': 'Interested in 3 BHK apartments',
      'interactions': [],
      'followUpDate': DateTime.now().add(const Duration(days: 3)),
      'lastContactedAt': null,
      'createdAt': DateTime.now(),
      'updatedAt': DateTime.now(),
    });

    return firestore;
  }

  /// Create mock Firebase Auth
  static MockFirebaseAuth createMockAuth() {
    final auth = MockFirebaseAuth();

    // Add test users
    auth.createUserWithEmailAndPassword(
      email: testUserEmail,
      password: testUserPassword,
    );

    auth.createUserWithEmailAndPassword(
      email: adminEmail,
      password: adminPassword,
    );

    return auth;
  }

  /// Test data constants
  static const Map<String, dynamic> testPropertyData = {
    'id': 'test-property-id',
    'title': 'Test Property',
    'description': 'Test property description',
    'price': 1000000.0,
    'type': 'Residential',
    'status': 'For Sale',
    'location': 'Test Location',
    'city': 'Test City',
    'state': 'Test State',
    'pincode': '123456',
    'bedrooms': 2,
    'bathrooms': 1,
    'area': 800.0,
    'imageUrls': ['https://example.com/test.jpg'],
    'amenities': ['Parking'],
    'agentId': 'test-agent-id',
    'agentName': 'Test Agent',
    'isApproved': true,
    'isFeatured': false,
  };

  static const Map<String, dynamic> testUserData = {
    'id': 'test-user-id',
    'name': 'Test User',
    'email': '<EMAIL>',
    'phoneNumber': '+919876543210',
    'role': 'agent',
    'level': 0,
    'uplineId': null,
    'downlineIds': [],
    'totalStars': 0,
    'totalCommissions': 0.0,
    'isActive': true,
    'isAdmin': false,
    'referralCode': 'TEST1234',
  };

  static const Map<String, dynamic> testLeadData = {
    'id': 'test-lead-id',
    'agentId': 'test-agent-id',
    'agentName': 'Test Agent',
    'propertyId': 'test-property-id',
    'propertyTitle': 'Test Property',
    'customerName': 'Test Customer',
    'customerPhone': '+919876543220',
    'customerEmail': '<EMAIL>',
    'status': 'new',
    'priority': 'medium',
    'source': 'whatsapp',
    'budgetMin': 800000.0,
    'budgetMax': 1200000.0,
    'notes': 'Test lead notes',
    'interactions': [],
  };

  /// Test helper methods
  static Future<void> pumpAndSettle(
    WidgetTester tester, {
    Duration? duration,
  }) async {
    await tester.pumpAndSettle(duration ?? const Duration(milliseconds: 100));
  }

  static Future<void> enterTextAndPump(
    WidgetTester tester,
    Finder finder,
    String text,
  ) async {
    await tester.enterText(finder, text);
    await tester.pump();
  }

  static Future<void> tapAndPump(WidgetTester tester, Finder finder) async {
    await tester.tap(finder);
    await tester.pump();
  }

  /// Verify common UI elements
  static void verifyCommonElements(WidgetTester tester) {
    // Verify no overflow errors
    expect(tester.takeException(), isNull);

    // Verify Material app is present
    expect(find.byType(MaterialApp), findsOneWidget);
  }

  /// Create test environment
  static Future<void> setupTestEnvironment() async {
    await initializeFirebaseForTesting();
  }

  /// Cleanup test environment
  static Future<void> cleanupTestEnvironment() async {
    // Cleanup any test resources
  }

  /// Performance test helpers
  static void measurePerformance(String testName, VoidCallback test) {
    final stopwatch = Stopwatch()..start();
    test();
    stopwatch.stop();
    debugPrint('$testName took ${stopwatch.elapsedMilliseconds}ms');
  }

  /// Mock network responses
  static Map<String, dynamic> mockSuccessResponse(Map<String, dynamic> data) {
    return {'success': true, 'data': data, 'message': 'Operation successful'};
  }

  static Map<String, dynamic> mockErrorResponse(String message) {
    return {
      'success': false,
      'data': null,
      'message': message,
      'error': 'test-error',
    };
  }

  /// Test assertions
  static void expectNoOverflow(WidgetTester tester) {
    expect(tester.takeException(), isNull);
  }

  static void expectWidgetExists(Finder finder) {
    expect(finder, findsOneWidget);
  }

  static void expectWidgetNotExists(Finder finder) {
    expect(finder, findsNothing);
  }

  static void expectMultipleWidgets(Finder finder, int count) {
    expect(finder, findsNWidgets(count));
  }

  /// Currency test helpers
  static void verifyCurrencyFormat(String amount, String expected) {
    expect(amount, expected);
  }

  /// Commission calculation test helpers
  static double calculateExpectedCommission(double saleAmount, int level) {
    const rates = [0.05, 0.02, 0.01, 0.005, 0.002];
    if (level >= 0 && level < rates.length) {
      return saleAmount * rates[level];
    }
    return 0.0;
  }
}
