import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:go_router/go_router.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../models/onboarding_data.dart';
import '../../providers/onboarding_provider.dart';

/// Onboarding completion step with success animation
class OnboardingCompleteStep extends ConsumerWidget {
  const OnboardingCompleteStep({super.key});

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    final onboardingState = ref.watch(onboardingProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          const SizedBox(height: 32),

          // Success Animation
          _buildSuccessAnimation(),

          const SizedBox(height: 32),

          // Welcome Message
          Text(
            'Welcome to Rama Realty MLM!',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 16),

          Text(
            'Your agent profile has been successfully created. You\'re now ready to start your journey in real estate MLM.',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 16,
              height: 1.5,
            ),
            textAlign: TextAlign.center,
          ),

          const SizedBox(height: 48),

          // Profile Summary Card
          _buildProfileSummary(onboardingState),

          const SizedBox(height: 32),

          // Next Steps Card
          _buildNextStepsCard(),

          const SizedBox(height: 32),

          // Get Started Button
          SizedBox(
            width: double.infinity,
            child: GradientWidgets.gradientButton(
              text: 'Start Exploring',
              onPressed: () {
                context.go('/dashboard');
              },
              icon: Icons.rocket_launch,
            ),
          ),
        ],
      ),
    );
  }

  /// Build success animation
  Widget _buildSuccessAnimation() {
    return Container(
      width: 200,
      height: 200,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: AppTheme.primaryGradient,
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 30,
            offset: const Offset(0, 10),
          ),
        ],
      ),
      child: const Icon(Icons.check_circle, size: 100, color: Colors.white),
    );
  }

  /// Build profile summary card
  Widget _buildProfileSummary(OnboardingState state) {
    final data = state.data;

    return GradientWidgets.gradientCard(
      gradient: AppTheme.cardGradient,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(Icons.person, color: Colors.white, size: 20),
              ),
              const SizedBox(width: 12),
              Text(
                'Your Profile',
                style: TextStyle(
                  color: AppTheme.darkPrimaryText,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          _buildProfileItem(
            icon: Icons.person,
            label: 'Name',
            value: data.name ?? 'Not provided',
          ),

          _buildProfileItem(
            icon: Icons.phone,
            label: 'Phone',
            value: data.phoneNumber ?? 'Not provided',
          ),

          _buildProfileItem(
            icon: Icons.group_add,
            label: 'Referral Code',
            value:
                data.referralCode?.isEmpty == true || data.referralCode == null
                ? 'Admin (Auto-assigned)'
                : data.referralCode!,
          ),

          _buildProfileItem(
            icon: Icons.photo_camera,
            label: 'Profile Picture',
            value: data.profileImage != null ? 'Uploaded' : 'Not uploaded',
          ),
        ],
      ),
    );
  }

  /// Build profile item
  Widget _buildProfileItem({
    required IconData icon,
    required String label,
    required String value,
  }) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        children: [
          Icon(icon, color: AppTheme.primaryColor, size: 20),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: TextStyle(
                    color: AppTheme.darkSecondaryText,
                    fontSize: 12,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 2),
                Text(
                  value,
                  style: TextStyle(
                    color: AppTheme.darkPrimaryText,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  /// Build next steps card
  Widget _buildNextStepsCard() {
    return GradientWidgets.gradientCard(
      gradient: AppTheme.secondaryGradient,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(Icons.lightbulb, color: Colors.white, size: 24),
              const SizedBox(width: 12),
              Text(
                'What\'s Next?',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 20),

          _buildNextStepItem(
            '🏠 Explore available properties',
            'Browse through our premium real estate listings',
          ),

          _buildNextStepItem(
            '👥 Build your network',
            'Invite new agents using your referral code',
          ),

          _buildNextStepItem(
            '💰 Track your earnings',
            'Monitor commissions and bonuses in real-time',
          ),

          _buildNextStepItem(
            '⭐ Earn stars and rewards',
            'Complete tasks to earn stars and unlock bonuses',
          ),
        ],
      ),
    );
  }

  /// Build next step item
  Widget _buildNextStepItem(String title, String description) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              color: Colors.white,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  description,
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.8),
                    fontSize: 12,
                    height: 1.3,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }
}
