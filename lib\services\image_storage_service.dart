import 'dart:convert';
import 'dart:typed_data';
import 'package:flutter/foundation.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:file_picker/file_picker.dart';
import 'package:image/image.dart' as img;

/// Image storage service that works without Firebase Storage
/// Uses Base64 encoding to store images in Firestore
class ImageStorageService {
  static const int maxImageSize = 500 * 1024; // 500KB max per image
  static const int maxImageWidth = 800;
  static const int maxImageHeight = 600;

  /// Upload image and return the document reference
  static Future<String> uploadImage({
    required Uint8List imageBytes,
    required String collection,
    required String fileName,
    String? userId,
  }) async {
    try {
      // Compress and resize image
      final compressedBytes = await _compressImage(imageBytes);
      
      // Convert to Base64
      final base64String = base64Encode(compressedBytes);
      
      // Create image document in Firestore
      final imageDoc = {
        'fileName': fileName,
        'base64Data': base64String,
        'uploadedAt': FieldValue.serverTimestamp(),
        'uploadedBy': userId,
        'size': compressedBytes.length,
        'type': 'image',
      };
      
      // Save to Firestore
      final docRef = await FirebaseFirestore.instance
          .collection('images')
          .add(imageDoc);
      
      if (kDebugMode) {
        print('Image uploaded successfully: ${docRef.id}');
      }
      
      return docRef.id;
    } catch (e) {
      if (kDebugMode) {
        print('Error uploading image: $e');
      }
      rethrow;
    }
  }

  /// Get image data by document ID
  static Future<Uint8List?> getImage(String imageId) async {
    try {
      final doc = await FirebaseFirestore.instance
          .collection('images')
          .doc(imageId)
          .get();
      
      if (!doc.exists) {
        return null;
      }
      
      final data = doc.data() as Map<String, dynamic>;
      final base64String = data['base64Data'] as String?;
      
      if (base64String == null) {
        return null;
      }
      
      return base64Decode(base64String);
    } catch (e) {
      if (kDebugMode) {
        print('Error getting image: $e');
      }
      return null;
    }
  }

  /// Get image URL (returns the document ID which can be used to fetch the image)
  static String getImageUrl(String imageId) {
    return imageId; // In this case, we return the document ID
  }

  /// Delete image
  static Future<void> deleteImage(String imageId) async {
    try {
      await FirebaseFirestore.instance
          .collection('images')
          .doc(imageId)
          .delete();
      
      if (kDebugMode) {
        print('Image deleted successfully: $imageId');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error deleting image: $e');
      }
      rethrow;
    }
  }

  /// Pick and upload image from device
  static Future<String?> pickAndUploadImage({
    required String collection,
    String? userId,
  }) async {
    try {
      // Pick image file
      final result = await FilePicker.platform.pickFiles(
        type: FileType.image,
        allowMultiple: false,
        withData: true,
      );

      if (result == null || result.files.isEmpty) {
        return null;
      }

      final file = result.files.first;
      if (file.bytes == null) {
        throw Exception('Could not read file data');
      }

      // Check file size
      if (file.size > maxImageSize * 2) {
        throw Exception('Image too large. Please select an image under 1MB.');
      }

      // Upload the image
      final imageId = await uploadImage(
        imageBytes: file.bytes!,
        collection: collection,
        fileName: file.name,
        userId: userId,
      );

      return imageId;
    } catch (e) {
      if (kDebugMode) {
        print('Error picking and uploading image: $e');
      }
      rethrow;
    }
  }

  /// Compress image to reduce size
  static Future<Uint8List> _compressImage(Uint8List imageBytes) async {
    try {
      // Decode image
      final image = img.decodeImage(imageBytes);
      if (image == null) {
        throw Exception('Could not decode image');
      }

      // Resize if too large
      img.Image resizedImage = image;
      if (image.width > maxImageWidth || image.height > maxImageHeight) {
        resizedImage = img.copyResize(
          image,
          width: image.width > maxImageWidth ? maxImageWidth : null,
          height: image.height > maxImageHeight ? maxImageHeight : null,
        );
      }

      // Compress as JPEG with quality 85
      final compressedBytes = img.encodeJpg(resizedImage, quality: 85);
      
      // If still too large, reduce quality
      if (compressedBytes.length > maxImageSize) {
        final moreCompressed = img.encodeJpg(resizedImage, quality: 60);
        if (moreCompressed.length <= maxImageSize) {
          return Uint8List.fromList(moreCompressed);
        }
        
        // Last resort - very low quality
        final finalCompressed = img.encodeJpg(resizedImage, quality: 30);
        return Uint8List.fromList(finalCompressed);
      }

      return Uint8List.fromList(compressedBytes);
    } catch (e) {
      if (kDebugMode) {
        print('Error compressing image: $e');
      }
      // Return original if compression fails
      return imageBytes;
    }
  }

  /// Get multiple images
  static Future<List<Uint8List>> getMultipleImages(List<String> imageIds) async {
    final images = <Uint8List>[];
    
    for (final imageId in imageIds) {
      final imageData = await getImage(imageId);
      if (imageData != null) {
        images.add(imageData);
      }
    }
    
    return images;
  }

  /// Upload multiple images
  static Future<List<String>> uploadMultipleImages({
    required List<Uint8List> imageBytesList,
    required String collection,
    String? userId,
  }) async {
    final imageIds = <String>[];
    
    for (int i = 0; i < imageBytesList.length; i++) {
      final imageId = await uploadImage(
        imageBytes: imageBytesList[i],
        collection: collection,
        fileName: 'image_${DateTime.now().millisecondsSinceEpoch}_$i.jpg',
        userId: userId,
      );
      imageIds.add(imageId);
    }
    
    return imageIds;
  }

  /// Clean up old images (call periodically to manage storage)
  static Future<void> cleanupOldImages({int daysOld = 30}) async {
    try {
      final cutoffDate = DateTime.now().subtract(Duration(days: daysOld));
      
      final query = await FirebaseFirestore.instance
          .collection('images')
          .where('uploadedAt', isLessThan: Timestamp.fromDate(cutoffDate))
          .get();
      
      final batch = FirebaseFirestore.instance.batch();
      
      for (final doc in query.docs) {
        batch.delete(doc.reference);
      }
      
      await batch.commit();
      
      if (kDebugMode) {
        print('Cleaned up ${query.docs.length} old images');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cleaning up old images: $e');
      }
    }
  }
}
