import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/models/property_model.dart';
import '../../../../core/services/property_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

/// All properties provider
final propertiesProvider = FutureProvider.autoDispose<List<PropertyModel>>((ref) async {
  return await PropertyService.getProperties(isApproved: true);
});

/// Properties by filter provider
final filteredPropertiesProvider = FutureProvider.autoDispose.family<List<PropertyModel>, PropertyFilter>(
  (ref, filter) async {
    return await PropertyService.getProperties(
      type: filter.type,
      status: filter.status,
      city: filter.city,
      state: filter.state,
      minPrice: filter.minPrice,
      maxPrice: filter.maxPrice,
      isApproved: filter.isApproved,
      isFeatured: filter.isFeatured,
      assignedAgentId: filter.assignedAgentId,
      limit: filter.limit,
    );
  },
);

/// Admin properties provider (includes unapproved)
final adminPropertiesProvider = FutureProvider.autoDispose<List<PropertyModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) return [];
  
  return await PropertyService.getProperties(limit: 100);
});

/// Property search provider
final propertySearchProvider = StateNotifierProvider<PropertySearchNotifier, PropertySearchState>((ref) {
  return PropertySearchNotifier();
});

/// Property filter provider
final propertyFilterProvider = StateNotifierProvider<PropertyFilterNotifier, PropertyFilter>((ref) {
  return PropertyFilterNotifier();
});

/// Search suggestions provider
final searchSuggestionsProvider = StateNotifierProvider<SearchSuggestionsNotifier, SearchSuggestionsState>((ref) {
  return SearchSuggestionsNotifier();
});

/// Popular searches provider
final popularSearchesProvider = FutureProvider.autoDispose<List<String>>((ref) async {
  // TODO: Implement analytics-based popular searches
  return [
    'Mumbai apartments',
    'Commercial spaces Delhi',
    'Residential plots Pune',
    'Luxury villas Bangalore',
    'Office spaces Hyderabad',
  ];
});

/// Recent searches provider
final recentSearchesProvider = StateNotifierProvider<RecentSearchesNotifier, List<String>>((ref) {
  return RecentSearchesNotifier();
});

/// Advanced filtered properties provider
final advancedFilteredPropertiesProvider = FutureProvider.autoDispose<List<PropertyModel>>((ref) async {
  final filter = ref.watch(propertyFilterProvider);
  final searchState = ref.watch(propertySearchProvider);

  // Get base properties
  List<PropertyModel> properties = await PropertyService.getProperties(
    type: filter.type,
    status: filter.status,
    city: filter.city,
    state: filter.state,
    minPrice: filter.minPrice,
    maxPrice: filter.maxPrice,
    isApproved: filter.isApproved,
    isFeatured: filter.isFeatured,
    assignedAgentId: filter.assignedAgentId,
    limit: filter.limit,
  );

  // Apply search query if present
  if (searchState.query.isNotEmpty) {
    properties = _applyAdvancedSearch(properties, searchState.query);
  }

  // Apply sorting
  properties = _applySorting(properties, filter.sortBy, filter.sortAscending);

  return properties;
});

/// Property statistics provider
final propertyStatsProvider = FutureProvider.autoDispose<PropertyStats>((ref) async {
  final allProperties = await PropertyService.getProperties(limit: 1000);

  return PropertyStats(
    totalProperties: allProperties.length,
    approvedProperties: allProperties.where((p) => p.isApproved).length,
    featuredProperties: allProperties.where((p) => p.isFeatured).length,
    averagePrice: allProperties.isNotEmpty
        ? allProperties.map((p) => p.price).reduce((a, b) => a + b) / allProperties.length
        : 0,
    priceRanges: _calculatePriceRanges(allProperties),
    cityDistribution: _calculateCityDistribution(allProperties),
    typeDistribution: _calculateTypeDistribution(allProperties),
  );
});

/// Admin property statistics provider
final adminPropertyStatsProvider = FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser?.isAdmin != true) return {};

  return await PropertyService.getPropertyStatistics();
});

/// Agent properties provider
final agentPropertiesProvider = FutureProvider.autoDispose<List<PropertyModel>>((ref) async {
  final currentUser = ref.watch(currentUserProvider);
  if (currentUser == null) return [];
  
  return await PropertyService.getPropertiesByAgent(currentUser.id);
});

/// Featured properties provider
final featuredPropertiesProvider = FutureProvider.autoDispose<List<PropertyModel>>((ref) async {
  return await PropertyService.getProperties(isFeatured: true, isApproved: true, limit: 10);
});

/// Property form provider
final propertyFormProvider = StateNotifierProvider<PropertyFormNotifier, PropertyFormState>((ref) {
  return PropertyFormNotifier();
});

/// Property search state
class PropertySearchState {
  final String query;
  final List<PropertyModel> results;
  final bool isLoading;
  final String? error;

  const PropertySearchState({
    this.query = '',
    this.results = const [],
    this.isLoading = false,
    this.error,
  });

  PropertySearchState copyWith({
    String? query,
    List<PropertyModel>? results,
    bool? isLoading,
    String? error,
  }) {
    return PropertySearchState(
      query: query ?? this.query,
      results: results ?? this.results,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Property search notifier
class PropertySearchNotifier extends StateNotifier<PropertySearchState> {
  PropertySearchNotifier() : super(const PropertySearchState());

  Future<void> searchProperties(String query) async {
    if (query.trim().isEmpty) {
      state = state.copyWith(query: '', results: [], error: null);
      return;
    }

    state = state.copyWith(query: query, isLoading: true, error: null);

    try {
      final results = await PropertyService.searchProperties(query);
      state = state.copyWith(
        results: results,
        isLoading: false,
        error: null,
      );
    } catch (e) {
      state = state.copyWith(
        isLoading: false,
        error: 'Search failed: ${e.toString()}',
      );
    }
  }

  void clearSearch() {
    state = const PropertySearchState();
  }
}

/// Property filter
class PropertyFilter {
  final String? type;
  final String? status;
  final String? city;
  final String? state;
  final double? minPrice;
  final double? maxPrice;
  final bool? isApproved;
  final bool? isFeatured;
  final String? assignedAgentId;
  final int limit;
  final String sortBy;
  final bool sortAscending;

  const PropertyFilter({
    this.type,
    this.status,
    this.city,
    this.state,
    this.minPrice,
    this.maxPrice,
    this.isApproved,
    this.isFeatured,
    this.assignedAgentId,
    this.limit = 50,
    this.sortBy = 'createdAt',
    this.sortAscending = false,
  });

  PropertyFilter copyWith({
    String? type,
    String? status,
    String? city,
    String? state,
    double? minPrice,
    double? maxPrice,
    bool? isApproved,
    bool? isFeatured,
    String? assignedAgentId,
    int? limit,
    String? sortBy,
    bool? sortAscending,
  }) {
    return PropertyFilter(
      type: type ?? this.type,
      status: status ?? this.status,
      city: city ?? this.city,
      state: state ?? this.state,
      minPrice: minPrice ?? this.minPrice,
      maxPrice: maxPrice ?? this.maxPrice,
      isApproved: isApproved ?? this.isApproved,
      isFeatured: isFeatured ?? this.isFeatured,
      assignedAgentId: assignedAgentId ?? this.assignedAgentId,
      limit: limit ?? this.limit,
      sortBy: sortBy ?? this.sortBy,
      sortAscending: sortAscending ?? this.sortAscending,
    );
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PropertyFilter &&
        other.type == type &&
        other.status == status &&
        other.city == city &&
        other.state == state &&
        other.minPrice == minPrice &&
        other.maxPrice == maxPrice &&
        other.isApproved == isApproved &&
        other.isFeatured == isFeatured &&
        other.assignedAgentId == assignedAgentId &&
        other.limit == limit &&
        other.sortBy == sortBy &&
        other.sortAscending == sortAscending;
  }

  @override
  int get hashCode => Object.hash(
        type,
        status,
        city,
        state,
        minPrice,
        maxPrice,
        isApproved,
        isFeatured,
        assignedAgentId,
        limit,
        sortBy,
        sortAscending,
      );
}

/// Property filter notifier
class PropertyFilterNotifier extends StateNotifier<PropertyFilter> {
  PropertyFilterNotifier() : super(const PropertyFilter());

  void setType(String? type) {
    state = state.copyWith(type: type);
  }

  void setStatus(String? status) {
    state = state.copyWith(status: status);
  }

  void setCity(String? city) {
    state = state.copyWith(city: city);
  }

  void setState(String? stateValue) {
    state = state.copyWith(state: stateValue);
  }

  void setPriceRange(double? minPrice, double? maxPrice) {
    state = state.copyWith(minPrice: minPrice, maxPrice: maxPrice);
  }

  void setApprovalStatus(bool? isApproved) {
    state = state.copyWith(isApproved: isApproved);
  }

  void setFeaturedStatus(bool? isFeatured) {
    state = state.copyWith(isFeatured: isFeatured);
  }

  void setAssignedAgent(String? agentId) {
    state = state.copyWith(assignedAgentId: agentId);
  }

  void setSortBy(String sortBy) {
    state = state.copyWith(sortBy: sortBy);
  }

  void setSortAscending(bool ascending) {
    state = state.copyWith(sortAscending: ascending);
  }

  void resetFilters() {
    state = const PropertyFilter();
  }

  void updateFilter(PropertyFilter newFilter) {
    state = newFilter;
  }
}

/// Search suggestions state
class SearchSuggestionsState {
  final List<String> locationSuggestions;
  final List<String> typeSuggestions;
  final List<String> priceSuggestions;
  final List<String> recentSuggestions;

  const SearchSuggestionsState({
    this.locationSuggestions = const [],
    this.typeSuggestions = const [],
    this.priceSuggestions = const [],
    this.recentSuggestions = const [],
  });

  SearchSuggestionsState copyWith({
    List<String>? locationSuggestions,
    List<String>? typeSuggestions,
    List<String>? priceSuggestions,
    List<String>? recentSuggestions,
  }) {
    return SearchSuggestionsState(
      locationSuggestions: locationSuggestions ?? this.locationSuggestions,
      typeSuggestions: typeSuggestions ?? this.typeSuggestions,
      priceSuggestions: priceSuggestions ?? this.priceSuggestions,
      recentSuggestions: recentSuggestions ?? this.recentSuggestions,
    );
  }
}

/// Search suggestions notifier
class SearchSuggestionsNotifier extends StateNotifier<SearchSuggestionsState> {
  SearchSuggestionsNotifier() : super(const SearchSuggestionsState());

  void updateSuggestions(String query) {
    final lowerQuery = query.toLowerCase();

    // Location suggestions
    final locationSuggestions = [
      'Mumbai', 'Delhi', 'Bangalore', 'Chennai', 'Pune', 'Hyderabad',
      'Kolkata', 'Ahmedabad', 'Jaipur', 'Surat', 'Lucknow', 'Kanpur'
    ].where((location) => location.toLowerCase().contains(lowerQuery)).toList();

    // Type suggestions
    final typeSuggestions = [
      'Residential', 'Commercial', 'Land', 'Industrial', 'Agricultural',
      'Apartment', 'Villa', 'Office', 'Shop', 'Warehouse'
    ].where((type) => type.toLowerCase().contains(lowerQuery)).toList();

    // Price suggestions
    final priceSuggestions = <String>[];
    if (lowerQuery.contains('lakh') || lowerQuery.contains('l')) {
      priceSuggestions.addAll(['Under 50 Lakh', '50-100 Lakh', 'Above 100 Lakh']);
    }
    if (lowerQuery.contains('crore') || lowerQuery.contains('cr')) {
      priceSuggestions.addAll(['Under 1 Crore', '1-5 Crore', 'Above 5 Crore']);
    }

    state = state.copyWith(
      locationSuggestions: locationSuggestions,
      typeSuggestions: typeSuggestions,
      priceSuggestions: priceSuggestions,
    );
  }
}

/// Recent searches notifier
class RecentSearchesNotifier extends StateNotifier<List<String>> {
  RecentSearchesNotifier() : super([]);

  void addSearch(String query) {
    if (query.trim().isEmpty) return;

    final updatedSearches = [query, ...state.where((s) => s != query)];
    state = updatedSearches.take(10).toList(); // Keep only last 10 searches
  }

  void clearSearches() {
    state = [];
  }
}

/// Property statistics model
class PropertyStats {
  final int totalProperties;
  final int approvedProperties;
  final int featuredProperties;
  final double averagePrice;
  final Map<String, int> priceRanges;
  final Map<String, int> cityDistribution;
  final Map<String, int> typeDistribution;

  const PropertyStats({
    this.totalProperties = 0,
    this.approvedProperties = 0,
    this.featuredProperties = 0,
    this.averagePrice = 0,
    this.priceRanges = const {},
    this.cityDistribution = const {},
    this.typeDistribution = const {},
  });
}

// Helper functions for advanced search and filtering

/// Apply advanced search with multiple criteria
List<PropertyModel> _applyAdvancedSearch(List<PropertyModel> properties, String query) {
  final lowerQuery = query.toLowerCase();

  return properties.where((property) {
    // Basic text search
    final basicMatch = property.title.toLowerCase().contains(lowerQuery) ||
                      property.description.toLowerCase().contains(lowerQuery) ||
                      property.location.toLowerCase().contains(lowerQuery) ||
                      property.city.toLowerCase().contains(lowerQuery) ||
                      property.state.toLowerCase().contains(lowerQuery) ||
                      property.type.toLowerCase().contains(lowerQuery);

    // Price-based search
    final priceMatch = _matchPriceQuery(property.price, lowerQuery);

    // Amenities search
    final amenitiesMatch = property.amenities.any((amenity) =>
        amenity.toLowerCase().contains(lowerQuery));

    // Area-based search
    final areaMatch = property.areaSquareFeet != null &&
        _matchAreaQuery(property.areaSquareFeet!, lowerQuery);

    // Bedroom/bathroom search
    final roomMatch = _matchRoomQuery(property, lowerQuery);

    return basicMatch || priceMatch || amenitiesMatch || areaMatch || roomMatch;
  }).toList();
}

/// Match price-related queries
bool _matchPriceQuery(double price, String query) {
  // Convert price to different formats for matching
  final priceInLakhs = price / 100000;
  final priceInCrores = price / 10000000;

  // Check for lakh mentions
  if (query.contains('lakh') || query.contains('l')) {
    if (query.contains('under') && priceInLakhs < 50) return true;
    if (query.contains('above') && priceInLakhs > 100) return true;
    if (query.contains('50') && priceInLakhs >= 50 && priceInLakhs <= 100) return true;
  }

  // Check for crore mentions
  if (query.contains('crore') || query.contains('cr')) {
    if (query.contains('under') && priceInCrores < 1) return true;
    if (query.contains('above') && priceInCrores > 5) return true;
    if (query.contains('1') && priceInCrores >= 1 && priceInCrores <= 5) return true;
  }

  // Check for specific price numbers
  final numbers = RegExp(r'\d+').allMatches(query);
  for (final match in numbers) {
    final number = int.tryParse(match.group(0)!);
    if (number != null) {
      // Check if price is close to the mentioned number (in lakhs or crores)
      if ((number * 100000 - price).abs() < 500000 || // Within 5 lakh range
          (number * 10000000 - price).abs() < 5000000) { // Within 50 lakh range
        return true;
      }
    }
  }

  return false;
}

/// Match area-related queries
bool _matchAreaQuery(double area, String query) {
  if (query.contains('sqft') || query.contains('square')) {
    final numbers = RegExp(r'\d+').allMatches(query);
    for (final match in numbers) {
      final number = int.tryParse(match.group(0)!);
      if (number != null && (area - number).abs() < 500) {
        return true;
      }
    }
  }
  return false;
}

/// Match room-related queries
bool _matchRoomQuery(PropertyModel property, String query) {
  // BHK patterns
  final bhkPattern = RegExp(r'(\d+)\s*bhk');
  final bhkMatch = bhkPattern.firstMatch(query);
  if (bhkMatch != null) {
    final bhk = int.tryParse(bhkMatch.group(1)!);
    if (bhk != null && property.bedrooms == bhk) {
      return true;
    }
  }

  // Bedroom patterns
  if (query.contains('bedroom') && property.bedrooms != null) {
    final numbers = RegExp(r'\d+').allMatches(query);
    for (final match in numbers) {
      final number = int.tryParse(match.group(0)!);
      if (number != null && property.bedrooms == number) {
        return true;
      }
    }
  }

  return false;
}

/// Apply sorting to properties
List<PropertyModel> _applySorting(List<PropertyModel> properties, String sortBy, bool ascending) {
  properties.sort((a, b) {
    int comparison = 0;

    switch (sortBy) {
      case 'price':
        comparison = a.price.compareTo(b.price);
        break;
      case 'title':
        comparison = a.title.compareTo(b.title);
        break;
      case 'city':
        comparison = a.city.compareTo(b.city);
        break;
      case 'createdAt':
      default:
        comparison = a.createdAt.compareTo(b.createdAt);
        break;
    }

    return ascending ? comparison : -comparison;
  });

  return properties;
}

/// Calculate price ranges distribution
Map<String, int> _calculatePriceRanges(List<PropertyModel> properties) {
  final ranges = <String, int>{
    'Under 50L': 0,
    '50L-1Cr': 0,
    '1-2Cr': 0,
    '2-5Cr': 0,
    'Above 5Cr': 0,
  };

  for (final property in properties) {
    final priceInCrores = property.price / 10000000;

    if (priceInCrores < 0.5) {
      ranges['Under 50L'] = ranges['Under 50L']! + 1;
    } else if (priceInCrores < 1) {
      ranges['50L-1Cr'] = ranges['50L-1Cr']! + 1;
    } else if (priceInCrores < 2) {
      ranges['1-2Cr'] = ranges['1-2Cr']! + 1;
    } else if (priceInCrores < 5) {
      ranges['2-5Cr'] = ranges['2-5Cr']! + 1;
    } else {
      ranges['Above 5Cr'] = ranges['Above 5Cr']! + 1;
    }
  }

  return ranges;
}

/// Calculate city distribution
Map<String, int> _calculateCityDistribution(List<PropertyModel> properties) {
  final distribution = <String, int>{};

  for (final property in properties) {
    distribution[property.city] = (distribution[property.city] ?? 0) + 1;
  }

  // Sort by count and return top 10
  final sortedEntries = distribution.entries.toList()
    ..sort((a, b) => b.value.compareTo(a.value));

  return Map.fromEntries(sortedEntries.take(10));
}

/// Calculate type distribution
Map<String, int> _calculateTypeDistribution(List<PropertyModel> properties) {
  final distribution = <String, int>{};

  for (final property in properties) {
    distribution[property.type] = (distribution[property.type] ?? 0) + 1;
  }

  return distribution;
}

/// Property form state
class PropertyFormState {
  final String title;
  final String description;
  final String type;
  final String status;
  final double price;
  final String location;
  final String city;
  final String state;
  final String pincode;
  final double? areaSquareFeet;
  final int? bedrooms;
  final int? bathrooms;
  final List<String> amenities;
  final List<String> imageUrls;
  final bool isLoading;
  final String? error;

  const PropertyFormState({
    this.title = '',
    this.description = '',
    this.type = 'Residential',
    this.status = 'for_sale',
    this.price = 0.0,
    this.location = '',
    this.city = '',
    this.state = '',
    this.pincode = '',
    this.areaSquareFeet,
    this.bedrooms,
    this.bathrooms,
    this.amenities = const [],
    this.imageUrls = const [],
    this.isLoading = false,
    this.error,
  });

  PropertyFormState copyWith({
    String? title,
    String? description,
    String? type,
    String? status,
    double? price,
    String? location,
    String? city,
    String? state,
    String? pincode,
    double? areaSquareFeet,
    int? bedrooms,
    int? bathrooms,
    List<String>? amenities,
    List<String>? imageUrls,
    bool? isLoading,
    String? error,
  }) {
    return PropertyFormState(
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      price: price ?? this.price,
      location: location ?? this.location,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      areaSquareFeet: areaSquareFeet ?? this.areaSquareFeet,
      bedrooms: bedrooms ?? this.bedrooms,
      bathrooms: bathrooms ?? this.bathrooms,
      amenities: amenities ?? this.amenities,
      imageUrls: imageUrls ?? this.imageUrls,
      isLoading: isLoading ?? this.isLoading,
      error: error,
    );
  }
}

/// Property form notifier
class PropertyFormNotifier extends StateNotifier<PropertyFormState> {
  PropertyFormNotifier() : super(const PropertyFormState());

  void updateTitle(String title) {
    state = state.copyWith(title: title, error: null);
  }

  void updateDescription(String description) {
    state = state.copyWith(description: description, error: null);
  }

  void updateType(String type) {
    state = state.copyWith(type: type, error: null);
  }

  void updateStatus(String status) {
    state = state.copyWith(status: status, error: null);
  }

  void updatePrice(double price) {
    state = state.copyWith(price: price, error: null);
  }

  void updateLocation(String location) {
    state = state.copyWith(location: location, error: null);
  }

  void updateCity(String city) {
    state = state.copyWith(city: city, error: null);
  }

  void updateState(String stateValue) {
    state = state.copyWith(state: stateValue, error: null);
  }

  void updatePincode(String pincode) {
    state = state.copyWith(pincode: pincode, error: null);
  }

  void updateAreaSquareFeet(double? area) {
    state = state.copyWith(areaSquareFeet: area, error: null);
  }

  void updateBedrooms(int? bedrooms) {
    state = state.copyWith(bedrooms: bedrooms, error: null);
  }

  void updateBathrooms(int? bathrooms) {
    state = state.copyWith(bathrooms: bathrooms, error: null);
  }

  void updateAmenities(List<String> amenities) {
    state = state.copyWith(amenities: amenities, error: null);
  }

  void updateImageUrls(List<String> imageUrls) {
    state = state.copyWith(imageUrls: imageUrls, error: null);
  }

  void setLoading(bool loading) {
    state = state.copyWith(isLoading: loading);
  }

  void setError(String? error) {
    state = state.copyWith(error: error, isLoading: false);
  }

  void resetForm() {
    state = const PropertyFormState();
  }

  String? validateForm() {
    if (state.title.trim().isEmpty) return 'Title is required';
    if (state.description.trim().isEmpty) return 'Description is required';
    if (state.price <= 0) return 'Price must be greater than 0';
    if (state.location.trim().isEmpty) return 'Location is required';
    if (state.city.trim().isEmpty) return 'City is required';
    if (state.state.trim().isEmpty) return 'State is required';
    if (state.pincode.trim().isEmpty) return 'Pincode is required';
    return null;
  }
}
