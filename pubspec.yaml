name: rama_realty_mlm
description: "Rama Realty MLM - Multi-Level Real Estate Marketing Application"
# The following line prevents the package from being accidentally published to
# pub.dev using `flutter pub publish`. This is preferred for private packages.
publish_to: 'none' # Remove this line if you wish to publish to pub.dev

# The following defines the version and build number for your application.
# A version number is three numbers separated by dots, like 1.2.43
# followed by an optional build number separated by a +.
# Both the version and the builder number may be overridden in flutter
# build by specifying --build-name and --build-number, respectively.
# In Android, build-name is used as versionName while build-number used as versionCode.
# Read more about Android versioning at https://developer.android.com/studio/publish/versioning
# In iOS, build-name is used as CFBundleShortVersionString while build-number is used as CFBundleVersion.
# Read more about iOS versioning at
# https://developer.apple.com/library/archive/documentation/General/Reference/InfoPlistKeyReference/Articles/CoreFoundationKeys.html
# In Windows, build-name is used as the major, minor, and patch parts
# of the product and file versions while build-number is used as the build suffix.
version: 1.0.0+1

environment:
  sdk: ^3.8.1

# Dependencies specify other packages that your package needs in order to work.
# To automatically upgrade your package dependencies to the latest versions
# consider running `flutter pub upgrade --major-versions`. Alternatively,
# dependencies can be manually updated by changing the version numbers below to
# the latest version available on pub.dev. To see which dependencies have newer
# versions available, run `flutter pub outdated`.
dependencies:
  flutter:
    sdk: flutter

  # UI and Icons
  cupertino_icons: ^1.0.8
  material_design_icons_flutter: ^7.0.7296

  # Firebase Core Services
  firebase_core: ^3.8.0
  firebase_auth: ^5.3.3
  cloud_firestore: ^5.5.0
  firebase_storage: ^12.3.6
  firebase_messaging: ^15.1.5
  firebase_analytics: ^11.3.5

  # State Management
  provider: ^6.1.2
  riverpod: ^2.6.1
  flutter_riverpod: ^2.6.1

  # Navigation
  go_router: ^14.6.2

  # HTTP and API
  http: ^1.2.2
  dio: ^5.7.0

  # Local Storage
  shared_preferences: ^2.3.3
  hive: ^2.2.3
  hive_flutter: ^1.1.0

  # Image Handling
  image_picker: ^1.1.2
  cached_network_image: ^3.4.1
  image_cropper: ^8.0.2
  crypto: ^3.0.3
  file_picker: ^6.1.1

  # UI Components
  flutter_staggered_grid_view: ^0.7.0
  shimmer: ^3.0.0
  lottie: ^3.1.3
  flutter_spinkit: ^5.2.1

  # Enhanced UI/UX Libraries
  flutter_animate: ^4.5.0
  fl_chart: ^0.68.0
  awesome_dialog: ^3.2.1
  flutter_speed_dial: ^7.0.0
  animated_bottom_navigation_bar: ^1.3.3
  glassmorphism: ^3.0.0
  pin_code_fields: ^8.0.1
  fluttertoast: ^8.2.4
  percent_indicator: ^4.2.3
  page_transition: ^2.1.0
  modal_bottom_sheet: ^3.0.0
  expandable: ^5.0.1

  # Utilities
  intl: ^0.19.0
  uuid: ^4.5.1
  url_launcher: ^6.3.1
  share_plus: ^10.1.2
  permission_handler: ^11.3.1

  # Charts and Analytics
  syncfusion_flutter_charts: ^27.2.5

  # Form Validation
  form_validator: ^2.1.1

  # Networking
  connectivity_plus: ^6.1.0

  # Location Services
  geolocator: ^13.0.1

  # Notifications
  flutter_local_notifications: ^18.0.1

  # QR Code Generation and Scanning
  qr_flutter: ^4.1.0
  qr_code_scanner: ^1.0.1



dev_dependencies:
  flutter_test:
    sdk: flutter

  # Linting and Code Quality
  flutter_lints: ^5.0.0
  very_good_analysis: ^6.0.0

  # Testing
  mockito: ^5.4.4
  build_runner: ^2.4.13
  hive_generator: ^2.0.1
  integration_test:
    sdk: flutter
  fake_cloud_firestore: ^3.0.3
  firebase_auth_mocks: ^0.14.1
  network_image_mock: ^2.1.1
  golden_toolkit: ^0.15.0
  patrol: ^3.12.0
  flutter_driver:
    sdk: flutter

  # Development Tools
  flutter_launcher_icons: ^0.14.1
  flutter_native_splash: ^2.4.1

# For information on the generic Dart part of this file, see the
# following page: https://dart.dev/tools/pub/pubspec

# The following section is specific to Flutter packages.
flutter:

  # The following line ensures that the Material Icons font is
  # included with your application, so that you can use the icons in
  # the material Icons class.
  uses-material-design: true

  # Assets
  assets:
    - assets/images/
    - assets/icons/
    - assets/animations/

  # An image asset can refer to one or more resolution-specific "variants", see
  # https://flutter.dev/to/resolution-aware-images

  # For details regarding adding assets from package dependencies, see
  # https://flutter.dev/to/asset-from-package

  # To add custom fonts to your application, add a fonts section here,
  # in this "flutter" section. Each entry in this list should have a
  # "family" key with the font family name, and a "fonts" key with a
  # list giving the asset and other descriptors for the font. For
  # example:
  # fonts:
  #   - family: Schyler
  #     fonts:
  #       - asset: fonts/Schyler-Regular.ttf
  #       - asset: fonts/Schyler-Italic.ttf
  #         style: italic
  #   - family: Trajan Pro
  #     fonts:
  #       - asset: fonts/TrajanPro.ttf
  #       - asset: fonts/TrajanPro_Bold.ttf
  #         weight: 700
  #
  # For details regarding fonts from package dependencies,
  # see https://flutter.dev/to/font-from-package
