import 'package:flutter/foundation.dart';
import 'package:flutter/services.dart';
import 'dart:html' as html;

/// Service for handling deep links and URL parameters
class DeepLinkService {
  static const String _referralParam = 'ref';
  
  /// Get referral code from current URL (web only)
  static String? getReferralCodeFromUrl() {
    if (kIsWeb) {
      try {
        final uri = Uri.parse(html.window.location.href);
        return uri.queryParameters[_referralParam];
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing URL for referral code: $e');
        }
        return null;
      }
    }
    return null;
  }
  
  /// Update URL with referral code (web only)
  static void updateUrlWithReferralCode(String referralCode) {
    if (kIsWeb) {
      try {
        final currentUri = Uri.parse(html.window.location.href);
        final newParams = Map<String, String>.from(currentUri.queryParameters);
        newParams[_referralParam] = referralCode;
        
        final newUri = currentUri.replace(queryParameters: newParams);
        html.window.history.replaceState(null, '', newUri.toString());
      } catch (e) {
        if (kDebugMode) {
          print('Error updating URL with referral code: $e');
        }
      }
    }
  }
  
  /// Clear referral code from URL (web only)
  static void clearReferralCodeFromUrl() {
    if (kIsWeb) {
      try {
        final currentUri = Uri.parse(html.window.location.href);
        final newParams = Map<String, String>.from(currentUri.queryParameters);
        newParams.remove(_referralParam);
        
        final newUri = currentUri.replace(queryParameters: newParams);
        html.window.history.replaceState(null, '', newUri.toString());
      } catch (e) {
        if (kDebugMode) {
          print('Error clearing referral code from URL: $e');
        }
      }
    }
  }
  
  /// Check if current URL has referral code
  static bool hasReferralCodeInUrl() {
    return getReferralCodeFromUrl() != null;
  }
  
  /// Parse all query parameters from current URL
  static Map<String, String> getUrlParameters() {
    if (kIsWeb) {
      try {
        final uri = Uri.parse(html.window.location.href);
        return uri.queryParameters;
      } catch (e) {
        if (kDebugMode) {
          print('Error parsing URL parameters: $e');
        }
        return {};
      }
    }
    return {};
  }
  
  /// Navigate to onboarding with referral code
  static void navigateToOnboardingWithReferral(String referralCode) {
    if (kIsWeb) {
      try {
        final baseUrl = html.window.location.origin;
        final newUrl = '$baseUrl/onboarding?$_referralParam=$referralCode';
        html.window.location.href = newUrl;
      } catch (e) {
        if (kDebugMode) {
          print('Error navigating to onboarding with referral: $e');
        }
      }
    }
  }
  
  /// Get current page path
  static String getCurrentPath() {
    if (kIsWeb) {
      try {
        final uri = Uri.parse(html.window.location.href);
        return uri.path;
      } catch (e) {
        if (kDebugMode) {
          print('Error getting current path: $e');
        }
        return '/';
      }
    }
    return '/';
  }
  
  /// Check if current page is onboarding
  static bool isOnboardingPage() {
    return getCurrentPath().contains('/onboarding') || 
           getCurrentPath().contains('/register');
  }
  
  /// Generate shareable URL with referral code
  static String generateShareableUrl(String referralCode, {String? customPath}) {
    if (kIsWeb) {
      final baseUrl = html.window.location.origin;
      final path = customPath ?? '/onboarding';
      return '$baseUrl$path?$_referralParam=$referralCode';
    } else {
      // For mobile, use a custom scheme or universal link
      return 'https://rama-samriddhi.web.app/onboarding?$_referralParam=$referralCode';
    }
  }
  
  /// Copy referral URL to clipboard
  static Future<void> copyReferralUrlToClipboard(String referralCode) async {
    try {
      final url = generateShareableUrl(referralCode);
      await Clipboard.setData(ClipboardData(text: url));
      if (kDebugMode) {
        print('Referral URL copied to clipboard: $url');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error copying referral URL to clipboard: $e');
      }
    }
  }
  
  /// Handle incoming deep link
  static void handleDeepLink(String url, Function(String) onReferralCodeFound) {
    try {
      final uri = Uri.parse(url);
      final referralCode = uri.queryParameters[_referralParam];
      
      if (referralCode != null && referralCode.isNotEmpty) {
        onReferralCodeFound(referralCode);
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error handling deep link: $e');
      }
    }
  }
  
  /// Initialize deep link listening (for mobile apps)
  static void initializeDeepLinkHandling(Function(String) onReferralCodeFound) {
    // For web, we check URL parameters on page load
    if (kIsWeb) {
      final referralCode = getReferralCodeFromUrl();
      if (referralCode != null) {
        onReferralCodeFound(referralCode);
      }
    }
    // For mobile, you would set up app link/universal link handling here
  }
  
  /// Validate referral URL format
  static bool isValidReferralUrl(String url) {
    try {
      final uri = Uri.parse(url);
      return uri.queryParameters.containsKey(_referralParam) &&
             uri.queryParameters[_referralParam]!.isNotEmpty;
    } catch (e) {
      return false;
    }
  }
}
