import 'package:flutter/material.dart';
import 'package:flutter_test/flutter_test.dart';
import 'package:integration_test/integration_test.dart';
import 'package:rama_realty_mlm/main.dart' as app;

void main() {
  IntegrationTestWidgetsFlutterBinding.ensureInitialized();

  group('Authentication Flow Integration Tests', () {
    testWidgets('complete registration and login flow', (
      WidgetTester tester,
    ) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should start on login page
      expect(find.text('Welcome Back'), findsOneWidget);
      expect(find.text('Sign In'), findsOneWidget);

      // Navigate to registration
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Should be on registration page
      expect(find.text('Create Account'), findsOneWidget);
      expect(find.text('Join <PERSON>'), findsOneWidget);

      // Fill registration form
      await tester.enterText(find.byKey(const Key('name_field')), 'Test User');
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.by<PERSON>ey(const Key('phone_field')),
        '+************',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password123',
      );
      await tester.enterText(
        find.byKey(const Key('confirm_password_field')),
        'password123',
      );

      // Optional: Enter referral code
      await tester.enterText(
        find.byKey(const Key('referral_code_field')),
        'REF123',
      );

      await tester.pumpAndSettle();

      // Submit registration
      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should show success message or navigate to verification
      expect(
        find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              (widget.data!.contains('Registration successful') ||
                  widget.data!.contains('Verification email sent') ||
                  widget.data!.contains('Verify Email')),
        ),
        findsOneWidget,
      );

      // If on verification page, simulate email verification
      if (find.text('Verify Email').evaluate().isNotEmpty) {
        // Simulate email verification (in real test, this would require email verification)
        await tester.tap(find.text('Resend Email'));
        await tester.pumpAndSettle();

        // For testing purposes, navigate back to login
        await tester.tap(find.text('Back to Login'));
        await tester.pumpAndSettle();
      }

      // Should be back on login page
      expect(find.text('Welcome Back'), findsOneWidget);

      // Test login with registered credentials
      await tester.enterText(
        find.byKey(const Key('login_email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('login_password_field')),
        'password123',
      );

      await tester.pumpAndSettle();

      // Submit login
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should navigate to dashboard or show verification required
      expect(
        find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              (widget.data!.contains('Dashboard') ||
                  widget.data!.contains('Email verification required') ||
                  widget.data!.contains('Welcome')),
        ),
        findsOneWidget,
      );
    });

    testWidgets('login with invalid credentials shows error', (
      WidgetTester tester,
    ) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should be on login page
      expect(find.text('Welcome Back'), findsOneWidget);

      // Enter invalid credentials
      await tester.enterText(
        find.byKey(const Key('login_email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('login_password_field')),
        'wrongpassword',
      );

      await tester.pumpAndSettle();

      // Submit login
      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should show error message
      expect(
        find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              (widget.data!.contains('Invalid credentials') ||
                  widget.data!.contains('Login failed') ||
                  widget.data!.contains('No user found') ||
                  widget.data!.contains('Wrong password')),
        ),
        findsOneWidget,
      );
    });

    testWidgets('registration with invalid data shows validation errors', (
      WidgetTester tester,
    ) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to registration
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Test empty form submission
      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle();

      // Should show validation errors
      expect(find.textContaining('required'), findsWidgets);

      // Test invalid email format
      await tester.enterText(
        find.byKey(const Key('email_field')),
        'invalid-email',
      );
      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle();

      expect(find.textContaining('valid email'), findsOneWidget);

      // Test password mismatch
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password123',
      );
      await tester.enterText(
        find.byKey(const Key('confirm_password_field')),
        'different123',
      );

      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle();

      expect(find.textContaining('Passwords do not match'), findsOneWidget);

      // Test weak password
      await tester.enterText(find.byKey(const Key('password_field')), '123');
      await tester.enterText(
        find.byKey(const Key('confirm_password_field')),
        '123',
      );

      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle();

      expect(
        find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              (widget.data!.contains('Password must be') ||
                  widget.data!.contains('too weak')),
        ),
        findsOneWidget,
      );
    });

    testWidgets('forgot password flow works correctly', (
      WidgetTester tester,
    ) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Should be on login page
      expect(find.text('Welcome Back'), findsOneWidget);

      // Tap forgot password
      await tester.tap(find.text('Forgot Password?'));
      await tester.pumpAndSettle();

      // Should be on forgot password page
      expect(find.text('Reset Password'), findsOneWidget);

      // Enter email
      await tester.enterText(
        find.byKey(const Key('reset_email_field')),
        '<EMAIL>',
      );
      await tester.pumpAndSettle();

      // Submit reset request
      await tester.tap(find.byKey(const Key('reset_button')));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should show success message
      expect(
        find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              (widget.data!.contains('Reset email sent') ||
                  widget.data!.contains('Check your email')),
        ),
        findsOneWidget,
      );

      // Should be able to go back to login
      await tester.tap(find.text('Back to Login'));
      await tester.pumpAndSettle();

      expect(find.text('Welcome Back'), findsOneWidget);
    });

    testWidgets('user can logout successfully', (WidgetTester tester) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // First, login with valid credentials (assuming user exists)
      await tester.enterText(
        find.byKey(const Key('login_email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('login_password_field')),
        'admin123',
      );

      await tester.tap(find.byKey(const Key('login_button')));
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // If successfully logged in, should be on dashboard
      if (find.text('Dashboard').evaluate().isNotEmpty ||
          find.byIcon(Icons.menu).evaluate().isNotEmpty) {
        // Open drawer or menu
        if (find.byIcon(Icons.menu).evaluate().isNotEmpty) {
          await tester.tap(find.byIcon(Icons.menu));
          await tester.pumpAndSettle();
        }

        // Find and tap logout
        await tester.tap(
          find.byWidgetPredicate(
            (widget) =>
                (widget is Text && widget.data == 'Logout') ||
                (widget is Icon && widget.icon == Icons.logout),
          ),
        );
        await tester.pumpAndSettle();

        // Should show confirmation dialog
        if (find.text('Confirm Logout').evaluate().isNotEmpty) {
          await tester.tap(find.text('Logout'));
          await tester.pumpAndSettle();
        }

        // Should be back on login page
        expect(find.text('Welcome Back'), findsOneWidget);
      }
    });

    testWidgets('referral code validation works correctly', (
      WidgetTester tester,
    ) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to registration
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Fill form with invalid referral code
      await tester.enterText(find.byKey(const Key('name_field')), 'Test User');
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('phone_field')),
        '+************',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password123',
      );
      await tester.enterText(
        find.byKey(const Key('confirm_password_field')),
        'password123',
      );
      await tester.enterText(
        find.byKey(const Key('referral_code_field')),
        'INVALID123',
      );

      await tester.pumpAndSettle();

      // Submit registration
      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle(const Duration(seconds: 3));

      // Should show error for invalid referral code
      expect(
        find.byWidgetPredicate(
          (widget) =>
              widget is Text &&
              (widget.data!.contains('Invalid referral code') ||
                  widget.data!.contains('Referral code not found')),
        ),
        findsOneWidget,
      );
    });

    testWidgets('email verification flow works correctly', (
      WidgetTester tester,
    ) async {
      // Start the app
      app.main();
      await tester.pumpAndSettle();

      // Navigate to registration
      await tester.tap(find.text('Create Account'));
      await tester.pumpAndSettle();

      // Fill registration form
      await tester.enterText(
        find.byKey(const Key('name_field')),
        'Verification Test',
      );
      await tester.enterText(
        find.byKey(const Key('email_field')),
        '<EMAIL>',
      );
      await tester.enterText(
        find.byKey(const Key('phone_field')),
        '+************',
      );
      await tester.enterText(
        find.byKey(const Key('password_field')),
        'password123',
      );
      await tester.enterText(
        find.byKey(const Key('confirm_password_field')),
        'password123',
      );

      await tester.pumpAndSettle();

      // Submit registration
      await tester.tap(find.byKey(const Key('register_button')));
      await tester.pumpAndSettle(const Duration(seconds: 5));

      // Should navigate to email verification page
      if (find.text('Verify Email').evaluate().isNotEmpty) {
        expect(find.text('Verify Email'), findsOneWidget);
        expect(find.textContaining('verification email'), findsOneWidget);

        // Test resend email functionality
        await tester.tap(find.text('Resend Email'));
        await tester.pumpAndSettle(const Duration(seconds: 2));

        expect(
          find.byWidgetPredicate(
            (widget) =>
                widget is Text &&
                (widget.data!.contains('Email sent') ||
                    widget.data!.contains('Resent')),
          ),
          findsOneWidget,
        );

        // Test back to login
        await tester.tap(find.text('Back to Login'));
        await tester.pumpAndSettle();

        expect(find.text('Welcome Back'), findsOneWidget);
      }
    });
  });
}
