import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../../../core/constants/app_constants.dart';
import '../../../core/models/user_model.dart';
import '../../commissions/models/commission_enums.dart';
import '../models/network_models.dart';

/// Service for managing MLM network data and operations
class NetworkService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Build complete network tree for an agent
  static Future<NetworkNode?> buildNetworkTree(String rootAgentId, {
    int maxDepth = 5,
  }) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock network tree
        return _buildMockNetworkTree();
      }

      // Get root agent
      final rootAgent = await _getAgentById(rootAgentId);
      if (rootAgent == null) return null;

      // Build tree recursively
      return await _buildNodeRecursively(rootAgent, 0, maxDepth);
    } catch (e) {
      if (kDebugMode) {
        print('Error building network tree: $e');
      }
      return null;
    }
  }

  /// Build network node recursively
  static Future<NetworkNode> _buildNodeRecursively(
    UserModel agent,
    int currentDepth,
    int maxDepth,
  ) async {
    final children = <NetworkNode>[];

    // Build children if not at max depth
    if (currentDepth < maxDepth && agent.downlineIds.isNotEmpty) {
      for (final childId in agent.downlineIds) {
        final childAgent = await _getAgentById(childId);
        if (childAgent != null) {
          final childNode = await _buildNodeRecursively(
            childAgent,
            currentDepth + 1,
            maxDepth,
          );
          children.add(childNode);
        }
      }
    }

    // Get performance metrics
    final performance = await _getAgentPerformance(agent.id);
    
    // Calculate tier based on sales
    final tier = _calculateTier(agent.totalSales);

    return NetworkNode.fromUser(
      agent,
      children: children,
      performance: performance,
    ).copyWith(tier: tier);
  }

  /// Get agent by ID
  static Future<UserModel?> _getAgentById(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock agent
        return _getMockAgent(agentId);
      }

      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(agentId)
          .get();

      if (doc.exists) {
        return UserModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      return null;
    }
  }

  /// Get agent performance metrics
  static Future<NetworkPerformance> _getAgentPerformance(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock performance
        return NetworkPerformance(
          monthlyGrowth: (15 + (agentId.hashCode % 20)).toDouble(),
          quarterlyGrowth: (25 + (agentId.hashCode % 30)).toDouble(),
          yearlyGrowth: (40 + (agentId.hashCode % 40)).toDouble(),
          newRecruits: 2 + (agentId.hashCode % 5),
          activeAgents: 5 + (agentId.hashCode % 10),
          teamSales: 500000 + (agentId.hashCode % 1000000).toDouble(),
          teamCommissions: 25000 + (agentId.hashCode % 50000).toDouble(),
          conversionRate: 0.15 + ((agentId.hashCode % 20) / 100),
          retentionRate: 0.80 + ((agentId.hashCode % 15) / 100),
        );
      }

      // Calculate real performance metrics from database
      final now = DateTime.now();
      final monthStart = DateTime(now.year, now.month, 1);
      final quarterStart = DateTime(now.year, ((now.month - 1) ~/ 3) * 3 + 1, 1);
      final yearStart = DateTime(now.year, 1, 1);

      // Get commission data for calculations
      final commissionSnapshot = await _firestore
          .collection('commissions')
          .where('agentId', isEqualTo: agentId)
          .where('status', isEqualTo: CommissionStatus.paid.value)
          .get();

      // Calculate metrics (simplified implementation)
      double monthlyGrowth = 0;
      double quarterlyGrowth = 0;
      double yearlyGrowth = 0;
      
      // This would involve complex calculations based on historical data
      // For now, return basic metrics
      return NetworkPerformance(
        monthlyGrowth: monthlyGrowth,
        quarterlyGrowth: quarterlyGrowth,
        yearlyGrowth: yearlyGrowth,
        newRecruits: 0,
        activeAgents: 0,
        teamSales: 0,
        teamCommissions: 0,
        conversionRate: 0,
        retentionRate: 0,
      );
    } catch (e) {
      return NetworkPerformance.empty();
    }
  }

  /// Calculate tier based on sales
  static CommissionTier _calculateTier(double totalSales) {
    for (final tier in CommissionTier.values.reversed) {
      if (totalSales >= tier.minimumSales) {
        return tier;
      }
    }
    return CommissionTier.bronze;
  }

  /// Get network statistics
  static Future<NetworkStatistics> getNetworkStatistics(String rootAgentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock statistics
        return _getMockNetworkStatistics();
      }

      // Build network tree to calculate statistics
      final networkTree = await buildNetworkTree(rootAgentId);
      if (networkTree == null) {
        return _getEmptyNetworkStatistics();
      }

      // Calculate statistics from tree
      final allNodes = _getAllNodesFromTree(networkTree);
      
      final totalAgents = allNodes.length;
      final activeAgents = allNodes.where((n) => n.isActive).length;
      final inactiveAgents = totalAgents - activeAgents;
      
      final totalSales = allNodes.fold(0.0, (sum, n) => sum + n.totalSales);
      final totalCommissions = allNodes.fold(0.0, (sum, n) => sum + n.totalCommissions);
      
      // Calculate level distribution
      final levelDistribution = <int, int>{};
      for (final node in allNodes) {
        levelDistribution[node.level] = (levelDistribution[node.level] ?? 0) + 1;
      }
      
      // Calculate tier distribution
      final tierDistribution = <CommissionTier, int>{};
      for (final tier in CommissionTier.values) {
        tierDistribution[tier] = allNodes.where((n) => n.tier == tier).length;
      }
      
      final averageTeamSize = totalAgents > 0 
          ? allNodes.fold(0, (sum, n) => sum + n.children.length) / totalAgents
          : 0.0;
      
      // Get top performers
      final topPerformers = _getTopPerformersFromNodes(allNodes);

      return NetworkStatistics(
        totalAgents: totalAgents,
        activeAgents: activeAgents,
        inactiveAgents: inactiveAgents,
        totalSales: totalSales,
        totalCommissions: totalCommissions,
        levelDistribution: levelDistribution,
        tierDistribution: tierDistribution,
        averageTeamSize: averageTeamSize,
        networkGrowthRate: 15.5, // Would be calculated from historical data
        topPerformers: topPerformers,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting network statistics: $e');
      }
      return _getEmptyNetworkStatistics();
    }
  }

  /// Get all nodes from tree (flattened)
  static List<NetworkNode> _getAllNodesFromTree(NetworkNode root) {
    final nodes = <NetworkNode>[root];
    for (final child in root.children) {
      nodes.addAll(_getAllNodesFromTree(child));
    }
    return nodes;
  }

  /// Get top performers from nodes
  static List<TopPerformer> _getTopPerformersFromNodes(List<NetworkNode> nodes) {
    final performers = nodes.map((node) => TopPerformer(
      id: node.id,
      name: node.name,
      profileImageUrl: node.profileImageUrl,
      sales: node.totalSales,
      commissions: node.totalCommissions,
      teamSize: node.totalNetworkSize - 1, // Exclude self
      tier: node.tier,
    )).toList();

    performers.sort((a, b) => b.sales.compareTo(a.sales));
    return performers.take(10).toList();
  }

  /// Search agents in network
  static Future<List<NetworkNode>> searchAgentsInNetwork(
    String rootAgentId,
    String query,
  ) async {
    try {
      final networkTree = await buildNetworkTree(rootAgentId);
      if (networkTree == null) return [];

      final allNodes = _getAllNodesFromTree(networkTree);
      final lowerQuery = query.toLowerCase();

      return allNodes.where((node) {
        return node.name.toLowerCase().contains(lowerQuery) ||
               node.email.toLowerCase().contains(lowerQuery) ||
               (node.phoneNumber?.contains(query) ?? false);
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Get agent path to root
  static Future<List<NetworkNode>> getAgentPathToRoot(
    String agentId,
    String rootAgentId,
  ) async {
    try {
      final path = <NetworkNode>[];
      String? currentId = agentId;

      while (currentId != null && currentId != rootAgentId) {
        final agent = await _getAgentById(currentId);
        if (agent == null) break;

        final performance = await _getAgentPerformance(agent.id);
        final tier = _calculateTier(agent.totalSales);
        
        final node = NetworkNode.fromUser(agent, performance: performance)
            .copyWith(tier: tier);
        
        path.add(node);
        currentId = agent.uplineId;
      }

      // Add root if reached
      if (currentId == rootAgentId) {
        final rootAgent = await _getAgentById(rootAgentId);
        if (rootAgent != null) {
          final performance = await _getAgentPerformance(rootAgent.id);
          final tier = _calculateTier(rootAgent.totalSales);
          
          final rootNode = NetworkNode.fromUser(rootAgent, performance: performance)
              .copyWith(tier: tier);
          
          path.add(rootNode);
        }
      }

      return path.reversed.toList(); // Return from root to agent
    } catch (e) {
      return [];
    }
  }

  /// Development mode helpers
  static NetworkNode _buildMockNetworkTree() {
    return NetworkNode(
      id: 'root',
      name: 'John Doe',
      email: '<EMAIL>',
      level: 0,
      tier: CommissionTier.gold,
      totalSales: 2500000,
      totalCommissions: 125000,
      downlineCount: 8,
      isActive: true,
      joinDate: DateTime.now().subtract(const Duration(days: 365)),
      performance: const NetworkPerformance(
        monthlyGrowth: 25,
        quarterlyGrowth: 35,
        yearlyGrowth: 50,
        newRecruits: 3,
        activeAgents: 8,
        teamSales: 5000000,
        teamCommissions: 250000,
        conversionRate: 0.25,
        retentionRate: 0.85,
      ),
      children: [
        NetworkNode(
          id: 'child1',
          name: 'Jane Smith',
          email: '<EMAIL>',
          level: 1,
          tier: CommissionTier.silver,
          totalSales: 1800000,
          totalCommissions: 90000,
          downlineCount: 3,
          isActive: true,
          joinDate: DateTime.now().subtract(const Duration(days: 200)),
          uplineId: 'root',
          performance: const NetworkPerformance(
            monthlyGrowth: 20,
            quarterlyGrowth: 30,
            yearlyGrowth: 45,
            newRecruits: 2,
            activeAgents: 3,
            teamSales: 2000000,
            teamCommissions: 100000,
            conversionRate: 0.20,
            retentionRate: 0.80,
          ),
          children: [
            NetworkNode(
              id: 'grandchild1',
              name: 'Mike Johnson',
              email: '<EMAIL>',
              level: 2,
              tier: CommissionTier.bronze,
              totalSales: 800000,
              totalCommissions: 40000,
              downlineCount: 1,
              isActive: true,
              joinDate: DateTime.now().subtract(const Duration(days: 100)),
              uplineId: 'child1',
              performance: const NetworkPerformance(
                monthlyGrowth: 15,
                quarterlyGrowth: 25,
                yearlyGrowth: 40,
                newRecruits: 1,
                activeAgents: 1,
                teamSales: 900000,
                teamCommissions: 45000,
                conversionRate: 0.15,
                retentionRate: 0.75,
              ),
            ),
          ],
        ),
        NetworkNode(
          id: 'child2',
          name: 'Sarah Wilson',
          email: '<EMAIL>',
          level: 1,
          tier: CommissionTier.silver,
          totalSales: 1500000,
          totalCommissions: 75000,
          downlineCount: 2,
          isActive: true,
          joinDate: DateTime.now().subtract(const Duration(days: 150)),
          uplineId: 'root',
          performance: const NetworkPerformance(
            monthlyGrowth: 18,
            quarterlyGrowth: 28,
            yearlyGrowth: 42,
            newRecruits: 1,
            activeAgents: 2,
            teamSales: 1800000,
            teamCommissions: 90000,
            conversionRate: 0.18,
            retentionRate: 0.82,
          ),
        ),
      ],
    );
  }

  static UserModel _getMockAgent(String agentId) {
    return UserModel(
      id: agentId,
      email: 'agent$<EMAIL>',
      name: 'Agent $agentId',
      phoneNumber: '+91-98765-43210',
      role: AppConstants.agentRole,
      level: agentId.hashCode % 5,
      totalSales: (500000 + (agentId.hashCode % 2000000)).toDouble(),
      totalCommissions: (25000 + (agentId.hashCode % 100000)).toDouble(),
      downlineIds: [], // Would be populated based on relationships
      isActive: true,
      createdAt: DateTime.now().subtract(Duration(days: agentId.hashCode % 365)),
      updatedAt: DateTime.now(),
    );
  }

  static NetworkStatistics _getMockNetworkStatistics() {
    return NetworkStatistics(
      totalAgents: 25,
      activeAgents: 22,
      inactiveAgents: 3,
      totalSales: 12500000,
      totalCommissions: 625000,
      levelDistribution: {0: 1, 1: 4, 2: 8, 3: 7, 4: 5},
      tierDistribution: {
        CommissionTier.bronze: 15,
        CommissionTier.silver: 7,
        CommissionTier.gold: 2,
        CommissionTier.platinum: 1,
        CommissionTier.diamond: 0,
      },
      averageTeamSize: 3.2,
      networkGrowthRate: 18.5,
      topPerformers: [
        const TopPerformer(
          id: 'top1',
          name: 'John Doe',
          sales: 2500000,
          commissions: 125000,
          teamSize: 8,
          tier: CommissionTier.gold,
        ),
        const TopPerformer(
          id: 'top2',
          name: 'Jane Smith',
          sales: 1800000,
          commissions: 90000,
          teamSize: 5,
          tier: CommissionTier.silver,
        ),
      ],
    );
  }

  static NetworkStatistics _getEmptyNetworkStatistics() {
    return NetworkStatistics(
      totalAgents: 0,
      activeAgents: 0,
      inactiveAgents: 0,
      totalSales: 0,
      totalCommissions: 0,
      levelDistribution: const {},
      tierDistribution: Map.fromEntries(
        CommissionTier.values.map((tier) => MapEntry(tier, 0)),
      ),
      averageTeamSize: 0,
      networkGrowthRate: 0,
      topPerformers: const [],
    );
  }
}
