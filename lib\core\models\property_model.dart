import 'package:cloud_firestore/cloud_firestore.dart';

/// Property model representing real estate listings
class PropertyModel {
  final String id;
  final String title;
  final String description;
  final String type; // residential, commercial, land, etc.
  final String status; // for_sale, for_rent, sold, rented
  final double price;
  final String currency;
  final String location;
  final String city;
  final String state;
  final String pincode;
  final double? latitude;
  final double? longitude;
  final double? areaSquareFeet;
  final int? bedrooms;
  final int? bathrooms;
  final List<String> amenities;
  final List<String> imageUrls;
  final String? videoUrl;
  final String ownerId; // Admin who added the property
  final String? assignedAgentId; // Agent handling this property
  final bool isApproved;
  final bool isFeatured;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalDetails;

  const PropertyModel({
    required this.id,
    required this.title,
    required this.description,
    required this.type,
    required this.status,
    required this.price,
    this.currency = 'INR',
    required this.location,
    required this.city,
    required this.state,
    required this.pincode,
    this.latitude,
    this.longitude,
    this.areaSquareFeet,
    this.bedrooms,
    this.bathrooms,
    this.amenities = const [],
    this.imageUrls = const [],
    this.videoUrl,
    required this.ownerId,
    this.assignedAgentId,
    this.isApproved = false,
    this.isFeatured = false,
    required this.createdAt,
    required this.updatedAt,
    this.additionalDetails,
  });

  /// Create PropertyModel from Firestore document
  factory PropertyModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return PropertyModel(
      id: doc.id,
      title: data['title'] ?? '',
      description: data['description'] ?? '',
      type: data['type'] ?? '',
      status: data['status'] ?? 'for_sale',
      price: (data['price'] ?? 0.0).toDouble(),
      currency: data['currency'] ?? 'INR',
      location: data['location'] ?? '',
      city: data['city'] ?? '',
      state: data['state'] ?? '',
      pincode: data['pincode'] ?? '',
      latitude: data['latitude']?.toDouble(),
      longitude: data['longitude']?.toDouble(),
      areaSquareFeet: data['areaSquareFeet']?.toDouble(),
      bedrooms: data['bedrooms'],
      bathrooms: data['bathrooms'],
      amenities: List<String>.from(data['amenities'] ?? []),
      imageUrls: List<String>.from(data['imageUrls'] ?? []),
      videoUrl: data['videoUrl'],
      ownerId: data['ownerId'] ?? '',
      assignedAgentId: data['assignedAgentId'],
      isApproved: data['isApproved'] ?? false,
      isFeatured: data['isFeatured'] ?? false,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      additionalDetails: data['additionalDetails'],
    );
  }

  /// Convert PropertyModel to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'title': title,
      'description': description,
      'type': type,
      'status': status,
      'price': price,
      'currency': currency,
      'location': location,
      'city': city,
      'state': state,
      'pincode': pincode,
      'latitude': latitude,
      'longitude': longitude,
      'areaSquareFeet': areaSquareFeet,
      'bedrooms': bedrooms,
      'bathrooms': bathrooms,
      'amenities': amenities,
      'imageUrls': imageUrls,
      'videoUrl': videoUrl,
      'ownerId': ownerId,
      'assignedAgentId': assignedAgentId,
      'isApproved': isApproved,
      'isFeatured': isFeatured,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalDetails': additionalDetails,
    };
  }

  /// Create a copy with updated fields
  PropertyModel copyWith({
    String? id,
    String? title,
    String? description,
    String? type,
    String? status,
    double? price,
    String? currency,
    String? location,
    String? city,
    String? state,
    String? pincode,
    double? latitude,
    double? longitude,
    double? areaSquareFeet,
    int? bedrooms,
    int? bathrooms,
    List<String>? amenities,
    List<String>? imageUrls,
    String? videoUrl,
    String? ownerId,
    String? assignedAgentId,
    bool? isApproved,
    bool? isFeatured,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalDetails,
  }) {
    return PropertyModel(
      id: id ?? this.id,
      title: title ?? this.title,
      description: description ?? this.description,
      type: type ?? this.type,
      status: status ?? this.status,
      price: price ?? this.price,
      currency: currency ?? this.currency,
      location: location ?? this.location,
      city: city ?? this.city,
      state: state ?? this.state,
      pincode: pincode ?? this.pincode,
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      areaSquareFeet: areaSquareFeet ?? this.areaSquareFeet,
      bedrooms: bedrooms ?? this.bedrooms,
      bathrooms: bathrooms ?? this.bathrooms,
      amenities: amenities ?? this.amenities,
      imageUrls: imageUrls ?? this.imageUrls,
      videoUrl: videoUrl ?? this.videoUrl,
      ownerId: ownerId ?? this.ownerId,
      assignedAgentId: assignedAgentId ?? this.assignedAgentId,
      isApproved: isApproved ?? this.isApproved,
      isFeatured: isFeatured ?? this.isFeatured,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalDetails: additionalDetails ?? this.additionalDetails,
    );
  }

  /// Check if property is available for sale/rent
  bool get isAvailable => status == 'for_sale' || status == 'for_rent';

  /// Check if property is sold/rented
  bool get isSoldOrRented => status == 'sold' || status == 'rented';

  /// Get formatted price
  String get formattedPrice {
    if (price >= 10000000) {
      return '₹${(price / 10000000).toStringAsFixed(2)} Cr';
    } else if (price >= 100000) {
      return '₹${(price / 100000).toStringAsFixed(2)} L';
    } else {
      return '₹${price.toStringAsFixed(0)}';
    }
  }

  /// Get property address
  String get fullAddress => '$location, $city, $state - $pincode';

  /// Get primary image URL
  String? get primaryImageUrl => imageUrls.isNotEmpty ? imageUrls.first : null;

  @override
  String toString() {
    return 'PropertyModel(id: $id, title: $title, type: $type, status: $status, price: $price)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is PropertyModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
