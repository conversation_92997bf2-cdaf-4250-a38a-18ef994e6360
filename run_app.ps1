# Rama Realty MLM Application Runner
# PowerShell script to run the Flutter app

Write-Host "========================================" -ForegroundColor Cyan
Write-Host "    Rama Realty MLM Application" -ForegroundColor Cyan
Write-Host "========================================" -ForegroundColor Cyan
Write-Host ""

# Step 1: Check Flutter installation
Write-Host "Step 1: Checking Flutter installation..." -ForegroundColor Yellow
try {
    $flutterVersion = flutter --version
    Write-Host "Flutter is installed:" -ForegroundColor Green
    Write-Host $flutterVersion
} catch {
    Write-Host "Error: Flutter is not installed or not in PATH" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Step 2: Clean previous builds
Write-Host "Step 2: Cleaning previous builds..." -ForegroundColor Yellow
try {
    flutter clean
    Write-Host "Clean completed successfully" -ForegroundColor Green
} catch {
    Write-Host "Warning: Clean failed, continuing..." -ForegroundColor Yellow
}

Write-Host ""

# Step 3: Get dependencies
Write-Host "Step 3: Getting dependencies..." -ForegroundColor Yellow
try {
    flutter pub get
    Write-Host "Dependencies installed successfully" -ForegroundColor Green
} catch {
    Write-Host "Error: Failed to get dependencies" -ForegroundColor Red
    Write-Host "Try running: flutter pub cache repair" -ForegroundColor Yellow
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Step 4: Check available devices
Write-Host "Step 4: Checking for available devices..." -ForegroundColor Yellow
try {
    $devices = flutter devices
    Write-Host "Available devices:" -ForegroundColor Green
    Write-Host $devices
} catch {
    Write-Host "Error: Failed to check devices" -ForegroundColor Red
    Read-Host "Press Enter to exit"
    exit 1
}

Write-Host ""

# Step 5: Run the application
Write-Host "Step 5: Running on Windows Desktop..." -ForegroundColor Yellow
Write-Host "This may take a few minutes for first build..." -ForegroundColor Cyan
Write-Host "The app will open in a new window when ready." -ForegroundColor Cyan
Write-Host ""

try {
    flutter run -d windows --debug
} catch {
    Write-Host "Error: Failed to run the application" -ForegroundColor Red
    Write-Host "Check the error messages above for details" -ForegroundColor Yellow
}

Write-Host ""
Write-Host "Application finished." -ForegroundColor Green
Read-Host "Press Enter to exit"
