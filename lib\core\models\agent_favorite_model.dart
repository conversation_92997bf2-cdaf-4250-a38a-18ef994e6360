import 'package:cloud_firestore/cloud_firestore.dart';

/// Agent favorite model for tracking agent's favorite/bookmarked properties
class AgentFavoriteModel {
  final String id;
  final String agentId;
  final String agentName;
  final String propertyId;
  final String propertyTitle;
  final String propertyType;
  final String propertyLocation;
  final double propertyPrice;
  final String? notes;
  final List<String> tags; // Custom tags like 'hot_lead', 'follow_up', 'priority'
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalInfo;

  const AgentFavoriteModel({
    required this.id,
    required this.agentId,
    required this.agentName,
    required this.propertyId,
    required this.propertyTitle,
    required this.propertyType,
    required this.propertyLocation,
    required this.propertyPrice,
    this.notes,
    this.tags = const [],
    required this.createdAt,
    required this.updatedAt,
    this.additionalInfo,
  });

  /// Create AgentFavoriteModel from Firestore document
  factory AgentFavoriteModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AgentFavoriteModel(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      propertyId: data['propertyId'] ?? '',
      propertyTitle: data['propertyTitle'] ?? '',
      propertyType: data['propertyType'] ?? '',
      propertyLocation: data['propertyLocation'] ?? '',
      propertyPrice: data['propertyPrice']?.toDouble() ?? 0.0,
      notes: data['notes'],
      tags: List<String>.from(data['tags'] ?? []),
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      additionalInfo: data['additionalInfo'],
    );
  }

  /// Convert AgentFavoriteModel to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'agentId': agentId,
      'agentName': agentName,
      'propertyId': propertyId,
      'propertyTitle': propertyTitle,
      'propertyType': propertyType,
      'propertyLocation': propertyLocation,
      'propertyPrice': propertyPrice,
      'notes': notes,
      'tags': tags,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalInfo': additionalInfo,
    };
  }

  /// Create a copy with updated fields
  AgentFavoriteModel copyWith({
    String? id,
    String? agentId,
    String? agentName,
    String? propertyId,
    String? propertyTitle,
    String? propertyType,
    String? propertyLocation,
    double? propertyPrice,
    String? notes,
    List<String>? tags,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalInfo,
  }) {
    return AgentFavoriteModel(
      id: id ?? this.id,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      propertyId: propertyId ?? this.propertyId,
      propertyTitle: propertyTitle ?? this.propertyTitle,
      propertyType: propertyType ?? this.propertyType,
      propertyLocation: propertyLocation ?? this.propertyLocation,
      propertyPrice: propertyPrice ?? this.propertyPrice,
      notes: notes ?? this.notes,
      tags: tags ?? this.tags,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  /// Get formatted property price
  String get formattedPrice {
    if (propertyPrice >= 10000000) {
      return '₹${(propertyPrice / 10000000).toStringAsFixed(1)} Cr';
    } else if (propertyPrice >= 100000) {
      return '₹${(propertyPrice / 100000).toStringAsFixed(1)} L';
    } else if (propertyPrice >= 1000) {
      return '₹${(propertyPrice / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${propertyPrice.toStringAsFixed(0)}';
    }
  }

  /// Check if favorite has specific tag
  bool hasTag(String tag) {
    return tags.contains(tag);
  }

  /// Get days since added to favorites
  int get daysSinceAdded {
    return DateTime.now().difference(createdAt).inDays;
  }
}

/// Agent property portfolio model for tracking agent's property assignments and performance
class AgentPropertyPortfolioModel {
  final String id;
  final String agentId;
  final String agentName;
  final String propertyId;
  final String propertyTitle;
  final String propertyType;
  final String propertyLocation;
  final double propertyPrice;
  final String status; // 'assigned', 'promoting', 'leads_generated', 'sold', 'inactive'
  final int leadsCount;
  final int viewsCount;
  final int sharesCount;
  final DateTime assignedAt;
  final DateTime? lastActivityAt;
  final Map<String, dynamic>? performanceMetrics;

  const AgentPropertyPortfolioModel({
    required this.id,
    required this.agentId,
    required this.agentName,
    required this.propertyId,
    required this.propertyTitle,
    required this.propertyType,
    required this.propertyLocation,
    required this.propertyPrice,
    this.status = 'assigned',
    this.leadsCount = 0,
    this.viewsCount = 0,
    this.sharesCount = 0,
    required this.assignedAt,
    this.lastActivityAt,
    this.performanceMetrics,
  });

  /// Create from Firestore document
  factory AgentPropertyPortfolioModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return AgentPropertyPortfolioModel(
      id: doc.id,
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      propertyId: data['propertyId'] ?? '',
      propertyTitle: data['propertyTitle'] ?? '',
      propertyType: data['propertyType'] ?? '',
      propertyLocation: data['propertyLocation'] ?? '',
      propertyPrice: data['propertyPrice']?.toDouble() ?? 0.0,
      status: data['status'] ?? 'assigned',
      leadsCount: data['leadsCount'] ?? 0,
      viewsCount: data['viewsCount'] ?? 0,
      sharesCount: data['sharesCount'] ?? 0,
      assignedAt: (data['assignedAt'] as Timestamp).toDate(),
      lastActivityAt: data['lastActivityAt'] != null 
          ? (data['lastActivityAt'] as Timestamp).toDate() 
          : null,
      performanceMetrics: data['performanceMetrics'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'agentId': agentId,
      'agentName': agentName,
      'propertyId': propertyId,
      'propertyTitle': propertyTitle,
      'propertyType': propertyType,
      'propertyLocation': propertyLocation,
      'propertyPrice': propertyPrice,
      'status': status,
      'leadsCount': leadsCount,
      'viewsCount': viewsCount,
      'sharesCount': sharesCount,
      'assignedAt': Timestamp.fromDate(assignedAt),
      'lastActivityAt': lastActivityAt != null 
          ? Timestamp.fromDate(lastActivityAt!) 
          : null,
      'performanceMetrics': performanceMetrics,
    };
  }

  /// Get formatted property price
  String get formattedPrice {
    if (propertyPrice >= 10000000) {
      return '₹${(propertyPrice / 10000000).toStringAsFixed(1)} Cr';
    } else if (propertyPrice >= 100000) {
      return '₹${(propertyPrice / 100000).toStringAsFixed(1)} L';
    } else if (propertyPrice >= 1000) {
      return '₹${(propertyPrice / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${propertyPrice.toStringAsFixed(0)}';
    }
  }

  /// Get status display name
  String get statusDisplayName {
    switch (status) {
      case 'assigned': return 'Assigned';
      case 'promoting': return 'Actively Promoting';
      case 'leads_generated': return 'Leads Generated';
      case 'sold': return 'Sold';
      case 'inactive': return 'Inactive';
      default: return 'Unknown';
    }
  }

  /// Get performance score (0-100)
  double get performanceScore {
    double score = 0.0;
    
    // Base score for having leads
    if (leadsCount > 0) score += 30.0;
    
    // Score for views (up to 20 points)
    score += (viewsCount * 2.0).clamp(0.0, 20.0);
    
    // Score for shares (up to 25 points)
    score += (sharesCount * 5.0).clamp(0.0, 25.0);
    
    // Score for recent activity (up to 25 points)
    if (lastActivityAt != null) {
      final daysSinceActivity = DateTime.now().difference(lastActivityAt!).inDays;
      if (daysSinceActivity <= 7) {
        score += 25.0;
      } else if (daysSinceActivity <= 30) {
        score += 15.0;
      } else if (daysSinceActivity <= 60) {
        score += 5.0;
      }
    }
    
    return score.clamp(0.0, 100.0);
  }

  /// Get days since assignment
  int get daysSinceAssigned {
    return DateTime.now().difference(assignedAt).inDays;
  }

  /// Check if property needs attention (no activity in 7 days)
  bool get needsAttention {
    if (lastActivityAt == null) return daysSinceAssigned > 7;
    return DateTime.now().difference(lastActivityAt!).inDays > 7;
  }
}

/// Property sharing analytics model
class PropertySharingModel {
  final String id;
  final String propertyId;
  final String agentId;
  final String shareMethod; // 'whatsapp', 'email', 'direct_link', 'sms'
  final String? recipientInfo; // Phone number, email, or other identifier
  final DateTime sharedAt;
  final bool wasViewed;
  final DateTime? viewedAt;
  final Map<String, dynamic>? metadata;

  const PropertySharingModel({
    required this.id,
    required this.propertyId,
    required this.agentId,
    required this.shareMethod,
    this.recipientInfo,
    required this.sharedAt,
    this.wasViewed = false,
    this.viewedAt,
    this.metadata,
  });

  /// Create from Firestore document
  factory PropertySharingModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return PropertySharingModel(
      id: doc.id,
      propertyId: data['propertyId'] ?? '',
      agentId: data['agentId'] ?? '',
      shareMethod: data['shareMethod'] ?? 'direct_link',
      recipientInfo: data['recipientInfo'],
      sharedAt: (data['sharedAt'] as Timestamp).toDate(),
      wasViewed: data['wasViewed'] ?? false,
      viewedAt: data['viewedAt'] != null 
          ? (data['viewedAt'] as Timestamp).toDate() 
          : null,
      metadata: data['metadata'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'id': id,
      'propertyId': propertyId,
      'agentId': agentId,
      'shareMethod': shareMethod,
      'recipientInfo': recipientInfo,
      'sharedAt': Timestamp.fromDate(sharedAt),
      'wasViewed': wasViewed,
      'viewedAt': viewedAt != null ? Timestamp.fromDate(viewedAt!) : null,
      'metadata': metadata,
    };
  }

  /// Get share method display name
  String get shareMethodDisplayName {
    switch (shareMethod) {
      case 'whatsapp': return 'WhatsApp';
      case 'email': return 'Email';
      case 'direct_link': return 'Direct Link';
      case 'sms': return 'SMS';
      default: return 'Unknown';
    }
  }
}
