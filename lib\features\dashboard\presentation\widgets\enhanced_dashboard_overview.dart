import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:fl_chart/fl_chart.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../commissions/models/commission_enums.dart';
import '../../models/dashboard_models.dart';
import '../../services/dashboard_service.dart';
import 'agent_mentor_widget.dart';
import '../../../../core/providers/app_providers.dart';

/// Enhanced dashboard overview with comprehensive metrics
class EnhancedDashboardOverview extends ConsumerStatefulWidget {
  final String agentId;

  const EnhancedDashboardOverview({super.key, required this.agentId});

  @override
  ConsumerState<EnhancedDashboardOverview> createState() =>
      _EnhancedDashboardOverviewState();
}

class _EnhancedDashboardOverviewState
    extends ConsumerState<EnhancedDashboardOverview>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;
  AgentDashboardData? _dashboardData;
  bool _isLoading = true;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
    _loadDashboardData();
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  /// Show mentor details
  void _showMentorDetails(String mentorId) {
    final mentorAsync = ref.read(userUplineProvider(mentorId));

    mentorAsync.when(
      data: (mentor) {
        if (mentor != null) {
          showDialog(
            context: context,
            builder: (context) => AlertDialog(
              backgroundColor: AppTheme.darkSurface,
              title: Row(
                children: [
                  Icon(
                    Icons.school,
                    color: AppTheme.primaryColor,
                    size: 24,
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'Agent Mentor',
                    style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),
              content: Column(
                mainAxisSize: MainAxisSize.min,
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Mentor basic info
                  ListTile(
                    contentPadding: EdgeInsets.zero,
                    leading: CircleAvatar(
                      radius: 25,
                      backgroundColor: AppTheme.primaryColor.withOpacity(0.2),
                      child: Text(
                        mentor.name.isNotEmpty ? mentor.name[0].toUpperCase() : 'M',
                        style: TextStyle(
                          color: AppTheme.primaryColor,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ),
                    title: Text(
                      mentor.name,
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    subtitle: Text(
                      mentor.email,
                      style: TextStyle(
                        color: Colors.white.withOpacity(0.7),
                      ),
                    ),
                  ),

                  const SizedBox(height: 16),

                  // Mentor stats
                  Row(
                    mainAxisAlignment: MainAxisAlignment.spaceAround,
                    children: [
                      _buildMentorStat('Level', '${mentor.level}', Icons.star),
                      _buildMentorStat('Team', '${mentor.downlineIds.length}', Icons.group),
                      _buildMentorStat('Sales', '₹${(mentor.totalSales / 100000).toStringAsFixed(1)}L', Icons.trending_up),
                    ],
                  ),
                ],
              ),
              actions: [
                TextButton(
                  onPressed: () => Navigator.pop(context),
                  child: Text(
                    'Close',
                    style: TextStyle(color: Colors.white.withOpacity(0.7)),
                  ),
                ),
                ElevatedButton(
                  onPressed: () {
                    Navigator.pop(context);
                    // TODO: Implement contact functionality
                  },
                  style: ElevatedButton.styleFrom(
                    backgroundColor: AppTheme.primaryColor,
                  ),
                  child: const Text('Contact'),
                ),
              ],
            ),
          );
        }
      },
      loading: () {},
      error: (_, __) {},
    );
  }

  Widget _buildMentorStat(String label, String value, IconData icon) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppTheme.primaryColor,
          size: 20,
        ),
        const SizedBox(height: 4),
        Text(
          value,
          style: const TextStyle(
            color: Colors.white,
            fontWeight: FontWeight.bold,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            color: Colors.white.withOpacity(0.7),
            fontSize: 12,
          ),
        ),
      ],
    );
  }

  Future<void> _loadDashboardData() async {
    setState(() => _isLoading = true);

    try {
      final data = await DashboardService.getAgentDashboardData(widget.agentId);
      setState(() {
        _dashboardData = data;
        _isLoading = false;
      });
    } catch (e) {
      setState(() => _isLoading = false);
    }
  }

  @override
  Widget build(BuildContext context) {
    if (_isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (_dashboardData == null) {
      return _buildErrorWidget();
    }

    return RefreshIndicator(
      onRefresh: _loadDashboardData,
      child: SingleChildScrollView(
        physics: const AlwaysScrollableScrollPhysics(),
        child: Column(
          children: [
            // Welcome Header
            _buildWelcomeHeader(),

            const SizedBox(height: 24),

            // Key Metrics Cards
            _buildKeyMetricsCards(),

            const SizedBox(height: 24),

            // Tab Section
            _buildTabSection(),
          ],
        ),
      ),
    );
  }

  /// Build welcome header
  Widget _buildWelcomeHeader() {
    return Container(
      margin: const EdgeInsets.all(16),
      padding: const EdgeInsets.all(20),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 12,
            offset: const Offset(0, 6),
          ),
        ],
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Expanded(
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      'Welcome back,',
                      style: TextStyle(
                        color: Colors.white.withValues(alpha: 0.9),
                        fontSize: 14,
                      ),
                    ),
                    Text(
                      _dashboardData!.agentName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 24,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ],
                ),
              ),
              Container(
                padding: const EdgeInsets.symmetric(
                  horizontal: 12,
                  vertical: 6,
                ),
                decoration: BoxDecoration(
                  color: _dashboardData!.currentTier.color,
                  borderRadius: BorderRadius.circular(20),
                ),
                child: Row(
                  mainAxisSize: MainAxisSize.min,
                  children: [
                    Icon(
                      _dashboardData!.currentTier.icon,
                      color: Colors.white,
                      size: 16,
                    ),
                    const SizedBox(width: 6),
                    Text(
                      _dashboardData!.currentTier.displayName,
                      style: const TextStyle(
                        color: Colors.white,
                        fontSize: 12,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                  ],
                ),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Monthly Progress
          Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    'Monthly Target Progress',
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.9),
                      fontSize: 14,
                    ),
                  ),
                  Text(
                    '${_dashboardData!.metrics.targetCompletionPercentage.toStringAsFixed(0)}%',
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 14,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              LinearProgressIndicator(
                value: _dashboardData!.metrics.targetCompletionPercentage / 100,
                backgroundColor: Colors.white.withValues(alpha: 0.3),
                valueColor: const AlwaysStoppedAnimation<Color>(Colors.white),
                minHeight: 6,
              ),

              const SizedBox(height: 8),

              Row(
                mainAxisAlignment: MainAxisAlignment.spaceBetween,
                children: [
                  Text(
                    _dashboardData!.metrics.formattedMonthlyProgress,
                    style: const TextStyle(
                      color: Colors.white,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  Text(
                    _dashboardData!.metrics.formattedMonthlyTarget,
                    style: TextStyle(
                      color: Colors.white.withValues(alpha: 0.8),
                      fontSize: 12,
                    ),
                  ),
                ],
              ),
            ],
          ),
        ],
      ),
    );
  }

  /// Build key metrics cards
  Widget _buildKeyMetricsCards() {
    return GridView.count(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      padding: const EdgeInsets.symmetric(horizontal: 16),
      crossAxisCount: 2,
      crossAxisSpacing: 16,
      mainAxisSpacing: 16,
      childAspectRatio: 1.1,
      children: [
        _buildMetricCard(
          'Total Sales',
          _dashboardData!.metrics.formattedTotalSales,
          Icons.trending_up,
          AppTheme.successColor,
          subtitle: '${_dashboardData!.metrics.propertiesSold} properties',
        ),
        _buildMetricCard(
          'Commissions',
          _dashboardData!.metrics.formattedTotalCommissions,
          Icons.account_balance_wallet,
          AppTheme.warningColor,
          subtitle: 'Total earned',
        ),
        _buildMetricCard(
          'Team Size',
          '${_dashboardData!.metrics.teamSize}',
          Icons.group,
          AppTheme.primaryColor,
          subtitle: '${_dashboardData!.teamOverview.activeMembers} active',
        ),
        _buildMetricCard(
          'Growth Rate',
          '${_dashboardData!.metrics.growthRate.toStringAsFixed(1)}%',
          Icons.show_chart,
          _dashboardData!.metrics.growthRate >= 0
              ? AppTheme.successColor
              : AppTheme.errorColor,
          subtitle: 'This month',
        ),
      ],
    );
  }

  /// Build metric card
  Widget _buildMetricCard(
    String title,
    String value,
    IconData icon,
    Color color, {
    String? subtitle,
  }) {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  color: color.withValues(alpha: 0.2),
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(icon, color: color, size: 20),
              ),
              const Spacer(),
              if (_dashboardData!.insights.trend != PerformanceTrend.stable)
                Icon(
                  _dashboardData!.insights.trend.icon,
                  color: _dashboardData!.insights.trend.color,
                  size: 16,
                ),
            ],
          ),
          const SizedBox(height: 12),
          Text(
            title,
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 12,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          if (subtitle != null) ...[
            const SizedBox(height: 4),
            Text(
              subtitle,
              style: TextStyle(color: AppTheme.darkHintText, fontSize: 10),
            ),
          ],
        ],
      ),
    );
  }

  /// Build tab section
  Widget _buildTabSection() {
    return Column(
      children: [
        // Tab Bar
        Container(
          margin: const EdgeInsets.symmetric(horizontal: 16),
          decoration: BoxDecoration(
            color: AppTheme.darkCard,
            borderRadius: BorderRadius.circular(12),
          ),
          child: TabBar(
            controller: _tabController,
            indicator: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(12),
            ),
            indicatorSize: TabBarIndicatorSize.tab,
            dividerColor: Colors.transparent,
            labelColor: Colors.white,
            unselectedLabelColor: AppTheme.darkSecondaryText,
            labelStyle: const TextStyle(
              fontSize: 12,
              fontWeight: FontWeight.w600,
            ),
            tabs: const [
              Tab(text: 'Goals'),
              Tab(text: 'Activity'),
              Tab(text: 'Team'),
              Tab(text: 'Insights'),
            ],
          ),
        ),

        // Tab Content
        SizedBox(
          height: 400,
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildGoalsTab(),
              _buildActivityTab(),
              _buildTeamTab(),
              _buildInsightsTab(),
            ],
          ),
        ),
      ],
    );
  }

  /// Build goals tab
  Widget _buildGoalsTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _dashboardData!.goals.length,
      itemBuilder: (context, index) {
        final goal = _dashboardData!.goals[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: AppTheme.createCardDecoration(),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: goal.type.icon == Icons.trending_up
                    ? AppTheme.successColor.withValues(alpha: 0.2)
                    : AppTheme.primaryColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(goal.type.icon, color: goal.progressColor, size: 20),
            ),
            title: Text(
              goal.title,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 4),
                LinearProgressIndicator(
                  value: goal.progressPercentage / 100,
                  backgroundColor: AppTheme.darkBorder,
                  valueColor: AlwaysStoppedAnimation<Color>(goal.progressColor),
                  minHeight: 4,
                ),
                const SizedBox(height: 4),
                Row(
                  mainAxisAlignment: MainAxisAlignment.spaceBetween,
                  children: [
                    Text(
                      '${goal.progressPercentage.toStringAsFixed(0)}% complete',
                      style: TextStyle(
                        color: AppTheme.darkSecondaryText,
                        fontSize: 12,
                      ),
                    ),
                    Text(
                      '${goal.daysRemaining} days left',
                      style: TextStyle(
                        color: goal.isOverdue
                            ? AppTheme.errorColor
                            : AppTheme.darkSecondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),
        );
      },
    );
  }

  /// Build activity tab
  Widget _buildActivityTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _dashboardData!.recentActivities.length,
      itemBuilder: (context, index) {
        final activity = _dashboardData!.recentActivities[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: AppTheme.createCardDecoration(),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: activity.type.color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                activity.type.icon,
                color: activity.type.color,
                size: 20,
              ),
            ),
            title: Text(
              activity.title,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(
              activity.description,
              style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 12),
            ),
            trailing: Text(
              activity.timeAgo,
              style: TextStyle(color: AppTheme.darkHintText, fontSize: 10),
            ),
          ),
        );
      },
    );
  }

  /// Build team tab
  Widget _buildTeamTab() {
    final currentUserAsync = ref.watch(currentUserProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Agent Mentor Section
          currentUserAsync.when(
            data: (user) => user?.uplineId != null
                ? Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      AgentMentorWidget(
                        uplineId: user!.uplineId,
                        onTap: () => _showMentorDetails(user.uplineId!),
                      ),
                      const SizedBox(height: 16),
                    ],
                  )
                : const SizedBox.shrink(),
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          ),

          // Team Overview
          GradientWidgets.gradientCard(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Team Overview',
                  style: TextStyle(
                    color: AppTheme.darkPrimaryText,
                    fontSize: 18,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTeamMetric(
                        'Total',
                        '${_dashboardData!.teamOverview.totalMembers}',
                        AppTheme.primaryColor,
                      ),
                    ),
                    Expanded(
                      child: _buildTeamMetric(
                        'Active',
                        '${_dashboardData!.teamOverview.activeMembers}',
                        AppTheme.successColor,
                      ),
                    ),
                    Expanded(
                      child: _buildTeamMetric(
                        'New',
                        '${_dashboardData!.teamOverview.newMembersThisMonth}',
                        AppTheme.warningColor,
                      ),
                    ),
                  ],
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Top Performers
          if (_dashboardData!.teamOverview.topPerformers.isNotEmpty) ...[
            Text(
              'Top Performers',
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ..._dashboardData!.teamOverview.topPerformers.map(
              (member) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                decoration: AppTheme.createCardDecoration(),
                child: ListTile(
                  leading: CircleAvatar(
                    backgroundColor: member.tier.color,
                    child: Text(
                      member.name.isNotEmpty ? member.name[0] : 'A',
                      style: const TextStyle(
                        color: Colors.white,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  title: Text(
                    member.name,
                    style: TextStyle(
                      color: AppTheme.darkPrimaryText,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                  subtitle: Text(
                    'Level ${member.level} • ${member.tier.displayName}',
                    style: TextStyle(
                      color: AppTheme.darkSecondaryText,
                      fontSize: 12,
                    ),
                  ),
                  trailing: Text(
                    '₹${(member.sales / 100000).toStringAsFixed(1)}L',
                    style: TextStyle(
                      color: AppTheme.primaryColor,
                      fontWeight: FontWeight.bold,
                    ),
                  ),
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build team metric
  Widget _buildTeamMetric(String label, String value, Color color) {
    return Column(
      children: [
        Text(
          value,
          style: TextStyle(
            color: color,
            fontSize: 24,
            fontWeight: FontWeight.bold,
          ),
        ),
        Text(
          label,
          style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 12),
        ),
      ],
    );
  }

  /// Build insights tab
  Widget _buildInsightsTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Performance Trend
          GradientWidgets.gradientCard(
            child: Row(
              children: [
                Container(
                  padding: const EdgeInsets.all(12),
                  decoration: BoxDecoration(
                    color: _dashboardData!.insights.trend.color.withValues(
                      alpha: 0.2,
                    ),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Icon(
                    _dashboardData!.insights.trend.icon,
                    color: _dashboardData!.insights.trend.color,
                    size: 24,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        'Performance Trend',
                        style: TextStyle(
                          color: AppTheme.darkSecondaryText,
                          fontSize: 12,
                        ),
                      ),
                      Text(
                        _dashboardData!.insights.trend.displayName,
                        style: TextStyle(
                          color: AppTheme.darkPrimaryText,
                          fontSize: 18,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 16),

          // Insights
          if (_dashboardData!.insights.insights.isNotEmpty) ...[
            Text(
              'Key Insights',
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ..._dashboardData!.insights.insights.map(
              (insight) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: AppTheme.createCardDecoration(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Text(
                      insight.title,
                      style: TextStyle(
                        color: AppTheme.darkPrimaryText,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      insight.description,
                      style: TextStyle(
                        color: AppTheme.darkSecondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],

          const SizedBox(height: 16),

          // Recommendations
          if (_dashboardData!.insights.recommendations.isNotEmpty) ...[
            Text(
              'Recommendations',
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 16,
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 12),
            ..._dashboardData!.insights.recommendations.map(
              (recommendation) => Container(
                margin: const EdgeInsets.only(bottom: 8),
                padding: const EdgeInsets.all(12),
                decoration: AppTheme.createCardDecoration(),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    Row(
                      children: [
                        Container(
                          padding: const EdgeInsets.symmetric(
                            horizontal: 6,
                            vertical: 2,
                          ),
                          decoration: BoxDecoration(
                            color: AppTheme.primaryColor.withValues(alpha: 0.2),
                            borderRadius: BorderRadius.circular(8),
                          ),
                          child: Text(
                            'Priority ${recommendation.priority}',
                            style: TextStyle(
                              color: AppTheme.primaryColor,
                              fontSize: 10,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        ),
                        const Spacer(),
                        Text(
                          recommendation.type.name.toUpperCase(),
                          style: TextStyle(
                            color: AppTheme.darkHintText,
                            fontSize: 10,
                          ),
                        ),
                      ],
                    ),
                    const SizedBox(height: 8),
                    Text(
                      recommendation.title,
                      style: TextStyle(
                        color: AppTheme.darkPrimaryText,
                        fontWeight: FontWeight.w600,
                      ),
                    ),
                    const SizedBox(height: 4),
                    Text(
                      recommendation.description,
                      style: TextStyle(
                        color: AppTheme.darkSecondaryText,
                        fontSize: 12,
                      ),
                    ),
                  ],
                ),
              ),
            ),
          ],
        ],
      ),
    );
  }

  /// Build error widget
  Widget _buildErrorWidget() {
    return Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.error_outline, size: 64, color: AppTheme.errorColor),
          const SizedBox(height: 16),
          Text(
            'Failed to load dashboard data',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 24),
          GradientWidgets.gradientButton(
            text: 'Retry',
            onPressed: _loadDashboardData,
            icon: Icons.refresh,
          ),
        ],
      ),
    );
  }
}
