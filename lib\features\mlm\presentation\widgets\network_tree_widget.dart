import 'package:flutter/material.dart';
import '../../../../core/models/network_node.dart';
import '../../../../core/models/user_model.dart';

/// Widget for displaying MLM network tree visualization
class NetworkTreeWidget extends StatefulWidget {
  final NetworkNode rootNode;
  final Function(UserModel)? onUserTap;
  final int maxDepth;

  const NetworkTreeWidget({
    super.key,
    required this.rootNode,
    this.onUserTap,
    this.maxDepth = 3,
  });

  @override
  State<NetworkTreeWidget> createState() => _NetworkTreeWidgetState();
}

class _NetworkTreeWidgetState extends State<NetworkTreeWidget> {
  final Set<String> _expandedNodes = {};

  @override
  void initState() {
    super.initState();
    // Expand root node by default
    _expandedNodes.add(widget.rootNode.user.id);
  }

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: SingleChildScrollView(
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: _buildNodeTree(widget.rootNode, 0),
        ),
      ),
    );
  }

  Widget _buildNodeTree(NetworkNode node, int depth) {
    final isExpanded = _expandedNodes.contains(node.user.id);
    final hasChildren = node.children.isNotEmpty;
    final showChildren = isExpanded && depth < widget.maxDepth;

    return Column(
      children: [
        // Node widget
        _buildNodeWidget(node, hasChildren, isExpanded),
        
        // Children
        if (showChildren && hasChildren) ...[
          const SizedBox(height: 20),
          // Connection line
          Container(
            width: 2,
            height: 20,
            color: Colors.grey[400],
          ),
          const SizedBox(height: 10),
          // Children row
          Row(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              for (int i = 0; i < node.children.length; i++) ...[
                if (i > 0) const SizedBox(width: 40),
                Column(
                  children: [
                    // Horizontal line to child
                    if (node.children.length > 1)
                      Container(
                        width: i == 0 ? 20 : (i == node.children.length - 1 ? 20 : 40),
                        height: 2,
                        color: Colors.grey[400],
                        margin: EdgeInsets.only(
                          left: i == 0 ? 20 : 0,
                          right: i == node.children.length - 1 ? 20 : 0,
                        ),
                      ),
                    // Vertical line to child
                    Container(
                      width: 2,
                      height: 20,
                      color: Colors.grey[400],
                    ),
                    // Child node
                    _buildNodeTree(node.children[i], depth + 1),
                  ],
                ),
              ],
            ],
          ),
        ],
      ],
    );
  }

  Widget _buildNodeWidget(NetworkNode node, bool hasChildren, bool isExpanded) {
    final user = node.user;
    final theme = Theme.of(context);
    
    return GestureDetector(
      onTap: () {
        if (hasChildren) {
          setState(() {
            if (isExpanded) {
              _expandedNodes.remove(user.id);
            } else {
              _expandedNodes.add(user.id);
            }
          });
        }
        widget.onUserTap?.call(user);
      },
      child: Container(
        width: 160,
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: _getNodeColor(node),
          borderRadius: BorderRadius.circular(12),
          border: Border.all(
            color: node.depth == 0 ? theme.primaryColor : Colors.grey[300]!,
            width: node.depth == 0 ? 2 : 1,
          ),
          boxShadow: [
            BoxShadow(
              color: Colors.black.withValues(alpha: 0.1),
              blurRadius: 4,
              offset: const Offset(0, 2),
            ),
          ],
        ),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            // Profile picture
            CircleAvatar(
              radius: 20,
              backgroundColor: theme.primaryColor,
              backgroundImage: user.profileImageUrl != null
                  ? NetworkImage(user.profileImageUrl!)
                  : null,
              child: user.profileImageUrl == null
                  ? Text(
                      user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                      style: const TextStyle(color: Colors.white, fontWeight: FontWeight.bold),
                    )
                  : null,
            ),
            
            const SizedBox(height: 8),
            
            // Name
            Text(
              user.name,
              style: theme.textTheme.bodyMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
              textAlign: TextAlign.center,
              maxLines: 2,
              overflow: TextOverflow.ellipsis,
            ),
            
            const SizedBox(height: 4),
            
            // Level
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 2),
              decoration: BoxDecoration(
                color: _getLevelColor(user.level),
                borderRadius: BorderRadius.circular(10),
              ),
              child: Text(
                'L${user.level}',
                style: const TextStyle(
                  color: Colors.white,
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            
            const SizedBox(height: 6),
            
            // Stats
            Row(
              mainAxisAlignment: MainAxisAlignment.spaceAround,
              children: [
                _buildStatItem(Icons.star, '${user.totalStars}', Colors.amber),
                _buildStatItem(Icons.group, '${node.children.length}', Colors.blue),
              ],
            ),
            
            // Expand/collapse indicator
            if (hasChildren) ...[
              const SizedBox(height: 4),
              Icon(
                isExpanded ? Icons.keyboard_arrow_up : Icons.keyboard_arrow_down,
                size: 16,
                color: Colors.grey[600],
              ),
            ],
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(IconData icon, String value, Color color) {
    return Column(
      children: [
        Icon(icon, size: 14, color: color),
        const SizedBox(height: 2),
        Text(
          value,
          style: TextStyle(
            fontSize: 10,
            fontWeight: FontWeight.bold,
            color: Colors.grey[700],
          ),
        ),
      ],
    );
  }

  Color _getNodeColor(NetworkNode node) {
    if (node.depth == 0) {
      return Colors.blue[50]!;
    } else if (node.user.isActive) {
      return Colors.green[50]!;
    } else {
      return Colors.grey[100]!;
    }
  }

  Color _getLevelColor(int level) {
    final colors = [
      Colors.purple,
      Colors.blue,
      Colors.green,
      Colors.orange,
      Colors.red,
    ];
    return colors[level % colors.length];
  }
}

/// Compact network tree widget for smaller spaces
class CompactNetworkTreeWidget extends StatelessWidget {
  final NetworkNode rootNode;
  final Function(UserModel)? onUserTap;

  const CompactNetworkTreeWidget({
    super.key,
    required this.rootNode,
    this.onUserTap,
  });

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      scrollDirection: Axis.horizontal,
      child: Padding(
        padding: const EdgeInsets.all(8),
        child: _buildCompactNode(rootNode),
      ),
    );
  }

  Widget _buildCompactNode(NetworkNode node) {
    return Column(
      children: [
        // Node
        GestureDetector(
          onTap: () => onUserTap?.call(node.user),
          child: Container(
            width: 80,
            height: 80,
            decoration: BoxDecoration(
              color: _getNodeColor(node),
              shape: BoxShape.circle,
              border: Border.all(
                color: node.depth == 0 ? Colors.blue : Colors.grey[300]!,
                width: 2,
              ),
            ),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  node.user.name.split(' ').map((n) => n[0]).take(2).join(),
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 12,
                  ),
                ),
                Text(
                  'L${node.user.level}',
                  style: TextStyle(
                    fontSize: 10,
                    color: Colors.grey[600],
                  ),
                ),
              ],
            ),
          ),
        ),
        
        // Children
        if (node.children.isNotEmpty) ...[
          const SizedBox(height: 10),
          Row(
            children: node.children
                .take(3) // Show max 3 children in compact view
                .map((child) => Padding(
                      padding: const EdgeInsets.symmetric(horizontal: 4),
                      child: _buildCompactNode(child),
                    ))
                .toList(),
          ),
          if (node.children.length > 3)
            Text(
              '+${node.children.length - 3} more',
              style: TextStyle(
                fontSize: 10,
                color: Colors.grey[600],
              ),
            ),
        ],
      ],
    );
  }

  Color _getNodeColor(NetworkNode node) {
    if (node.depth == 0) {
      return Colors.blue[100]!;
    } else if (node.user.isActive) {
      return Colors.green[100]!;
    } else {
      return Colors.grey[200]!;
    }
  }
}
