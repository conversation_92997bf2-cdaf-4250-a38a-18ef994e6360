import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';

import '../models/commission_enums.dart';
import '../models/enhanced_commission_model.dart';

/// Commission analytics and reporting service
class CommissionAnalyticsService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get comprehensive commission analytics for an agent
  static Future<CommissionAnalytics> getAgentAnalytics(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock analytics
        return _getMockAnalytics();
      }

      // Get all commissions for the agent
      final commissions = await _getAgentCommissions(agentId);
      
      // Calculate analytics
      final totalEarned = _calculateTotalEarned(commissions);
      final totalPending = _calculateTotalPending(commissions);
      final totalPaid = _calculateTotalPaid(commissions);
      final monthlyAverage = _calculateMonthlyAverage(commissions);
      final growthRate = _calculateGrowthRate(commissions);
      final typeBreakdown = _calculateTypeBreakdown(commissions);
      final tierBreakdown = _calculateTierBreakdown(commissions);
      final monthlyData = _calculateMonthlyData(commissions);
      final currentTier = await _getCurrentTier(agentId);
      final nextTierProgress = await _calculateNextTierProgress(agentId, currentTier);

      return CommissionAnalytics(
        totalEarned: totalEarned,
        totalPending: totalPending,
        totalPaid: totalPaid,
        monthlyAverage: monthlyAverage,
        growthRate: growthRate,
        typeBreakdown: typeBreakdown,
        tierBreakdown: tierBreakdown,
        monthlyData: monthlyData,
        currentTier: currentTier,
        nextTierProgress: nextTierProgress,
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting agent analytics: $e');
      }
      return _getEmptyAnalytics();
    }
  }

  /// Get agent commissions from Firestore
  static Future<List<EnhancedCommissionModel>> _getAgentCommissions(String agentId) async {
    try {
      final snapshot = await _firestore
          .collection('commissions')
          .where('agentId', isEqualTo: agentId)
          .orderBy('createdAt', descending: true)
          .get();

      return snapshot.docs
          .map((doc) => EnhancedCommissionModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      return [];
    }
  }

  /// Calculate total earned commissions
  static double _calculateTotalEarned(List<EnhancedCommissionModel> commissions) {
    return commissions
        .where((c) => c.status == CommissionStatus.paid)
        .fold(0.0, (sum, c) => sum + c.amount);
  }

  /// Calculate total pending commissions
  static double _calculateTotalPending(List<EnhancedCommissionModel> commissions) {
    return commissions
        .where((c) => c.status == CommissionStatus.pending || c.status == CommissionStatus.approved)
        .fold(0.0, (sum, c) => sum + c.amount);
  }

  /// Calculate total paid commissions
  static double _calculateTotalPaid(List<EnhancedCommissionModel> commissions) {
    return commissions
        .where((c) => c.status == CommissionStatus.paid)
        .fold(0.0, (sum, c) => sum + c.amount);
  }

  /// Calculate monthly average
  static double _calculateMonthlyAverage(List<EnhancedCommissionModel> commissions) {
    if (commissions.isEmpty) return 0.0;

    final paidCommissions = commissions
        .where((c) => c.status == CommissionStatus.paid)
        .toList();

    if (paidCommissions.isEmpty) return 0.0;

    final oldestDate = paidCommissions
        .map((c) => c.paidAt ?? c.createdAt)
        .reduce((a, b) => a.isBefore(b) ? a : b);

    final monthsDiff = DateTime.now().difference(oldestDate).inDays / 30;
    if (monthsDiff < 1) return _calculateTotalPaid(paidCommissions);

    return _calculateTotalPaid(paidCommissions) / monthsDiff;
  }

  /// Calculate growth rate (month over month)
  static double _calculateGrowthRate(List<EnhancedCommissionModel> commissions) {
    final now = DateTime.now();
    final currentMonth = DateTime(now.year, now.month);
    final lastMonth = DateTime(now.year, now.month - 1);

    final currentMonthTotal = commissions
        .where((c) => c.status == CommissionStatus.paid)
        .where((c) => (c.paidAt ?? c.createdAt).isAfter(currentMonth))
        .fold(0.0, (sum, c) => sum + c.amount);

    final lastMonthTotal = commissions
        .where((c) => c.status == CommissionStatus.paid)
        .where((c) {
          final date = c.paidAt ?? c.createdAt;
          return date.isAfter(lastMonth) && date.isBefore(currentMonth);
        })
        .fold(0.0, (sum, c) => sum + c.amount);

    if (lastMonthTotal == 0) return 0.0;
    return ((currentMonthTotal - lastMonthTotal) / lastMonthTotal) * 100;
  }

  /// Calculate commission breakdown by type
  static Map<CommissionType, double> _calculateTypeBreakdown(List<EnhancedCommissionModel> commissions) {
    final breakdown = <CommissionType, double>{};
    
    for (final type in CommissionType.values) {
      breakdown[type] = commissions
          .where((c) => c.type == type && c.status == CommissionStatus.paid)
          .fold(0.0, (sum, c) => sum + c.amount);
    }

    return breakdown;
  }

  /// Calculate commission breakdown by tier
  static Map<CommissionTier, double> _calculateTierBreakdown(List<EnhancedCommissionModel> commissions) {
    final breakdown = <CommissionTier, double>{};
    
    for (final tier in CommissionTier.values) {
      breakdown[tier] = commissions
          .where((c) => c.tier == tier && c.status == CommissionStatus.paid)
          .fold(0.0, (sum, c) => sum + c.amount);
    }

    return breakdown;
  }

  /// Calculate monthly commission data
  static List<MonthlyCommission> _calculateMonthlyData(List<EnhancedCommissionModel> commissions) {
    final monthlyMap = <String, MonthlyCommission>{};

    for (final commission in commissions) {
      if (commission.status != CommissionStatus.paid) continue;

      final date = commission.paidAt ?? commission.createdAt;
      final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';
      final month = DateTime(date.year, date.month);

      if (monthlyMap.containsKey(monthKey)) {
        final existing = monthlyMap[monthKey]!;
        monthlyMap[monthKey] = MonthlyCommission(
          month: month,
          amount: existing.amount + commission.amount,
          count: existing.count + 1,
        );
      } else {
        monthlyMap[monthKey] = MonthlyCommission(
          month: month,
          amount: commission.amount,
          count: 1,
        );
      }
    }

    final monthlyData = monthlyMap.values.toList();
    monthlyData.sort((a, b) => a.month.compareTo(b.month));
    
    // Return last 12 months
    return monthlyData.length > 12 
        ? monthlyData.sublist(monthlyData.length - 12)
        : monthlyData;
  }

  /// Get current tier for agent
  static Future<CommissionTier> _getCurrentTier(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        return CommissionTier.silver;
      }

      // Calculate based on total sales
      final totalSales = await _getAgentTotalSales(agentId);
      
      for (final tier in CommissionTier.values.reversed) {
        if (totalSales >= tier.minimumSales) {
          return tier;
        }
      }
      
      return CommissionTier.bronze;
    } catch (e) {
      return CommissionTier.bronze;
    }
  }

  /// Calculate progress to next tier
  static Future<double> _calculateNextTierProgress(String agentId, CommissionTier currentTier) async {
    try {
      final totalSales = await _getAgentTotalSales(agentId);
      final currentTierIndex = CommissionTier.values.indexOf(currentTier);
      
      if (currentTierIndex >= CommissionTier.values.length - 1) {
        return 1.0; // Already at highest tier
      }

      final nextTier = CommissionTier.values[currentTierIndex + 1];
      final currentTierMin = currentTier.minimumSales;
      final nextTierMin = nextTier.minimumSales;
      
      final progress = (totalSales - currentTierMin) / (nextTierMin - currentTierMin);
      return progress.clamp(0.0, 1.0);
    } catch (e) {
      return 0.0;
    }
  }

  /// Get agent's total sales
  static Future<double> _getAgentTotalSales(String agentId) async {
    try {
      if (kIsWeb && kDebugMode) {
        return 2500000; // 25L for silver tier
      }

      final snapshot = await _firestore
          .collection('commissions')
          .where('agentId', isEqualTo: agentId)
          .where('type', isEqualTo: CommissionType.directSale.value)
          .where('status', isEqualTo: CommissionStatus.paid.value)
          .get();

      double totalSales = 0;
      for (final doc in snapshot.docs) {
        final data = doc.data();
        final propertyPrice = data['metadata']?['propertyPrice'] ?? 0;
        totalSales += propertyPrice.toDouble();
      }

      return totalSales;
    } catch (e) {
      return 0;
    }
  }

  /// Get mock analytics for development
  static CommissionAnalytics _getMockAnalytics() {
    return CommissionAnalytics(
      totalEarned: 125000,
      totalPending: 45000,
      totalPaid: 125000,
      monthlyAverage: 25000,
      growthRate: 15.5,
      typeBreakdown: {
        CommissionType.directSale: 85000,
        CommissionType.referralBonus: 15000,
        CommissionType.levelBonus: 20000,
        CommissionType.performanceBonus: 5000,
        CommissionType.teamBonus: 0,
        CommissionType.leadershipBonus: 0,
        CommissionType.achievementBonus: 0,
      },
      tierBreakdown: {
        CommissionTier.bronze: 45000,
        CommissionTier.silver: 80000,
        CommissionTier.gold: 0,
        CommissionTier.platinum: 0,
        CommissionTier.diamond: 0,
      },
      monthlyData: [
        MonthlyCommission(month: DateTime(2024, 7), amount: 15000, count: 2),
        MonthlyCommission(month: DateTime(2024, 8), amount: 22000, count: 3),
        MonthlyCommission(month: DateTime(2024, 9), amount: 18000, count: 2),
        MonthlyCommission(month: DateTime(2024, 10), amount: 28000, count: 4),
        MonthlyCommission(month: DateTime(2024, 11), amount: 25000, count: 3),
        MonthlyCommission(month: DateTime(2024, 12), amount: 17000, count: 2),
      ],
      currentTier: CommissionTier.silver,
      nextTierProgress: 0.65,
    );
  }

  /// Get empty analytics
  static CommissionAnalytics _getEmptyAnalytics() {
    return CommissionAnalytics(
      totalEarned: 0,
      totalPending: 0,
      totalPaid: 0,
      monthlyAverage: 0,
      growthRate: 0,
      typeBreakdown: Map.fromEntries(
        CommissionType.values.map((type) => MapEntry(type, 0.0)),
      ),
      tierBreakdown: Map.fromEntries(
        CommissionTier.values.map((tier) => MapEntry(tier, 0.0)),
      ),
      monthlyData: [],
      currentTier: CommissionTier.bronze,
      nextTierProgress: 0,
    );
  }

  /// Get commission leaderboard
  static Future<List<AgentCommissionSummary>> getCommissionLeaderboard({
    CommissionPeriod period = CommissionPeriod.monthly,
    int limit = 10,
  }) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock leaderboard
        return _getMockLeaderboard();
      }

      // Calculate date range based on period
      final now = DateTime.now();
      DateTime startDate;
      
      switch (period) {
        case CommissionPeriod.daily:
          startDate = DateTime(now.year, now.month, now.day);
          break;
        case CommissionPeriod.weekly:
          startDate = now.subtract(Duration(days: now.weekday - 1));
          break;
        case CommissionPeriod.monthly:
          startDate = DateTime(now.year, now.month, 1);
          break;
        case CommissionPeriod.quarterly:
          final quarter = ((now.month - 1) ~/ 3) * 3 + 1;
          startDate = DateTime(now.year, quarter, 1);
          break;
        case CommissionPeriod.yearly:
          startDate = DateTime(now.year, 1, 1);
          break;
      }

      // Query commissions for the period
      final snapshot = await _firestore
          .collection('commissions')
          .where('status', isEqualTo: CommissionStatus.paid.value)
          .where('paidAt', isGreaterThanOrEqualTo: Timestamp.fromDate(startDate))
          .get();

      // Group by agent and calculate totals
      final agentTotals = <String, AgentCommissionSummary>{};
      
      for (final doc in snapshot.docs) {
        final commission = EnhancedCommissionModel.fromFirestore(doc);
        
        if (agentTotals.containsKey(commission.agentId)) {
          final existing = agentTotals[commission.agentId]!;
          agentTotals[commission.agentId] = AgentCommissionSummary(
            agentId: existing.agentId,
            agentName: existing.agentName,
            totalAmount: existing.totalAmount + commission.amount,
            commissionCount: existing.commissionCount + 1,
            tier: existing.tier,
          );
        } else {
          agentTotals[commission.agentId] = AgentCommissionSummary(
            agentId: commission.agentId,
            agentName: commission.agentName,
            totalAmount: commission.amount,
            commissionCount: 1,
            tier: commission.tier,
          );
        }
      }

      // Sort by total amount and return top performers
      final leaderboard = agentTotals.values.toList();
      leaderboard.sort((a, b) => b.totalAmount.compareTo(a.totalAmount));
      
      return leaderboard.take(limit).toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting commission leaderboard: $e');
      }
      return [];
    }
  }

  /// Get mock leaderboard for development
  static List<AgentCommissionSummary> _getMockLeaderboard() {
    return [
      AgentCommissionSummary(
        agentId: 'agent1',
        agentName: 'John Doe',
        totalAmount: 85000,
        commissionCount: 8,
        tier: CommissionTier.gold,
      ),
      AgentCommissionSummary(
        agentId: 'agent2',
        agentName: 'Jane Smith',
        totalAmount: 72000,
        commissionCount: 6,
        tier: CommissionTier.silver,
      ),
      AgentCommissionSummary(
        agentId: 'agent3',
        agentName: 'Mike Johnson',
        totalAmount: 58000,
        commissionCount: 5,
        tier: CommissionTier.silver,
      ),
    ];
  }
}

/// Agent commission summary for leaderboard
class AgentCommissionSummary {
  final String agentId;
  final String agentName;
  final double totalAmount;
  final int commissionCount;
  final CommissionTier tier;

  const AgentCommissionSummary({
    required this.agentId,
    required this.agentName,
    required this.totalAmount,
    required this.commissionCount,
    required this.tier,
  });
}
