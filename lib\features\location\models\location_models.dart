import '../../properties/models/property_model.dart';

/// User location model
class UserLocation {
  final double latitude;
  final double longitude;
  final double accuracy;
  final DateTime timestamp;
  final String? address;

  const UserLocation({
    required this.latitude,
    required this.longitude,
    required this.accuracy,
    required this.timestamp,
    this.address,
  });

  Map<String, dynamic> toJson() => {
    'latitude': latitude,
    'longitude': longitude,
    'accuracy': accuracy,
    'timestamp': timestamp.toIso8601String(),
    'address': address,
  };

  factory UserLocation.fromJson(Map<String, dynamic> json) => UserLocation(
    latitude: json['latitude'].toDouble(),
    longitude: json['longitude'].toDouble(),
    accuracy: json['accuracy'].toDouble(),
    timestamp: DateTime.parse(json['timestamp']),
    address: json['address'],
  );

  UserLocation copyWith({
    double? latitude,
    double? longitude,
    double? accuracy,
    DateTime? timestamp,
    String? address,
  }) {
    return UserLocation(
      latitude: latitude ?? this.latitude,
      longitude: longitude ?? this.longitude,
      accuracy: accuracy ?? this.accuracy,
      timestamp: timestamp ?? this.timestamp,
      address: address ?? this.address,
    );
  }

  @override
  String toString() {
    return 'UserLocation(lat: $latitude, lng: $longitude, accuracy: ${accuracy}m)';
  }

  @override
  bool operator ==(Object other) =>
      identical(this, other) ||
      other is UserLocation &&
          runtimeType == other.runtimeType &&
          latitude == other.latitude &&
          longitude == other.longitude;

  @override
  int get hashCode => latitude.hashCode ^ longitude.hashCode;
}

/// Property with distance information
class PropertyWithDistance {
  final PropertyModel property;
  final double distance; // in kilometers

  const PropertyWithDistance({
    required this.property,
    required this.distance,
  });

  Map<String, dynamic> toJson() => {
    'property': property.toJson(),
    'distance': distance,
  };

  factory PropertyWithDistance.fromJson(Map<String, dynamic> json) => PropertyWithDistance(
    property: PropertyModel.fromJson(json['property']),
    distance: json['distance'].toDouble(),
  );

  /// Get formatted distance string
  String get formattedDistance {
    if (distance < 1.0) {
      return '${(distance * 1000).round()}m away';
    } else {
      return '${distance.toStringAsFixed(1)}km away';
    }
  }

  /// Get distance category
  DistanceCategory get distanceCategory {
    if (distance < 0.5) {
      return DistanceCategory.veryClose;
    } else if (distance < 2.0) {
      return DistanceCategory.close;
    } else if (distance < 5.0) {
      return DistanceCategory.moderate;
    } else if (distance < 10.0) {
      return DistanceCategory.far;
    } else {
      return DistanceCategory.veryFar;
    }
  }

  @override
  String toString() {
    return 'PropertyWithDistance(${property.title}, ${formattedDistance})';
  }
}

/// Distance categories
enum DistanceCategory {
  veryClose,
  close,
  moderate,
  far,
  veryFar,
}

/// Distance category extensions
extension DistanceCategoryExtension on DistanceCategory {
  String get displayName {
    switch (this) {
      case DistanceCategory.veryClose:
        return 'Very Close';
      case DistanceCategory.close:
        return 'Close';
      case DistanceCategory.moderate:
        return 'Moderate';
      case DistanceCategory.far:
        return 'Far';
      case DistanceCategory.veryFar:
        return 'Very Far';
    }
  }

  String get icon {
    switch (this) {
      case DistanceCategory.veryClose:
        return '🟢';
      case DistanceCategory.close:
        return '🟡';
      case DistanceCategory.moderate:
        return '🟠';
      case DistanceCategory.far:
        return '🔴';
      case DistanceCategory.veryFar:
        return '⚫';
    }
  }

  double get maxDistance {
    switch (this) {
      case DistanceCategory.veryClose:
        return 0.5;
      case DistanceCategory.close:
        return 2.0;
      case DistanceCategory.moderate:
        return 5.0;
      case DistanceCategory.far:
        return 10.0;
      case DistanceCategory.veryFar:
        return double.infinity;
    }
  }
}

/// Location search filters
class LocationSearchFilters {
  final double? radiusKm;
  final List<DistanceCategory>? allowedCategories;
  final PropertyType? propertyType;
  final PriceRange? priceRange;
  final bool sortByDistance;

  const LocationSearchFilters({
    this.radiusKm,
    this.allowedCategories,
    this.propertyType,
    this.priceRange,
    this.sortByDistance = true,
  });

  Map<String, dynamic> toJson() => {
    'radiusKm': radiusKm,
    'allowedCategories': allowedCategories?.map((c) => c.name).toList(),
    'propertyType': propertyType?.name,
    'priceRange': priceRange?.toJson(),
    'sortByDistance': sortByDistance,
  };

  factory LocationSearchFilters.fromJson(Map<String, dynamic> json) => LocationSearchFilters(
    radiusKm: json['radiusKm']?.toDouble(),
    allowedCategories: json['allowedCategories'] != null
        ? (json['allowedCategories'] as List)
            .map((name) => DistanceCategory.values.firstWhere((c) => c.name == name))
            .toList()
        : null,
    propertyType: json['propertyType'] != null
        ? PropertyType.values.firstWhere((t) => t.name == json['propertyType'])
        : null,
    priceRange: json['priceRange'] != null
        ? PriceRange.fromJson(json['priceRange'])
        : null,
    sortByDistance: json['sortByDistance'] ?? true,
  );

  LocationSearchFilters copyWith({
    double? radiusKm,
    List<DistanceCategory>? allowedCategories,
    PropertyType? propertyType,
    PriceRange? priceRange,
    bool? sortByDistance,
  }) {
    return LocationSearchFilters(
      radiusKm: radiusKm ?? this.radiusKm,
      allowedCategories: allowedCategories ?? this.allowedCategories,
      propertyType: propertyType ?? this.propertyType,
      priceRange: priceRange ?? this.priceRange,
      sortByDistance: sortByDistance ?? this.sortByDistance,
    );
  }
}

/// Price range for location searches
class PriceRange {
  final double min;
  final double max;

  const PriceRange({
    required this.min,
    required this.max,
  });

  Map<String, dynamic> toJson() => {
    'min': min,
    'max': max,
  };

  factory PriceRange.fromJson(Map<String, dynamic> json) => PriceRange(
    min: json['min'].toDouble(),
    max: json['max'].toDouble(),
  );

  bool contains(double price) {
    return price >= min && price <= max;
  }

  String get formattedRange {
    return '₹${_formatPrice(min)} - ₹${_formatPrice(max)}';
  }

  String _formatPrice(double price) {
    if (price >= 10000000) {
      return '${(price / 10000000).toStringAsFixed(1)}Cr';
    } else if (price >= 100000) {
      return '${(price / 100000).toStringAsFixed(1)}L';
    } else if (price >= 1000) {
      return '${(price / 1000).toStringAsFixed(1)}K';
    } else {
      return price.toStringAsFixed(0);
    }
  }

  @override
  String toString() => formattedRange;
}

/// Location-based search result
class LocationSearchResult {
  final List<PropertyWithDistance> properties;
  final UserLocation searchCenter;
  final LocationSearchFilters filters;
  final int totalFound;
  final DateTime searchTime;

  const LocationSearchResult({
    required this.properties,
    required this.searchCenter,
    required this.filters,
    required this.totalFound,
    required this.searchTime,
  });

  Map<String, dynamic> toJson() => {
    'properties': properties.map((p) => p.toJson()).toList(),
    'searchCenter': searchCenter.toJson(),
    'filters': filters.toJson(),
    'totalFound': totalFound,
    'searchTime': searchTime.toIso8601String(),
  };

  factory LocationSearchResult.fromJson(Map<String, dynamic> json) => LocationSearchResult(
    properties: (json['properties'] as List)
        .map((p) => PropertyWithDistance.fromJson(p))
        .toList(),
    searchCenter: UserLocation.fromJson(json['searchCenter']),
    filters: LocationSearchFilters.fromJson(json['filters']),
    totalFound: json['totalFound'],
    searchTime: DateTime.parse(json['searchTime']),
  );

  /// Get properties by distance category
  List<PropertyWithDistance> getPropertiesByCategory(DistanceCategory category) {
    return properties.where((p) => p.distanceCategory == category).toList();
  }

  /// Get average distance
  double get averageDistance {
    if (properties.isEmpty) return 0.0;
    return properties.map((p) => p.distance).reduce((a, b) => a + b) / properties.length;
  }

  /// Get closest property
  PropertyWithDistance? get closestProperty {
    if (properties.isEmpty) return null;
    return properties.reduce((a, b) => a.distance < b.distance ? a : b);
  }

  /// Get farthest property
  PropertyWithDistance? get farthestProperty {
    if (properties.isEmpty) return null;
    return properties.reduce((a, b) => a.distance > b.distance ? a : b);
  }
}
