# How to Run Rama Realty MLM Application

## Prerequisites
1. **Flutter SDK** installed and configured
2. **Android Studio** or **VS Code** with Flutter extensions
3. **Android device/emulator** or **Chrome browser** for web

## Quick Start

### Option 1: Using the Batch Script (Windows)
```bash
# Double-click or run from command prompt
run_app.bat
```

### Option 2: Manual Commands
```bash
# 1. Get dependencies
flutter pub get

# 2. Check available devices
flutter devices

# 3. Run the app
flutter run --debug
```

### Option 3: Using IDE
1. Open the project in **Android Studio** or **VS Code**
2. Select a device from the device selector
3. Press **F5** or click the **Run** button

## Available Platforms

### 🤖 Android
- **Physical Device**: Enable USB debugging and connect via USB
- **Emulator**: Start an Android emulator from Android Studio

### 🌐 Web (Chrome)
```bash
flutter run -d chrome
```

### 🖥️ Windows Desktop
```bash
flutter run -d windows
```

## Test Accounts

### Admin Account
- **Email**: `<EMAIL>`
- **Password**: `admin123`
- **Features**: Full admin access, analytics, user management

### Agent Account
- **Email**: `<EMAIL>`
- **Password**: `agent123`
- **Features**: Property browsing, lead management, commission tracking

## Key Features to Test

### 🔐 Authentication
- [x] User registration with referral codes
- [x] Email/password login
- [x] Password reset functionality
- [x] Email verification

### 🏠 Property Management
- [x] Browse properties with filters
- [x] Property search by location, price, type
- [x] Property details with image gallery
- [x] Add properties to favorites

### 💰 Commission System
- [x] View commission history
- [x] Track earnings by level (L0: 5%, L1: 2%, L2: 1%, L3: 0.5%, L4: 0.2%)
- [x] Commission distribution through MLM hierarchy
- [x] Indian Rupee formatting (₹1.5 Cr, ₹25.0 L, ₹15.0 K)

### ⭐ Star System
- [x] Earn stars for sales (1 star per sale)
- [x] Track progress toward 12-star bonus
- [x] View star leaderboard
- [x] Star bonus eligibility

### 👥 MLM Network
- [x] View network hierarchy
- [x] Track downline agents
- [x] Referral code generation
- [x] Network statistics

### 📱 Lead Management
- [x] Create and track leads
- [x] Lead status management
- [x] Customer interaction history
- [x] Follow-up reminders

### 📊 Admin Dashboard
- [x] System analytics and metrics
- [x] User management (activate/deactivate)
- [x] Property approval workflow
- [x] Commission configuration
- [x] Performance monitoring

### 💬 WhatsApp Integration
- [x] Share property details via WhatsApp
- [x] Lead generation from WhatsApp
- [x] Quick property sharing

## Troubleshooting

### Common Issues

#### 1. Flutter Command Hanging
```bash
# Clear Flutter cache
flutter clean
flutter pub cache repair

# Restart Flutter daemon
flutter daemon --shutdown
```

#### 2. Dependencies Issues
```bash
# Clean and reinstall dependencies
flutter clean
flutter pub get
```

#### 3. Device Not Detected
```bash
# For Android
adb devices
adb kill-server
adb start-server

# For web
flutter config --enable-web
```

#### 4. Build Errors
```bash
# Check for analysis issues
flutter analyze

# Fix formatting
flutter format .
```

### Performance Tips

#### 1. Debug vs Release Mode
```bash
# Debug mode (slower, with hot reload)
flutter run --debug

# Release mode (faster, optimized)
flutter run --release
```

#### 2. Web Performance
```bash
# Use Chrome for better performance
flutter run -d chrome --web-renderer html
```

#### 3. Memory Usage
- Close other applications while running
- Use release mode for performance testing
- Monitor memory usage in Flutter Inspector

## Development Workflow

### 1. Hot Reload
- Press **r** in terminal or **Ctrl+S** in IDE
- Changes reflect instantly without losing app state

### 2. Hot Restart
- Press **R** in terminal or **Ctrl+Shift+S** in IDE
- Restarts app completely with new changes

### 3. Debugging
- Use Flutter Inspector for widget tree analysis
- Add breakpoints in IDE for debugging
- Use `print()` statements for console logging

## Firebase Configuration

### Required Setup
1. **Firebase Project**: Create project at https://console.firebase.google.com
2. **Authentication**: Enable Email/Password authentication
3. **Firestore**: Set up Firestore database
4. **Storage**: Configure Firebase Storage for images

### Configuration Files
- `android/app/google-services.json` (Android)
- `web/firebase-config.js` (Web)
- `ios/Runner/GoogleService-Info.plist` (iOS)

## Support

### Documentation
- [Flutter Documentation](https://docs.flutter.dev/)
- [Firebase Documentation](https://firebase.google.com/docs)
- [Dart Language Guide](https://dart.dev/guides)

### Common Commands
```bash
# Check Flutter installation
flutter doctor

# Update Flutter
flutter upgrade

# List available devices
flutter devices

# Run tests
flutter test

# Build for production
flutter build apk --release
flutter build web --release
```

---

**Happy Coding! 🚀**

For any issues or questions, please check the documentation or contact the development team.
