@echo off
echo ========================================
echo    Rama Realty MLM Application
echo ========================================
echo.

echo Step 1: Checking Flutter installation...
flutter --version
if %errorlevel% neq 0 (
    echo Error: Flutter is not installed or not in PATH
    pause
    exit /b 1
)

echo.
echo Step 2: Cleaning previous builds...
flutter clean
if %errorlevel% neq 0 (
    echo Warning: Clean failed, continuing...
)

echo.
echo Step 3: Getting dependencies...
flutter pub get
if %errorlevel% neq 0 (
    echo Error: Failed to get dependencies
    echo Try running: flutter pub cache repair
    pause
    exit /b 1
)

echo.
echo Step 4: Checking for available devices...
flutter devices
if %errorlevel% neq 0 (
    echo Error: Failed to check devices
    pause
    exit /b 1
)

echo.
echo Step 5: Running on Windows Desktop...
echo This may take a few minutes for first build...
echo.
flutter run -d windows --debug

echo.
echo Application finished.
pause
