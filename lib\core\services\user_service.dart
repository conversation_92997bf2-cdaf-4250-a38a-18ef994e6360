import 'package:cloud_firestore/cloud_firestore.dart';

import 'package:flutter/foundation.dart';

import '../models/user_model.dart';
import '../constants/app_constants.dart';

/// Service for managing user operations in Firestore
class UserService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Create a new user profile in Firestore
  static Future<bool> createUserProfile(UserModel user) async {
    try {
      if (kDebugMode) {
        print('🔄 UserService: Creating profile for ${user.email}');
        print('📊 UserService: Collection: ${AppConstants.usersCollection}');
        print('🆔 UserService: Document ID: ${user.id}');
      }

      // Convert user to JSON for Firestore
      final userData = user.toFirestore();

      if (kDebugMode) {
        print('📝 UserService: Data to save: $userData');
      }

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(user.id)
          .set(userData);

      if (kDebugMode) {
        print('✅ UserService: User profile created successfully: ${user.id}');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ UserService: Error creating user profile: $e');
        print('🔍 UserService: Error type: ${e.runtimeType}');
        if (e is FirebaseException) {
          print('🔥 UserService: Firebase error code: ${e.code}');
          print('🔥 UserService: Firebase error message: ${e.message}');
        }
      }
      return false;
    }
  }

  /// Get user profile by ID
  static Future<UserModel?> getUserProfile(String userId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (doc.exists && doc.data() != null) {
        return UserModel.fromFirestore(doc);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user profile: $e');
      }
      return null;
    }
  }

  /// Update user profile
  static Future<bool> updateUserProfile(
    String userId,
    Map<String, dynamic> updates,
  ) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({...updates, 'updatedAt': FieldValue.serverTimestamp()});

      if (kDebugMode) {
        print('User profile updated successfully: $userId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating user profile: $e');
      }
      return false;
    }
  }

  /// Get all agents (for admin)
  static Future<List<UserModel>> getAllAgents() async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('role', isEqualTo: AppConstants.agentRole)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting all agents: $e');
      }
      return [];
    }
  }

  /// Get user's downlines (direct referrals)
  static Future<List<UserModel>> getUserDownlines(String userId) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('uplineId', isEqualTo: userId)
          .orderBy('createdAt', descending: true)
          .get();

      return querySnapshot.docs
          .map((doc) => UserModel.fromFirestore(doc))
          .toList();
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user downlines: $e');
      }
      return [];
    }
  }

  /// Get user's upline (referrer)
  static Future<UserModel?> getUserUpline(String uplineId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(uplineId)
          .get();

      if (doc.exists && doc.data() != null) {
        return UserModel.fromFirestore(doc);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user upline: $e');
      }
      return null;
    }
  }

  /// Search users by name or email
  static Future<List<UserModel>> searchUsers(String searchTerm) async {
    try {
      // Search by name (case-insensitive)
      final nameQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .where('name', isGreaterThanOrEqualTo: searchTerm)
          .where('name', isLessThanOrEqualTo: '$searchTerm\uf8ff')
          .limit(20)
          .get();

      // Search by email
      final emailQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .where('email', isGreaterThanOrEqualTo: searchTerm)
          .where('email', isLessThanOrEqualTo: '$searchTerm\uf8ff')
          .limit(20)
          .get();

      final Set<String> userIds = {};
      final List<UserModel> users = [];

      // Add users from name search
      for (final doc in nameQuery.docs) {
        if (!userIds.contains(doc.id)) {
          userIds.add(doc.id);
          users.add(UserModel.fromFirestore(doc));
        }
      }

      // Add users from email search
      for (final doc in emailQuery.docs) {
        if (!userIds.contains(doc.id)) {
          userIds.add(doc.id);
          users.add(UserModel.fromFirestore(doc));
        }
      }

      return users;
    } catch (e) {
      if (kDebugMode) {
        print('Error searching users: $e');
      }
      return [];
    }
  }

  /// Update user statistics (sales, commissions, etc.)
  static Future<bool> updateUserStats(
    String userId, {
    double? totalSales,
    double? totalCommissions,
    int? totalStars,
    double? totalBonuses,
  }) async {
    try {
      final Map<String, dynamic> updates = {};

      if (totalSales != null) {
        updates['totalSales'] = FieldValue.increment(totalSales);
      }
      if (totalCommissions != null) {
        updates['totalCommissions'] = FieldValue.increment(totalCommissions);
      }
      if (totalStars != null) {
        updates['totalStars'] = FieldValue.increment(totalStars);
      }
      if (totalBonuses != null) {
        updates['totalBonuses'] = FieldValue.increment(totalBonuses);
      }

      updates['updatedAt'] = FieldValue.serverTimestamp();

      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update(updates);

      if (kDebugMode) {
        print('User stats updated successfully: $userId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error updating user stats: $e');
      }
      return false;
    }
  }

  /// Deactivate user account
  static Future<bool> deactivateUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isActive': false,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (kDebugMode) {
        print('User deactivated successfully: $userId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error deactivating user: $e');
      }
      return false;
    }
  }

  /// Reactivate user account
  static Future<bool> reactivateUser(String userId) async {
    try {
      await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .update({
            'isActive': true,
            'updatedAt': FieldValue.serverTimestamp(),
          });

      if (kDebugMode) {
        print('User reactivated successfully: $userId');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('Error reactivating user: $e');
      }
      return false;
    }
  }

  /// Get user by referral code
  static Future<UserModel?> getUserByReferralCode(String referralCode) async {
    try {
      final querySnapshot = await _firestore
          .collection(AppConstants.usersCollection)
          .where('additionalInfo.referralCode', isEqualTo: referralCode)
          .limit(1)
          .get();

      if (querySnapshot.docs.isNotEmpty) {
        return UserModel.fromFirestore(querySnapshot.docs.first);
      }

      return null;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting user by referral code: $e');
      }
      return null;
    }
  }

  /// Check if referral code exists
  static Future<bool> isReferralCodeExists(String referralCode) async {
    try {
      final user = await getUserByReferralCode(referralCode);
      return user != null;
    } catch (e) {
      if (kDebugMode) {
        print('Error checking referral code: $e');
      }
      return false;
    }
  }

  /// Generate unique referral code
  static Future<String> generateUniqueReferralCode(String baseName) async {
    String baseCode = baseName
        .toUpperCase()
        .replaceAll(' ', '')
        .substring(0, 3);
    int counter = 1;

    while (true) {
      String referralCode = '$baseCode${counter.toString().padLeft(3, '0')}';
      bool exists = await isReferralCodeExists(referralCode);

      if (!exists) {
        return referralCode;
      }

      counter++;
      if (counter > 999) {
        // Fallback to timestamp-based code
        return '$baseCode${DateTime.now().millisecondsSinceEpoch.toString().substring(8)}';
      }
    }
  }
}
