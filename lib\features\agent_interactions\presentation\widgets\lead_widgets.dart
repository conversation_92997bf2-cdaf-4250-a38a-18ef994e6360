import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import '../../../../core/models/lead_model.dart';
import '../../../../core/services/lead_service.dart';

/// Lead card widget for displaying lead information
class LeadCard extends ConsumerWidget {
  final LeadModel lead;
  final VoidCallback? onTap;
  final VoidCallback? onStatusUpdate;

  const LeadCard({
    super.key,
    required this.lead,
    this.onTap,
    this.onStatusUpdate,
  });

  @override
  Widget build(BuildContext context, WidgetRef ref) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      elevation: 2,
      child: InkWell(
        onTap: onTap,
        borderRadius: BorderRadius.circular(12),
        child: Padding(
          padding: const EdgeInsets.all(16),
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              // Header with customer name and priority
              Row(
                children: [
                  Expanded(
                    child: Text(
                      lead.customerName,
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: lead.priorityColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(12),
                    ),
                    child: Text(
                      lead.priority.toUpperCase(),
                      style: TextStyle(
                        color: lead.priorityColor,
                        fontSize: 10,
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Property title
              Text(
                lead.propertyTitle,
                style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                  color: Colors.grey[600],
                ),
              ),

              const SizedBox(height: 8),

              // Contact information
              Row(
                children: [
                  Icon(Icons.phone, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Text(
                    lead.customerPhone,
                    style: Theme.of(context).textTheme.bodySmall,
                  ),
                  const SizedBox(width: 16),
                  Icon(Icons.email, size: 16, color: Colors.grey[600]),
                  const SizedBox(width: 4),
                  Expanded(
                    child: Text(
                      lead.customerEmail,
                      style: Theme.of(context).textTheme.bodySmall,
                      overflow: TextOverflow.ellipsis,
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 8),

              // Budget range
              if (lead.budgetMin != null || lead.budgetMax != null) ...[
                Row(
                  children: [
                    Icon(Icons.currency_rupee, size: 16, color: Colors.grey[600]),
                    const SizedBox(width: 4),
                    Text(
                      'Budget: ${lead.formattedBudgetRange}',
                      style: Theme.of(context).textTheme.bodySmall,
                    ),
                  ],
                ),
                const SizedBox(height: 8),
              ],

              // Status and source
              Row(
                children: [
                  Container(
                    padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
                    decoration: BoxDecoration(
                      color: lead.statusColor.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                    ),
                    child: Text(
                      lead.statusDisplayName,
                      style: TextStyle(
                        color: lead.statusColor,
                        fontSize: 12,
                        fontWeight: FontWeight.w500,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    'via ${lead.sourceDisplayName}',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                  const Spacer(),
                  Text(
                    '${lead.daysSinceLastContact}d ago',
                    style: Theme.of(context).textTheme.bodySmall?.copyWith(
                      color: Colors.grey[600],
                    ),
                  ),
                ],
              ),

              const SizedBox(height: 12),

              // Action buttons
              Row(
                children: [
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _makePhoneCall(lead.customerPhone),
                      icon: const Icon(Icons.phone, size: 16),
                      label: const Text('Call'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: OutlinedButton.icon(
                      onPressed: () => _sendWhatsApp(lead.customerPhone),
                      icon: const Icon(Icons.chat, size: 16),
                      label: const Text('WhatsApp'),
                      style: OutlinedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Expanded(
                    child: ElevatedButton.icon(
                      onPressed: () => _showStatusUpdateDialog(context, ref),
                      icon: const Icon(Icons.edit, size: 16),
                      label: const Text('Update'),
                      style: ElevatedButton.styleFrom(
                        padding: const EdgeInsets.symmetric(vertical: 8),
                      ),
                    ),
                  ),
                ],
              ),

              // Follow-up indicator
              if (lead.needsFollowUp) ...[
                const SizedBox(height: 8),
                Container(
                  padding: const EdgeInsets.all(8),
                  decoration: BoxDecoration(
                    color: Colors.orange.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(color: Colors.orange.withValues(alpha: 0.3)),
                  ),
                  child: Row(
                    children: [
                      const Icon(Icons.schedule, color: Colors.orange, size: 16),
                      const SizedBox(width: 8),
                      Text(
                        'Follow-up needed',
                        style: TextStyle(
                          color: Colors.orange[700],
                          fontWeight: FontWeight.w500,
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ],
          ),
        ),
      ),
    );
  }

  void _makePhoneCall(String phoneNumber) async {
    final uri = Uri(scheme: 'tel', path: phoneNumber);
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
    }
  }

  void _sendWhatsApp(String phoneNumber) async {
    final message = 'Hi! I\'m reaching out regarding the property "${lead.propertyTitle}". Are you still interested?';
    final uri = Uri.parse('https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}');
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
    }
  }

  void _showStatusUpdateDialog(BuildContext context, WidgetRef ref) {
    showDialog(
      context: context,
      builder: (context) => LeadStatusUpdateDialog(
        lead: lead,
        onUpdate: onStatusUpdate,
      ),
    );
  }
}

/// Lead status update dialog
class LeadStatusUpdateDialog extends StatefulWidget {
  final LeadModel lead;
  final VoidCallback? onUpdate;

  const LeadStatusUpdateDialog({
    super.key,
    required this.lead,
    this.onUpdate,
  });

  @override
  State<LeadStatusUpdateDialog> createState() => _LeadStatusUpdateDialogState();
}

class _LeadStatusUpdateDialogState extends State<LeadStatusUpdateDialog> {
  late String _selectedStatus;
  late String _selectedPriority;
  final TextEditingController _notesController = TextEditingController();
  DateTime? _followUpDate;
  bool _isLoading = false;

  @override
  void initState() {
    super.initState();
    _selectedStatus = widget.lead.status;
    _selectedPriority = widget.lead.priority;
    _notesController.text = widget.lead.notes ?? '';
    _followUpDate = widget.lead.followUpDate;
  }

  @override
  void dispose() {
    _notesController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: Text('Update Lead: ${widget.lead.customerName}'),
      content: SingleChildScrollView(
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Status dropdown
            DropdownButtonFormField<String>(
              value: _selectedStatus,
              decoration: const InputDecoration(
                labelText: 'Status',
                border: OutlineInputBorder(),
              ),
              items: [
                {'value': 'new', 'label': 'New Lead'},
                {'value': 'contacted', 'label': 'Contacted'},
                {'value': 'interested', 'label': 'Interested'},
                {'value': 'not_interested', 'label': 'Not Interested'},
                {'value': 'converted', 'label': 'Converted'},
                {'value': 'lost', 'label': 'Lost'},
              ].map((status) => DropdownMenuItem(
                value: status['value'],
                child: Text(status['label']!),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedStatus = value!;
                });
              },
            ),

            const SizedBox(height: 16),

            // Priority dropdown
            DropdownButtonFormField<String>(
              value: _selectedPriority,
              decoration: const InputDecoration(
                labelText: 'Priority',
                border: OutlineInputBorder(),
              ),
              items: [
                {'value': 'low', 'label': 'Low Priority'},
                {'value': 'medium', 'label': 'Medium Priority'},
                {'value': 'high', 'label': 'High Priority'},
                {'value': 'urgent', 'label': 'Urgent'},
              ].map((priority) => DropdownMenuItem(
                value: priority['value'],
                child: Text(priority['label']!),
              )).toList(),
              onChanged: (value) {
                setState(() {
                  _selectedPriority = value!;
                });
              },
            ),

            const SizedBox(height: 16),

            // Notes field
            TextField(
              controller: _notesController,
              decoration: const InputDecoration(
                labelText: 'Notes',
                border: OutlineInputBorder(),
                hintText: 'Add any notes about this lead...',
              ),
              maxLines: 3,
            ),

            const SizedBox(height: 16),

            // Follow-up date
            ListTile(
              contentPadding: EdgeInsets.zero,
              leading: const Icon(Icons.calendar_today),
              title: Text(_followUpDate != null 
                  ? 'Follow-up: ${_followUpDate!.day}/${_followUpDate!.month}/${_followUpDate!.year}'
                  : 'Set follow-up date'),
              trailing: _followUpDate != null 
                  ? IconButton(
                      icon: const Icon(Icons.clear),
                      onPressed: () => setState(() => _followUpDate = null),
                    )
                  : null,
              onTap: () => _selectFollowUpDate(),
            ),
          ],
        ),
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: _isLoading ? null : _updateLead,
          child: _isLoading 
              ? const SizedBox(
                  width: 16,
                  height: 16,
                  child: CircularProgressIndicator(strokeWidth: 2),
                )
              : const Text('Update'),
        ),
      ],
    );
  }

  Future<void> _selectFollowUpDate() async {
    final date = await showDatePicker(
      context: context,
      initialDate: _followUpDate ?? DateTime.now().add(const Duration(days: 1)),
      firstDate: DateTime.now(),
      lastDate: DateTime.now().add(const Duration(days: 365)),
    );

    if (date != null) {
      setState(() {
        _followUpDate = date;
      });
    }
  }

  Future<void> _updateLead() async {
    setState(() {
      _isLoading = true;
    });

    try {
      final result = await LeadService.updateLeadStatus(
        widget.lead.id,
        _selectedStatus,
        notes: _notesController.text.trim().isNotEmpty ? _notesController.text.trim() : null,
        followUpDate: _followUpDate,
      );

      if (result.isSuccess) {
        widget.onUpdate?.call();
        if (mounted) {
          Navigator.of(context).pop();
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Lead updated successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to update lead: ${result.message}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error updating lead: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() {
          _isLoading = false;
        });
      }
    }
  }
}
