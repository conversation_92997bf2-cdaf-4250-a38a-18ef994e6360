import 'package:flutter/material.dart';

/// Property type enumeration with enhanced categorization
enum PropertyType {
  residential,
  commercial,
  land,
  industrial,
  agricultural,
}

extension PropertyTypeExtension on PropertyType {
  String get displayName {
    switch (this) {
      case PropertyType.residential:
        return 'Residential';
      case PropertyType.commercial:
        return 'Commercial';
      case PropertyType.land:
        return 'Land';
      case PropertyType.industrial:
        return 'Industrial';
      case PropertyType.agricultural:
        return 'Agricultural';
    }
  }

  String get value {
    switch (this) {
      case PropertyType.residential:
        return 'Residential';
      case PropertyType.commercial:
        return 'Commercial';
      case PropertyType.land:
        return 'Land';
      case PropertyType.industrial:
        return 'Industrial';
      case PropertyType.agricultural:
        return 'Agricultural';
    }
  }

  IconData get icon {
    switch (this) {
      case PropertyType.residential:
        return Icons.home;
      case PropertyType.commercial:
        return Icons.business;
      case PropertyType.land:
        return Icons.landscape;
      case PropertyType.industrial:
        return Icons.factory;
      case PropertyType.agricultural:
        return Icons.agriculture;
    }
  }

  Color get color {
    switch (this) {
      case PropertyType.residential:
        return const Color(0xFF4CAF50);
      case PropertyType.commercial:
        return const Color(0xFF2196F3);
      case PropertyType.land:
        return const Color(0xFF795548);
      case PropertyType.industrial:
        return const Color(0xFF607D8B);
      case PropertyType.agricultural:
        return const Color(0xFF8BC34A);
    }
  }

  List<String> get subTypes {
    switch (this) {
      case PropertyType.residential:
        return ['Apartment', 'Villa', 'Independent House', 'Penthouse', 'Studio', 'Duplex'];
      case PropertyType.commercial:
        return ['Office', 'Shop', 'Warehouse', 'Showroom', 'Restaurant', 'Hotel'];
      case PropertyType.land:
        return ['Residential Plot', 'Commercial Plot', 'Agricultural Land', 'Industrial Land'];
      case PropertyType.industrial:
        return ['Factory', 'Manufacturing Unit', 'Godown', 'Cold Storage'];
      case PropertyType.agricultural:
        return ['Farmland', 'Plantation', 'Orchard', 'Dairy Farm'];
    }
  }
}

/// Property status enumeration
enum PropertyStatus {
  forSale,
  forRent,
  sold,
  rented,
  underConstruction,
  readyToMove,
}

extension PropertyStatusExtension on PropertyStatus {
  String get displayName {
    switch (this) {
      case PropertyStatus.forSale:
        return 'For Sale';
      case PropertyStatus.forRent:
        return 'For Rent';
      case PropertyStatus.sold:
        return 'Sold';
      case PropertyStatus.rented:
        return 'Rented';
      case PropertyStatus.underConstruction:
        return 'Under Construction';
      case PropertyStatus.readyToMove:
        return 'Ready to Move';
    }
  }

  String get value {
    switch (this) {
      case PropertyStatus.forSale:
        return 'for_sale';
      case PropertyStatus.forRent:
        return 'for_rent';
      case PropertyStatus.sold:
        return 'sold';
      case PropertyStatus.rented:
        return 'rented';
      case PropertyStatus.underConstruction:
        return 'under_construction';
      case PropertyStatus.readyToMove:
        return 'ready_to_move';
    }
  }

  Color get color {
    switch (this) {
      case PropertyStatus.forSale:
        return const Color(0xFF4CAF50);
      case PropertyStatus.forRent:
        return const Color(0xFF2196F3);
      case PropertyStatus.sold:
        return const Color(0xFF9E9E9E);
      case PropertyStatus.rented:
        return const Color(0xFF9E9E9E);
      case PropertyStatus.underConstruction:
        return const Color(0xFFFF9800);
      case PropertyStatus.readyToMove:
        return const Color(0xFF8BC34A);
    }
  }

  IconData get icon {
    switch (this) {
      case PropertyStatus.forSale:
        return Icons.sell;
      case PropertyStatus.forRent:
        return Icons.key;
      case PropertyStatus.sold:
        return Icons.check_circle;
      case PropertyStatus.rented:
        return Icons.check_circle_outline;
      case PropertyStatus.underConstruction:
        return Icons.construction;
      case PropertyStatus.readyToMove:
        return Icons.home_filled;
    }
  }

  bool get isAvailable => this == PropertyStatus.forSale || this == PropertyStatus.forRent;
}

/// Property sort options
enum PropertySortOption {
  priceAsc,
  priceDesc,
  areaAsc,
  areaDesc,
  newest,
  oldest,
  featured,
  alphabetical,
}

extension PropertySortOptionExtension on PropertySortOption {
  String get displayName {
    switch (this) {
      case PropertySortOption.priceAsc:
        return 'Price: Low to High';
      case PropertySortOption.priceDesc:
        return 'Price: High to Low';
      case PropertySortOption.areaAsc:
        return 'Area: Small to Large';
      case PropertySortOption.areaDesc:
        return 'Area: Large to Small';
      case PropertySortOption.newest:
        return 'Newest First';
      case PropertySortOption.oldest:
        return 'Oldest First';
      case PropertySortOption.featured:
        return 'Featured First';
      case PropertySortOption.alphabetical:
        return 'A to Z';
    }
  }

  IconData get icon {
    switch (this) {
      case PropertySortOption.priceAsc:
        return Icons.trending_up;
      case PropertySortOption.priceDesc:
        return Icons.trending_down;
      case PropertySortOption.areaAsc:
        return Icons.expand_more;
      case PropertySortOption.areaDesc:
        return Icons.expand_less;
      case PropertySortOption.newest:
        return Icons.new_releases;
      case PropertySortOption.oldest:
        return Icons.history;
      case PropertySortOption.featured:
        return Icons.star;
      case PropertySortOption.alphabetical:
        return Icons.sort_by_alpha;
    }
  }
}

/// Price range options for filtering
enum PriceRange {
  under10L,
  range10L25L,
  range25L50L,
  range50L1Cr,
  range1Cr2Cr,
  above2Cr,
  custom,
}

extension PriceRangeExtension on PriceRange {
  String get displayName {
    switch (this) {
      case PriceRange.under10L:
        return 'Under ₹10 L';
      case PriceRange.range10L25L:
        return '₹10 L - ₹25 L';
      case PriceRange.range25L50L:
        return '₹25 L - ₹50 L';
      case PriceRange.range50L1Cr:
        return '₹50 L - ₹1 Cr';
      case PriceRange.range1Cr2Cr:
        return '₹1 Cr - ₹2 Cr';
      case PriceRange.above2Cr:
        return 'Above ₹2 Cr';
      case PriceRange.custom:
        return 'Custom Range';
    }
  }

  double? get minPrice {
    switch (this) {
      case PriceRange.under10L:
        return 0;
      case PriceRange.range10L25L:
        return 1000000;
      case PriceRange.range25L50L:
        return 2500000;
      case PriceRange.range50L1Cr:
        return 5000000;
      case PriceRange.range1Cr2Cr:
        return 10000000;
      case PriceRange.above2Cr:
        return 20000000;
      case PriceRange.custom:
        return null;
    }
  }

  double? get maxPrice {
    switch (this) {
      case PriceRange.under10L:
        return 1000000;
      case PriceRange.range10L25L:
        return 2500000;
      case PriceRange.range25L50L:
        return 5000000;
      case PriceRange.range50L1Cr:
        return 10000000;
      case PriceRange.range1Cr2Cr:
        return 20000000;
      case PriceRange.above2Cr:
        return null;
      case PriceRange.custom:
        return null;
    }
  }
}

/// Common amenities for properties
class PropertyAmenities {
  static const List<String> residential = [
    'Parking',
    'Lift',
    'Security',
    'Power Backup',
    'Water Supply',
    'Garden',
    'Gym',
    'Swimming Pool',
    'Club House',
    'Children Play Area',
    'CCTV',
    'Intercom',
    'Maintenance Staff',
    'Visitor Parking',
    'Waste Disposal',
    'Fire Safety',
    'Earthquake Resistant',
    'Vastu Compliant',
    'Gated Community',
    'Pet Friendly',
  ];

  static const List<String> commercial = [
    'Parking',
    'Lift',
    'Security',
    'Power Backup',
    'Water Supply',
    'Air Conditioning',
    'Conference Room',
    'Reception Area',
    'Cafeteria',
    'CCTV',
    'Intercom',
    'High Speed Internet',
    'Visitor Parking',
    'Waste Disposal',
    'Fire Safety',
    'Wheelchair Accessible',
    'Public Transport',
    'ATM',
    'Food Court',
    'Medical Facility',
  ];

  static const List<String> land = [
    'Clear Title',
    'Boundary Wall',
    'Road Access',
    'Water Connection',
    'Electricity Connection',
    'Drainage',
    'Corner Plot',
    'Gated Community',
    'Security',
    'Garden',
    'Tree Plantation',
    'Soil Testing Done',
    'Survey Done',
    'Approved Layout',
    'RERA Approved',
    'Bank Loan Available',
    'Investment Potential',
    'Development Ready',
    'Commercial Potential',
    'Residential Potential',
  ];

  static List<String> getAmenitiesForType(PropertyType type) {
    switch (type) {
      case PropertyType.residential:
        return residential;
      case PropertyType.commercial:
        return commercial;
      case PropertyType.land:
      case PropertyType.industrial:
      case PropertyType.agricultural:
        return land;
    }
  }
}
