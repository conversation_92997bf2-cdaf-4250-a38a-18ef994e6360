import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import 'core/constants/app_constants.dart';
import 'shared/themes/app_theme.dart';

/// Simple demo app that bypasses all authentication
class DemoMLMApp extends StatelessWidget {
  const DemoMLMApp({super.key});

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: AppConstants.appName,
      theme: AppTheme.lightTheme,
      darkTheme: AppTheme.darkTheme,
      themeMode: ThemeMode.dark,
      home: const DemoMainScreen(),
      debugShowCheckedModeBanner: false,
    );
  }
}

/// Main demo screen with all MLM features
class DemoMainScreen extends StatefulWidget {
  const DemoMainScreen({super.key});

  @override
  State<DemoMainScreen> createState() => _DemoMainScreenState();
}

class _DemoMainScreenState extends State<DemoMainScreen> {
  int _currentIndex = 0;

  final List<Widget> _screens = [
    const DemoDashboard(),
    const DemoProperties(),
    const DemoCommissions(),
    const DemoNetwork(),
    const DemoReports(),
  ];

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      appBar: AppBar(
        title: const Text('Rama Realty MLM'),
        backgroundColor: AppTheme.darkSurface,
        elevation: 0,
        actions: [
          IconButton(icon: const Icon(Icons.notifications), onPressed: () {}),
          IconButton(icon: const Icon(Icons.account_circle), onPressed: () {}),
        ],
      ),
      body: _screens[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        type: BottomNavigationBarType.fixed,
        backgroundColor: AppTheme.darkSurface,
        selectedItemColor: AppTheme.primaryColor,
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.home_work),
            label: 'Properties',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.monetization_on),
            label: 'Commissions',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_tree),
            label: 'Network',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Reports',
          ),
        ],
      ),
    );
  }
}

/// Demo Dashboard Screen
class DemoDashboard extends StatelessWidget {
  const DemoDashboard({super.key});

  @override
  Widget build(BuildContext context) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: AppTheme.primaryGradient,
              borderRadius: BorderRadius.circular(16),
            ),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const Text(
                  'Welcome back, Demo Agent!',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 24,
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 8),
                Text(
                  'Gold Tier • Level 2 Agent',
                  style: TextStyle(
                    color: Colors.white.withValues(alpha: 0.9),
                    fontSize: 16,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Stats Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            children: [
              _buildStatCard(
                'Total Sales',
                '₹25,00,000',
                Icons.trending_up,
                Colors.green,
              ),
              _buildStatCard(
                'Commissions',
                '₹1,25,000',
                Icons.monetization_on,
                AppTheme.primaryColor,
              ),
              _buildStatCard(
                'Network Size',
                '12 Members',
                Icons.people,
                AppTheme.secondaryColor,
              ),
              _buildStatCard(
                'This Month',
                '₹85,000',
                Icons.calendar_today,
                Colors.blue,
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Recent Activity
          const Text(
            'Recent Activity',
            style: TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 16),

          ...List.generate(
            5,
            (index) => _buildActivityItem(
              'New commission earned',
              '₹${(5000 + index * 1000).toString()}',
              '${index + 1} hour${index == 0 ? '' : 's'} ago',
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatCard(
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface,
        borderRadius: BorderRadius.circular(12),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Icon(icon, color: color, size: 32),
          const SizedBox(height: 12),
          Text(title, style: TextStyle(color: Colors.grey[400], fontSize: 14)),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildActivityItem(String title, String amount, String time) {
    return Container(
      margin: const EdgeInsets.only(bottom: 12),
      padding: const EdgeInsets.all(16),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface,
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Container(
            width: 40,
            height: 40,
            decoration: BoxDecoration(
              color: AppTheme.primaryColor.withValues(alpha: 0.2),
              borderRadius: BorderRadius.circular(8),
            ),
            child: Icon(
              Icons.monetization_on,
              color: AppTheme.primaryColor,
              size: 20,
            ),
          ),
          const SizedBox(width: 12),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                Text(
                  time,
                  style: TextStyle(color: Colors.grey[400], fontSize: 14),
                ),
              ],
            ),
          ),
          Text(
            amount,
            style: TextStyle(
              color: AppTheme.primaryColor,
              fontSize: 16,
              fontWeight: FontWeight.bold,
            ),
          ),
        ],
      ),
    );
  }
}

/// Demo Properties Screen
class DemoProperties extends StatelessWidget {
  const DemoProperties({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Properties Management\n\n🏠 Property Listings\n📋 Property Details\n✅ Approval Workflow\n📊 Property Analytics',
        textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white, fontSize: 18, height: 1.5),
      ),
    );
  }
}

/// Demo Commissions Screen
class DemoCommissions extends StatelessWidget {
  const DemoCommissions({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Commission Management\n\n💰 Commission Tracking\n📈 Performance Analytics\n🎯 Tier Progression\n💎 Bonus Calculations',
        textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white, fontSize: 18, height: 1.5),
      ),
    );
  }
}

/// Demo Network Screen
class DemoNetwork extends StatelessWidget {
  const DemoNetwork({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'MLM Network\n\n🌐 Network Tree\n👥 Downline Management\n📊 Team Performance\n🔗 Referral Tracking',
        textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white, fontSize: 18, height: 1.5),
      ),
    );
  }
}

/// Demo Reports Screen
class DemoReports extends StatelessWidget {
  const DemoReports({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Text(
        'Reports & Analytics\n\n📊 Performance Reports\n📈 Sales Analytics\n💼 Commission Reports\n🎯 Goal Tracking',
        textAlign: TextAlign.center,
        style: TextStyle(color: Colors.white, fontSize: 18, height: 1.5),
      ),
    );
  }
}
