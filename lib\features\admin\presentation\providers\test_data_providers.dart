import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/services/test_data_service.dart';
import '../../../../core/models/admin_analytics_model.dart';

/// Test data status provider
final testDataStatusProvider = FutureProvider.autoDispose<bool>((ref) async {
  return await TestDataService.isTestDataLoaded();
});

/// Test data users provider
final testDataUsersProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  return await TestDataService.getUsers();
});

/// Test data properties provider
final testDataPropertiesProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  return await TestDataService.getProperties();
});

/// Test data transactions provider
final testDataTransactionsProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  return await TestDataService.getTransactions();
});

/// Test data commissions provider
final testDataCommissionsProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  return await TestDataService.getCommissions();
});

/// Test data stars provider
final testDataStarsProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  return await TestDataService.getStars();
});

/// Test data leads provider
final testDataLeadsProvider = FutureProvider.autoDispose<List<Map<String, dynamic>>>((ref) async {
  return await TestDataService.getLeads();
});

/// Test data summary provider
final testDataSummaryProvider = FutureProvider.autoDispose<Map<String, dynamic>>((ref) async {
  return await TestDataService.getTestDataSummary();
});

/// Combined analytics provider that uses test data when available
final combinedAnalyticsProvider = FutureProvider.autoDispose<AdminAnalyticsModel>((ref) async {
  final isTestDataLoaded = await TestDataService.isTestDataLoaded();
  
  if (isTestDataLoaded) {
    // Use test data to generate analytics
    return await _generateTestDataAnalytics();
  } else {
    // Fall back to regular analytics service
    // This would be the original analytics service
    return AdminAnalyticsModel(
      totalUsers: 0,
      totalProperties: 0,
      totalTransactions: 0,
      totalCommissions: 0.0,
      activeUsers: 0,
      pendingTransactions: 0,
      monthlyGrowth: 0.0,
      conversionRate: 0.0,
      averageCommission: 0.0,
      topPerformers: [],
      recentTransactions: [],
      userGrowthData: [],
      commissionTrends: [],
      propertyTypeDistribution: {},
      regionPerformance: {},
    );
  }
});

/// Generate analytics from test data
Future<AdminAnalyticsModel> _generateTestDataAnalytics() async {
  final users = await TestDataService.getUsers();
  final properties = await TestDataService.getProperties();
  final transactions = await TestDataService.getTransactions();
  final commissions = await TestDataService.getCommissions();
  final stars = await TestDataService.getStars();
  final leads = await TestDataService.getLeads();

  // Calculate analytics from test data
  final totalUsers = users.length;
  final totalProperties = properties.length;
  final totalTransactions = transactions.length;
  final activeUsers = users.where((user) => user['isActive'] == true).length;
  final pendingTransactions = transactions.where((t) => t['status'] == 'pending').length;
  
  // Calculate total commissions
  final totalCommissionsAmount = commissions
      .where((c) => c['status'] == 'paid')
      .fold<double>(0.0, (sum, c) => sum + (c['amount'] as num).toDouble());
  
  // Calculate average commission
  final paidCommissions = commissions.where((c) => c['status'] == 'paid').toList();
  final averageCommission = paidCommissions.isNotEmpty 
      ? totalCommissionsAmount / paidCommissions.length 
      : 0.0;

  // Calculate monthly growth (mock calculation)
  final monthlyGrowth = 15.5; // Mock value
  
  // Calculate conversion rate from leads
  final convertedLeads = leads.where((l) => l['status'] == 'converted').length;
  final conversionRate = leads.isNotEmpty ? (convertedLeads / leads.length) * 100 : 0.0;

  // Get top performers based on stars
  final userStarCounts = <String, int>{};
  for (final star in stars) {
    final agentId = star['agentId'] as String;
    userStarCounts[agentId] = (userStarCounts[agentId] ?? 0) + 1;
  }
  
  final topPerformers = userStarCounts.entries
      .map((entry) {
        final user = users.firstWhere((u) => u['id'] == entry.key, orElse: () => {});
        return TopPerformer(
          id: entry.key,
          name: user['name'] ?? 'Unknown',
          stars: entry.value,
          commissions: commissions
              .where((c) => c['agentId'] == entry.key && c['status'] == 'paid')
              .fold<double>(0.0, (sum, c) => sum + (c['amount'] as num).toDouble()),
          sales: transactions.where((t) => t['agentId'] == entry.key).length,
        );
      })
      .toList()
    ..sort((a, b) => b.stars.compareTo(a.stars))
    ..take(10);

  // Get recent transactions
  final recentTransactions = transactions
      .map((t) => RecentTransaction(
            id: t['id'] as String,
            propertyTitle: 'Property ${t['propertyId']}',
            buyerName: t['buyerName'] as String,
            amount: (t['saleAmount'] as num).toDouble(),
            agentName: users.firstWhere((u) => u['id'] == t['agentId'], orElse: () => {})['name'] ?? 'Unknown',
            date: DateTime.parse(t['saleDate'] as String),
            status: t['status'] as String,
          ))
      .toList()
    ..sort((a, b) => b.date.compareTo(a.date))
    ..take(10);

  // Generate user growth data (mock data based on join dates)
  final userGrowthData = <UserGrowthData>[];
  final now = DateTime.now();
  for (int i = 11; i >= 0; i--) {
    final month = DateTime(now.year, now.month - i, 1);
    final usersInMonth = users.where((user) {
      final joinDate = DateTime.parse(user['joinedAt'] as String);
      return joinDate.year == month.year && joinDate.month == month.month;
    }).length;
    
    userGrowthData.add(UserGrowthData(
      month: month,
      newUsers: usersInMonth,
      totalUsers: users.where((user) {
        final joinDate = DateTime.parse(user['joinedAt'] as String);
        return joinDate.isBefore(month.add(const Duration(days: 31)));
      }).length,
    ));
  }

  // Generate commission trends (mock data)
  final commissionTrends = <CommissionTrend>[];
  for (int i = 11; i >= 0; i--) {
    final month = DateTime(now.year, now.month - i, 1);
    final monthCommissions = commissions.where((commission) {
      final createdDate = DateTime.parse(commission['createdAt'] as String);
      return createdDate.year == month.year && createdDate.month == month.month;
    }).fold<double>(0.0, (sum, c) => sum + (c['amount'] as num).toDouble());
    
    commissionTrends.add(CommissionTrend(
      month: month,
      amount: monthCommissions,
      count: commissions.where((commission) {
        final createdDate = DateTime.parse(commission['createdAt'] as String);
        return createdDate.year == month.year && createdDate.month == month.month;
      }).length,
    ));
  }

  // Property type distribution
  final propertyTypeDistribution = <String, int>{};
  for (final property in properties) {
    final type = property['type'] as String;
    propertyTypeDistribution[type] = (propertyTypeDistribution[type] ?? 0) + 1;
  }

  // Region performance (mock data based on user addresses)
  final regionPerformance = <String, RegionPerformance>{};
  for (final user in users) {
    final address = user['address'] as String? ?? '';
    final region = address.split(',').last.trim();
    if (region.isNotEmpty) {
      final existing = regionPerformance[region] ?? RegionPerformance(
        region: region,
        users: 0,
        sales: 0,
        revenue: 0.0,
      );
      
      final userSales = transactions.where((t) => t['agentId'] == user['id']).length;
      final userRevenue = transactions
          .where((t) => t['agentId'] == user['id'])
          .fold<double>(0.0, (sum, t) => sum + (t['saleAmount'] as num).toDouble());
      
      regionPerformance[region] = RegionPerformance(
        region: region,
        users: existing.users + 1,
        sales: existing.sales + userSales,
        revenue: existing.revenue + userRevenue,
      );
    }
  }

  return AdminAnalyticsModel(
    totalUsers: totalUsers,
    totalProperties: totalProperties,
    totalTransactions: totalTransactions,
    totalCommissions: totalCommissionsAmount,
    activeUsers: activeUsers,
    pendingTransactions: pendingTransactions,
    monthlyGrowth: monthlyGrowth,
    conversionRate: conversionRate,
    averageCommission: averageCommission,
    topPerformers: topPerformers.toList(),
    recentTransactions: recentTransactions.toList(),
    userGrowthData: userGrowthData,
    commissionTrends: commissionTrends,
    propertyTypeDistribution: propertyTypeDistribution,
    regionPerformance: regionPerformance,
  );
}
