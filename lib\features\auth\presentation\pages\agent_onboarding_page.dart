import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../../../../core/models/user_model.dart';
import '../../../../core/services/user_service.dart';
import '../../../../core/utils/firestore_connection_test.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/web_image_upload_widget.dart';

/// Agent onboarding page for new user registration
class AgentOnboardingPage extends ConsumerStatefulWidget {
  const AgentOnboardingPage({super.key});

  @override
  ConsumerState<AgentOnboardingPage> createState() =>
      _AgentOnboardingPageState();
}

class _AgentOnboardingPageState extends ConsumerState<AgentOnboardingPage> {
  final _formKey = GlobalKey<FormState>();
  final _nameController = TextEditingController();
  final _phoneController = TextEditingController();
  final _referralCodeController = TextEditingController();

  String? _profileImageUrl;
  bool _isLoading = false;

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.darkBackground,
      body: SafeArea(
        child: SingleChildScrollView(
          padding: const EdgeInsets.all(24),
          child: Form(
            key: _formKey,
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                const SizedBox(height: 40),

                // Welcome Header
                Container(
                  width: double.infinity,
                  padding: const EdgeInsets.all(24),
                  decoration: BoxDecoration(
                    gradient: AppTheme.primaryGradient,
                    borderRadius: BorderRadius.circular(16),
                  ),
                  child: const Column(
                    children: [
                      Icon(Icons.person_add, size: 48, color: Colors.white),
                      SizedBox(height: 16),
                      Text(
                        'Welcome to Rama Samriddhi!',
                        style: TextStyle(
                          color: Color(0xFFFF4500), // Bright Bold Orange
                          fontSize: 28,
                          fontWeight: FontWeight.w900, // Extra bold
                          letterSpacing: 0.8,
                        ),
                        textAlign: TextAlign.center,
                      ),
                      SizedBox(height: 8),
                      Text(
                        'Complete your agent profile to get started',
                        style: TextStyle(color: Colors.white, fontSize: 16),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),

                const SizedBox(height: 32),

                // Profile Image Upload
                const Text(
                  'Profile Picture (Optional)',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 12),

                Center(
                  child: WebImageUploadWidget(
                    currentImageUrl: _profileImageUrl,
                    width: 120,
                    height: 120,
                    placeholder: 'Add Photo',
                    folder: 'profiles',
                    onImageUploaded: (url) {
                      setState(() {
                        _profileImageUrl = url;
                      });
                    },
                    onImageRemoved: () {
                      setState(() {
                        _profileImageUrl = null;
                      });
                    },
                  ),
                ),

                const SizedBox(height: 32),

                // Name Field
                const Text(
                  'Full Name *',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _nameController,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter your full name',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    filled: true,
                    fillColor: AppTheme.darkSurface,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    prefixIcon: const Icon(Icons.person, color: Colors.grey),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter your full name';
                    }
                    if (value.trim().length < 2) {
                      return 'Name must be at least 2 characters';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 20),

                // Phone Field
                const Text(
                  'Phone Number *',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _phoneController,
                  style: const TextStyle(color: Colors.white),
                  keyboardType: TextInputType.phone,
                  decoration: InputDecoration(
                    hintText: '+91 9876543210',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    filled: true,
                    fillColor: AppTheme.darkSurface,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    prefixIcon: const Icon(Icons.phone, color: Colors.grey),
                  ),
                  validator: (value) {
                    if (value == null || value.trim().isEmpty) {
                      return 'Please enter your phone number';
                    }
                    if (value.trim().length < 10) {
                      return 'Please enter a valid phone number';
                    }
                    return null;
                  },
                ),

                const SizedBox(height: 20),

                // Referral Code Field
                const Text(
                  'Referral Code (Optional)',
                  style: TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 8),
                TextFormField(
                  controller: _referralCodeController,
                  style: const TextStyle(color: Colors.white),
                  decoration: InputDecoration(
                    hintText: 'Enter referral code if you have one',
                    hintStyle: TextStyle(color: Colors.grey[400]),
                    filled: true,
                    fillColor: AppTheme.darkSurface,
                    border: OutlineInputBorder(
                      borderRadius: BorderRadius.circular(12),
                      borderSide: BorderSide.none,
                    ),
                    prefixIcon: const Icon(Icons.group_add, color: Colors.grey),
                  ),
                ),

                const SizedBox(height: 32),

                // Debug: Test Firestore Connection Button (always visible for debugging)
                ...[
                  const SizedBox(height: 16),
                  SizedBox(
                    width: double.infinity,
                    height: 48,
                    child: OutlinedButton(
                      onPressed: _isLoading ? null : _testFirestoreConnection,
                      style: OutlinedButton.styleFrom(
                        side: BorderSide(color: Colors.orange),
                        shape: RoundedRectangleBorder(
                          borderRadius: BorderRadius.circular(12),
                        ),
                      ),
                      child: const Text(
                        'Test Firestore Connection',
                        style: TextStyle(
                          color: Colors.orange,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ),
                  ),
                  const SizedBox(height: 16),
                ],

                // Complete Profile Button
                SizedBox(
                  width: double.infinity,
                  height: 56,
                  child: ElevatedButton(
                    onPressed: _isLoading ? null : _completeProfile,
                    style: ElevatedButton.styleFrom(
                      backgroundColor: AppTheme.primaryColor,
                      shape: RoundedRectangleBorder(
                        borderRadius: BorderRadius.circular(12),
                      ),
                    ),
                    child: _isLoading
                        ? const CircularProgressIndicator(color: Colors.white)
                        : const Text(
                            'Complete Profile',
                            style: TextStyle(
                              color: Colors.white,
                              fontSize: 16,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                  ),
                ),

                const SizedBox(height: 24),

                // Sign Out Option
                Center(
                  child: TextButton(
                    onPressed: _signOut,
                    child: const Text(
                      'Sign Out',
                      style: TextStyle(color: Colors.grey, fontSize: 14),
                    ),
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Future<void> _completeProfile() async {
    if (!_formKey.currentState!.validate()) {
      return;
    }

    setState(() => _isLoading = true);

    try {
      final user = FirebaseAuth.instance.currentUser;
      if (user == null) {
        throw Exception('No authenticated user found');
      }

      // Create user profile
      final userModel = UserModel(
        id: user.uid,
        email: user.email!,
        name: _nameController.text.trim(),
        phoneNumber: _phoneController.text.trim(),
        role: 'agent',
        level: 1,
        uplineId: null, // Will be set based on referral code
        downlineIds: [],
        totalSales: 0,
        totalCommissions: 0,
        totalStars: 0,
        totalBonuses: 0,
        isActive: true,
        profileImageUrl: _profileImageUrl,
        createdAt: DateTime.now(),
        updatedAt: DateTime.now(),
        additionalInfo: {
          'referralCode': _referralCodeController.text.trim().isEmpty
              ? null
              : _referralCodeController.text.trim(),
          'tier': 'bronze',
          'signupSource': 'web_onboarding',
        },
      );

      // Save to Firestore using UserService
      try {
        print('🔄 Attempting to create user profile for: ${userModel.email}');
        print('🆔 User ID: ${userModel.id}');
        print('📝 User data: ${userModel.toJson()}');
        print('🔥 Firebase user: ${user.uid}');
        print('📧 Firebase email: ${user.email}');

        final success = await UserService.createUserProfile(userModel);

        if (mounted) {
          if (success) {
            print('✅ Profile created successfully!');

            // Verify the document was actually created
            try {
              final doc = await FirebaseFirestore.instance
                  .collection('users')
                  .doc(userModel.id)
                  .get();
              print('📄 Document exists: ${doc.exists}');
              if (doc.exists) {
                print('📊 Document data: ${doc.data()}');
              }
            } catch (e) {
              print('❌ Error verifying document: $e');
            }

            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Profile completed successfully!'),
                backgroundColor: Colors.green,
              ),
            );
          } else {
            print('❌ Profile creation failed');
            ScaffoldMessenger.of(context).showSnackBar(
              const SnackBar(
                content: Text('Failed to create profile. Please try again.'),
                backgroundColor: Colors.red,
              ),
            );
            setState(() => _isLoading = false);
            return;
          }
        }
      } catch (e) {
        print('💥 Exception during profile creation: $e');
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(content: Text('Error: $e'), backgroundColor: Colors.red),
          );
          setState(() => _isLoading = false);
          return;
        }
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error completing profile: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  /// Test Firestore connection
  Future<void> _testFirestoreConnection() async {
    setState(() => _isLoading = true);

    try {
      print('🚀 Starting Firestore connection tests...');

      final results = await FirestoreConnectionTest.runAllTests();

      if (mounted) {
        final allPassed = results.values.every((result) => result);

        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text(
              allPassed
                  ? '✅ All Firestore tests passed!'
                  : '❌ Some Firestore tests failed. Check console.',
            ),
            backgroundColor: allPassed ? Colors.green : Colors.orange,
            duration: const Duration(seconds: 3),
          ),
        );
      }
    } catch (e) {
      print('💥 Error running Firestore tests: $e');
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error running tests: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    } finally {
      if (mounted) {
        setState(() => _isLoading = false);
      }
    }
  }

  Future<void> _signOut() async {
    try {
      await FirebaseAuth.instance.signOut();
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Error signing out: $e'),
            backgroundColor: Colors.red,
          ),
        );
      }
    }
  }

  @override
  void dispose() {
    _nameController.dispose();
    _phoneController.dispose();
    _referralCodeController.dispose();
    super.dispose();
  }
}
