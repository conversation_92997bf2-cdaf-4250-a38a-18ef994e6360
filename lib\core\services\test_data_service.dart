import 'package:flutter/foundation.dart';
import 'package:shared_preferences/shared_preferences.dart';
import 'dart:convert';
import '../utils/simple_test_data.dart';

/// Service to manage test data loading and storage
class TestDataService {
  static const String _testDataKey = 'mlm_test_data_loaded';
  static const String _usersKey = 'mlm_test_users';
  static const String _propertiesKey = 'mlm_test_properties';
  static const String _transactionsKey = 'mlm_test_transactions';
  static const String _commissionsKey = 'mlm_test_commissions';
  static const String _starsKey = 'mlm_test_stars';
  static const String _leadsKey = 'mlm_test_leads';

  /// Check if test data has been loaded
  static Future<bool> isTestDataLoaded() async {
    final prefs = await SharedPreferences.getInstance();
    return prefs.getBool(_testDataKey) ?? false;
  }

  /// Load test data into local storage
  static Future<bool> loadTestData() async {
    try {
      if (kDebugMode) {
        print('🔄 Loading test data...');
      }

      // Generate test data
      final testData = await SimpleTestData.generateAllTestData();
      
      // Store in SharedPreferences
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.setString(_usersKey, jsonEncode(testData['users']));
      await prefs.setString(_propertiesKey, jsonEncode(testData['properties']));
      await prefs.setString(_transactionsKey, jsonEncode(testData['transactions']));
      await prefs.setString(_commissionsKey, jsonEncode(testData['commissions']));
      await prefs.setString(_starsKey, jsonEncode(testData['stars']));
      await prefs.setString(_leadsKey, jsonEncode(testData['leads']));
      
      // Mark as loaded
      await prefs.setBool(_testDataKey, true);

      if (kDebugMode) {
        print('✅ Test data loaded successfully!');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error loading test data: $e');
      }
      return false;
    }
  }

  /// Get users from test data
  static Future<List<Map<String, dynamic>>> getUsers() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final usersJson = prefs.getString(_usersKey);
      
      if (usersJson != null) {
        final List<dynamic> usersList = jsonDecode(usersJson);
        return usersList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting users: $e');
      }
      return [];
    }
  }

  /// Get properties from test data
  static Future<List<Map<String, dynamic>>> getProperties() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final propertiesJson = prefs.getString(_propertiesKey);
      
      if (propertiesJson != null) {
        final List<dynamic> propertiesList = jsonDecode(propertiesJson);
        return propertiesList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting properties: $e');
      }
      return [];
    }
  }

  /// Get transactions from test data
  static Future<List<Map<String, dynamic>>> getTransactions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final transactionsJson = prefs.getString(_transactionsKey);
      
      if (transactionsJson != null) {
        final List<dynamic> transactionsList = jsonDecode(transactionsJson);
        return transactionsList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting transactions: $e');
      }
      return [];
    }
  }

  /// Get commissions from test data
  static Future<List<Map<String, dynamic>>> getCommissions() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final commissionsJson = prefs.getString(_commissionsKey);
      
      if (commissionsJson != null) {
        final List<dynamic> commissionsList = jsonDecode(commissionsJson);
        return commissionsList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting commissions: $e');
      }
      return [];
    }
  }

  /// Get stars from test data
  static Future<List<Map<String, dynamic>>> getStars() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final starsJson = prefs.getString(_starsKey);
      
      if (starsJson != null) {
        final List<dynamic> starsList = jsonDecode(starsJson);
        return starsList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting stars: $e');
      }
      return [];
    }
  }

  /// Get leads from test data
  static Future<List<Map<String, dynamic>>> getLeads() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      final leadsJson = prefs.getString(_leadsKey);
      
      if (leadsJson != null) {
        final List<dynamic> leadsList = jsonDecode(leadsJson);
        return leadsList.cast<Map<String, dynamic>>();
      }
      
      return [];
    } catch (e) {
      if (kDebugMode) {
        print('Error getting leads: $e');
      }
      return [];
    }
  }

  /// Get user by ID
  static Future<Map<String, dynamic>?> getUserById(String userId) async {
    final users = await getUsers();
    try {
      return users.firstWhere((user) => user['id'] == userId);
    } catch (e) {
      return null;
    }
  }

  /// Get user by email
  static Future<Map<String, dynamic>?> getUserByEmail(String email) async {
    final users = await getUsers();
    try {
      return users.firstWhere((user) => user['email'] == email);
    } catch (e) {
      return null;
    }
  }

  /// Get downline users for a specific user
  static Future<List<Map<String, dynamic>>> getDownlineUsers(String userId) async {
    final users = await getUsers();
    return users.where((user) => user['uplineId'] == userId).toList();
  }

  /// Get upline chain for a specific user
  static Future<List<Map<String, dynamic>>> getUplineChain(String userId) async {
    final users = await getUsers();
    final uplineChain = <Map<String, dynamic>>[];
    
    String? currentUserId = userId;
    while (currentUserId != null) {
      final user = users.where((u) => u['id'] == currentUserId).firstOrNull;
      if (user != null && user['uplineId'] != null) {
        final uplineUser = users.where((u) => u['id'] == user['uplineId']).firstOrNull;
        if (uplineUser != null) {
          uplineChain.add(uplineUser);
          currentUserId = uplineUser['uplineId'];
        } else {
          break;
        }
      } else {
        break;
      }
    }
    
    return uplineChain;
  }

  /// Get properties by agent
  static Future<List<Map<String, dynamic>>> getPropertiesByAgent(String agentId) async {
    final properties = await getProperties();
    return properties.where((property) => property['agentId'] == agentId).toList();
  }

  /// Get transactions by agent
  static Future<List<Map<String, dynamic>>> getTransactionsByAgent(String agentId) async {
    final transactions = await getTransactions();
    return transactions.where((transaction) => transaction['agentId'] == agentId).toList();
  }

  /// Get commissions by agent
  static Future<List<Map<String, dynamic>>> getCommissionsByAgent(String agentId) async {
    final commissions = await getCommissions();
    return commissions.where((commission) => commission['agentId'] == agentId).toList();
  }

  /// Get stars by agent
  static Future<List<Map<String, dynamic>>> getStarsByAgent(String agentId) async {
    final stars = await getStars();
    return stars.where((star) => star['agentId'] == agentId).toList();
  }

  /// Get leads by agent
  static Future<List<Map<String, dynamic>>> getLeadsByAgent(String agentId) async {
    final leads = await getLeads();
    return leads.where((lead) => lead['agentId'] == agentId).toList();
  }

  /// Clear all test data
  static Future<bool> clearTestData() async {
    try {
      final prefs = await SharedPreferences.getInstance();
      
      await prefs.remove(_testDataKey);
      await prefs.remove(_usersKey);
      await prefs.remove(_propertiesKey);
      await prefs.remove(_transactionsKey);
      await prefs.remove(_commissionsKey);
      await prefs.remove(_starsKey);
      await prefs.remove(_leadsKey);

      if (kDebugMode) {
        print('🗑️ Test data cleared successfully!');
      }

      return true;
    } catch (e) {
      if (kDebugMode) {
        print('❌ Error clearing test data: $e');
      }
      return false;
    }
  }

  /// Get summary statistics
  static Future<Map<String, dynamic>> getTestDataSummary() async {
    final users = await getUsers();
    final properties = await getProperties();
    final transactions = await getTransactions();
    final commissions = await getCommissions();
    final stars = await getStars();
    final leads = await getLeads();

    return {
      'totalUsers': users.length,
      'totalProperties': properties.length,
      'totalTransactions': transactions.length,
      'totalCommissions': commissions.length,
      'totalStars': stars.length,
      'totalLeads': leads.length,
      'usersByLevel': _getUsersByLevel(users),
      'propertiesByStatus': _getPropertiesByStatus(properties),
      'transactionsByStatus': _getTransactionsByStatus(transactions),
      'commissionsByStatus': _getCommissionsByStatus(commissions),
      'leadsByStatus': _getLeadsByStatus(leads),
    };
  }

  static Map<String, int> _getUsersByLevel(List<Map<String, dynamic>> users) {
    final levelCounts = <String, int>{};
    for (final user in users) {
      final level = user['level'].toString();
      levelCounts[level] = (levelCounts[level] ?? 0) + 1;
    }
    return levelCounts;
  }

  static Map<String, int> _getPropertiesByStatus(List<Map<String, dynamic>> properties) {
    final statusCounts = <String, int>{};
    for (final property in properties) {
      final status = property['status'].toString();
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }
    return statusCounts;
  }

  static Map<String, int> _getTransactionsByStatus(List<Map<String, dynamic>> transactions) {
    final statusCounts = <String, int>{};
    for (final transaction in transactions) {
      final status = transaction['status'].toString();
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }
    return statusCounts;
  }

  static Map<String, int> _getCommissionsByStatus(List<Map<String, dynamic>> commissions) {
    final statusCounts = <String, int>{};
    for (final commission in commissions) {
      final status = commission['status'].toString();
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }
    return statusCounts;
  }

  static Map<String, int> _getLeadsByStatus(List<Map<String, dynamic>> leads) {
    final statusCounts = <String, int>{};
    for (final lead in leads) {
      final status = lead['status'].toString();
      statusCounts[status] = (statusCounts[status] ?? 0) + 1;
    }
    return statusCounts;
  }
}
