import 'package:flutter/foundation.dart';
import 'package:geolocator/geolocator.dart';
import 'package:url_launcher/url_launcher.dart';

import '../../properties/models/property_model.dart';
import '../../properties/services/property_service.dart';
import '../models/location_models.dart';

/// Service for handling location-based features
class LocationService {
  static const double _defaultRadiusKm = 5.0;

  /// Check if location services are enabled
  static Future<bool> isLocationServiceEnabled() async {
    try {
      return await Geolocator.isLocationServiceEnabled();
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location service: $e');
      }
      return false;
    }
  }

  /// Check location permissions
  static Future<LocationPermission> checkPermissions() async {
    try {
      return await Geolocator.checkPermission();
    } catch (e) {
      if (kDebugMode) {
        print('Error checking location permissions: $e');
      }
      return LocationPermission.denied;
    }
  }

  /// Request location permissions
  static Future<LocationPermission> requestPermissions() async {
    try {
      return await Geolocator.requestPermission();
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting location permissions: $e');
      }
      return LocationPermission.denied;
    }
  }

  /// Get current location
  static Future<UserLocation?> getCurrentLocation() async {
    try {
      // Check if location services are enabled
      if (!await isLocationServiceEnabled()) {
        if (kDebugMode) {
          print('Location services are disabled');
        }
        return null;
      }

      // Check permissions
      LocationPermission permission = await checkPermissions();
      if (permission == LocationPermission.denied) {
        permission = await requestPermissions();
        if (permission == LocationPermission.denied) {
          if (kDebugMode) {
            print('Location permissions denied');
          }
          return null;
        }
      }

      if (permission == LocationPermission.deniedForever) {
        if (kDebugMode) {
          print('Location permissions permanently denied');
        }
        return null;
      }

      // Get current position
      final position = await Geolocator.getCurrentPosition(
        desiredAccuracy: LocationAccuracy.high,
        timeLimit: const Duration(seconds: 10),
      );

      return UserLocation(
        latitude: position.latitude,
        longitude: position.longitude,
        accuracy: position.accuracy,
        timestamp: position.timestamp ?? DateTime.now(),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error getting current location: $e');
      }
      return null;
    }
  }

  /// Find nearby properties
  static Future<List<PropertyWithDistance>> findNearbyProperties({
    required double latitude,
    required double longitude,
    double radiusKm = _defaultRadiusKm,
  }) async {
    try {
      final allProperties = await PropertyService.getAllProperties();
      final nearbyProperties = <PropertyWithDistance>[];

      for (final property in allProperties) {
        // Mock coordinates for properties if not available
        final propertyLat = property.latitude ?? _getMockLatitude(property.location);
        final propertyLng = property.longitude ?? _getMockLongitude(property.location);

        final distance = Geolocator.distanceBetween(
          latitude,
          longitude,
          propertyLat,
          propertyLng,
        ) / 1000; // Convert to kilometers

        if (distance <= radiusKm) {
          nearbyProperties.add(PropertyWithDistance(
            property: property,
            distance: distance,
          ));
        }
      }

      // Sort by distance
      nearbyProperties.sort((a, b) => a.distance.compareTo(b.distance));

      if (kDebugMode) {
        print('Found ${nearbyProperties.length} properties within ${radiusKm}km');
      }

      return nearbyProperties;
    } catch (e) {
      if (kDebugMode) {
        print('Error finding nearby properties: $e');
      }
      return [];
    }
  }

  /// Calculate distance between two points
  static double calculateDistance({
    required double lat1,
    required double lng1,
    required double lat2,
    required double lng2,
  }) {
    try {
      return Geolocator.distanceBetween(lat1, lng1, lat2, lng2) / 1000; // km
    } catch (e) {
      if (kDebugMode) {
        print('Error calculating distance: $e');
      }
      return 0.0;
    }
  }

  /// Get directions to a location
  static Future<void> openDirections({
    required double destinationLat,
    required double destinationLng,
    String? destinationName,
  }) async {
    try {
      final url = Uri.parse(
        'https://www.google.com/maps/dir/?api=1&destination=$destinationLat,$destinationLng'
      );

      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
        
        if (kDebugMode) {
          print('Opened directions to: $destinationLat, $destinationLng');
        }
      } else {
        if (kDebugMode) {
          print('Could not launch directions URL');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening directions: $e');
      }
    }
  }

  /// Open location in maps
  static Future<void> openInMaps({
    required double latitude,
    required double longitude,
    String? label,
  }) async {
    try {
      final url = Uri.parse(
        'https://www.google.com/maps/search/?api=1&query=$latitude,$longitude'
      );

      if (await canLaunchUrl(url)) {
        await launchUrl(url, mode: LaunchMode.externalApplication);
        
        if (kDebugMode) {
          print('Opened location in maps: $latitude, $longitude');
        }
      } else {
        if (kDebugMode) {
          print('Could not launch maps URL');
        }
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error opening location in maps: $e');
      }
    }
  }

  /// Get location from address (mock implementation)
  static Future<UserLocation?> getLocationFromAddress(String address) async {
    try {
      // Mock implementation - in real app, use geocoding service
      final mockLocation = _getMockLocationFromAddress(address);
      
      if (kDebugMode) {
        print('Mock location for "$address": ${mockLocation?.latitude}, ${mockLocation?.longitude}');
      }
      
      return mockLocation;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting location from address: $e');
      }
      return null;
    }
  }

  /// Get address from location (mock implementation)
  static Future<String?> getAddressFromLocation({
    required double latitude,
    required double longitude,
  }) async {
    try {
      // Mock implementation - in real app, use reverse geocoding
      final mockAddress = _getMockAddressFromLocation(latitude, longitude);
      
      if (kDebugMode) {
        print('Mock address for $latitude, $longitude: $mockAddress');
      }
      
      return mockAddress;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting address from location: $e');
      }
      return null;
    }
  }

  /// Track location changes
  static Stream<UserLocation> trackLocation() {
    return Geolocator.getPositionStream(
      locationSettings: const LocationSettings(
        accuracy: LocationAccuracy.high,
        distanceFilter: 10, // Update every 10 meters
      ),
    ).map((position) => UserLocation(
      latitude: position.latitude,
      longitude: position.longitude,
      accuracy: position.accuracy,
      timestamp: position.timestamp ?? DateTime.now(),
    ));
  }

  /// Check if point is within radius
  static bool isWithinRadius({
    required double centerLat,
    required double centerLng,
    required double pointLat,
    required double pointLng,
    required double radiusKm,
  }) {
    final distance = calculateDistance(
      lat1: centerLat,
      lng1: centerLng,
      lat2: pointLat,
      lng2: pointLng,
    );
    return distance <= radiusKm;
  }

  /// Get mock latitude for location name
  static double _getMockLatitude(String location) {
    // Mock coordinates for common locations
    final mockCoordinates = {
      'mumbai': 19.0760,
      'delhi': 28.7041,
      'bangalore': 12.9716,
      'pune': 18.5204,
      'hyderabad': 17.3850,
      'chennai': 13.0827,
      'kolkata': 22.5726,
      'ahmedabad': 23.0225,
    };

    final key = location.toLowerCase();
    for (final entry in mockCoordinates.entries) {
      if (key.contains(entry.key)) {
        return entry.value;
      }
    }

    // Default to Mumbai coordinates
    return 19.0760;
  }

  /// Get mock longitude for location name
  static double _getMockLongitude(String location) {
    // Mock coordinates for common locations
    final mockCoordinates = {
      'mumbai': 72.8777,
      'delhi': 77.1025,
      'bangalore': 77.5946,
      'pune': 73.8567,
      'hyderabad': 78.4867,
      'chennai': 80.2707,
      'kolkata': 88.3639,
      'ahmedabad': 72.5714,
    };

    final key = location.toLowerCase();
    for (final entry in mockCoordinates.entries) {
      if (key.contains(entry.key)) {
        return entry.value;
      }
    }

    // Default to Mumbai coordinates
    return 72.8777;
  }

  /// Get mock location from address
  static UserLocation? _getMockLocationFromAddress(String address) {
    final latitude = _getMockLatitude(address);
    final longitude = _getMockLongitude(address);
    
    return UserLocation(
      latitude: latitude,
      longitude: longitude,
      accuracy: 10.0,
      timestamp: DateTime.now(),
    );
  }

  /// Get mock address from location
  static String _getMockAddressFromLocation(double latitude, double longitude) {
    // Simple mock implementation
    if (latitude >= 19.0 && latitude <= 19.3 && longitude >= 72.8 && longitude <= 73.0) {
      return 'Mumbai, Maharashtra, India';
    } else if (latitude >= 28.5 && latitude <= 28.9 && longitude >= 77.0 && longitude <= 77.3) {
      return 'New Delhi, Delhi, India';
    } else if (latitude >= 12.8 && latitude <= 13.1 && longitude >= 77.5 && longitude <= 77.7) {
      return 'Bangalore, Karnataka, India';
    } else {
      return 'Unknown Location, India';
    }
  }
}
