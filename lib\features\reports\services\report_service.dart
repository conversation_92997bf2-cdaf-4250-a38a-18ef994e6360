import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';

import '../../../core/constants/app_constants.dart';
import '../../commissions/services/commission_analytics_service.dart';
import '../../mlm/services/network_service.dart';
import '../models/report_models.dart';

/// Service for generating and managing reports
class ReportService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Generate a report based on configuration
  static Future<GeneratedReport> generateReport(ReportConfig config) async {
    try {
      if (kIsWeb && kDebugMode) {
        // Development mode - return mock report
        return _generateMockReport(config);
      }

      // Create report entry
      final reportId = await _createReportEntry(config);

      // Generate report data
      final reportData = await _generateReportData(config);

      // Generate file based on format
      final filePath = await _generateReportFile(config, reportData);

      // Update report with completion
      await _updateReportStatus(
        reportId,
        ReportStatus.completed,
        filePath: filePath,
      );

      return GeneratedReport(
        id: reportId,
        config: config,
        data: reportData,
        status: ReportStatus.completed,
        generatedAt: DateTime.now(),
        filePath: filePath,
        fileSize: await _getFileSize(filePath),
      );
    } catch (e) {
      if (kDebugMode) {
        print('Error generating report: $e');
      }

      return GeneratedReport(
        id: 'error',
        config: config,
        data: const ReportData(summary: {}, dataPoints: [], charts: []),
        status: ReportStatus.failed,
        generatedAt: DateTime.now(),
        errorMessage: e.toString(),
      );
    }
  }

  /// Create report entry in database
  static Future<String> _createReportEntry(ReportConfig config) async {
    try {
      final docRef = await _firestore.collection('reports').add({
        'name': config.name,
        'type': config.type.name,
        'period': config.period.name,
        'startDate': Timestamp.fromDate(config.startDate),
        'endDate': Timestamp.fromDate(config.endDate),
        'metrics': config.metrics,
        'format': config.format.name,
        'agentId': config.agentId,
        'status': ReportStatus.generating.name,
        'createdAt': Timestamp.now(),
      });

      return docRef.id;
    } catch (e) {
      return 'temp_${DateTime.now().millisecondsSinceEpoch}';
    }
  }

  /// Generate report data based on configuration
  static Future<ReportData> _generateReportData(ReportConfig config) async {
    switch (config.type) {
      case ReportType.sales:
        return await _generateSalesReportData(config);
      case ReportType.commissions:
        return await _generateCommissionReportData(config);
      case ReportType.team:
        return await _generateTeamReportData(config);
      case ReportType.network:
        return await _generateNetworkReportData(config);
      case ReportType.performance:
        return await _generatePerformanceReportData(config);
      case ReportType.financial:
        return await _generateFinancialReportData(config);
      case ReportType.custom:
        return await _generateCustomReportData(config);
    }
  }

  /// Generate sales report data
  static Future<ReportData> _generateSalesReportData(
    ReportConfig config,
  ) async {
    try {
      // Get sales data from database
      final salesData = await _getSalesData(config);

      // Calculate summary metrics
      final summary = {
        'total_sales': salesData.fold(0.0, (sum, sale) => sum + sale['amount']),
        'properties_sold': salesData.length,
        'average_sale_value': salesData.isNotEmpty
            ? salesData.fold(0.0, (sum, sale) => sum + sale['amount']) /
                  salesData.length
            : 0.0,
        'period': config.formattedDateRange,
      };

      // Create data points
      final dataPoints = <ReportDataPoint>[
        ReportDataPoint(
          label: 'Total Sales',
          value: summary['total_sales'],
          unit: '₹',
        ),
        ReportDataPoint(
          label: 'Properties Sold',
          value: summary['properties_sold'],
          unit: 'properties',
        ),
        ReportDataPoint(
          label: 'Average Sale Value',
          value: summary['average_sale_value'],
          unit: '₹',
        ),
      ];

      // Create charts
      final charts = <ReportChart>[
        _createSalesTrendChart(salesData),
        _createSalesByTypeChart(salesData),
      ];

      return ReportData(
        summary: summary,
        dataPoints: dataPoints,
        charts: charts,
      );
    } catch (e) {
      return const ReportData(summary: {}, dataPoints: [], charts: []);
    }
  }

  /// Generate commission report data
  static Future<ReportData> _generateCommissionReportData(
    ReportConfig config,
  ) async {
    try {
      final agentId = config.agentId ?? 'default';
      final analytics = await CommissionAnalyticsService.getAgentAnalytics(
        agentId,
      );

      final summary = {
        'total_commissions': analytics.totalEarned,
        'pending_commissions': analytics.totalPending,
        'paid_commissions': analytics.totalPaid,
        'growth_rate': analytics.growthRate,
        'period': config.formattedDateRange,
      };

      final dataPoints = <ReportDataPoint>[
        ReportDataPoint(
          label: 'Total Earned',
          value: analytics.totalEarned,
          unit: '₹',
        ),
        ReportDataPoint(
          label: 'Pending Amount',
          value: analytics.totalPending,
          unit: '₹',
        ),
        ReportDataPoint(
          label: 'Growth Rate',
          value: analytics.growthRate,
          unit: '%',
        ),
      ];

      final charts = <ReportChart>[
        _createCommissionTrendChart(analytics.monthlyData),
        _createCommissionTypeChart(analytics.typeBreakdown),
      ];

      return ReportData(
        summary: summary,
        dataPoints: dataPoints,
        charts: charts,
      );
    } catch (e) {
      return const ReportData(summary: {}, dataPoints: [], charts: []);
    }
  }

  /// Generate team report data
  static Future<ReportData> _generateTeamReportData(ReportConfig config) async {
    try {
      final agentId = config.agentId ?? 'default';
      final networkStats = await NetworkService.getNetworkStatistics(agentId);

      final summary = {
        'total_members': networkStats.totalAgents,
        'active_members': networkStats.activeAgents,
        'team_sales': networkStats.totalSales,
        'activity_rate': networkStats.activityRate,
        'period': config.formattedDateRange,
      };

      final dataPoints = <ReportDataPoint>[
        ReportDataPoint(
          label: 'Team Size',
          value: networkStats.totalAgents,
          unit: 'members',
        ),
        ReportDataPoint(
          label: 'Active Members',
          value: networkStats.activeAgents,
          unit: 'members',
        ),
        ReportDataPoint(
          label: 'Activity Rate',
          value: networkStats.activityRate,
          unit: '%',
        ),
      ];

      final charts = <ReportChart>[
        _createTeamGrowthChart(networkStats),
        _createTeamPerformanceChart(networkStats.topPerformers),
      ];

      return ReportData(
        summary: summary,
        dataPoints: dataPoints,
        charts: charts,
      );
    } catch (e) {
      return const ReportData(summary: {}, dataPoints: [], charts: []);
    }
  }

  /// Generate network report data
  static Future<ReportData> _generateNetworkReportData(
    ReportConfig config,
  ) async {
    try {
      final agentId = config.agentId ?? 'default';
      final networkStats = await NetworkService.getNetworkStatistics(agentId);

      final summary = {
        'network_size': networkStats.totalAgents,
        'network_sales': networkStats.totalSales,
        'growth_rate': networkStats.networkGrowthRate,
        'average_team_size': networkStats.averageTeamSize,
        'period': config.formattedDateRange,
      };

      final dataPoints = <ReportDataPoint>[
        ReportDataPoint(
          label: 'Network Size',
          value: networkStats.totalAgents,
          unit: 'agents',
        ),
        ReportDataPoint(
          label: 'Network Sales',
          value: networkStats.totalSales,
          unit: '₹',
        ),
        ReportDataPoint(
          label: 'Growth Rate',
          value: networkStats.networkGrowthRate,
          unit: '%',
        ),
      ];

      final charts = <ReportChart>[
        _createNetworkStructureChart(networkStats.levelDistribution),
        _createNetworkPerformanceChart(networkStats),
      ];

      return ReportData(
        summary: summary,
        dataPoints: dataPoints,
        charts: charts,
      );
    } catch (e) {
      return const ReportData(summary: {}, dataPoints: [], charts: []);
    }
  }

  /// Generate performance report data
  static Future<ReportData> _generatePerformanceReportData(
    ReportConfig config,
  ) async {
    // Implementation for performance report
    return const ReportData(summary: {}, dataPoints: [], charts: []);
  }

  /// Generate financial report data
  static Future<ReportData> _generateFinancialReportData(
    ReportConfig config,
  ) async {
    // Implementation for financial report
    return const ReportData(summary: {}, dataPoints: [], charts: []);
  }

  /// Generate custom report data
  static Future<ReportData> _generateCustomReportData(
    ReportConfig config,
  ) async {
    // Implementation for custom report
    return const ReportData(summary: {}, dataPoints: [], charts: []);
  }

  /// Get sales data from database
  static Future<List<Map<String, dynamic>>> _getSalesData(
    ReportConfig config,
  ) async {
    try {
      final snapshot = await _firestore
          .collection('commissions')
          .where('type', isEqualTo: 'direct_sale')
          .where('status', isEqualTo: 'paid')
          .where(
            'paidAt',
            isGreaterThanOrEqualTo: Timestamp.fromDate(config.startDate),
          )
          .where(
            'paidAt',
            isLessThanOrEqualTo: Timestamp.fromDate(config.endDate),
          )
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data();
        return {
          'id': doc.id,
          'amount': data['metadata']?['propertyPrice'] ?? 0.0,
          'date': (data['paidAt'] as Timestamp).toDate(),
          'type': data['metadata']?['propertyType'] ?? 'unknown',
        };
      }).toList();
    } catch (e) {
      return [];
    }
  }

  /// Create sales trend chart
  static ReportChart _createSalesTrendChart(
    List<Map<String, dynamic>> salesData,
  ) {
    // Group sales by month
    final monthlyData = <String, double>{};
    for (final sale in salesData) {
      final date = sale['date'] as DateTime;
      final monthKey = '${date.year}-${date.month.toString().padLeft(2, '0')}';
      monthlyData[monthKey] = (monthlyData[monthKey] ?? 0) + sale['amount'];
    }

    final dataPoints = monthlyData.entries
        .map((entry) => ChartDataPoint(label: entry.key, value: entry.value))
        .toList();

    return ReportChart(
      id: 'sales_trend',
      title: 'Sales Trend',
      type: ChartType.line,
      series: [
        ChartDataSeries(
          name: 'Sales',
          data: dataPoints,
          color: const Color(0xFF4CAF50),
        ),
      ],
    );
  }

  /// Create sales by type chart
  static ReportChart _createSalesByTypeChart(
    List<Map<String, dynamic>> salesData,
  ) {
    final typeData = <String, double>{};
    for (final sale in salesData) {
      final type = sale['type'] as String;
      typeData[type] = (typeData[type] ?? 0) + sale['amount'];
    }

    final dataPoints = typeData.entries
        .map((entry) => ChartDataPoint(label: entry.key, value: entry.value))
        .toList();

    return ReportChart(
      id: 'sales_by_type',
      title: 'Sales by Property Type',
      type: ChartType.pie,
      series: [
        ChartDataSeries(
          name: 'Property Types',
          data: dataPoints,
          color: const Color(0xFF2196F3),
        ),
      ],
    );
  }

  /// Create commission trend chart
  static ReportChart _createCommissionTrendChart(List<dynamic> monthlyData) {
    final dataPoints = monthlyData
        .map(
          (data) => ChartDataPoint(
            label: '${data.month.month}/${data.month.year}',
            value: data.amount,
          ),
        )
        .toList();

    return ReportChart(
      id: 'commission_trend',
      title: 'Commission Trend',
      type: ChartType.area,
      series: [
        ChartDataSeries(
          name: 'Commissions',
          data: dataPoints,
          color: const Color(0xFFFF9800),
        ),
      ],
    );
  }

  /// Create commission type chart
  static ReportChart _createCommissionTypeChart(
    Map<dynamic, double> typeBreakdown,
  ) {
    final dataPoints = typeBreakdown.entries
        .map(
          (entry) =>
              ChartDataPoint(label: entry.key.toString(), value: entry.value),
        )
        .toList();

    return ReportChart(
      id: 'commission_types',
      title: 'Commission by Type',
      type: ChartType.donut,
      series: [
        ChartDataSeries(
          name: 'Commission Types',
          data: dataPoints,
          color: const Color(0xFF9C27B0),
        ),
      ],
    );
  }

  /// Create team growth chart
  static ReportChart _createTeamGrowthChart(dynamic networkStats) {
    // Mock data for team growth
    final dataPoints = [
      const ChartDataPoint(label: 'Jan', value: 5),
      const ChartDataPoint(label: 'Feb', value: 8),
      const ChartDataPoint(label: 'Mar', value: 12),
      const ChartDataPoint(label: 'Apr', value: 15),
      const ChartDataPoint(label: 'May', value: 18),
      const ChartDataPoint(label: 'Jun', value: 22),
    ];

    return ReportChart(
      id: 'team_growth',
      title: 'Team Growth Over Time',
      type: ChartType.bar,
      series: [
        ChartDataSeries(
          name: 'Team Size',
          data: dataPoints,
          color: const Color(0xFF607D8B),
        ),
      ],
    );
  }

  /// Create team performance chart
  static ReportChart _createTeamPerformanceChart(List<dynamic> topPerformers) {
    final dataPoints = topPerformers
        .take(5)
        .map(
          (performer) =>
              ChartDataPoint(label: performer.name, value: performer.sales),
        )
        .toList();

    return ReportChart(
      id: 'team_performance',
      title: 'Top Team Performers',
      type: ChartType.bar,
      series: [
        ChartDataSeries(
          name: 'Sales',
          data: dataPoints,
          color: const Color(0xFF4CAF50),
        ),
      ],
    );
  }

  /// Create network structure chart
  static ReportChart _createNetworkStructureChart(
    Map<int, int> levelDistribution,
  ) {
    final dataPoints = levelDistribution.entries
        .map(
          (entry) => ChartDataPoint(
            label: 'Level ${entry.key}',
            value: entry.value.toDouble(),
          ),
        )
        .toList();

    return ReportChart(
      id: 'network_structure',
      title: 'Network Level Distribution',
      type: ChartType.bar,
      series: [
        ChartDataSeries(
          name: 'Agents',
          data: dataPoints,
          color: const Color(0xFF3F51B5),
        ),
      ],
    );
  }

  /// Create network performance chart
  static ReportChart _createNetworkPerformanceChart(dynamic networkStats) {
    // Mock data for network performance
    final dataPoints = [
      const ChartDataPoint(label: 'Q1', value: 2500000),
      const ChartDataPoint(label: 'Q2', value: 3200000),
      const ChartDataPoint(label: 'Q3', value: 2800000),
      const ChartDataPoint(label: 'Q4', value: 3500000),
    ];

    return ReportChart(
      id: 'network_performance',
      title: 'Network Performance by Quarter',
      type: ChartType.line,
      series: [
        ChartDataSeries(
          name: 'Network Sales',
          data: dataPoints,
          color: const Color(0xFF795548),
        ),
      ],
    );
  }

  /// Generate report file
  static Future<String> _generateReportFile(
    ReportConfig config,
    ReportData data,
  ) async {
    // Mock file generation - in real implementation, this would create actual files
    final fileName =
        '${config.name}_${DateTime.now().millisecondsSinceEpoch}${config.format.extension}';
    return 'reports/$fileName';
  }

  /// Update report status
  static Future<void> _updateReportStatus(
    String reportId,
    ReportStatus status, {
    String? filePath,
  }) async {
    try {
      await _firestore.collection('reports').doc(reportId).update({
        'status': status.name,
        'updatedAt': Timestamp.now(),
        if (filePath != null) 'filePath': filePath,
      });
    } catch (e) {
      // Handle error
    }
  }

  /// Get file size
  static Future<int> _getFileSize(String filePath) async {
    // Mock file size calculation
    return 1024 * 1024; // 1MB
  }

  /// Get mock report for development
  static GeneratedReport _generateMockReport(ReportConfig config) {
    return GeneratedReport(
      id: 'mock_${DateTime.now().millisecondsSinceEpoch}',
      config: config,
      data: ReportData(
        summary: {
          'total_sales': 2500000.0,
          'properties_sold': 8,
          'average_sale_value': 312500.0,
          'period': config.formattedDateRange,
        },
        dataPoints: [
          const ReportDataPoint(
            label: 'Total Sales',
            value: 2500000.0,
            unit: '₹',
          ),
          const ReportDataPoint(
            label: 'Properties Sold',
            value: 8,
            unit: 'properties',
          ),
        ],
        charts: [
          ReportChart(
            id: 'mock_chart',
            title: 'Sales Trend',
            type: ChartType.line,
            series: [
              ChartDataSeries(
                name: 'Sales',
                data: [
                  const ChartDataPoint(label: 'Jan', value: 500000),
                  const ChartDataPoint(label: 'Feb', value: 750000),
                  const ChartDataPoint(label: 'Mar', value: 1250000),
                ],
                color: const Color(0xFF4CAF50),
              ),
            ],
          ),
        ],
      ),
      status: ReportStatus.completed,
      generatedAt: DateTime.now(),
      filePath: 'mock_report.pdf',
      fileSize: 1024 * 1024,
    );
  }

  /// Get available report templates
  static Future<List<ReportTemplate>> getReportTemplates() async {
    return [
      ReportTemplate(
        id: 'sales_monthly',
        name: 'Monthly Sales Report',
        description: 'Comprehensive monthly sales analysis',
        type: ReportType.sales,
        defaultMetrics: ['total_sales', 'properties_sold', 'monthly_trends'],
        isSystem: true,
        createdAt: DateTime.now(),
      ),
      ReportTemplate(
        id: 'commission_summary',
        name: 'Commission Summary',
        description: 'Commission earnings and breakdown',
        type: ReportType.commissions,
        defaultMetrics: [
          'total_commissions',
          'commission_by_type',
          'growth_rate',
        ],
        isSystem: true,
        createdAt: DateTime.now(),
      ),
      ReportTemplate(
        id: 'team_performance',
        name: 'Team Performance Report',
        description: 'Team member performance analysis',
        type: ReportType.team,
        defaultMetrics: ['team_size', 'team_sales', 'member_performance'],
        isSystem: true,
        createdAt: DateTime.now(),
      ),
    ];
  }

  /// Get report history
  static Future<List<GeneratedReport>> getReportHistory({
    String? agentId,
  }) async {
    try {
      Query query = _firestore.collection('reports');

      if (agentId != null) {
        query = query.where('agentId', isEqualTo: agentId);
      }

      final snapshot = await query
          .orderBy('createdAt', descending: true)
          .limit(20)
          .get();

      return snapshot.docs.map((doc) {
        final data = doc.data() as Map<String, dynamic>;

        // Create mock config for history
        final config = ReportConfig(
          id: doc.id,
          name: data['name'] ?? 'Unknown Report',
          description: '',
          type: ReportType.values.firstWhere(
            (t) => t.name == data['type'],
            orElse: () => ReportType.custom,
          ),
          period: ReportPeriod.values.firstWhere(
            (p) => p.name == data['period'],
            orElse: () => ReportPeriod.monthly,
          ),
          startDate: (data['startDate'] as Timestamp).toDate(),
          endDate: (data['endDate'] as Timestamp).toDate(),
          metrics: List<String>.from(data['metrics'] ?? []),
          format: ReportFormat.values.firstWhere(
            (f) => f.name == data['format'],
            orElse: () => ReportFormat.pdf,
          ),
          agentId: data['agentId'],
          createdAt: (data['createdAt'] as Timestamp).toDate(),
        );

        return GeneratedReport(
          id: doc.id,
          config: config,
          data: const ReportData(summary: {}, dataPoints: [], charts: []),
          status: ReportStatus.values.firstWhere(
            (s) => s.name == data['status'],
            orElse: () => ReportStatus.completed,
          ),
          generatedAt: (data['createdAt'] as Timestamp).toDate(),
          filePath: data['filePath'],
          fileSize: data['fileSize'],
        );
      }).toList();
    } catch (e) {
      return [];
    }
  }
}
