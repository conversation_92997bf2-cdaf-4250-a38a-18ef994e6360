/// Application-wide constants
class AppConstants {
  // App Information
  static const String appName = 'Rama Samriddhi';
  static const String appVersion = '1.0.0';
  static const String companyName = 'Rama Samriddhi Private Limited';

  // Firebase Collections
  static const String usersCollection = 'users';
  static const String propertiesCollection = 'properties';
  static const String mlmNetworkCollection = 'mlm_network';
  static const String transactionsCollection = 'transactions';
  static const String commissionsCollection = 'commissions';
  static const String starsCollection = 'stars';
  static const String starBonusesCollection = 'star_bonuses';
  static const String leadsCollection = 'leads';
  static const String leadInteractionsCollection = 'lead_interactions';
  static const String agentFavoritesCollection = 'agent_favorites';
  static const String agentPortfolioCollection = 'agent_portfolio';
  static const String propertySharingCollection = 'property_sharing';
  static const String notificationsCollection = 'notifications';
  static const String adminConfigCollection = 'admin_config';

  // MLM Configuration
  static const int maxMLMLevels = 5;
  static const int starBenchmark = 12;

  // Commission Rates (Default - configurable by admin)
  static const Map<int, double> defaultCommissionRates = {
    0: 0.05, // Direct sales: 5%
    1: 0.02, // Level 1: 2%
    2: 0.01, // Level 2: 1%
    3: 0.005, // Level 3: 0.5%
    4: 0.002, // Level 4: 0.2%
  };

  // User Roles
  static const String agentRole = 'agent';
  static const String adminRole = 'admin';
  static const String superAdminRole = 'super_admin';

  // Property Types
  static const List<String> propertyTypes = [
    'Residential',
    'Commercial',
    'Land',
    'Industrial',
    'Agricultural',
  ];

  // Property Status
  static const String forSale = 'for_sale';
  static const String forRent = 'for_rent';
  static const String sold = 'sold';
  static const String rented = 'rented';

  // Transaction Types
  static const String saleTransaction = 'sale';
  static const String rentalTransaction = 'rental';

  // Notification Types
  static const String newPropertyNotification = 'new_property';
  static const String commissionNotification = 'commission';
  static const String starNotification = 'star';
  static const String bonusNotification = 'bonus';
  static const String networkNotification = 'network';

  // Storage Paths
  static const String propertyImagesPath = 'property_images';
  static const String userProfilesPath = 'user_profiles';
  static const String documentsPath = 'documents';

  // Validation Constants
  static const int minPasswordLength = 8;
  static const int maxPropertyImagesCount = 10;
  static const double maxImageSizeMB = 5.0;

  // UI Constants
  static const double defaultPadding = 16.0;
  static const double smallPadding = 8.0;
  static const double largePadding = 24.0;
  static const double borderRadius = 12.0;
  static const double cardElevation = 4.0;

  // Animation Durations
  static const Duration shortAnimation = Duration(milliseconds: 200);
  static const Duration mediumAnimation = Duration(milliseconds: 400);
  static const Duration longAnimation = Duration(milliseconds: 600);

  // API Timeouts
  static const Duration apiTimeout = Duration(seconds: 30);
  static const Duration uploadTimeout = Duration(minutes: 5);

  // Pagination
  static const int defaultPageSize = 20;
  static const int maxPageSize = 100;

  // Cache Keys
  static const String userCacheKey = 'user_cache';
  static const String propertiesCacheKey = 'properties_cache';
  static const String networkCacheKey = 'network_cache';
}
