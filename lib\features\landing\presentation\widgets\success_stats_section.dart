import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../shared/themes/app_theme.dart';

/// Success statistics and company info section
class SuccessStatsSection extends StatelessWidget {
  const SuccessStatsSection({super.key});

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.symmetric(vertical: 80, horizontal: 20),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.backgroundColor,
            AppTheme.cardColor.withOpacity(0.3),
          ],
        ),
      ),
      child: Column(
        children: [
          // Section Header
          Text(
            '📈 Our Success Story',
            style: TextStyle(
              fontSize: 36,
              fontWeight: FontWeight.bold,
              color: Colors.white,
              shadows: [
                Shadow(
                  offset: const Offset(0, 2),
                  blurRadius: 4,
                  color: Colors.black.withOpacity(0.3),
                ),
              ],
            ),
            textAlign: TextAlign.center,
          )
            .animate()
            .fadeIn(duration: 800.ms)
            .slideY(begin: -0.2, end: 0),
          
          const SizedBox(height: 16),
          
          Text(
            'Building dreams, creating wealth, transforming lives',
            style: TextStyle(
              fontSize: 18,
              color: Colors.white.withOpacity(0.8),
            ),
            textAlign: TextAlign.center,
          )
            .animate(delay: 200.ms)
            .fadeIn(duration: 600.ms),
          
          const SizedBox(height: 60),
          
          // Stats Grid
          LayoutBuilder(
            builder: (context, constraints) {
              int crossAxisCount = constraints.maxWidth > 800 ? 4 : 2;
              
              return GridView.builder(
                shrinkWrap: true,
                physics: const NeverScrollableScrollPhysics(),
                gridDelegate: SliverGridDelegateWithFixedCrossAxisCount(
                  crossAxisCount: crossAxisCount,
                  crossAxisSpacing: 24,
                  mainAxisSpacing: 24,
                  childAspectRatio: 1.2,
                ),
                itemCount: _statsData.length,
                itemBuilder: (context, index) {
                  final stat = _statsData[index];
                  return _buildStatCard(stat, index);
                },
              );
            },
          ),
          
          const SizedBox(height: 60),
          
          // Company Description
          Container(
            padding: const EdgeInsets.all(32),
            decoration: BoxDecoration(
              color: AppTheme.cardColor.withOpacity(0.5),
              borderRadius: BorderRadius.circular(16),
              border: Border.all(
                color: AppTheme.primaryColor.withOpacity(0.3),
              ),
            ),
            child: Column(
              children: [
                Text(
                  'About Rama Samriddhi',
                  style: const TextStyle(
                    fontSize: 28,
                    fontWeight: FontWeight.bold,
                    color: AppTheme.primaryColor,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 20),
                
                Text(
                  'We are a leading real estate company committed to providing premium properties and exceptional investment opportunities. Our network of dedicated professionals ensures that every client receives personalized service and maximum returns on their investments.',
                  style: TextStyle(
                    fontSize: 16,
                    color: Colors.white.withOpacity(0.9),
                    height: 1.6,
                  ),
                  textAlign: TextAlign.center,
                ),
                
                const SizedBox(height: 30),
                
                // Key Features
                Wrap(
                  spacing: 20,
                  runSpacing: 16,
                  alignment: WrapAlignment.center,
                  children: [
                    _buildFeatureChip('Premium Locations'),
                    _buildFeatureChip('Verified Properties'),
                    _buildFeatureChip('Expert Guidance'),
                    _buildFeatureChip('Guaranteed Returns'),
                    _buildFeatureChip('24/7 Support'),
                  ],
                ),
              ],
            ),
          )
            .animate(delay: 800.ms)
            .fadeIn(duration: 800.ms)
            .slideY(begin: 0.3, end: 0),
        ],
      ),
    );
  }

  Widget _buildStatCard(Map<String, dynamic> stat, int index) {
    return Container(
      padding: const EdgeInsets.all(24),
      decoration: BoxDecoration(
        color: AppTheme.cardColor,
        borderRadius: BorderRadius.circular(16),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.2),
        ),
        boxShadow: [
          BoxShadow(
            color: Colors.black.withOpacity(0.3),
            blurRadius: 8,
            offset: const Offset(0, 4),
          ),
        ],
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(
            stat['icon'],
            color: AppTheme.primaryColor,
            size: 48,
          ),
          
          const SizedBox(height: 16),
          
          Text(
            stat['number'],
            style: const TextStyle(
              fontSize: 32,
              fontWeight: FontWeight.bold,
              color: Colors.white,
            ),
          ),
          
          const SizedBox(height: 8),
          
          Text(
            stat['label'],
            style: TextStyle(
              fontSize: 16,
              color: Colors.white.withOpacity(0.8),
              fontWeight: FontWeight.w500,
            ),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    )
      .animate(delay: (400 + index * 150).ms)
      .fadeIn(duration: 600.ms)
      .scale(begin: const Offset(0.8, 0.8));
  }

  Widget _buildFeatureChip(String label) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
      decoration: BoxDecoration(
        color: AppTheme.primaryColor.withOpacity(0.2),
        borderRadius: BorderRadius.circular(20),
        border: Border.all(
          color: AppTheme.primaryColor.withOpacity(0.5),
        ),
      ),
      child: Text(
        label,
        style: const TextStyle(
          color: AppTheme.primaryColor,
          fontWeight: FontWeight.w600,
        ),
      ),
    );
  }

  // Sample stats data
  static const List<Map<String, dynamic>> _statsData = [
    {
      'icon': Icons.home_work,
      'number': '500+',
      'label': 'Premium Properties',
    },
    {
      'icon': Icons.people,
      'number': '1000+',
      'label': 'Happy Clients',
    },
    {
      'icon': Icons.trending_up,
      'number': '₹50Cr+',
      'label': 'Total Sales',
    },
    {
      'icon': Icons.location_city,
      'number': '25+',
      'label': 'Prime Locations',
    },
  ];
}
