import '../models/user_model.dart';
import '../models/admin_analytics_model.dart';
import 'test_data_service.dart';
import 'auth_service.dart';
import 'admin_analytics_service.dart';

/// Service that intelligently switches between test data and Firebase data
class HybridDataService {
  /// Get all users - uses test data if available, otherwise Firebase
  static Future<List<UserModel>> getAllUsers() async {
    try {
      final isTestDataLoaded = await TestDataService.isTestDataLoaded();

      if (isTestDataLoaded) {
        // Convert test data to UserModel objects
        final testUsers = await TestDataService.getUsers();
        return testUsers
            .map((userData) => UserModel.fromMap(userData))
            .toList();
      } else {
        // Use Firebase
        return await AuthService.getAllUsers();
      }
    } catch (e) {
      // If Firebase fails, try test data as fallback
      try {
        final isTestDataLoaded = await TestDataService.isTestDataLoaded();
        if (isTestDataLoaded) {
          final testUsers = await TestDataService.getUsers();
          return testUsers
              .map((userData) => UserModel.fromMap(userData))
              .toList();
        }
      } catch (_) {
        // Ignore test data errors
      }

      // Return empty list if both fail
      print('HybridDataService: Failed to get users from both sources: $e');
      return [];
    }
  }

  /// Generate analytics - uses test data if available, otherwise Firebase
  static Future<AdminAnalyticsModel> generateAnalytics() async {
    try {
      final isTestDataLoaded = await TestDataService.isTestDataLoaded();

      if (isTestDataLoaded) {
        // Generate analytics from test data
        return await _generateTestDataAnalytics();
      } else {
        // Use Firebase
        return await AdminAnalyticsService.generateAnalytics();
      }
    } catch (e) {
      // If Firebase fails, try test data as fallback
      try {
        final isTestDataLoaded = await TestDataService.isTestDataLoaded();
        if (isTestDataLoaded) {
          return await _generateTestDataAnalytics();
        }
      } catch (_) {
        // Ignore test data errors
      }

      // Throw helpful error message
      throw Exception(
        'Analytics require admin privileges. Check Users tab for test data.',
      );
    }
  }

  /// Generate simple analytics from test data
  static Future<AdminAnalyticsModel> _generateTestDataAnalytics() async {
    final users = await TestDataService.getUsers();
    final properties = await TestDataService.getProperties();
    final transactions = await TestDataService.getTransactions();
    final commissions = await TestDataService.getCommissions();
    final stars = await TestDataService.getStars();

    // Create basic system overview
    final systemOverview = SystemOverview(
      totalUsers: users.length,
      activeUsers: users.where((u) => u['isActive'] == true).length,
      totalProperties: properties.length,
      approvedProperties: properties
          .where((p) => p['status'] == 'available' || p['status'] == 'sold')
          .length,
      totalCommissionsPaid: commissions
          .where((c) => c['status'] == 'paid')
          .fold<double>(0.0, (sum, c) => sum + (c['amount'] as num).toDouble()),
      totalStarsAwarded: stars.length,
      totalTransactions: transactions.length,
      systemRevenue: transactions
          .where((t) => t['status'] == 'completed')
          .fold<double>(
            0.0,
            (sum, t) => sum + (t['saleAmount'] as num).toDouble(),
          ),
      userGrowthTrend: _generateMockGrowthTrend(),
      revenueGrowthTrend: _generateMockRevenueTrend(),
    );

    // Create basic user analytics
    final userAnalytics = UserAnalytics(
      usersByLevel: _groupUsersByLevel(users),
      usersByState: {},
      userRegistrationTrend: {},
      topPerformers: [],
      activeThisMonth: 0,
      newRegistrationsThisMonth: 0,
      averageStarsPerUser: 0,
      averageCommissionPerUser: 0,
    );

    // Create basic property analytics
    final propertyAnalytics = PropertyAnalytics(
      propertiesByType: _groupPropertiesByType(properties),
      propertiesByState: {},
      propertiesByStatus: {},
      averagePriceByType: {},
      propertyAdditionTrend: {},
      pendingApprovals: 0,
      featuredProperties: 0,
      averagePropertyPrice: 0,
      propertiesWithLeads: 0,
    );

    // Create basic commission analytics
    final commissionAnalytics = CommissionAnalytics(
      commissionsByMonth: {},
      commissionsByLevel: {},
      commissionsByAgent: {},
      totalPaidCommissions: 0,
      totalPendingCommissions: 0,
      averageCommissionPerTransaction: 0,
      totalTransactions: 0,
      transactionTrend: {},
    );

    // Create basic star analytics
    final starAnalytics = StarAnalytics(
      starsByMonth: {},
      starDistribution: {},
      topStarEarners: [],
      usersNearingBonus: 0,
      bonusesAwarded: 0,
      totalBonusAmount: 0,
      starSourceDistribution: {},
    );

    // Create basic performance metrics
    final performanceMetrics = PerformanceMetrics(
      conversionRate: 0,
      averageResponseTime: 0,
      customerSatisfactionScore: 0,
      activeLeads: 0,
      convertedLeads: 0,
      performanceTrends: {},
      systemEfficiency: 0,
    );

    return AdminAnalyticsModel(
      systemOverview: systemOverview,
      userAnalytics: userAnalytics,
      propertyAnalytics: propertyAnalytics,
      commissionAnalytics: commissionAnalytics,
      starAnalytics: starAnalytics,
      performanceMetrics: performanceMetrics,
      generatedAt: DateTime.now(),
    );
  }

  // Helper methods for generating mock data
  static Map<String, int> _generateMockGrowthTrend() {
    return {
      'Jan': 5,
      'Feb': 8,
      'Mar': 12,
      'Apr': 15,
      'May': 18,
      'Jun': 22,
      'Jul': 25,
      'Aug': 28,
      'Sep': 32,
      'Oct': 35,
      'Nov': 38,
      'Dec': 43,
    };
  }

  static Map<String, double> _generateMockRevenueTrend() {
    return {
      'Jan': 500000,
      'Feb': 750000,
      'Mar': 1200000,
      'Apr': 1500000,
      'May': 1800000,
      'Jun': 2200000,
      'Jul': 2500000,
      'Aug': 2800000,
      'Sep': 3200000,
      'Oct': 3500000,
      'Nov': 3800000,
      'Dec': 4300000,
    };
  }

  static Map<int, int> _groupUsersByLevel(List<Map<String, dynamic>> users) {
    final result = <int, int>{};
    for (final user in users) {
      final level = user['level'] as int;
      result[level] = (result[level] ?? 0) + 1;
    }
    return result;
  }

  static Map<String, int> _groupUsersByRegion(
    List<Map<String, dynamic>> users,
  ) {
    final result = <String, int>{};
    for (final user in users) {
      final address = user['address'] as String? ?? '';
      final region = address.split(',').last.trim();
      if (region.isNotEmpty) {
        result[region] = (result[region] ?? 0) + 1;
      }
    }
    return result;
  }

  static List<TopPerformer> _getTopPerformers(
    List<Map<String, dynamic>> users,
    List<Map<String, dynamic>> stars,
    List<Map<String, dynamic>> commissions,
  ) {
    // Simple implementation - return top 5 users by total stars
    final userStarCounts = <String, int>{};
    for (final star in stars) {
      final agentId = star['agentId'] as String;
      userStarCounts[agentId] = (userStarCounts[agentId] ?? 0) + 1;
    }

    return userStarCounts.entries.map((entry) {
        final user = users.firstWhere(
          (u) => u['id'] == entry.key,
          orElse: () => {},
        );
        return TopPerformer(
          name: user['name'] ?? 'Unknown',
          totalStars: entry.value,
          totalCommissions: commissions
              .where((c) => c['agentId'] == entry.key && c['status'] == 'paid')
              .fold<double>(
                0.0,
                (sum, c) => sum + (c['amount'] as num).toDouble(),
              ),
          totalSales: 0, // Mock value
        );
      }).toList()
      ..sort((a, b) => b.totalStars.compareTo(a.totalStars))
      ..take(5);
  }

  static List<RecentRegistration> _getRecentRegistrations(
    List<Map<String, dynamic>> users,
  ) {
    return users
        .map(
          (user) => RecentRegistration(
            name: user['name'] as String,
            email: user['email'] as String,
            joinDate: DateTime.parse(user['joinedAt'] as String),
            level: user['level'] as int,
          ),
        )
        .toList()
      ..sort((a, b) => b.joinDate.compareTo(a.joinDate))
      ..take(10);
  }

  static Map<String, int> _groupPropertiesByType(
    List<Map<String, dynamic>> properties,
  ) {
    final result = <String, int>{};
    for (final property in properties) {
      final type = property['type'] as String;
      result[type] = (result[type] ?? 0) + 1;
    }
    return result;
  }

  static Map<String, int> _groupPropertiesByRegion(
    List<Map<String, dynamic>> properties,
  ) {
    final result = <String, int>{};
    for (final property in properties) {
      final location = property['location'] as String;
      result[location] = (result[location] ?? 0) + 1;
    }
    return result;
  }

  static List<RecentListing> _getRecentListings(
    List<Map<String, dynamic>> properties,
  ) {
    return properties
        .map(
          (property) => RecentListing(
            title: property['title'] as String,
            price: (property['price'] as num).toDouble(),
            location: property['location'] as String,
            listedDate: DateTime.parse(property['listedAt'] as String),
            status: property['status'] as String,
          ),
        )
        .toList()
      ..sort((a, b) => b.listedDate.compareTo(a.listedDate))
      ..take(10);
  }

  static Map<String, double> _groupCommissionsByLevel(
    List<Map<String, dynamic>> commissions,
    List<Map<String, dynamic>> users,
  ) {
    final result = <String, double>{};
    for (final commission in commissions) {
      final agentId = commission['agentId'] as String;
      final user = users.firstWhere(
        (u) => u['id'] == agentId,
        orElse: () => {},
      );
      if (user.isNotEmpty) {
        final level = 'Level ${user['level']}';
        result[level] =
            (result[level] ?? 0.0) + (commission['amount'] as num).toDouble();
      }
    }
    return result;
  }

  static Map<String, double> _generateMockCommissionTrend() {
    return {
      'Jan': 50000,
      'Feb': 75000,
      'Mar': 120000,
      'Apr': 150000,
      'May': 180000,
      'Jun': 220000,
      'Jul': 250000,
      'Aug': 280000,
      'Sep': 320000,
      'Oct': 350000,
      'Nov': 380000,
      'Dec': 430000,
    };
  }

  static List<TopEarner> _getTopEarners(
    List<Map<String, dynamic>> users,
    List<Map<String, dynamic>> commissions,
  ) {
    final userCommissions = <String, double>{};
    for (final commission in commissions) {
      if (commission['status'] == 'paid') {
        final agentId = commission['agentId'] as String;
        userCommissions[agentId] =
            (userCommissions[agentId] ?? 0.0) +
            (commission['amount'] as num).toDouble();
      }
    }

    return userCommissions.entries.map((entry) {
        final user = users.firstWhere(
          (u) => u['id'] == entry.key,
          orElse: () => {},
        );
        return TopEarner(
          name: user['name'] ?? 'Unknown',
          totalCommissions: entry.value,
          level: user['level'] ?? 0,
        );
      }).toList()
      ..sort((a, b) => b.totalCommissions.compareTo(a.totalCommissions))
      ..take(10);
  }

  static Map<String, int> _groupStarsByCategory(
    List<Map<String, dynamic>> stars,
  ) {
    final result = <String, int>{};
    for (final star in stars) {
      final category = star['category'] as String;
      result[category] = (result[category] ?? 0) + 1;
    }
    return result;
  }

  static List<TopStarEarner> _getTopStarEarners(
    List<Map<String, dynamic>> users,
    List<Map<String, dynamic>> stars,
  ) {
    final userStarCounts = <String, int>{};
    for (final star in stars) {
      final agentId = star['agentId'] as String;
      userStarCounts[agentId] = (userStarCounts[agentId] ?? 0) + 1;
    }

    return userStarCounts.entries.map((entry) {
        final user = users.firstWhere(
          (u) => u['id'] == entry.key,
          orElse: () => {},
        );
        return TopStarEarner(
          name: user['name'] ?? 'Unknown',
          totalStars: entry.value,
          level: user['level'] ?? 0,
        );
      }).toList()
      ..sort((a, b) => b.totalStars.compareTo(a.totalStars))
      ..take(10);
  }

  static List<RecentAchievement> _getRecentAchievements(
    List<Map<String, dynamic>> stars,
  ) {
    return stars
        .map(
          (star) => RecentAchievement(
            agentName: 'Agent ${star['agentId']}',
            category: star['category'] as String,
            description: star['description'] as String,
            earnedDate: DateTime.parse(star['earnedAt'] as String),
          ),
        )
        .toList()
      ..sort((a, b) => b.earnedDate.compareTo(a.earnedDate))
      ..take(10);
  }
}
