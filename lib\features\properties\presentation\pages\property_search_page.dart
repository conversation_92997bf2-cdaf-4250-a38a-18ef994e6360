import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/property_providers.dart';
import '../widgets/property_card.dart';
import '../widgets/search_widgets.dart';
import '../../../../core/models/property_model.dart';

/// Dedicated property search page with advanced features
class PropertySearchPage extends ConsumerStatefulWidget {
  final String? initialQuery;

  const PropertySearchPage({
    super.key,
    this.initialQuery,
  });

  @override
  ConsumerState<PropertySearchPage> createState() => _PropertySearchPageState();
}

class _PropertySearchPageState extends ConsumerState<PropertySearchPage> {
  @override
  void initState() {
    super.initState();
    if (widget.initialQuery != null && widget.initialQuery!.isNotEmpty) {
      WidgetsBinding.instance.addPostFrameCallback((_) {
        ref.read(propertySearchProvider.notifier).searchProperties(widget.initialQuery!);
      });
    }
  }

  @override
  Widget build(BuildContext context) {
    final searchState = ref.watch(propertySearchProvider);
    final recentSearches = ref.watch(recentSearchesProvider);
    final popularSearches = ref.watch(popularSearchesProvider);
    final propertyStats = ref.watch(propertyStatsProvider);

    return Scaffold(
      appBar: AppBar(
        title: const Text('Search Properties'),
        elevation: 0,
      ),
      body: Column(
        children: [
          // Enhanced Search Bar
          Container(
            padding: const EdgeInsets.all(16),
            decoration: BoxDecoration(
              color: Theme.of(context).primaryColor.withValues(alpha: 0.05),
            ),
            child: PropertySearchBar(
              onPropertySelected: (property) => _showPropertyDetails(context, property),
              hintText: 'Search by location, type, price, amenities...',
              showFilters: true,
            ),
          ),

          // Search Content
          Expanded(
            child: _buildSearchContent(searchState, recentSearches, popularSearches, propertyStats),
          ),
        ],
      ),
    );
  }

  Widget _buildSearchContent(
    PropertySearchState searchState,
    List<String> recentSearches,
    AsyncValue<List<String>> popularSearches,
    AsyncValue<PropertyStats> propertyStats,
  ) {
    // Show search results if query exists
    if (searchState.query.isNotEmpty) {
      return _buildSearchResults(searchState);
    }

    // Show search suggestions and popular searches
    return _buildSearchSuggestions(recentSearches, popularSearches, propertyStats);
  }

  Widget _buildSearchResults(PropertySearchState searchState) {
    if (searchState.isLoading) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            CircularProgressIndicator(),
            SizedBox(height: 16),
            Text('Searching properties...'),
          ],
        ),
      );
    }

    if (searchState.results.isEmpty) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(
              Icons.search_off,
              size: 64,
              color: Colors.grey[400],
            ),
            const SizedBox(height: 16),
            Text(
              'No properties found for "${searchState.query}"',
              style: Theme.of(context).textTheme.titleMedium,
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 8),
            Text(
              'Try different keywords or adjust your filters',
              style: Theme.of(context).textTheme.bodyMedium?.copyWith(
                color: Colors.grey[600],
              ),
              textAlign: TextAlign.center,
            ),
            const SizedBox(height: 24),
            ElevatedButton.icon(
              onPressed: () => _showSearchSuggestions(),
              icon: const Icon(Icons.lightbulb_outline),
              label: const Text('Get Search Suggestions'),
            ),
          ],
        ),
      );
    }

    return Column(
      children: [
        // Results Header
        Container(
          padding: const EdgeInsets.all(16),
          child: Row(
            children: [
              Text(
                '${searchState.results.length} properties found',
                style: Theme.of(context).textTheme.titleMedium?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              TextButton.icon(
                onPressed: () => _showSortOptions(),
                icon: const Icon(Icons.sort),
                label: const Text('Sort'),
              ),
            ],
          ),
        ),

        // Results List
        Expanded(
          child: ListView.builder(
            padding: const EdgeInsets.symmetric(horizontal: 16),
            itemCount: searchState.results.length,
            itemBuilder: (context, index) {
              final property = searchState.results[index];
              return Padding(
                padding: const EdgeInsets.only(bottom: 16),
                child: PropertyCard(
                  property: property,
                  onTap: () => _showPropertyDetails(context, property),
                ),
              );
            },
          ),
        ),
      ],
    );
  }

  Widget _buildSearchSuggestions(
    List<String> recentSearches,
    AsyncValue<List<String>> popularSearches,
    AsyncValue<PropertyStats> propertyStats,
  ) {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Property Statistics
          propertyStats.when(
            data: (stats) => _buildPropertyStatsCard(stats),
            loading: () => const Card(child: LinearProgressIndicator()),
            error: (_, __) => const SizedBox.shrink(),
          ),

          const SizedBox(height: 24),

          // Recent Searches
          if (recentSearches.isNotEmpty) ...[
            _buildSectionHeader('Recent Searches', Icons.history),
            const SizedBox(height: 12),
            _buildSearchChips(recentSearches, onClear: () {
              ref.read(recentSearchesProvider.notifier).clearSearches();
            }),
            const SizedBox(height: 24),
          ],

          // Popular Searches
          popularSearches.when(
            data: (searches) => Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                _buildSectionHeader('Popular Searches', Icons.trending_up),
                const SizedBox(height: 12),
                _buildSearchChips(searches),
              ],
            ),
            loading: () => const SizedBox.shrink(),
            error: (_, __) => const SizedBox.shrink(),
          ),

          const SizedBox(height: 24),

          // Quick Filters
          _buildSectionHeader('Quick Filters', Icons.filter_list),
          const SizedBox(height: 12),
          _buildQuickFilters(),
        ],
      ),
    );
  }

  Widget _buildPropertyStatsCard(PropertyStats stats) {
    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Property Overview',
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
              ),
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildStatItem(
                    'Total Properties',
                    '${stats.totalProperties}',
                    Icons.home_work,
                    Colors.blue,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Average Price',
                    _formatPrice(stats.averagePrice),
                    Icons.currency_rupee,
                    Colors.green,
                  ),
                ),
                Expanded(
                  child: _buildStatItem(
                    'Featured',
                    '${stats.featuredProperties}',
                    Icons.star,
                    Colors.amber,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(String label, String value, IconData icon, Color color) {
    return Column(
      children: [
        Icon(icon, color: color, size: 24),
        const SizedBox(height: 8),
        Text(
          value,
          style: TextStyle(
            fontWeight: FontWeight.bold,
            color: color,
            fontSize: 16,
          ),
        ),
        Text(
          label,
          style: const TextStyle(fontSize: 12),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildSectionHeader(String title, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 20, color: Colors.grey[600]),
        const SizedBox(width: 8),
        Text(
          title,
          style: Theme.of(context).textTheme.titleMedium?.copyWith(
            fontWeight: FontWeight.bold,
          ),
        ),
      ],
    );
  }

  Widget _buildSearchChips(List<String> searches, {VoidCallback? onClear}) {
    return Column(
      children: [
        Wrap(
          spacing: 8,
          runSpacing: 8,
          children: searches.map((search) => ActionChip(
            label: Text(search),
            onPressed: () => _performSearch(search),
            avatar: const Icon(Icons.search, size: 16),
          )).toList(),
        ),
        if (onClear != null) ...[
          const SizedBox(height: 8),
          Align(
            alignment: Alignment.centerRight,
            child: TextButton(
              onPressed: onClear,
              child: const Text('Clear All'),
            ),
          ),
        ],
      ],
    );
  }

  Widget _buildQuickFilters() {
    return Wrap(
      spacing: 8,
      runSpacing: 8,
      children: [
        _buildFilterChip('Residential', Icons.home),
        _buildFilterChip('Commercial', Icons.business),
        _buildFilterChip('Under 1 Cr', Icons.currency_rupee),
        _buildFilterChip('Mumbai', Icons.location_city),
        _buildFilterChip('Featured', Icons.star),
        _buildFilterChip('New Listings', Icons.new_releases),
      ],
    );
  }

  Widget _buildFilterChip(String label, IconData icon) {
    return ActionChip(
      label: Text(label),
      avatar: Icon(icon, size: 16),
      onPressed: () => _performSearch(label),
    );
  }

  void _performSearch(String query) {
    ref.read(recentSearchesProvider.notifier).addSearch(query);
    ref.read(propertySearchProvider.notifier).searchProperties(query);
  }

  void _showPropertyDetails(BuildContext context, PropertyModel property) {
    // TODO: Navigate to property details page
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text(property.title),
        content: Text('Property details for ${property.title}'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showSearchSuggestions() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Suggestions'),
        content: const Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Try searching for:'),
            SizedBox(height: 8),
            Text('• Location: "Mumbai", "Bangalore", "Delhi"'),
            Text('• Type: "Residential", "Commercial", "Land"'),
            Text('• Price: "Under 1 crore", "50 lakh"'),
            Text('• Features: "3 BHK", "Swimming pool", "Parking"'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Got it'),
          ),
        ],
      ),
    );
  }

  void _showSortOptions() {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            const Text(
              'Sort Properties',
              style: TextStyle(fontSize: 18, fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            ListTile(
              leading: const Icon(Icons.access_time),
              title: const Text('Newest First'),
              onTap: () => _applySorting('createdAt', false),
            ),
            ListTile(
              leading: const Icon(Icons.currency_rupee),
              title: const Text('Price: Low to High'),
              onTap: () => _applySorting('price', true),
            ),
            ListTile(
              leading: const Icon(Icons.currency_rupee),
              title: const Text('Price: High to Low'),
              onTap: () => _applySorting('price', false),
            ),
            ListTile(
              leading: const Icon(Icons.location_city),
              title: const Text('City A-Z'),
              onTap: () => _applySorting('city', true),
            ),
          ],
        ),
      ),
    );
  }

  void _applySorting(String sortBy, bool ascending) {
    final currentFilter = ref.read(propertyFilterProvider);
    ref.read(propertyFilterProvider.notifier).updateFilter(
      currentFilter.copyWith(
        sortBy: sortBy,
        sortAscending: ascending,
      ),
    );
    Navigator.of(context).pop();
  }

  String _formatPrice(double price) {
    if (price >= 10000000) {
      return '₹${(price / 10000000).toStringAsFixed(1)} Cr';
    } else if (price >= 100000) {
      return '₹${(price / 100000).toStringAsFixed(1)} L';
    } else if (price >= 1000) {
      return '₹${(price / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${price.toStringAsFixed(0)}';
    }
  }
}
