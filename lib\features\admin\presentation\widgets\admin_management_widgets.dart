import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/admin_providers.dart';
import '../../../../core/models/user_model.dart';

/// User management widget for admin
class UserManagementWidget extends ConsumerStatefulWidget {
  const UserManagementWidget({super.key});

  @override
  ConsumerState<UserManagementWidget> createState() =>
      _UserManagementWidgetState();
}

class _UserManagementWidgetState extends ConsumerState<UserManagementWidget> {
  final TextEditingController _searchController = TextEditingController();

  @override
  void initState() {
    super.initState();
    // Load users when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(userManagementProvider.notifier).loadUsers();
    });
  }

  @override
  void dispose() {
    _searchController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final userManagementState = ref.watch(userManagementProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                'User Management',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                icon: const Icon(Icons.refresh),
                onPressed: () =>
                    ref.read(userManagementProvider.notifier).loadUsers(),
                tooltip: 'Refresh Users',
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Search and filters
          Row(
            children: [
              Expanded(
                child: TextField(
                  controller: _searchController,
                  decoration: const InputDecoration(
                    hintText: 'Search users by name, email, or phone...',
                    prefixIcon: Icon(Icons.search),
                    border: OutlineInputBorder(),
                  ),
                  onChanged: (query) {
                    final currentFilter = userManagementState.filter;
                    ref
                        .read(userManagementProvider.notifier)
                        .updateFilter(
                          currentFilter.copyWith(
                            searchQuery: query.isEmpty ? null : query,
                          ),
                        );
                  },
                ),
              ),
              const SizedBox(width: 16),
              _buildFilterDropdown(context, userManagementState),
            ],
          ),

          const SizedBox(height: 16),

          // User stats
          _buildUserStats(context, userManagementState),

          const SizedBox(height: 16),

          // Users list
          Expanded(child: _buildUsersList(context, userManagementState)),
        ],
      ),
    );
  }

  Widget _buildFilterDropdown(BuildContext context, UserManagementState state) {
    return PopupMenuButton<String>(
      icon: const Icon(Icons.filter_list),
      tooltip: 'Filter Users',
      onSelected: (value) {
        final currentFilter = state.filter;
        UserFilter newFilter;

        switch (value) {
          case 'all':
            newFilter = currentFilter.copyWith(status: null, role: null);
            break;
          case 'active':
            newFilter = currentFilter.copyWith(status: 'active');
            break;
          case 'inactive':
            newFilter = currentFilter.copyWith(status: 'inactive');
            break;
          case 'agents':
            newFilter = currentFilter.copyWith(role: 'agent');
            break;
          case 'admins':
            newFilter = currentFilter.copyWith(role: 'admin');
            break;
          default:
            newFilter = currentFilter;
        }

        ref.read(userManagementProvider.notifier).updateFilter(newFilter);
      },
      itemBuilder: (context) => [
        const PopupMenuItem(value: 'all', child: Text('All Users')),
        const PopupMenuItem(value: 'active', child: Text('Active Only')),
        const PopupMenuItem(value: 'inactive', child: Text('Inactive Only')),
        const PopupMenuItem(value: 'agents', child: Text('Agents Only')),
        const PopupMenuItem(value: 'admins', child: Text('Admins Only')),
      ],
    );
  }

  Widget _buildUserStats(BuildContext context, UserManagementState state) {
    final totalUsers = state.users.length;
    final activeUsers = state.users.where((u) => u.isActive).length;
    final agents = state.users.where((u) => u.role == 'agent').length;
    final admins = state.users.where((u) => u.role == 'admin').length;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard(
            context,
            'Total Users',
            '$totalUsers',
            Icons.people,
            Colors.blue,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            'Active',
            '$activeUsers',
            Icons.check_circle,
            Colors.green,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            'Agents',
            '$agents',
            Icons.person,
            Colors.orange,
          ),
        ),
        const SizedBox(width: 12),
        Expanded(
          child: _buildStatCard(
            context,
            'Admins',
            '$admins',
            Icons.admin_panel_settings,
            Colors.purple,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(
    BuildContext context,
    String title,
    String value,
    IconData icon,
    Color color,
  ) {
    return Card(
      elevation: 1,
      child: Padding(
        padding: const EdgeInsets.all(12),
        child: Column(
          children: [
            Icon(icon, color: color, size: 20),
            const SizedBox(height: 4),
            Text(
              value,
              style: Theme.of(context).textTheme.titleMedium?.copyWith(
                fontWeight: FontWeight.bold,
                color: color,
              ),
            ),
            Text(
              title,
              style: Theme.of(
                context,
              ).textTheme.bodySmall?.copyWith(color: Colors.grey[600]),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildUsersList(BuildContext context, UserManagementState state) {
    if (state.isLoading) {
      return const Center(child: CircularProgressIndicator());
    }

    if (state.error != null) {
      return Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error: ${state.error}'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () =>
                  ref.read(userManagementProvider.notifier).loadUsers(),
              child: const Text('Retry'),
            ),
          ],
        ),
      );
    }

    if (state.filteredUsers.isEmpty) {
      return const Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Icon(Icons.people_outline, size: 64, color: Colors.grey),
            SizedBox(height: 16),
            Text('No users found'),
            Text('Try adjusting your search or filters'),
          ],
        ),
      );
    }

    return ListView.builder(
      itemCount: state.filteredUsers.length,
      itemBuilder: (context, index) {
        final user = state.filteredUsers[index];
        return UserCard(
          user: user,
          onToggleStatus: () => ref
              .read(userManagementProvider.notifier)
              .toggleUserStatus(user.id),
          onUpdateRole: (newRole) => ref
              .read(userManagementProvider.notifier)
              .updateUserRole(user.id, newRole),
        );
      },
    );
  }
}

/// User card widget for displaying user information
class UserCard extends StatelessWidget {
  final UserModel user;
  final VoidCallback onToggleStatus;
  final Function(String) onUpdateRole;

  const UserCard({
    super.key,
    required this.user,
    required this.onToggleStatus,
    required this.onUpdateRole,
  });

  @override
  Widget build(BuildContext context) {
    return Card(
      margin: const EdgeInsets.symmetric(vertical: 4),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            // Header row
            Row(
              children: [
                CircleAvatar(
                  radius: 20,
                  backgroundColor: user.isActive
                      ? Colors.green.withValues(alpha: 0.1)
                      : Colors.grey.withValues(alpha: 0.1),
                  child: Text(
                    user.name.isNotEmpty ? user.name[0].toUpperCase() : 'U',
                    style: TextStyle(
                      fontWeight: FontWeight.bold,
                      color: user.isActive ? Colors.green : Colors.grey,
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        user.name,
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      Text(
                        user.email,
                        style: Theme.of(context).textTheme.bodySmall?.copyWith(
                          color: Colors.grey[600],
                        ),
                      ),
                    ],
                  ),
                ),

                // Status badge
                Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 8,
                    vertical: 4,
                  ),
                  decoration: BoxDecoration(
                    color: user.isActive
                        ? Colors.green.withValues(alpha: 0.1)
                        : Colors.red.withValues(alpha: 0.1),
                    borderRadius: BorderRadius.circular(12),
                  ),
                  child: Text(
                    user.isActive ? 'Active' : 'Inactive',
                    style: TextStyle(
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                      color: user.isActive ? Colors.green : Colors.red,
                    ),
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // User details
            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'Phone',
                    user.phoneNumber,
                    Icons.phone,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Role',
                    user.role.toUpperCase(),
                    Icons.badge,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Level',
                    'L${user.level}',
                    Icons.layers,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 8),

            Row(
              children: [
                Expanded(
                  child: _buildDetailItem(
                    'Stars',
                    '${user.totalStars}',
                    Icons.star,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Commissions',
                    _formatAmount(user.totalCommissions),
                    Icons.currency_rupee,
                  ),
                ),
                Expanded(
                  child: _buildDetailItem(
                    'Network',
                    '${user.downlineIds.length}',
                    Icons.people,
                  ),
                ),
              ],
            ),

            const SizedBox(height: 12),

            // Action buttons
            Row(
              children: [
                Expanded(
                  child: OutlinedButton.icon(
                    onPressed: onToggleStatus,
                    icon: Icon(
                      user.isActive ? Icons.block : Icons.check_circle,
                    ),
                    label: Text(user.isActive ? 'Deactivate' : 'Activate'),
                    style: OutlinedButton.styleFrom(
                      foregroundColor: user.isActive
                          ? Colors.red
                          : Colors.green,
                    ),
                  ),
                ),

                const SizedBox(width: 12),

                Expanded(
                  child: ElevatedButton.icon(
                    onPressed: () => _showRoleUpdateDialog(context),
                    icon: const Icon(Icons.edit),
                    label: const Text('Update Role'),
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailItem(String label, String value, IconData icon) {
    return Row(
      children: [
        Icon(icon, size: 16, color: Colors.grey[600]),
        const SizedBox(width: 4),
        Expanded(
          child: Column(
            crossAxisAlignment: CrossAxisAlignment.start,
            children: [
              Text(
                label,
                style: const TextStyle(
                  fontSize: 10,
                  color: Colors.grey,
                  fontWeight: FontWeight.w500,
                ),
              ),
              Text(
                value,
                style: const TextStyle(
                  fontSize: 12,
                  fontWeight: FontWeight.w500,
                ),
              ),
            ],
          ),
        ),
      ],
    );
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(1)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(1)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(1)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }

  void _showRoleUpdateDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: Text('Update Role: ${user.name}'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            ListTile(
              title: const Text('Agent'),
              leading: Radio<String>(
                value: 'agent',
                groupValue: user.role,
                onChanged: (value) {
                  if (value != null) {
                    onUpdateRole(value);
                    Navigator.of(context).pop();
                  }
                },
              ),
            ),
            ListTile(
              title: const Text('Admin'),
              leading: Radio<String>(
                value: 'admin',
                groupValue: user.role,
                onChanged: (value) {
                  if (value != null) {
                    onUpdateRole(value);
                    Navigator.of(context).pop();
                  }
                },
              ),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
        ],
      ),
    );
  }
}

/// System configuration widget
class SystemConfigurationWidget extends ConsumerStatefulWidget {
  const SystemConfigurationWidget({super.key});

  @override
  ConsumerState<SystemConfigurationWidget> createState() =>
      _SystemConfigurationWidgetState();
}

class _SystemConfigurationWidgetState
    extends ConsumerState<SystemConfigurationWidget> {
  @override
  void initState() {
    super.initState();
    // Load configuration when widget initializes
    WidgetsBinding.instance.addPostFrameCallback((_) {
      ref.read(systemConfigProvider.notifier).loadConfig();
    });
  }

  @override
  Widget build(BuildContext context) {
    final configState = ref.watch(systemConfigProvider);

    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                'System Configuration',
                style: Theme.of(context).textTheme.headlineSmall?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              ElevatedButton.icon(
                onPressed: configState.isLoading
                    ? null
                    : () => _saveConfiguration(context),
                icon: configState.isLoading
                    ? const SizedBox(
                        width: 16,
                        height: 16,
                        child: CircularProgressIndicator(strokeWidth: 2),
                      )
                    : const Icon(Icons.save),
                label: const Text('Save Changes'),
              ),
            ],
          ),

          const SizedBox(height: 24),

          Expanded(
            child: SingleChildScrollView(
              child: Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  // Commission rates configuration
                  _buildCommissionRatesSection(context, configState),

                  const SizedBox(height: 24),

                  // Star bonus configuration
                  _buildStarBonusSection(context, configState),

                  const SizedBox(height: 24),

                  // Feature flags
                  _buildFeatureFlagsSection(context, configState),

                  const SizedBox(height: 24),

                  // System information
                  _buildSystemInfoSection(context),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildCommissionRatesSection(
    BuildContext context,
    SystemConfigState state,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'MLM Commission Rates',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 8),

            Text(
              'Configure commission percentages for each MLM level',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),

            const SizedBox(height: 16),

            ...List.generate(5, (index) {
              final level = index;
              final rate = state.commissionRates[level] ?? 0.0;

              return Padding(
                padding: const EdgeInsets.symmetric(vertical: 8),
                child: Row(
                  children: [
                    Container(
                      width: 40,
                      height: 40,
                      decoration: BoxDecoration(
                        color: _getLevelColor(level).withValues(alpha: 0.1),
                        borderRadius: BorderRadius.circular(8),
                      ),
                      child: Center(
                        child: Text(
                          'L$level',
                          style: TextStyle(
                            fontWeight: FontWeight.bold,
                            color: _getLevelColor(level),
                          ),
                        ),
                      ),
                    ),

                    const SizedBox(width: 16),

                    Expanded(child: Text('Level $level Commission')),

                    SizedBox(
                      width: 100,
                      child: TextFormField(
                        initialValue: (rate * 100).toStringAsFixed(1),
                        decoration: const InputDecoration(
                          suffixText: '%',
                          border: OutlineInputBorder(),
                          contentPadding: EdgeInsets.symmetric(
                            horizontal: 12,
                            vertical: 8,
                          ),
                        ),
                        keyboardType: TextInputType.number,
                        onChanged: (value) {
                          final percentage = double.tryParse(value) ?? 0.0;
                          ref
                              .read(systemConfigProvider.notifier)
                              .updateCommissionRate(level, percentage / 100);
                        },
                      ),
                    ),
                  ],
                ),
              );
            }),
          ],
        ),
      ),
    );
  }

  Widget _buildStarBonusSection(BuildContext context, SystemConfigState state) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              '12-Star Bonus Configuration',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 8),

            Text(
              'Bonus amount awarded when users reach 12 stars',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),

            const SizedBox(height: 16),

            Row(
              children: [
                const Icon(Icons.star, color: Colors.amber, size: 32),

                const SizedBox(width: 16),

                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      const Text(
                        '12-Star Bonus Amount',
                        style: TextStyle(fontWeight: FontWeight.w600),
                      ),
                      Text(
                        'One-time bonus for achieving 12 stars',
                        style: TextStyle(color: Colors.grey[600], fontSize: 12),
                      ),
                    ],
                  ),
                ),

                SizedBox(
                  width: 150,
                  child: TextFormField(
                    initialValue: state.starBonusAmount.toStringAsFixed(0),
                    decoration: const InputDecoration(
                      prefixText: '₹ ',
                      border: OutlineInputBorder(),
                      contentPadding: EdgeInsets.symmetric(
                        horizontal: 12,
                        vertical: 8,
                      ),
                    ),
                    keyboardType: TextInputType.number,
                    onChanged: (value) {
                      final amount = double.tryParse(value) ?? 0.0;
                      ref
                          .read(systemConfigProvider.notifier)
                          .updateStarBonusAmount(amount);
                    },
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureFlagsSection(
    BuildContext context,
    SystemConfigState state,
  ) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Feature Flags',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 8),

            Text(
              'Enable or disable system features',
              style: Theme.of(
                context,
              ).textTheme.bodyMedium?.copyWith(color: Colors.grey[600]),
            ),

            const SizedBox(height: 16),

            ...state.featureFlags.entries.map(
              (entry) => _buildFeatureFlagItem(context, entry.key, entry.value),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildFeatureFlagItem(
    BuildContext context,
    String feature,
    bool enabled,
  ) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 8),
      child: Row(
        children: [
          Switch(
            value: enabled,
            onChanged: (value) {
              ref
                  .read(systemConfigProvider.notifier)
                  .toggleFeatureFlag(feature, value);
            },
          ),

          const SizedBox(width: 16),

          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  _getFeatureFlagDisplayName(feature),
                  style: const TextStyle(fontWeight: FontWeight.w500),
                ),
                Text(
                  _getFeatureFlagDescription(feature),
                  style: TextStyle(color: Colors.grey[600], fontSize: 12),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildSystemInfoSection(BuildContext context) {
    return Card(
      elevation: 2,
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'System Information',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),

            const SizedBox(height: 16),

            _buildInfoRow('Application Version', '1.0.0'),
            _buildInfoRow('Database Status', 'Connected'),
            _buildInfoRow('Last Backup', '2 hours ago'),
            _buildInfoRow('System Uptime', '99.9%'),
          ],
        ),
      ),
    );
  }

  Widget _buildInfoRow(String label, String value) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Expanded(
            child: Text(
              label,
              style: const TextStyle(fontWeight: FontWeight.w500),
            ),
          ),
          Text(value, style: TextStyle(color: Colors.grey[600])),
        ],
      ),
    );
  }

  Color _getLevelColor(int level) {
    switch (level) {
      case 0:
        return Colors.purple;
      case 1:
        return Colors.blue;
      case 2:
        return Colors.green;
      case 3:
        return Colors.orange;
      case 4:
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _getFeatureFlagDisplayName(String feature) {
    switch (feature) {
      case 'property_approval_required':
        return 'Property Approval Required';
      case 'auto_commission_distribution':
        return 'Auto Commission Distribution';
      case 'star_bonus_enabled':
        return 'Star Bonus System';
      case 'lead_tracking_enabled':
        return 'Lead Tracking';
      default:
        return feature.replaceAll('_', ' ').toUpperCase();
    }
  }

  String _getFeatureFlagDescription(String feature) {
    switch (feature) {
      case 'property_approval_required':
        return 'Require admin approval for new properties';
      case 'auto_commission_distribution':
        return 'Automatically distribute commissions';
      case 'star_bonus_enabled':
        return 'Enable 12-star bonus system';
      case 'lead_tracking_enabled':
        return 'Enable lead tracking and management';
      default:
        return 'System feature configuration';
    }
  }

  Future<void> _saveConfiguration(BuildContext context) async {
    // TODO: Get current admin user ID
    const adminId = 'current_admin_id';

    await ref.read(systemConfigProvider.notifier).saveConfig(adminId);

    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Configuration saved successfully!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }
}
