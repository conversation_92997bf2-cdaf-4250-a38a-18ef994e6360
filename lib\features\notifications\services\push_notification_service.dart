import 'package:flutter/foundation.dart';
import 'package:flutter_local_notifications/flutter_local_notifications.dart';
import 'package:firebase_messaging/firebase_messaging.dart';
import 'package:shared_preferences/shared_preferences.dart';

/// Service for handling push notifications
class PushNotificationService {
  static final FlutterLocalNotificationsPlugin _localNotifications = 
      FlutterLocalNotificationsPlugin();
  static final FirebaseMessaging _messaging = FirebaseMessaging.instance;
  
  static bool _isInitialized = false;
  static String? _fcmToken;

  /// Initialize push notification service
  static Future<void> initialize() async {
    if (_isInitialized) return;

    try {
      // Initialize local notifications
      await _initializeLocalNotifications();
      
      // Initialize Firebase messaging (mock for now)
      await _initializeFirebaseMessaging();
      
      _isInitialized = true;
      
      if (kDebugMode) {
        print('PushNotificationService initialized successfully');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing PushNotificationService: $e');
      }
    }
  }

  /// Initialize local notifications
  static Future<void> _initializeLocalNotifications() async {
    const androidSettings = AndroidInitializationSettings('@mipmap/ic_launcher');
    const iosSettings = DarwinInitializationSettings(
      requestAlertPermission: true,
      requestBadgePermission: true,
      requestSoundPermission: true,
    );
    
    const initializationSettings = InitializationSettings(
      android: androidSettings,
      iOS: iosSettings,
    );

    await _localNotifications.initialize(
      initializationSettings,
      onDidReceiveNotificationResponse: _onNotificationTapped,
    );

    if (kDebugMode) {
      print('Local notifications initialized');
    }
  }

  /// Initialize Firebase messaging (mock implementation)
  static Future<void> _initializeFirebaseMessaging() async {
    try {
      // Mock implementation - in real app, this would initialize Firebase Messaging
      if (kDebugMode) {
        print('Firebase messaging initialized (mock)');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error initializing Firebase messaging: $e');
      }
    }
  }

  /// Request notification permissions
  static Future<bool> requestPermissions() async {
    try {
      // Request local notification permissions
      final androidPermission = await _localNotifications
          .resolvePlatformSpecificImplementation<
              AndroidFlutterLocalNotificationsPlugin>()
          ?.requestNotificationsPermission();

      if (kDebugMode) {
        print('Notification permissions requested: $androidPermission');
      }

      return androidPermission ?? true;
    } catch (e) {
      if (kDebugMode) {
        print('Error requesting notification permissions: $e');
      }
      return false;
    }
  }

  /// Show local notification
  static Future<void> showLocalNotification({
    required String title,
    required String body,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
    String? imageUrl,
  }) async {
    try {
      final androidDetails = AndroidNotificationDetails(
        'mlm_channel',
        'MLM Notifications',
        channelDescription: 'Notifications for MLM app',
        importance: _getAndroidImportance(priority),
        priority: _getAndroidPriority(priority),
        showWhen: true,
        icon: '@mipmap/ic_launcher',
        largeIcon: imageUrl != null 
            ? FilePathAndroidBitmap(imageUrl)
            : null,
      );
      
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      await _localNotifications.show(
        DateTime.now().millisecondsSinceEpoch ~/ 1000,
        title,
        body,
        notificationDetails,
        payload: payload,
      );

      if (kDebugMode) {
        print('Local notification shown: $title');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error showing local notification: $e');
      }
    }
  }

  /// Schedule notification
  static Future<void> scheduleNotification({
    required String title,
    required String body,
    required DateTime scheduledTime,
    String? payload,
    NotificationPriority priority = NotificationPriority.normal,
  }) async {
    try {
      final androidDetails = AndroidNotificationDetails(
        'scheduled_channel',
        'Scheduled Notifications',
        channelDescription: 'Scheduled notifications for MLM app',
        importance: _getAndroidImportance(priority),
        priority: _getAndroidPriority(priority),
      );
      
      const iosDetails = DarwinNotificationDetails(
        presentAlert: true,
        presentBadge: true,
        presentSound: true,
      );

      final notificationDetails = NotificationDetails(
        android: androidDetails,
        iOS: iosDetails,
      );

      // Mock implementation - in real app, use timezone package
      if (kDebugMode) {
        print('Notification scheduled for: $scheduledTime');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error scheduling notification: $e');
      }
    }
  }

  /// Show commission notification
  static Future<void> showCommissionNotification({
    required String agentName,
    required double amount,
    required String type,
  }) async {
    await showLocalNotification(
      title: 'New Commission Earned! 💰',
      body: '$agentName earned ₹${amount.toStringAsFixed(0)} from $type',
      payload: 'commission:new',
      priority: NotificationPriority.high,
    );
  }

  /// Show goal achievement notification
  static Future<void> showGoalAchievementNotification({
    required String goalTitle,
    required String agentName,
  }) async {
    await showLocalNotification(
      title: 'Goal Achieved! 🎉',
      body: '$agentName completed: $goalTitle',
      payload: 'goal:achieved',
      priority: NotificationPriority.high,
    );
  }

  /// Show new property notification
  static Future<void> showNewPropertyNotification({
    required String propertyTitle,
    required String location,
    required double price,
  }) async {
    await showLocalNotification(
      title: 'New Property Listed! 🏠',
      body: '$propertyTitle in $location - ₹${price.toStringAsFixed(0)}',
      payload: 'property:new',
      priority: NotificationPriority.normal,
    );
  }

  /// Show network update notification
  static Future<void> showNetworkUpdateNotification({
    required String message,
    required String agentName,
  }) async {
    await showLocalNotification(
      title: 'Network Update 👥',
      body: '$agentName: $message',
      payload: 'network:update',
      priority: NotificationPriority.normal,
    );
  }

  /// Show message notification
  static Future<void> showMessageNotification({
    required String senderName,
    required String message,
    required String chatId,
  }) async {
    await showLocalNotification(
      title: 'New Message from $senderName 💬',
      body: message,
      payload: 'message:$chatId',
      priority: NotificationPriority.high,
    );
  }

  /// Cancel notification
  static Future<void> cancelNotification(int id) async {
    try {
      await _localNotifications.cancel(id);
      
      if (kDebugMode) {
        print('Notification cancelled: $id');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cancelling notification: $e');
      }
    }
  }

  /// Cancel all notifications
  static Future<void> cancelAllNotifications() async {
    try {
      await _localNotifications.cancelAll();
      
      if (kDebugMode) {
        print('All notifications cancelled');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error cancelling all notifications: $e');
      }
    }
  }

  /// Get FCM token (mock implementation)
  static Future<String?> getFCMToken() async {
    try {
      // Mock implementation - in real app, get from Firebase
      _fcmToken = 'mock_fcm_token_${DateTime.now().millisecondsSinceEpoch}';
      
      if (kDebugMode) {
        print('FCM Token: $_fcmToken');
      }
      
      return _fcmToken;
    } catch (e) {
      if (kDebugMode) {
        print('Error getting FCM token: $e');
      }
      return null;
    }
  }

  /// Subscribe to topic
  static Future<void> subscribeToTopic(String topic) async {
    try {
      // Mock implementation - in real app, subscribe to Firebase topic
      if (kDebugMode) {
        print('Subscribed to topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error subscribing to topic: $e');
      }
    }
  }

  /// Unsubscribe from topic
  static Future<void> unsubscribeFromTopic(String topic) async {
    try {
      // Mock implementation - in real app, unsubscribe from Firebase topic
      if (kDebugMode) {
        print('Unsubscribed from topic: $topic');
      }
    } catch (e) {
      if (kDebugMode) {
        print('Error unsubscribing from topic: $e');
      }
    }
  }

  /// Handle notification tap
  static void _onNotificationTapped(NotificationResponse response) {
    final payload = response.payload;
    if (payload != null) {
      _processNotificationAction(payload);
    }
  }

  /// Process notification action
  static void _processNotificationAction(String payload) {
    final parts = payload.split(':');
    final actionType = parts[0];
    final actionData = parts.length > 1 ? parts[1] : null;

    if (kDebugMode) {
      print('Processing notification action: $actionType with data: $actionData');
    }

    switch (actionType) {
      case 'commission':
        // Navigate to commissions page
        break;
      case 'goal':
        // Navigate to goals page
        break;
      case 'property':
        // Navigate to property details
        break;
      case 'network':
        // Navigate to network page
        break;
      case 'message':
        // Navigate to chat
        break;
    }
  }

  /// Get Android importance level
  static Importance _getAndroidImportance(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Importance.low;
      case NotificationPriority.normal:
        return Importance.defaultImportance;
      case NotificationPriority.high:
        return Importance.high;
      case NotificationPriority.urgent:
        return Importance.max;
    }
  }

  /// Get Android priority level
  static Priority _getAndroidPriority(NotificationPriority priority) {
    switch (priority) {
      case NotificationPriority.low:
        return Priority.low;
      case NotificationPriority.normal:
        return Priority.defaultPriority;
      case NotificationPriority.high:
        return Priority.high;
      case NotificationPriority.urgent:
        return Priority.max;
    }
  }
}

/// Notification priority levels
enum NotificationPriority {
  low,
  normal,
  high,
  urgent,
}
