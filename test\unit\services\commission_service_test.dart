import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:rama_realty_mlm/core/services/commission_service.dart';
import 'package:rama_realty_mlm/core/models/transaction_model.dart';
import 'package:rama_realty_mlm/core/models/user_model.dart';
import 'package:rama_realty_mlm/core/models/commission_model.dart';

// Generate mocks
@GenerateMocks([
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
  QuerySnapshot,
  QueryDocumentSnapshot,
])
import 'commission_service_test.mocks.dart';

void main() {
  group('CommissionService Tests', () {
    late MockFirebaseFirestore mockFirestore;
    late MockCollectionReference mockCollection;
    late MockDocumentReference mockDocument;
    late MockDocumentSnapshot mockSnapshot;
    late MockQuerySnapshot mockQuerySnapshot;

    setUp(() {
      mockFirestore = MockFirebaseFirestore();
      mockCollection = MockCollectionReference();
      mockDocument = MockDocumentReference();
      mockSnapshot = MockDocumentSnapshot();
      mockQuerySnapshot = MockQuerySnapshot();
    });

    group('Commission Calculation', () {
      test('should calculate correct commission for direct sale', () {
        // Arrange
        const saleAmount = 1000000.0; // ₹10 Lakh
        const level = 0; // Direct sale
        const expectedCommission = 50000.0; // 5% of ₹10 Lakh

        // Act
        final commission = CommissionService.calculateCommission(saleAmount, level);

        // Assert
        expect(commission, expectedCommission);
      });

      test('should calculate correct commission for level 1', () {
        // Arrange
        const saleAmount = 2000000.0; // ₹20 Lakh
        const level = 1; // Level 1 upline
        const expectedCommission = 40000.0; // 2% of ₹20 Lakh

        // Act
        final commission = CommissionService.calculateCommission(saleAmount, level);

        // Assert
        expect(commission, expectedCommission);
      });

      test('should calculate correct commission for level 2', () {
        // Arrange
        const saleAmount = 5000000.0; // ₹50 Lakh
        const level = 2; // Level 2 upline
        const expectedCommission = 50000.0; // 1% of ₹50 Lakh

        // Act
        final commission = CommissionService.calculateCommission(saleAmount, level);

        // Assert
        expect(commission, expectedCommission);
      });

      test('should calculate correct commission for level 3', () {
        // Arrange
        const saleAmount = 10000000.0; // ₹1 Crore
        const level = 3; // Level 3 upline
        const expectedCommission = 50000.0; // 0.5% of ₹1 Crore

        // Act
        final commission = CommissionService.calculateCommission(saleAmount, level);

        // Assert
        expect(commission, expectedCommission);
      });

      test('should calculate correct commission for level 4', () {
        // Arrange
        const saleAmount = 15000000.0; // ₹1.5 Crore
        const level = 4; // Level 4 upline
        const expectedCommission = 30000.0; // 0.2% of ₹1.5 Crore

        // Act
        final commission = CommissionService.calculateCommission(saleAmount, level);

        // Assert
        expect(commission, expectedCommission);
      });

      test('should return zero commission for invalid level', () {
        // Arrange
        const saleAmount = 1000000.0;
        const level = 10; // Invalid level

        // Act
        final commission = CommissionService.calculateCommission(saleAmount, level);

        // Assert
        expect(commission, 0.0);
      });

      test('should return zero commission for zero sale amount', () {
        // Arrange
        const saleAmount = 0.0;
        const level = 0;

        // Act
        final commission = CommissionService.calculateCommission(saleAmount, level);

        // Assert
        expect(commission, 0.0);
      });

      test('should return zero commission for negative sale amount', () {
        // Arrange
        const saleAmount = -1000000.0;
        const level = 0;

        // Act
        final commission = CommissionService.calculateCommission(saleAmount, level);

        // Assert
        expect(commission, 0.0);
      });
    });

    group('Transaction Creation', () {
      test('should create transaction successfully', () async {
        // Arrange
        final agent = UserModel(
          id: 'agent-id',
          name: 'Test Agent',
          email: '<EMAIL>',
          phoneNumber: '+919876543210',
          role: 'agent',
          level: 0,
          uplineId: 'upline-id',
          downlineIds: [],
          totalStars: 5,
          totalCommissions: 25000.0,
          isActive: true,
          isAdmin: false,
          referralCode: 'AGENT123',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        const propertyId = 'property-id';
        const saleAmount = 1000000.0;
        const type = 'sale';

        // Mock Firestore operations
        when(mockFirestore.collection('transactions')).thenReturn(mockCollection);
        when(mockCollection.add(any)).thenAnswer((_) async {
          when(mockDocument.id).thenReturn('transaction-id');
          return mockDocument;
        });
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Mock property update
        when(mockFirestore.collection('properties')).thenReturn(mockCollection);
        when(mockCollection.doc(propertyId)).thenReturn(mockDocument);
        when(mockDocument.update(any)).thenAnswer((_) async => {});

        // Mock commission distribution
        when(mockFirestore.collection('commissions')).thenReturn(mockCollection);
        when(mockCollection.add(any)).thenAnswer((_) async => mockDocument);

        // Mock star service
        when(mockFirestore.collection('stars')).thenReturn(mockCollection);
        when(mockCollection.add(any)).thenAnswer((_) async => mockDocument);

        // Act
        final result = await CommissionService.createTransaction(
          agent: agent,
          propertyId: propertyId,
          saleAmount: saleAmount,
          type: type,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.transaction?.agentId, agent.id);
        expect(result.transaction?.propertyId, propertyId);
        expect(result.transaction?.amount, saleAmount);
        expect(result.transaction?.type, type);

        // Verify Firestore operations
        verify(mockFirestore.collection('transactions')).called(1);
        verify(mockCollection.add(any)).called(greaterThan(0));
      });

      test('should handle transaction creation failure', () async {
        // Arrange
        final agent = UserModel(
          id: 'agent-id',
          name: 'Test Agent',
          email: '<EMAIL>',
          phoneNumber: '+919876543210',
          role: 'agent',
          level: 0,
          uplineId: null,
          downlineIds: [],
          totalStars: 0,
          totalCommissions: 0.0,
          isActive: true,
          isAdmin: false,
          referralCode: 'AGENT123',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Mock Firestore failure
        when(mockFirestore.collection('transactions')).thenReturn(mockCollection);
        when(mockCollection.add(any)).thenThrow(Exception('Firestore error'));

        // Act
        final result = await CommissionService.createTransaction(
          agent: agent,
          propertyId: 'property-id',
          saleAmount: 1000000.0,
          type: 'sale',
        );

        // Assert
        expect(result.isSuccess, false);
        expect(result.message, contains('Failed to create transaction'));
      });
    });

    group('Commission Distribution', () {
      test('should distribute commissions through MLM hierarchy', () async {
        // Arrange
        final sellingAgent = UserModel(
          id: 'selling-agent',
          name: 'Selling Agent',
          email: '<EMAIL>',
          phoneNumber: '+919876543210',
          role: 'agent',
          level: 4,
          uplineId: 'level3-agent',
          downlineIds: [],
          totalStars: 2,
          totalCommissions: 10000.0,
          isActive: true,
          isAdmin: false,
          referralCode: 'SELL123',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        final transaction = TransactionModel(
          id: 'transaction-id',
          agentId: sellingAgent.id,
          agentName: sellingAgent.name,
          propertyId: 'property-id',
          propertyTitle: 'Test Property',
          amount: 1000000.0,
          type: 'sale',
          status: 'completed',
          createdAt: DateTime.now(),
          updatedAt: DateTime.now(),
        );

        // Mock upline hierarchy
        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        
        // Mock level 3 agent
        when(mockCollection.doc('level3-agent')).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async {
          when(mockSnapshot.exists).thenReturn(true);
          when(mockSnapshot.data()).thenReturn({
            'id': 'level3-agent',
            'name': 'Level 3 Agent',
            'uplineId': 'level2-agent',
            'level': 3,
            'isActive': true,
          });
          return mockSnapshot;
        });

        // Mock level 2 agent
        when(mockCollection.doc('level2-agent')).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async {
          when(mockSnapshot.exists).thenReturn(true);
          when(mockSnapshot.data()).thenReturn({
            'id': 'level2-agent',
            'name': 'Level 2 Agent',
            'uplineId': 'level1-agent',
            'level': 2,
            'isActive': true,
          });
          return mockSnapshot;
        });

        // Mock commission creation
        when(mockFirestore.collection('commissions')).thenReturn(mockCollection);
        when(mockCollection.add(any)).thenAnswer((_) async => mockDocument);

        // Act
        await CommissionService.distributeCommissions(transaction, sellingAgent);

        // Assert
        // Verify that commissions were created for the selling agent and uplines
        verify(mockCollection.add(any)).called(greaterThan(0));
      });
    });

    group('Commission Statistics', () {
      test('should calculate commission statistics correctly', () async {
        // Arrange
        const agentId = 'agent-id';
        
        // Mock commission query
        when(mockFirestore.collection('commissions')).thenReturn(mockCollection);
        when(mockCollection.where('agentId', isEqualTo: agentId)).thenReturn(mockCollection);
        when(mockCollection.get()).thenAnswer((_) async {
          final mockDocs = [
            createMockCommissionDoc('comm1', 5000.0, true),
            createMockCommissionDoc('comm2', 3000.0, true),
            createMockCommissionDoc('comm3', 2000.0, false),
          ];
          when(mockQuerySnapshot.docs).thenReturn(mockDocs);
          return mockQuerySnapshot;
        });

        // Act
        final stats = await CommissionService.getCommissionStatistics(agentId);

        // Assert
        expect(stats['totalCommissions'], 10000.0); // 5000 + 3000 + 2000
        expect(stats['paidCommissions'], 8000.0); // 5000 + 3000
        expect(stats['pendingCommissions'], 2000.0); // 2000
        expect(stats['commissionCount'], 3);
      });
    });
  });
}

// Helper function to create mock commission documents
MockQueryDocumentSnapshot createMockCommissionDoc(String id, double amount, bool isPaid) {
  final mockDoc = MockQueryDocumentSnapshot();
  when(mockDoc.id).thenReturn(id);
  when(mockDoc.data()).thenReturn({
    'id': id,
    'amount': amount,
    'isPaid': isPaid,
    'agentId': 'agent-id',
    'agentName': 'Test Agent',
    'transactionId': 'transaction-id',
    'level': 0,
    'createdAt': Timestamp.now(),
    'updatedAt': Timestamp.now(),
  });
  return mockDoc;
}

class MockQueryDocumentSnapshot extends Mock implements QueryDocumentSnapshot<Map<String, dynamic>> {}
