import 'dart:typed_data';
import 'dart:html' as html;
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../services/cloudinary_service.dart';

/// Web-compatible image upload widget using HTML file input
class WebImageUploadWidget extends StatefulWidget {
  final String? currentImageUrl;
  final Function(String imageUrl)? onImageUploaded;
  final Function()? onImageRemoved;
  final double width;
  final double height;
  final String? placeholder;
  final bool allowRemove;
  final String? folder;

  const WebImageUploadWidget({
    super.key,
    this.currentImageUrl,
    this.onImageUploaded,
    this.onImageRemoved,
    this.width = 200,
    this.height = 150,
    this.placeholder = 'Tap to upload image',
    this.allowRemove = true,
    this.folder,
  });

  @override
  State<WebImageUploadWidget> createState() => _WebImageUploadWidgetState();
}

class _WebImageUploadWidgetState extends State<WebImageUploadWidget> {
  bool _isUploading = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[600]!,
          style: BorderStyle.solid,
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // Image display or placeholder
          ClipRRect(
            borderRadius: BorderRadius.circular(12),
            child: widget.currentImageUrl != null
                ? CachedNetworkImage(
                    imageUrl: widget.currentImageUrl!,
                    width: widget.width,
                    height: widget.height,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: Colors.grey[800],
                      child: const Center(
                        child: CircularProgressIndicator(),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: Colors.grey[800],
                      child: const Center(
                        child: Icon(Icons.error, color: Colors.red),
                      ),
                    ),
                  )
                : Container(
                    width: widget.width,
                    height: widget.height,
                    color: Colors.grey[800],
                    child: Center(
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.add_photo_alternate,
                            size: 48,
                            color: Colors.grey[400],
                          ),
                          const SizedBox(height: 8),
                          Text(
                            widget.placeholder ?? 'Tap to upload',
                            style: TextStyle(
                              color: Colors.grey[400],
                              fontSize: 12,
                            ),
                            textAlign: TextAlign.center,
                          ),
                        ],
                      ),
                    ),
                  ),
          ),

          // Upload overlay
          if (_isUploading)
            Container(
              width: widget.width,
              height: widget.height,
              decoration: BoxDecoration(
                color: Colors.black54,
                borderRadius: BorderRadius.circular(12),
              ),
              child: const Center(
                child: Column(
                  mainAxisAlignment: MainAxisAlignment.center,
                  children: [
                    CircularProgressIndicator(color: Colors.white),
                    SizedBox(height: 8),
                    Text(
                      'Uploading...',
                      style: TextStyle(color: Colors.white, fontSize: 12),
                    ),
                  ],
                ),
              ),
            ),

          // Tap to upload
          Positioned.fill(
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                borderRadius: BorderRadius.circular(12),
                onTap: _isUploading ? null : _pickAndUploadImage,
              ),
            ),
          ),

          // Remove button
          if (widget.currentImageUrl != null && widget.allowRemove && !_isUploading)
            Positioned(
              top: 8,
              right: 8,
              child: Container(
                decoration: const BoxDecoration(
                  color: Colors.red,
                  shape: BoxShape.circle,
                ),
                child: IconButton(
                  icon: const Icon(Icons.close, color: Colors.white, size: 16),
                  onPressed: () {
                    widget.onImageRemoved?.call();
                  },
                  padding: const EdgeInsets.all(4),
                  constraints: const BoxConstraints(
                    minWidth: 24,
                    minHeight: 24,
                  ),
                ),
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _pickAndUploadImage() async {
    if (!CloudinaryService.isConfigured) {
      _showError('Cloudinary service is not configured. Please check your credentials.');
      return;
    }

    try {
      if (kDebugMode) {
        print('🔄 WebImageUpload: Starting image selection...');
      }

      // Create HTML file input
      final html.FileUploadInputElement uploadInput = html.FileUploadInputElement();
      uploadInput.accept = 'image/*';
      uploadInput.multiple = false;

      // Trigger file selection
      uploadInput.click();

      // Wait for file selection
      await uploadInput.onChange.first;

      if (uploadInput.files?.isEmpty ?? true) {
        if (kDebugMode) {
          print('📝 WebImageUpload: No file selected');
        }
        return;
      }

      final html.File file = uploadInput.files!.first;
      
      if (kDebugMode) {
        print('📄 WebImageUpload: File selected: ${file.name}');
        print('📊 WebImageUpload: File size: ${file.size} bytes');
      }

      // Check file size (max 10MB for free tier)
      if (file.size > 10 * 1024 * 1024) {
        _showError('Image too large. Please select an image under 10MB.');
        return;
      }

      // Check file type
      if (!file.type.startsWith('image/')) {
        _showError('Please select a valid image file.');
        return;
      }

      setState(() => _isUploading = true);

      // Read file as bytes
      final html.FileReader reader = html.FileReader();
      reader.readAsArrayBuffer(file);
      await reader.onLoad.first;

      final Uint8List bytes = Uint8List.fromList(reader.result as List<int>);

      if (kDebugMode) {
        print('📊 WebImageUpload: File read successfully, ${bytes.length} bytes');
      }

      // Upload to Cloudinary
      final imageUrl = await CloudinaryService.uploadImage(
        imageBytes: bytes,
        fileName: file.name,
        folder: widget.folder,
        tags: {'app': 'rama_samriddhi_mlm'},
      );

      if (imageUrl != null) {
        widget.onImageUploaded?.call(imageUrl);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Image uploaded successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _showError('Failed to upload image. Please try again.');
      }
    } catch (e) {
      if (kDebugMode) {
        print('❌ WebImageUpload: Error: $e');
      }
      _showError('Error uploading image: $e');
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(message),
          backgroundColor: Colors.red,
        ),
      );
    }
  }
}
