import 'package:cloud_firestore/cloud_firestore.dart';

/// User model representing an agent in the MLM system
class UserModel {
  final String id;
  final String email;
  final String name;
  final String phoneNumber;
  final String role;
  final String? profileImageUrl;
  final String? uplineId; // ID of the agent who recruited this user
  final List<String> downlineIds; // IDs of agents recruited by this user
  final int level; // MLM level (0 = top level)
  final double totalCommissions;
  final double totalSales; // Add totalSales property
  final int totalStars;
  final int totalBonuses;
  final bool isActive;
  final DateTime createdAt;
  final DateTime updatedAt;
  final Map<String, dynamic>? additionalInfo;

  const UserModel({
    required this.id,
    required this.email,
    required this.name,
    required this.phoneNumber,
    required this.role,
    this.profileImageUrl,
    this.uplineId,
    this.downlineIds = const [],
    this.level = 0,
    this.totalCommissions = 0.0,
    this.totalSales = 0.0,
    this.totalStars = 0,
    this.totalBonuses = 0,
    this.isActive = true,
    required this.createdAt,
    required this.updatedAt,
    this.additionalInfo,
  });

  /// Create UserModel from Firestore document
  factory UserModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;

    return UserModel(
      id: doc.id,
      email: data['email'] ?? '',
      name: data['name'] ?? '',
      phoneNumber: data['phoneNumber'] ?? '',
      role: data['role'] ?? 'agent',
      profileImageUrl: data['profileImageUrl'],
      uplineId: data['uplineId'],
      downlineIds: List<String>.from(data['downlineIds'] ?? []),
      level: data['level'] ?? 0,
      totalCommissions: (data['totalCommissions'] ?? 0.0).toDouble(),
      totalSales: (data['totalSales'] ?? 0.0).toDouble(),
      totalStars: data['totalStars'] ?? 0,
      totalBonuses: data['totalBonuses'] ?? 0,
      isActive: data['isActive'] ?? true,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      updatedAt: (data['updatedAt'] as Timestamp).toDate(),
      additionalInfo: data['additionalInfo'],
    );
  }

  /// Convert UserModel to JSON
  Map<String, dynamic> toJson() {
    return {
      'id': id,
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'role': role,
      'profileImageUrl': profileImageUrl,
      'uplineId': uplineId,
      'downlineIds': downlineIds,
      'level': level,
      'totalCommissions': totalCommissions,
      'totalSales': totalSales,
      'totalStars': totalStars,
      'totalBonuses': totalBonuses,
      'isActive': isActive,
      'createdAt': createdAt.toIso8601String(),
      'updatedAt': updatedAt.toIso8601String(),
      'additionalInfo': additionalInfo,
    };
  }

  /// Convert UserModel to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'email': email,
      'name': name,
      'phoneNumber': phoneNumber,
      'role': role,
      'profileImageUrl': profileImageUrl,
      'uplineId': uplineId,
      'downlineIds': downlineIds,
      'level': level,
      'totalCommissions': totalCommissions,
      'totalSales': totalSales,
      'totalStars': totalStars,
      'totalBonuses': totalBonuses,
      'isActive': isActive,
      'createdAt': Timestamp.fromDate(createdAt),
      'updatedAt': Timestamp.fromDate(updatedAt),
      'additionalInfo': additionalInfo,
    };
  }

  /// Create a copy with updated fields
  UserModel copyWith({
    String? id,
    String? email,
    String? name,
    String? phoneNumber,
    String? role,
    String? profileImageUrl,
    String? uplineId,
    List<String>? downlineIds,
    int? level,
    double? totalCommissions,
    double? totalSales,
    int? totalStars,
    int? totalBonuses,
    bool? isActive,
    DateTime? createdAt,
    DateTime? updatedAt,
    Map<String, dynamic>? additionalInfo,
  }) {
    return UserModel(
      id: id ?? this.id,
      email: email ?? this.email,
      name: name ?? this.name,
      phoneNumber: phoneNumber ?? this.phoneNumber,
      role: role ?? this.role,
      profileImageUrl: profileImageUrl ?? this.profileImageUrl,
      uplineId: uplineId ?? this.uplineId,
      downlineIds: downlineIds ?? this.downlineIds,
      level: level ?? this.level,
      totalCommissions: totalCommissions ?? this.totalCommissions,
      totalSales: totalSales ?? this.totalSales,
      totalStars: totalStars ?? this.totalStars,
      totalBonuses: totalBonuses ?? this.totalBonuses,
      isActive: isActive ?? this.isActive,
      createdAt: createdAt ?? this.createdAt,
      updatedAt: updatedAt ?? this.updatedAt,
      additionalInfo: additionalInfo ?? this.additionalInfo,
    );
  }

  /// Check if user is admin
  bool get isAdmin => role == 'admin' || role == 'super_admin';

  /// Check if user is eligible for 12-star bonus
  bool get isEligibleForStarBonus => totalStars >= 12;

  /// Get display name
  String get displayName => name.isNotEmpty ? name : email;

  @override
  String toString() {
    return 'UserModel(id: $id, name: $name, email: $email, role: $role, level: $level, stars: $totalStars)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is UserModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
