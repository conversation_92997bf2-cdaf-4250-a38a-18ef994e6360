# Database Integration Guide

## Overview
This guide explains how to integrate the MLM features with Firebase Firestore and implement proper data synchronization.

## Firestore Collection Structure

```
/users/{userId}
  - id: string
  - email: string
  - name: string
  - phoneNumber: string
  - role: string
  - level: number
  - uplineId: string?
  - downlineIds: string[]
  - totalSales: number
  - totalCommissions: number
  - isActive: boolean
  - profileImageUrl: string?
  - createdAt: timestamp
  - updatedAt: timestamp

/properties/{propertyId}
  - id: string
  - title: string
  - description: string
  - price: number
  - type: string
  - status: string
  - location: string
  - bedrooms: number
  - bathrooms: number
  - area: number
  - amenities: string[]
  - images: string[]
  - agentId: string
  - createdAt: timestamp
  - updatedAt: timestamp

/commissions/{commissionId}
  - id: string
  - agentId: string
  - agentName: string
  - propertyId: string?
  - propertyTitle: string?
  - type: string
  - status: string
  - amount: number
  - baseAmount: number
  - percentage: number
  - tier: string
  - tierMultiplier: number
  - sourceAgentId: string?
  - sourceAgentName: string?
  - level: number
  - calculatedAt: timestamp
  - approvedAt: timestamp?
  - approvedBy: string?
  - paidAt: timestamp?
  - paidBy: string?
  - transactionId: string?
  - paymentMethod: string?
  - metadata: map
  - createdAt: timestamp
  - updatedAt: timestamp

/goals/{goalId}
  - id: string
  - agentId: string
  - title: string
  - description: string
  - type: string
  - targetValue: number
  - currentValue: number
  - deadline: timestamp
  - status: string
  - reward: string?
  - createdAt: timestamp
  - updatedAt: timestamp

/reports/{reportId}
  - id: string
  - name: string
  - type: string
  - period: string
  - startDate: timestamp
  - endDate: timestamp
  - metrics: string[]
  - format: string
  - agentId: string?
  - status: string
  - filePath: string?
  - fileSize: number?
  - createdAt: timestamp
  - updatedAt: timestamp

/activities/{activityId}
  - id: string
  - agentId: string
  - type: string
  - title: string
  - description: string
  - timestamp: timestamp
  - metadata: map

/notifications/{notificationId}
  - id: string
  - agentId: string
  - type: string
  - title: string
  - message: string
  - timestamp: timestamp
  - isRead: boolean
  - priority: string
  - actionData: map
```

## Database Service Implementation

```dart
// lib/core/services/database_service.dart
class DatabaseService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  // Generic CRUD operations
  static Future<String> create(String collection, Map<String, dynamic> data) async {
    final docRef = await _firestore.collection(collection).add({
      ...data,
      'createdAt': FieldValue.serverTimestamp(),
      'updatedAt': FieldValue.serverTimestamp(),
    });
    return docRef.id;
  }

  static Future<Map<String, dynamic>?> read(String collection, String id) async {
    final doc = await _firestore.collection(collection).doc(id).get();
    if (doc.exists) {
      return {'id': doc.id, ...doc.data()!};
    }
    return null;
  }

  static Future<void> update(String collection, String id, Map<String, dynamic> data) async {
    await _firestore.collection(collection).doc(id).update({
      ...data,
      'updatedAt': FieldValue.serverTimestamp(),
    });
  }

  static Future<void> delete(String collection, String id) async {
    await _firestore.collection(collection).doc(id).delete();
  }

  // Query operations
  static Future<List<Map<String, dynamic>>> query(
    String collection, {
    List<QueryFilter>? filters,
    List<QueryOrder>? orderBy,
    int? limit,
  }) async {
    Query query = _firestore.collection(collection);

    // Apply filters
    if (filters != null) {
      for (final filter in filters) {
        query = query.where(filter.field, isEqualTo: filter.value);
      }
    }

    // Apply ordering
    if (orderBy != null) {
      for (final order in orderBy) {
        query = query.orderBy(order.field, descending: order.descending);
      }
    }

    // Apply limit
    if (limit != null) {
      query = query.limit(limit);
    }

    final snapshot = await query.get();
    return snapshot.docs.map((doc) => {
      'id': doc.id,
      ...doc.data() as Map<String, dynamic>,
    }).toList();
  }

  // Batch operations
  static Future<void> batchWrite(List<BatchOperation> operations) async {
    final batch = _firestore.batch();

    for (final operation in operations) {
      switch (operation.type) {
        case BatchOperationType.create:
          final docRef = _firestore.collection(operation.collection).doc();
          batch.set(docRef, {
            ...operation.data!,
            'createdAt': FieldValue.serverTimestamp(),
            'updatedAt': FieldValue.serverTimestamp(),
          });
          break;
        case BatchOperationType.update:
          final docRef = _firestore.collection(operation.collection).doc(operation.id);
          batch.update(docRef, {
            ...operation.data!,
            'updatedAt': FieldValue.serverTimestamp(),
          });
          break;
        case BatchOperationType.delete:
          final docRef = _firestore.collection(operation.collection).doc(operation.id);
          batch.delete(docRef);
          break;
      }
    }

    await batch.commit();
  }

  // Real-time subscriptions
  static Stream<List<Map<String, dynamic>>> streamCollection(
    String collection, {
    List<QueryFilter>? filters,
    List<QueryOrder>? orderBy,
    int? limit,
  }) {
    Query query = _firestore.collection(collection);

    // Apply filters and ordering (same as query method)
    if (filters != null) {
      for (final filter in filters) {
        query = query.where(filter.field, isEqualTo: filter.value);
      }
    }

    if (orderBy != null) {
      for (final order in orderBy) {
        query = query.orderBy(order.field, descending: order.descending);
      }
    }

    if (limit != null) {
      query = query.limit(limit);
    }

    return query.snapshots().map((snapshot) => 
      snapshot.docs.map((doc) => {
        'id': doc.id,
        ...doc.data() as Map<String, dynamic>,
      }).toList()
    );
  }

  static Stream<Map<String, dynamic>?> streamDocument(String collection, String id) {
    return _firestore.collection(collection).doc(id).snapshots().map((doc) {
      if (doc.exists) {
        return {'id': doc.id, ...doc.data()!};
      }
      return null;
    });
  }
}

// Helper classes
class QueryFilter {
  final String field;
  final dynamic value;
  final String operator;

  const QueryFilter(this.field, this.value, {this.operator = '=='});
}

class QueryOrder {
  final String field;
  final bool descending;

  const QueryOrder(this.field, {this.descending = false});
}

class BatchOperation {
  final BatchOperationType type;
  final String collection;
  final String? id;
  final Map<String, dynamic>? data;

  const BatchOperation({
    required this.type,
    required this.collection,
    this.id,
    this.data,
  });
}

enum BatchOperationType { create, update, delete }
```

## Data Synchronization

```dart
// lib/core/services/sync_service.dart
class SyncService {
  static final Map<String, StreamSubscription> _subscriptions = {};

  // Sync user data
  static void syncUserData(String userId) {
    _subscriptions['user_$userId']?.cancel();
    _subscriptions['user_$userId'] = DatabaseService
        .streamDocument('users', userId)
        .listen((userData) {
      if (userData != null) {
        // Update local user data
        UserCache.updateUser(UserModel.fromMap(userData));
      }
    });
  }

  // Sync commission data
  static void syncCommissionData(String agentId) {
    _subscriptions['commissions_$agentId']?.cancel();
    _subscriptions['commissions_$agentId'] = DatabaseService
        .streamCollection(
          'commissions',
          filters: [QueryFilter('agentId', agentId)],
          orderBy: [QueryOrder('createdAt', descending: true)],
          limit: 50,
        )
        .listen((commissionsData) {
      // Update local commission cache
      final commissions = commissionsData
          .map((data) => EnhancedCommissionModel.fromMap(data))
          .toList();
      CommissionCache.updateCommissions(agentId, commissions);
    });
  }

  // Sync network data
  static void syncNetworkData(String agentId) {
    _subscriptions['network_$agentId']?.cancel();
    _subscriptions['network_$agentId'] = DatabaseService
        .streamCollection(
          'users',
          filters: [QueryFilter('uplineId', agentId)],
        )
        .listen((networkData) {
      // Update local network cache
      final networkMembers = networkData
          .map((data) => UserModel.fromMap(data))
          .toList();
      NetworkCache.updateNetworkMembers(agentId, networkMembers);
    });
  }

  // Sync goals data
  static void syncGoalsData(String agentId) {
    _subscriptions['goals_$agentId']?.cancel();
    _subscriptions['goals_$agentId'] = DatabaseService
        .streamCollection(
          'goals',
          filters: [QueryFilter('agentId', agentId)],
          orderBy: [QueryOrder('deadline')],
        )
        .listen((goalsData) {
      // Update local goals cache
      final goals = goalsData
          .map((data) => DashboardGoal.fromMap(data))
          .toList();
      GoalsCache.updateGoals(agentId, goals);
    });
  }

  // Stop all syncing
  static void stopAllSync() {
    for (final subscription in _subscriptions.values) {
      subscription.cancel();
    }
    _subscriptions.clear();
  }

  // Stop specific sync
  static void stopSync(String key) {
    _subscriptions[key]?.cancel();
    _subscriptions.remove(key);
  }
}
```

## Offline Support

```dart
// lib/core/services/offline_service.dart
class OfflineService {
  static final Map<String, List<Map<String, dynamic>>> _pendingOperations = {};

  // Queue operation for when online
  static void queueOperation(String collection, String operation, Map<String, dynamic> data) {
    final key = '${collection}_$operation';
    _pendingOperations[key] ??= [];
    _pendingOperations[key]!.add({
      'timestamp': DateTime.now().millisecondsSinceEpoch,
      'data': data,
    });
    
    // Save to local storage
    _savePendingOperations();
  }

  // Process pending operations when online
  static Future<void> processPendingOperations() async {
    if (_pendingOperations.isEmpty) return;

    final operations = <BatchOperation>[];

    for (final entry in _pendingOperations.entries) {
      final parts = entry.key.split('_');
      final collection = parts[0];
      final operation = parts[1];

      for (final pendingOp in entry.value) {
        final data = pendingOp['data'] as Map<String, dynamic>;
        
        switch (operation) {
          case 'create':
            operations.add(BatchOperation(
              type: BatchOperationType.create,
              collection: collection,
              data: data,
            ));
            break;
          case 'update':
            operations.add(BatchOperation(
              type: BatchOperationType.update,
              collection: collection,
              id: data['id'],
              data: data,
            ));
            break;
          case 'delete':
            operations.add(BatchOperation(
              type: BatchOperationType.delete,
              collection: collection,
              id: data['id'],
            ));
            break;
        }
      }
    }

    try {
      await DatabaseService.batchWrite(operations);
      _pendingOperations.clear();
      _savePendingOperations();
    } catch (e) {
      // Handle error - operations remain queued
      print('Error processing pending operations: $e');
    }
  }

  static void _savePendingOperations() {
    // Save to SharedPreferences or Hive
    // Implementation depends on chosen local storage solution
  }

  static void _loadPendingOperations() {
    // Load from SharedPreferences or Hive
    // Implementation depends on chosen local storage solution
  }
}
```

## Data Validation

```dart
// lib/core/services/validation_service.dart
class ValidationService {
  // Validate user data
  static ValidationResult validateUser(Map<String, dynamic> userData) {
    final errors = <String>[];

    if (userData['email'] == null || !_isValidEmail(userData['email'])) {
      errors.add('Invalid email address');
    }

    if (userData['name'] == null || userData['name'].toString().trim().isEmpty) {
      errors.add('Name is required');
    }

    if (userData['phoneNumber'] == null || !_isValidPhoneNumber(userData['phoneNumber'])) {
      errors.add('Invalid phone number');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  // Validate property data
  static ValidationResult validateProperty(Map<String, dynamic> propertyData) {
    final errors = <String>[];

    if (propertyData['title'] == null || propertyData['title'].toString().trim().isEmpty) {
      errors.add('Property title is required');
    }

    if (propertyData['price'] == null || propertyData['price'] <= 0) {
      errors.add('Valid price is required');
    }

    if (propertyData['type'] == null || !PropertyType.values.any((t) => t.name == propertyData['type'])) {
      errors.add('Valid property type is required');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  // Validate commission data
  static ValidationResult validateCommission(Map<String, dynamic> commissionData) {
    final errors = <String>[];

    if (commissionData['agentId'] == null || commissionData['agentId'].toString().trim().isEmpty) {
      errors.add('Agent ID is required');
    }

    if (commissionData['amount'] == null || commissionData['amount'] <= 0) {
      errors.add('Valid commission amount is required');
    }

    if (commissionData['type'] == null || !CommissionType.values.any((t) => t.name == commissionData['type'])) {
      errors.add('Valid commission type is required');
    }

    return ValidationResult(isValid: errors.isEmpty, errors: errors);
  }

  static bool _isValidEmail(String email) {
    return RegExp(r'^[\w-\.]+@([\w-]+\.)+[\w-]{2,4}$').hasMatch(email);
  }

  static bool _isValidPhoneNumber(String phoneNumber) {
    return RegExp(r'^\+?[\d\s\-\(\)]{10,}$').hasMatch(phoneNumber);
  }
}

class ValidationResult {
  final bool isValid;
  final List<String> errors;

  const ValidationResult({required this.isValid, required this.errors});
}
```
