import 'package:flutter/foundation.dart';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../../core/models/property_model.dart';
import '../../../auth/presentation/pages/login_page.dart';
import '../../../auth/presentation/pages/register_page.dart';
import '../../../admin/presentation/pages/test_data_page.dart';
import '../widgets/hero_section.dart';
import '../widgets/featured_properties_section.dart';

import '../widgets/success_stats_section.dart';
import '../widgets/footer_section.dart';

/// Landing page for showcasing properties and MLM opportunity
class LandingPage extends ConsumerStatefulWidget {
  const LandingPage({super.key});

  @override
  ConsumerState<LandingPage> createState() => _LandingPageState();
}

class _LandingPageState extends ConsumerState<LandingPage> {
  final ScrollController _scrollController = ScrollController();

  @override
  void dispose() {
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return Scaffold(
      backgroundColor: AppTheme.backgroundColor,
      body: CustomScrollView(
        controller: _scrollController,
        slivers: [
          // App Bar - Mobile Responsive
          SliverAppBar(
            expandedHeight: 80,
            floating: true,
            pinned: true,
            backgroundColor: AppTheme.backgroundColor,
            elevation: 0,
            flexibleSpace: FlexibleSpaceBar(
              background: Container(
                decoration: BoxDecoration(
                  gradient: LinearGradient(
                    colors: [
                      AppTheme.backgroundColor,
                      AppTheme.backgroundColor.withOpacity(0.9),
                    ],
                  ),
                ),
                child: SafeArea(
                  child: Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: LayoutBuilder(
                      builder: (context, constraints) {
                        bool isMobile = constraints.maxWidth < 768;

                        return Row(
                          mainAxisAlignment: MainAxisAlignment.spaceBetween,
                          children: [
                            // Logo and Brand
                            Flexible(
                              child: Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  Container(
                                    width: isMobile ? 32 : 40,
                                    height: isMobile ? 32 : 40,
                                    decoration: BoxDecoration(
                                      gradient: LinearGradient(
                                        colors: [
                                          AppTheme.primaryColor,
                                          AppTheme.accentColor,
                                        ],
                                      ),
                                      borderRadius: BorderRadius.circular(8),
                                    ),
                                    child: Icon(
                                      Icons.home_work,
                                      color: Colors.white,
                                      size: isMobile ? 18 : 24,
                                    ),
                                  ),
                                  SizedBox(width: isMobile ? 8 : 12),
                                  Flexible(
                                    child: Text(
                                      'Rama Samriddhi',
                                      style: TextStyle(
                                        fontSize: isMobile ? 18 : 24,
                                        fontWeight: FontWeight.bold,
                                        color: AppTheme.primaryColor,
                                      ),
                                      overflow: TextOverflow.ellipsis,
                                    ),
                                  ),
                                ],
                              ),
                            ),

                            // Navigation Buttons
                            if (isMobile)
                              // Mobile: Show only essential buttons
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  TextButton(
                                    onPressed: () =>
                                        _navigateToAuth(isLogin: true),
                                    child: const Text(
                                      'Login',
                                      style: TextStyle(
                                        color: AppTheme.primaryColor,
                                        fontSize: 14,
                                      ),
                                    ),
                                  ),
                                  const SizedBox(width: 4),
                                  ElevatedButton(
                                    onPressed: () =>
                                        _navigateToAuth(isLogin: false),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppTheme.primaryColor,
                                      padding: const EdgeInsets.symmetric(
                                        horizontal: 12,
                                        vertical: 8,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(6),
                                      ),
                                    ),
                                    child: const Text(
                                      'Join',
                                      style: TextStyle(fontSize: 14),
                                    ),
                                  ),
                                ],
                              )
                            else
                              // Desktop: Show all navigation
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  TextButton(
                                    onPressed: () => _scrollToSection(1),
                                    child: const Text(
                                      'Properties',
                                      style: TextStyle(color: Colors.white70),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  TextButton(
                                    onPressed: () => _scrollToSection(2),
                                    child: const Text(
                                      'About',
                                      style: TextStyle(color: Colors.white70),
                                    ),
                                  ),
                                  const SizedBox(width: 8),
                                  ElevatedButton(
                                    onPressed: () =>
                                        _navigateToAuth(isLogin: true),
                                    style: ElevatedButton.styleFrom(
                                      backgroundColor: AppTheme.primaryColor,
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: const Text('Login'),
                                  ),
                                  const SizedBox(width: 8),
                                  OutlinedButton(
                                    onPressed: () =>
                                        _navigateToAuth(isLogin: false),
                                    style: OutlinedButton.styleFrom(
                                      side: const BorderSide(
                                        color: AppTheme.primaryColor,
                                      ),
                                      shape: RoundedRectangleBorder(
                                        borderRadius: BorderRadius.circular(8),
                                      ),
                                    ),
                                    child: const Text(
                                      'Join Now',
                                      style: TextStyle(
                                        color: AppTheme.primaryColor,
                                      ),
                                    ),
                                  ),
                                  // Admin Portal Button (Debug Mode)
                                  if (kDebugMode) ...[
                                    const SizedBox(width: 8),
                                    TextButton(
                                      onPressed: () => _navigateToAdmin(),
                                      child: Text(
                                        'Admin',
                                        style: TextStyle(
                                          color: Colors.orange.withOpacity(0.8),
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                    const SizedBox(width: 4),
                                    TextButton(
                                      onPressed: () => _navigateToTestData(),
                                      child: Text(
                                        'Test Data',
                                        style: TextStyle(
                                          color: Colors.green.withOpacity(0.8),
                                          fontSize: 12,
                                        ),
                                      ),
                                    ),
                                  ],
                                ],
                              ),
                          ],
                        );
                      },
                    ),
                  ),
                ),
              ),
            ),
          ),

          // Hero Section
          SliverToBoxAdapter(
            child: HeroSection(
              onExploreProperties: () => _scrollToSection(1),
              onBecomeAgent: () => _navigateToAuth(isLogin: false),
            ),
          ),

          // Featured Properties Section
          SliverToBoxAdapter(
            child: FeaturedPropertiesSection(
              onViewAllProperties: () =>
                  _navigateToAuth(isLogin: true), // Require login to view all
              onPropertyTap: (property) => _showPropertyDetails(property),
            ),
          ),

          // Success Stats Section
          const SliverToBoxAdapter(child: SuccessStatsSection()),

          // Footer Section
          const SliverToBoxAdapter(child: FooterSection()),
        ],
      ),

      // Floating Action Button for Quick Contact
      floatingActionButton: FloatingActionButton.extended(
        onPressed: _showContactDialog,
        backgroundColor: AppTheme.primaryColor,
        icon: const Icon(Icons.phone),
        label: const Text('Contact Us'),
      ),
    );
  }

  void _scrollToSection(int section) {
    double offset = 0;
    switch (section) {
      case 1: // Properties
        offset = 600;
        break;
      case 2: // About/Stats
        offset = 1200;
        break;
    }

    _scrollController.animateTo(
      offset,
      duration: const Duration(milliseconds: 800),
      curve: Curves.easeInOut,
    );
  }

  void _navigateToAuth({required bool isLogin}) {
    if (isLogin) {
      // Navigate to login page
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const LoginPage()),
      );
    } else {
      // Navigate to registration/agent onboarding page
      Navigator.push(
        context,
        MaterialPageRoute(builder: (context) => const RegisterPage()),
      );
    }
  }

  void _navigateToAdmin() {
    Navigator.pushNamed(context, '/admin');
  }

  void _navigateToTestData() {
    Navigator.push(
      context,
      MaterialPageRoute(builder: (context) => const TestDataPage()),
    );
  }

  void _showPropertyDetails(Map<String, dynamic> property) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardColor,
        title: Text(
          property['title'],
          style: const TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            ClipRRect(
              borderRadius: BorderRadius.circular(8),
              child: Image.network(
                property['image'],
                height: 200,
                width: double.infinity,
                fit: BoxFit.cover,
                errorBuilder: (context, error, stackTrace) => Container(
                  height: 200,
                  color: Colors.grey[800],
                  child: const Center(
                    child: Icon(Icons.error, color: Colors.red),
                  ),
                ),
              ),
            ),
            const SizedBox(height: 16),
            Text(
              property['price'],
              style: const TextStyle(
                fontSize: 24,
                fontWeight: FontWeight.bold,
                color: AppTheme.primaryColor,
              ),
            ),
            const SizedBox(height: 8),
            Row(
              children: [
                const Icon(Icons.location_on, color: Colors.white70, size: 16),
                const SizedBox(width: 4),
                Expanded(
                  child: Text(
                    property['location'],
                    style: const TextStyle(color: Colors.white70),
                  ),
                ),
              ],
            ),
            const SizedBox(height: 16),
            const Text(
              'To view full details and contact the agent, please login or register.',
              style: TextStyle(color: Colors.white70),
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text('Close', style: TextStyle(color: Colors.white70)),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.pop(context);
              _navigateToAuth(isLogin: true);
            },
            style: ElevatedButton.styleFrom(
              backgroundColor: AppTheme.primaryColor,
            ),
            child: const Text('Login to View Details'),
          ),
        ],
      ),
    );
  }

  void _showContactDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        backgroundColor: AppTheme.cardColor,
        title: const Text(
          'Contact Rama Samriddhi',
          style: TextStyle(color: Colors.white),
        ),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            _buildContactItem(Icons.phone, '+91 98765 43210'),
            const SizedBox(height: 12),
            _buildContactItem(Icons.email, '<EMAIL>'),
            const SizedBox(height: 12),
            _buildContactItem(Icons.location_on, 'Mumbai, Maharashtra'),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.pop(context),
            child: const Text(
              'Close',
              style: TextStyle(color: AppTheme.primaryColor),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildContactItem(IconData icon, String text) {
    return Row(
      children: [
        Icon(icon, color: AppTheme.primaryColor, size: 20),
        const SizedBox(width: 12),
        Text(text, style: const TextStyle(color: Colors.white70)),
      ],
    );
  }
}
