import 'package:cloud_firestore/cloud_firestore.dart';
import 'test_data_service.dart';

/// Service to populate Firestore with test data for realistic testing
class FirestoreTestDataService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Populate Firestore with all test data
  static Future<void> populateFirestoreWithTestData() async {
    try {
      print('🔄 Starting Firestore population with test data...');

      // Get all test data
      final users = await TestDataService.getUsers();
      final properties = await TestDataService.getProperties();
      final transactions = await TestDataService.getTransactions();
      final commissions = await TestDataService.getCommissions();
      final stars = await TestDataService.getStars();
      final leads = await TestDataService.getLeads();

      // Use batch writes for better performance
      WriteBatch batch = _firestore.batch();
      int operationCount = 0;

      // Add users to Firestore
      print('📝 Adding ${users.length} users to Firestore...');
      for (final user in users) {
        final userRef = _firestore.collection('users').doc(user['id']);
        batch.set(userRef, {
          ...user,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        operationCount++;

        // Commit batch every 450 operations (Firestore limit is 500)
        if (operationCount >= 450) {
          await batch.commit();
          batch = _firestore.batch();
          operationCount = 0;
        }
      }

      // Add properties to Firestore
      print('🏠 Adding ${properties.length} properties to Firestore...');
      for (final property in properties) {
        final propertyRef = _firestore.collection('properties').doc(property['id']);
        batch.set(propertyRef, {
          ...property,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        operationCount++;

        if (operationCount >= 450) {
          await batch.commit();
          batch = _firestore.batch();
          operationCount = 0;
        }
      }

      // Add transactions to Firestore
      print('💰 Adding ${transactions.length} transactions to Firestore...');
      for (final transaction in transactions) {
        final transactionRef = _firestore.collection('transactions').doc(transaction['id']);
        batch.set(transactionRef, {
          ...transaction,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        operationCount++;

        if (operationCount >= 450) {
          await batch.commit();
          batch = _firestore.batch();
          operationCount = 0;
        }
      }

      // Add commissions to Firestore
      print('💵 Adding ${commissions.length} commissions to Firestore...');
      for (final commission in commissions) {
        final commissionRef = _firestore.collection('commissions').doc(commission['id']);
        batch.set(commissionRef, {
          ...commission,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        operationCount++;

        if (operationCount >= 450) {
          await batch.commit();
          batch = _firestore.batch();
          operationCount = 0;
        }
      }

      // Add stars to Firestore
      print('⭐ Adding ${stars.length} stars to Firestore...');
      for (final star in stars) {
        final starRef = _firestore.collection('stars').doc(star['id']);
        batch.set(starRef, {
          ...star,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        operationCount++;

        if (operationCount >= 450) {
          await batch.commit();
          batch = _firestore.batch();
          operationCount = 0;
        }
      }

      // Add leads to Firestore
      print('📞 Adding ${leads.length} leads to Firestore...');
      for (final lead in leads) {
        final leadRef = _firestore.collection('leads').doc(lead['id']);
        batch.set(leadRef, {
          ...lead,
          'createdAt': FieldValue.serverTimestamp(),
          'updatedAt': FieldValue.serverTimestamp(),
        });
        operationCount++;

        if (operationCount >= 450) {
          await batch.commit();
          batch = _firestore.batch();
          operationCount = 0;
        }
      }

      // Commit any remaining operations
      if (operationCount > 0) {
        await batch.commit();
      }

      // Add a metadata document to track test data
      await _firestore.collection('system').doc('test_data_metadata').set({
        'isTestDataLoaded': true,
        'loadedAt': FieldValue.serverTimestamp(),
        'dataVersion': '1.0',
        'totalUsers': users.length,
        'totalProperties': properties.length,
        'totalTransactions': transactions.length,
        'totalCommissions': commissions.length,
        'totalStars': stars.length,
        'totalLeads': leads.length,
      });

      print('✅ Firestore population completed successfully!');
      print('📊 Data Summary:');
      print('   Users: ${users.length}');
      print('   Properties: ${properties.length}');
      print('   Transactions: ${transactions.length}');
      print('   Commissions: ${commissions.length}');
      print('   Stars: ${stars.length}');
      print('   Leads: ${leads.length}');

    } catch (e) {
      print('❌ Error populating Firestore: $e');
      rethrow;
    }
  }

  /// Clear all test data from Firestore
  static Future<void> clearFirestoreTestData() async {
    try {
      print('🔄 Starting Firestore test data cleanup...');

      // Collections to clear
      final collections = ['users', 'properties', 'transactions', 'commissions', 'stars', 'leads'];

      for (final collectionName in collections) {
        print('🗑️ Clearing $collectionName collection...');
        
        // Get all documents in batches
        QuerySnapshot snapshot = await _firestore.collection(collectionName).get();
        
        // Delete in batches
        WriteBatch batch = _firestore.batch();
        int operationCount = 0;

        for (final doc in snapshot.docs) {
          batch.delete(doc.reference);
          operationCount++;

          // Commit batch every 450 operations
          if (operationCount >= 450) {
            await batch.commit();
            batch = _firestore.batch();
            operationCount = 0;
          }
        }

        // Commit any remaining operations
        if (operationCount > 0) {
          await batch.commit();
        }

        print('✅ Cleared ${snapshot.docs.length} documents from $collectionName');
      }

      // Remove metadata document
      await _firestore.collection('system').doc('test_data_metadata').delete();

      print('✅ Firestore test data cleanup completed!');

    } catch (e) {
      print('❌ Error clearing Firestore test data: $e');
      rethrow;
    }
  }

  /// Check if test data is loaded in Firestore
  static Future<bool> isTestDataLoadedInFirestore() async {
    try {
      final doc = await _firestore.collection('system').doc('test_data_metadata').get();
      return doc.exists && (doc.data()?['isTestDataLoaded'] == true);
    } catch (e) {
      print('Error checking test data status: $e');
      return false;
    }
  }

  /// Get test data summary from Firestore
  static Future<Map<String, dynamic>> getFirestoreTestDataSummary() async {
    try {
      final doc = await _firestore.collection('system').doc('test_data_metadata').get();
      if (doc.exists) {
        return doc.data() ?? {};
      }
      return {};
    } catch (e) {
      print('Error getting test data summary: $e');
      return {};
    }
  }

  /// Populate Firestore with authentication data for test users
  static Future<void> populateAuthenticationData() async {
    try {
      print('🔐 Setting up authentication data for test users...');

      final users = await TestDataService.getUsers();
      
      // Create authentication records for test users
      WriteBatch batch = _firestore.batch();
      int operationCount = 0;

      for (final user in users) {
        // Create user authentication document
        final authRef = _firestore.collection('user_auth').doc(user['id']);
        batch.set(authRef, {
          'email': user['email'],
          'password': 'agent123', // Default password for all test users
          'isActive': user['isActive'],
          'role': user['role'],
          'level': user['level'],
          'createdAt': FieldValue.serverTimestamp(),
        });
        operationCount++;

        if (operationCount >= 450) {
          await batch.commit();
          batch = _firestore.batch();
          operationCount = 0;
        }
      }

      if (operationCount > 0) {
        await batch.commit();
      }

      print('✅ Authentication data setup completed for ${users.length} users');

    } catch (e) {
      print('❌ Error setting up authentication data: $e');
      rethrow;
    }
  }
}
