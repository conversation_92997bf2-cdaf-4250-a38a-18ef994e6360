import 'package:flutter/material.dart';
import 'widgets/demo_widgets.dart';

void main() {
  runApp(const SimpleMLMDemo());
}

class SimpleMLMDemo extends StatefulWidget {
  const SimpleMLMDemo({super.key});

  @override
  State<SimpleMLMDemo> createState() => _SimpleMLMDemoState();
}

class _SimpleMLMDemoState extends State<SimpleMLMDemo> {
  bool _darkMode = true;

  @override
  Widget build(BuildContext context) {
    return MaterialApp(
      title: 'Rama Realty MLM Demo',
      theme: ThemeData.light().copyWith(
        primaryColor: const Color(0xFFFF6B35),
        colorScheme: ColorScheme.light(
          primary: const Color(0xFFFF6B35),
          secondary: const Color(0xFF8B5CF6),
          surface: const Color(0xFFF5F5F5),
          background: const Color(0xFFF0F0F0),
        ),
      ),
      darkTheme: ThemeData.dark().copyWith(
        primaryColor: const Color(0xFFFF6B35),
        colorScheme: ColorScheme.dark(
          primary: const Color(0xFFFF6B35),
          secondary: const Color(0xFF8B5CF6),
          surface: const Color(0xFF1A1A1A),
          background: const Color(0xFF0F0F0F),
        ),
      ),
      themeMode: _darkMode ? ThemeMode.dark : ThemeMode.light,
      home: MLMDashboard(
        darkMode: _darkMode,
        onToggleDarkMode: () => setState(() => _darkMode = !_darkMode),
      ),
      debugShowCheckedModeBanner: false,
    );
  }
}

class MLMDashboard extends StatefulWidget {
  final bool darkMode;
  final VoidCallback onToggleDarkMode;
  const MLMDashboard({super.key, required this.darkMode, required this.onToggleDarkMode});

  @override
  State<MLMDashboard> createState() => _MLMDashboardState();
}

class _MLMDashboardState extends State<MLMDashboard> {
  int _currentIndex = 0;

  @override
  Widget build(BuildContext context) {
    final List<Widget> _pages = [
      DashboardPage(
        darkMode: widget.darkMode,
        onToggleDarkMode: widget.onToggleDarkMode,
      ),
      const PropertiesPage(),
      const CommissionsPage(),
      const NetworkPage(),
      const ReportsPage(),
    ];
    return Scaffold(
      backgroundColor: Theme.of(context).colorScheme.background,
      appBar: AppBar(
        title: const Text('Rama Realty MLM'),
        backgroundColor: Theme.of(context).colorScheme.surface,
        elevation: 0,
        actions: [
          IconButton(
            icon: const Icon(Icons.notifications),
            tooltip: 'Notifications',
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(content: Text('Notifications feature coming soon!')),
              );
            },
          ),
          IconButton(
            icon: Icon(widget.darkMode ? Icons.dark_mode : Icons.light_mode),
            tooltip: widget.darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode',
            onPressed: widget.onToggleDarkMode,
          ),
          IconButton(
            icon: const Icon(Icons.account_circle),
            tooltip: 'Profile',
            onPressed: () {
              ScaffoldMessenger.of(context).showSnackBar(
                const SnackBar(
                  content: Text('Profile: Demo Agent (Gold Tier)'),
                ),
              );
            },
          ),
        ],
      ),
      body: _pages[_currentIndex],
      bottomNavigationBar: BottomNavigationBar(
        currentIndex: _currentIndex,
        onTap: (index) => setState(() => _currentIndex = index),
        type: BottomNavigationBarType.fixed,
        backgroundColor: Theme.of(context).colorScheme.surface,
        selectedItemColor: const Color(0xFFFF6B35),
        unselectedItemColor: Colors.grey,
        items: const [
          BottomNavigationBarItem(
            icon: Icon(Icons.dashboard),
            label: 'Dashboard',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.home_work),
            label: 'Properties',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.monetization_on),
            label: 'Commissions',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.account_tree),
            label: 'Network',
          ),
          BottomNavigationBarItem(
            icon: Icon(Icons.analytics),
            label: 'Reports',
          ),
        ],
      ),
      floatingActionButton: _buildFAB(),
    );
  }

  Widget? _buildFAB() {
    switch (_currentIndex) {
      case 1:
        return FloatingActionButton.extended(
          onPressed: () => _showDemoModal('Add Property', 'This would open the property creation form.'),
          icon: const Icon(Icons.add_home_work),
          label: const Text('Add Property'),
        );
      case 2:
        return FloatingActionButton.extended(
          onPressed: () => _showDemoModal('Add Commission', 'This would open the commission entry form.'),
          icon: const Icon(Icons.add),
          label: const Text('Add Commission'),
        );
      case 3:
        return FloatingActionButton.extended(
          onPressed: () => _showDemoModal('Invite Downline', 'This would open the invite downline dialog.'),
          icon: const Icon(Icons.person_add),
          label: const Text('Invite Downline'),
        );
      default:
        return null;
    }
  }

  void _showDemoModal(String title, String content) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(18)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(title, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(content, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 8),
            const Divider(),
            const Text('This is a demo preview. In the full app, this would be a full-featured form or dialog.'),
          ],
        ),
      ),
    );
  }
}

class DashboardPage extends StatelessWidget {
  final bool darkMode;
  final VoidCallback onToggleDarkMode;
  const DashboardPage({super.key, required this.darkMode, required this.onToggleDarkMode});

  @override
  Widget build(BuildContext context) {
    final statCards = [
      AnimatedStatCard(
        title: 'Total Sales',
        value: '₹25,00,000',
        icon: Icons.trending_up,
        color: Colors.green,
        tooltip: 'Total value of all sales',
      ),
      AnimatedStatCard(
        title: 'Commissions',
        value: '₹1,25,000',
        icon: Icons.monetization_on,
        color: const Color(0xFFFF6B35),
        tooltip: 'Total commissions earned',
      ),
      AnimatedStatCard(
        title: 'Network Size',
        value: '12 Members',
        icon: Icons.people,
        color: const Color(0xFF8B5CF6),
        tooltip: 'Total downline members',
      ),
      AnimatedStatCard(
        title: 'This Month',
        value: '₹85,000',
        icon: Icons.calendar_today,
        color: Colors.blue,
        tooltip: 'Earnings this month',
      ),
    ];

    final quickActions = [
      QuickActionButton(
        icon: Icons.add_home_work,
        label: 'Add Property',
        color: const Color(0xFFFF6B35),
        onTap: () => _showDemoModal(context, 'Add Property', 'This would open the property creation form.'),
      ),
      QuickActionButton(
        icon: Icons.add,
        label: 'Add Commission',
        color: Colors.green,
        onTap: () => _showDemoModal(context, 'Add Commission', 'This would open the commission entry form.'),
      ),
      QuickActionButton(
        icon: Icons.person_add,
        label: 'Invite Downline',
        color: const Color(0xFF8B5CF6),
        onTap: () => _showDemoModal(context, 'Invite Downline', 'This would open the invite downline dialog.'),
      ),
    ];

    final activities = [
      ActivityItem(
        icon: Icons.monetization_on,
        title: 'Commission Credited',
        subtitle: '₹25,000 from Bandra Sale • 2h ago',
        color: const Color(0xFFFF6B35),
      ),
      ActivityItem(
        icon: Icons.star,
        title: 'Star Earned',
        subtitle: '12-star milestone achieved • 1d ago',
        color: Colors.amber,
      ),
      ActivityItem(
        icon: Icons.account_tree,
        title: 'New Downline Added',
        subtitle: 'Agent Priya joined your network • 3d ago',
        color: const Color(0xFF8B5CF6),
      ),
      ActivityItem(
        icon: Icons.home_work,
        title: 'Property Approved',
        subtitle: '3BHK in Andheri approved • 4d ago',
        color: Colors.green,
      ),
    ];

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Welcome Card
          Container(
            width: double.infinity,
            padding: const EdgeInsets.all(20),
            decoration: BoxDecoration(
              gradient: const LinearGradient(
                colors: [Color(0xFFFF6B35), Color(0xFF8B5CF6)],
                begin: Alignment.topLeft,
                end: Alignment.bottomRight,
              ),
              borderRadius: BorderRadius.circular(16),
              boxShadow: [
                BoxShadow(
                  color: Colors.black.withOpacity(0.08),
                  blurRadius: 8,
                  offset: const Offset(0, 2),
                ),
              ],
            ),
            child: Row(
              children: [
                const CircleAvatar(
                  radius: 32,
                  backgroundColor: Colors.white,
                  child: Icon(Icons.account_circle, size: 48, color: Color(0xFFFF6B35)),
                ),
                const SizedBox(width: 18),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: const [
                      Text(
                        'Welcome, Demo Agent',
                        style: TextStyle(
                          color: Colors.white,
                          fontSize: 22,
                          fontWeight: FontWeight.bold,
                        ),
                      ),
                      SizedBox(height: 6),
                      Text(
                        'Gold Tier • Level 2',
                        style: TextStyle(color: Colors.white, fontSize: 15),
                      ),
                    ],
                  ),
                ),
                Tooltip(
                  message: darkMode ? 'Switch to Light Mode' : 'Switch to Dark Mode',
                  child: IconButton(
                    icon: Icon(darkMode ? Icons.dark_mode : Icons.light_mode, color: Colors.white),
                    onPressed: onToggleDarkMode,
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Quick Actions
          SingleChildScrollView(
            scrollDirection: Axis.horizontal,
            child: Row(
              children: quickActions
                  .map((action) => Padding(
                        padding: const EdgeInsets.only(right: 12),
                        child: action,
                      ))
                  .toList(),
            ),
          ),

          const SizedBox(height: 24),

          // Stats Grid
          GridView.count(
            shrinkWrap: true,
            physics: const NeverScrollableScrollPhysics(),
            crossAxisCount: 2,
            crossAxisSpacing: 16,
            mainAxisSpacing: 16,
            childAspectRatio: 1.25,
            children: statCards,
          ),

          const SizedBox(height: 24),

          // Recent Activities
          Row(
            children: const [
              Text(
                'Recent Activities',
                style: TextStyle(
                  color: Colors.white,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
              SizedBox(width: 8),
              Tooltip(
                message: 'See your latest actions and updates',
                child: Icon(Icons.info_outline, color: Colors.grey, size: 18),
              ),
            ],
          ),
          const SizedBox(height: 10),
          ...activities,

          const SizedBox(height: 24),

          // Features List
          const Text(
            'Available Features:',
            style: TextStyle(
              color: Colors.white,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),
          const SizedBox(height: 12),

          ...[
            'Dashboard Analytics',
            'Property Management',
            'Commission Tracking',
            'MLM Network Tree',
            'Performance Reports',
          ].map(
            (feature) => Container(
              margin: const EdgeInsets.only(bottom: 8),
              padding: const EdgeInsets.all(12),
              decoration: BoxDecoration(
                color: Theme.of(context).colorScheme.surface,
                borderRadius: BorderRadius.circular(8),
              ),
              child: Row(
                children: [
                  const Icon(Icons.check, color: Colors.green, size: 20),
                  const SizedBox(width: 12),
                  Text(feature, style: const TextStyle(color: Colors.white)),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  void _showDemoModal(BuildContext context, String title, String content) {
    showModalBottomSheet(
      context: context,
      shape: const RoundedRectangleBorder(
        borderRadius: BorderRadius.vertical(top: Radius.circular(18)),
      ),
      builder: (context) => Padding(
        padding: const EdgeInsets.all(24.0),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Row(
              children: [
                Text(title, style: const TextStyle(fontSize: 20, fontWeight: FontWeight.bold)),
                const Spacer(),
                IconButton(
                  icon: const Icon(Icons.close),
                  onPressed: () => Navigator.pop(context),
                ),
              ],
            ),
            const SizedBox(height: 12),
            Text(content, style: const TextStyle(fontSize: 16)),
            const SizedBox(height: 8),
            const Divider(),
            const Text('This is a demo preview. In the full app, this would be a full-featured form or dialog.'),
          ],
        ),
      ),
    );
  }
}

class PropertiesPage extends StatelessWidget {
  const PropertiesPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.home_work, size: 64, color: Color(0xFFFF6B35)),
          SizedBox(height: 16),
          Text(
            'Property Management',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Manage real estate listings\nProperty approval workflow\nAgent assignments',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey, fontSize: 16),
          ),
        ],
      ),
    );
  }
}

class CommissionsPage extends StatelessWidget {
  const CommissionsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.monetization_on, size: 64, color: Color(0xFFFF6B35)),
          SizedBox(height: 16),
          Text(
            'Commission System',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Track commission earnings\nMLM tier progression\nPerformance bonuses',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey, fontSize: 16),
          ),
        ],
      ),
    );
  }
}

class NetworkPage extends StatelessWidget {
  const NetworkPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.account_tree, size: 64, color: Color(0xFF8B5CF6)),
          SizedBox(height: 16),
          Text(
            'MLM Network',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Network tree visualization\nDownline management\nReferral tracking',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey, fontSize: 16),
          ),
        ],
      ),
    );
  }
}

class ReportsPage extends StatelessWidget {
  const ReportsPage({super.key});

  @override
  Widget build(BuildContext context) {
    return const Center(
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(Icons.analytics, size: 64, color: Colors.blue),
          SizedBox(height: 16),
          Text(
            'Reports & Analytics',
            style: TextStyle(
              color: Colors.white,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          SizedBox(height: 8),
          Text(
            'Performance reports\nSales analytics\nGoal tracking',
            textAlign: TextAlign.center,
            style: TextStyle(color: Colors.grey, fontSize: 16),
          ),
        ],
      ),
    );
  }
}
