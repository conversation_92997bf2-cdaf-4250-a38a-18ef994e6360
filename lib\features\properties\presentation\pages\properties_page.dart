import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/property_providers.dart';
import '../widgets/property_card.dart';
import '../widgets/property_form.dart';
import '../widgets/search_widgets.dart';
import '../../../../core/models/property_model.dart';
import '../../../../core/services/property_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

class PropertiesPage extends ConsumerStatefulWidget {
  const PropertiesPage({super.key});

  @override
  ConsumerState<PropertiesPage> createState() => _PropertiesPageState();
}

class _PropertiesPageState extends ConsumerState<PropertiesPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final isAdmin = currentUser?.isAdmin ?? false;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Properties'),
        actions: [
          IconButton(
            icon: const Icon(Icons.search),
            onPressed: () => _showSearchDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          if (isAdmin)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _addProperty(context),
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            const Tab(icon: Icon(Icons.home), text: 'All Properties'),
            if (isAdmin)
              const Tab(icon: Icon(Icons.admin_panel_settings), text: 'Admin'),
            const Tab(icon: Icon(Icons.star), text: 'Featured'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // All Properties Tab
          _buildAllPropertiesTab(),

          // Admin Tab (only for admins)
          if (isAdmin) _buildAdminTab(),

          // Featured Properties Tab
          _buildFeaturedPropertiesTab(),
        ],
      ),
      floatingActionButton: isAdmin
          ? FloatingActionButton(
              onPressed: () => _addProperty(context),
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildAllPropertiesTab() {
    final propertiesAsync = ref.watch(advancedFilteredPropertiesProvider);
    final searchState = ref.watch(propertySearchProvider);

    return Column(
      children: [
        // Enhanced Search Bar
        Container(
          padding: const EdgeInsets.all(16),
          child: PropertySearchBar(
            onPropertySelected: (property) =>
                _showPropertyDetails(context, property),
            hintText: 'Search properties by location, type, price...',
            showFilters: true,
          ),
        ),

        // Properties List
        Expanded(
          child: propertiesAsync.when(
            data: (properties) {
              // Show search results if searching
              if (searchState.query.isNotEmpty &&
                  searchState.results.isNotEmpty) {
                return RefreshIndicator(
                  onRefresh: () async {
                    ref.invalidate(advancedFilteredPropertiesProvider);
                  },
                  child: ListView.builder(
                    padding: const EdgeInsets.symmetric(horizontal: 8),
                    itemCount: searchState.results.length,
                    itemBuilder: (context, index) {
                      final property = searchState.results[index];
                      return PropertyCard(
                        property: property,
                        onTap: () => _showPropertyDetails(context, property),
                      );
                    },
                  ),
                );
              }

              // Show filtered properties
              if (properties.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.home_work, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No properties found'),
                      Text('Try adjusting your search or filters'),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: () async {
                  ref.invalidate(advancedFilteredPropertiesProvider);
                },
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(horizontal: 8),
                  itemCount: properties.length,
                  itemBuilder: (context, index) {
                    final property = properties[index];
                    return PropertyCard(
                      property: property,
                      onTap: () => _showPropertyDetails(context, property),
                    );
                  },
                ),
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error loading properties: $error'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () =>
                        ref.refresh(advancedFilteredPropertiesProvider),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildAdminTab() {
    final adminPropertiesAsync = ref.watch(adminPropertiesProvider);
    final statsAsync = ref.watch(adminPropertyStatsProvider);

    return Column(
      children: [
        // Statistics Card
        statsAsync.when(
          data: (stats) => _buildStatsCard(stats),
          loading: () => const LinearProgressIndicator(),
          error: (_, __) => const SizedBox.shrink(),
        ),

        // Properties List
        Expanded(
          child: adminPropertiesAsync.when(
            data: (properties) {
              if (properties.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.inventory, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No properties to manage'),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(8),
                itemCount: properties.length,
                itemBuilder: (context, index) {
                  final property = properties[index];
                  return PropertyCard(
                    property: property,
                    showAdminActions: true,
                    onTap: () => _showPropertyDetails(context, property),
                    onEdit: (property) => _editProperty(context, property),
                    onDelete: (property) => _deleteProperty(context, property),
                    onApprove: (property) => _approveProperty(property),
                    onReject: (property) => _rejectProperty(property),
                    onToggleFeatured: (property) => _toggleFeatured(property),
                  );
                },
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => Center(child: Text('Error: $error')),
          ),
        ),
      ],
    );
  }

  Widget _buildFeaturedPropertiesTab() {
    final featuredPropertiesAsync = ref.watch(featuredPropertiesProvider);

    return featuredPropertiesAsync.when(
      data: (properties) {
        if (properties.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.star_outline, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No featured properties'),
                Text('Featured properties will appear here'),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(8),
          itemCount: properties.length,
          itemBuilder: (context, index) {
            final property = properties[index];
            return PropertyCard(
              property: property,
              onTap: () => _showPropertyDetails(context, property),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildStatsCard(Map<String, dynamic> stats) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Property Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 4,
              crossAxisSpacing: 8,
              mainAxisSpacing: 8,
              childAspectRatio: 1.2,
              children: [
                _buildStatItem(
                  'Total',
                  '${stats['totalProperties'] ?? 0}',
                  Icons.home_work,
                  Colors.blue,
                ),
                _buildStatItem(
                  'Approved',
                  '${stats['approvedProperties'] ?? 0}',
                  Icons.check_circle,
                  Colors.green,
                ),
                _buildStatItem(
                  'Pending',
                  '${stats['pendingApproval'] ?? 0}',
                  Icons.pending,
                  Colors.orange,
                ),
                _buildStatItem(
                  'Featured',
                  '${stats['featuredProperties'] ?? 0}',
                  Icons.star,
                  Colors.amber,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildValueCard(
                    'Total Value',
                    PropertyService.formatIndianRupees(
                      stats['totalValue']?.toDouble() ?? 0,
                    ),
                    Icons.currency_rupee,
                    Colors.green,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildValueCard(
                    'Average Price',
                    PropertyService.formatIndianRupees(
                      stats['averagePrice']?.toDouble() ?? 0,
                    ),
                    Icons.analytics,
                    Colors.purple,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(8),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 16,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildValueCard(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Icon(icon, color: color, size: 20),
              const SizedBox(width: 8),
              Text(
                label,
                style: TextStyle(color: color, fontWeight: FontWeight.w500),
              ),
            ],
          ),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontSize: 18,
              fontWeight: FontWeight.bold,
              color: color,
            ),
          ),
        ],
      ),
    );
  }

  void _showSearchDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Search Properties'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              decoration: const InputDecoration(
                labelText: 'Search by title, location, or city',
                prefixIcon: Icon(Icons.search),
              ),
              onChanged: (query) {
                ref
                    .read(propertySearchProvider.notifier)
                    .searchProperties(query);
              },
            ),
            const SizedBox(height: 16),
            Consumer(
              builder: (context, ref, child) {
                final searchState = ref.watch(propertySearchProvider);

                if (searchState.isLoading) {
                  return const CircularProgressIndicator();
                }

                if (searchState.error != null) {
                  return Text(
                    searchState.error!,
                    style: const TextStyle(color: Colors.red),
                  );
                }

                if (searchState.results.isEmpty &&
                    searchState.query.isNotEmpty) {
                  return const Text('No properties found');
                }

                return SizedBox(
                  height: 200,
                  width: double.maxFinite,
                  child: ListView.builder(
                    itemCount: searchState.results.length,
                    itemBuilder: (context, index) {
                      final property = searchState.results[index];
                      return ListTile(
                        title: Text(property.title),
                        subtitle: Text(
                          '${property.formattedPrice} • ${property.city}',
                        ),
                        onTap: () {
                          Navigator.of(context).pop();
                          _showPropertyDetails(context, property);
                        },
                      );
                    },
                  ),
                );
              },
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(propertySearchProvider.notifier).clearSearch();
              Navigator.of(context).pop();
            },
            child: const Text('Close'),
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Properties'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Property Type Filter
              Consumer(
                builder: (context, ref, child) {
                  final filter = ref.watch(propertyFilterProvider);
                  return DropdownButtonFormField<String?>(
                    value: filter.type,
                    decoration: const InputDecoration(
                      labelText: 'Property Type',
                    ),
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Types'),
                      ),
                      ...[
                        'Residential',
                        'Commercial',
                        'Land',
                        'Industrial',
                        'Agricultural',
                      ].map(
                        (type) =>
                            DropdownMenuItem(value: type, child: Text(type)),
                      ),
                    ],
                    onChanged: (value) {
                      ref.read(propertyFilterProvider.notifier).setType(value);
                    },
                  );
                },
              ),

              const SizedBox(height: 16),

              // Status Filter
              Consumer(
                builder: (context, ref, child) {
                  final filter = ref.watch(propertyFilterProvider);
                  return DropdownButtonFormField<String?>(
                    value: filter.status,
                    decoration: const InputDecoration(labelText: 'Status'),
                    items: [
                      const DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Status'),
                      ),
                      const DropdownMenuItem(
                        value: 'for_sale',
                        child: Text('For Sale'),
                      ),
                      const DropdownMenuItem(
                        value: 'for_rent',
                        child: Text('For Rent'),
                      ),
                      const DropdownMenuItem(
                        value: 'sold',
                        child: Text('Sold'),
                      ),
                      const DropdownMenuItem(
                        value: 'rented',
                        child: Text('Rented'),
                      ),
                    ],
                    onChanged: (value) {
                      ref
                          .read(propertyFilterProvider.notifier)
                          .setStatus(value);
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(propertyFilterProvider.notifier).resetFilters();
              Navigator.of(context).pop();
            },
            child: const Text('Reset'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _addProperty(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PropertyForm(
          onSaved: (property) {
            ref.invalidate(adminPropertiesProvider);
            ref.invalidate(propertiesProvider);
          },
        ),
      ),
    );
  }

  void _editProperty(BuildContext context, PropertyModel property) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => PropertyForm(
          property: property,
          onSaved: (updatedProperty) {
            ref.invalidate(adminPropertiesProvider);
            ref.invalidate(propertiesProvider);
          },
        ),
      ),
    );
  }

  void _showPropertyDetails(BuildContext context, PropertyModel property) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.8,
        maxChildSize: 0.95,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 16),

              // Property details
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and Price
                      Text(
                        property.title,
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(
                        property.formattedPrice,
                        style: Theme.of(context).textTheme.headlineMedium
                            ?.copyWith(
                              color: Theme.of(context).primaryColor,
                              fontWeight: FontWeight.bold,
                            ),
                      ),

                      const SizedBox(height: 16),

                      // Location
                      _buildDetailRow(
                        'Location',
                        property.fullAddress,
                        Icons.location_on,
                      ),
                      _buildDetailRow('Type', property.type, Icons.category),
                      _buildDetailRow('Status', property.status, Icons.info),

                      if (property.areaSquareFeet != null)
                        _buildDetailRow(
                          'Area',
                          '${property.areaSquareFeet!.toStringAsFixed(0)} sq ft',
                          Icons.square_foot,
                        ),

                      if (property.bedrooms != null)
                        _buildDetailRow(
                          'Bedrooms',
                          '${property.bedrooms}',
                          Icons.bed,
                        ),

                      if (property.bathrooms != null)
                        _buildDetailRow(
                          'Bathrooms',
                          '${property.bathrooms}',
                          Icons.bathroom,
                        ),

                      const SizedBox(height: 16),

                      // Description
                      Text(
                        'Description',
                        style: Theme.of(context).textTheme.titleMedium
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),
                      const SizedBox(height: 8),
                      Text(property.description),

                      const SizedBox(height: 16),

                      // Amenities
                      if (property.amenities.isNotEmpty) ...[
                        Text(
                          'Amenities',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Wrap(
                          spacing: 8,
                          runSpacing: 4,
                          children: property.amenities
                              .map(
                                (amenity) => Chip(
                                  label: Text(amenity),
                                  backgroundColor: Theme.of(
                                    context,
                                  ).primaryColor.withValues(alpha: 0.1),
                                ),
                              )
                              .toList(),
                        ),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.w500)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Future<void> _deleteProperty(
    BuildContext context,
    PropertyModel property,
  ) async {
    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Delete Property'),
        content: Text('Are you sure you want to delete "${property.title}"?'),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            style: ElevatedButton.styleFrom(backgroundColor: Colors.red),
            child: const Text('Delete'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await PropertyService.deleteProperty(property.id);
      if (result.isSuccess) {
        ref.refresh(adminPropertiesProvider);
        ref.refresh(propertiesProvider);
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(content: Text('Property deleted successfully')),
          );
        }
      } else {
        if (context.mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to delete property: ${result.message}'),
            ),
          );
        }
      }
    }
  }

  Future<void> _approveProperty(PropertyModel property) async {
    final result = await PropertyService.approveProperty(property.id);
    if (result.isSuccess) {
      ref.refresh(adminPropertiesProvider);
      ref.refresh(propertiesProvider);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Property approved successfully')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to approve property: ${result.message}'),
        ),
      );
    }
  }

  Future<void> _rejectProperty(PropertyModel property) async {
    final result = await PropertyService.rejectProperty(property.id);
    if (result.isSuccess) {
      ref.refresh(adminPropertiesProvider);
      ref.refresh(propertiesProvider);
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Property rejected successfully')),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text('Failed to reject property: ${result.message}')),
      );
    }
  }

  Future<void> _toggleFeatured(PropertyModel property) async {
    final result = await PropertyService.toggleFeaturedStatus(
      property.id,
      !property.isFeatured,
    );
    if (result.isSuccess) {
      ref.refresh(adminPropertiesProvider);
      ref.refresh(propertiesProvider);
      ref.refresh(featuredPropertiesProvider);
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            property.isFeatured
                ? 'Property removed from featured'
                : 'Property marked as featured',
          ),
        ),
      );
    } else {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to update featured status: ${result.message}'),
        ),
      );
    }
  }
}
