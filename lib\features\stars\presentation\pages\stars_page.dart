import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/star_providers.dart';
import '../widgets/star_card.dart';
import '../../../../core/models/star_model.dart';
import '../../../../core/services/star_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

class StarsPage extends ConsumerStatefulWidget {
  const StarsPage({super.key});

  @override
  ConsumerState<StarsPage> createState() => _StarsPageState();
}

class _StarsPageState extends ConsumerState<StarsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 4, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final isAdmin = currentUser?.isAdmin ?? false;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Stars & Rewards'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          IconButton(
            icon: const Icon(Icons.leaderboard),
            onPressed: () => _showLeaderboard(context),
          ),
        ],
        bottom: TabBar(
          controller: _tabController,
          isScrollable: true,
          tabs: [
            const Tab(icon: Icon(Icons.star), text: 'My Stars'),
            const Tab(icon: Icon(Icons.trending_up), text: 'Progress'),
            const Tab(icon: Icon(Icons.card_giftcard), text: 'Bonuses'),
            if (isAdmin)
              const Tab(icon: Icon(Icons.admin_panel_settings), text: 'Admin'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // My Stars Tab
          _buildMyStarsTab(),

          // Progress Tab
          _buildProgressTab(),

          // Bonuses Tab
          _buildBonusesTab(),

          // Admin Tab (only for admins)
          if (isAdmin) _buildAdminTab(),
        ],
      ),
    );
  }

  Widget _buildMyStarsTab() {
    final starsAsync = ref.watch(filteredStarHistoryProvider);
    final statsAsync = ref.watch(agentStarStatsProvider);

    return Column(
      children: [
        // Star Statistics
        statsAsync.when(
          data: (stats) => StarStatsWidget(stats: stats),
          loading: () => const LinearProgressIndicator(),
          error: (_, __) => const SizedBox.shrink(),
        ),

        // Star History
        Expanded(
          child: starsAsync.when(
            data: (stars) {
              if (stars.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.star_outline, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No stars earned yet'),
                      Text('Complete property sales to earn stars'),
                    ],
                  ),
                );
              }

              return RefreshIndicator(
                onRefresh: () async {
                  ref.invalidate(filteredStarHistoryProvider);
                  ref.invalidate(agentStarStatsProvider);
                },
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: stars.length,
                  itemBuilder: (context, index) {
                    final star = stars[index];
                    return StarCard(
                      star: star,
                      onTap: () => _showStarDetails(context, star),
                    );
                  },
                ),
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, stack) => Center(
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  const Icon(Icons.error, size: 64, color: Colors.red),
                  const SizedBox(height: 16),
                  Text('Error loading stars: $error'),
                  const SizedBox(height: 16),
                  ElevatedButton(
                    onPressed: () => ref.refresh(filteredStarHistoryProvider),
                    child: const Text('Retry'),
                  ),
                ],
              ),
            ),
          ),
        ),
      ],
    );
  }

  Widget _buildProgressTab() {
    final milestoneAsync = ref.watch(starMilestoneProgressProvider);
    final achievementsAsync = ref.watch(starAchievementsProvider);
    final trendsAsync = ref.watch(monthlyStarTrendsProvider);

    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Milestone Progress
          milestoneAsync.when(
            data: (milestone) => StarProgressWidget(
              totalStars: milestone['totalStars'] ?? 0,
              starsToNextBonus: milestone['starsToNextMilestone'] ?? 12,
              progressPercentage: milestone['progressPercentage'] ?? 0.0,
              nextMilestone: milestone['nextMilestone'] ?? 12,
            ),
            loading: () => const Card(child: LinearProgressIndicator()),
            error: (_, __) => const SizedBox.shrink(),
          ),

          const SizedBox(height: 16),

          // Achievements
          Text(
            'Achievements',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          achievementsAsync.when(
            data: (achievements) => _buildAchievementsList(achievements),
            loading: () => const CircularProgressIndicator(),
            error: (_, __) => const Text('Error loading achievements'),
          ),

          const SizedBox(height: 24),

          // Monthly Trends
          Text(
            'Monthly Star Trends',
            style: Theme.of(
              context,
            ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
          ),
          const SizedBox(height: 8),
          trendsAsync.when(
            data: (trends) => _buildMonthlyTrends(trends),
            loading: () => const CircularProgressIndicator(),
            error: (_, __) => const Text('Error loading trends'),
          ),
        ],
      ),
    );
  }

  Widget _buildBonusesTab() {
    final bonusesAsync = ref.watch(agentStarBonusesProvider);

    return bonusesAsync.when(
      data: (bonuses) {
        if (bonuses.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.card_giftcard, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No star bonuses yet'),
                Text('Reach 12-star milestones to earn bonuses'),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.symmetric(vertical: 8),
          itemCount: bonuses.length,
          itemBuilder: (context, index) {
            final bonus = bonuses[index];
            return StarBonusCard(
              bonus: bonus,
              onTap: () => _showBonusDetails(context, bonus),
            );
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildAdminTab() {
    final allBonusesAsync = ref.watch(allStarBonusesProvider);
    final leaderboardAsync = ref.watch(starLeaderboardProvider);

    return Column(
      children: [
        // Admin Statistics
        Card(
          margin: const EdgeInsets.all(16),
          child: Padding(
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  'Star System Overview',
                  style: Theme.of(context).textTheme.titleMedium?.copyWith(
                    fontWeight: FontWeight.bold,
                  ),
                ),
                const SizedBox(height: 16),
                leaderboardAsync.when(
                  data: (leaderboard) => _buildAdminStats(leaderboard),
                  loading: () => const CircularProgressIndicator(),
                  error: (_, __) => const Text('Error loading stats'),
                ),
              ],
            ),
          ),
        ),

        // Pending Bonuses
        Expanded(
          child: allBonusesAsync.when(
            data: (bonuses) {
              final pendingBonuses = bonuses.where((b) => b.isPending).toList();

              if (pendingBonuses.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.check_circle, size: 64, color: Colors.green),
                      SizedBox(height: 16),
                      Text('No pending bonuses'),
                      Text('All star bonuses have been processed'),
                    ],
                  ),
                );
              }

              return Column(
                crossAxisAlignment: CrossAxisAlignment.start,
                children: [
                  Padding(
                    padding: const EdgeInsets.symmetric(horizontal: 16),
                    child: Text(
                      'Pending Star Bonuses (${pendingBonuses.length})',
                      style: Theme.of(context).textTheme.titleMedium?.copyWith(
                        fontWeight: FontWeight.bold,
                      ),
                    ),
                  ),
                  const SizedBox(height: 8),
                  Expanded(
                    child: ListView.builder(
                      itemCount: pendingBonuses.length,
                      itemBuilder: (context, index) {
                        final bonus = pendingBonuses[index];
                        return StarBonusCard(
                          bonus: bonus,
                          showAdminActions: true,
                          onTap: () => _showBonusDetails(context, bonus),
                          onAward: (bonus) => _awardBonus(bonus),
                        );
                      },
                    ),
                  ),
                ],
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => Center(child: Text('Error: $error')),
          ),
        ),
      ],
    );
  }

  Widget _buildAchievementsList(List<Map<String, dynamic>> achievements) {
    return GridView.builder(
      shrinkWrap: true,
      physics: const NeverScrollableScrollPhysics(),
      gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
        crossAxisCount: 2,
        crossAxisSpacing: 8,
        mainAxisSpacing: 8,
        childAspectRatio: 1.5,
      ),
      itemCount: achievements.length,
      itemBuilder: (context, index) {
        final achievement = achievements[index];
        final isUnlocked = achievement['isUnlocked'] as bool;
        final progress = achievement['progress'] as double;

        return Card(
          color: isUnlocked ? Colors.amber.withValues(alpha: 0.1) : null,
          child: Padding(
            padding: const EdgeInsets.all(8),
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Text(
                  achievement['icon'] as String,
                  style: TextStyle(
                    fontSize: 24,
                    color: isUnlocked ? Colors.amber : Colors.grey,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  achievement['title'] as String,
                  style: TextStyle(
                    fontWeight: FontWeight.bold,
                    color: isUnlocked ? Colors.amber : Colors.grey,
                    fontSize: 12,
                  ),
                  textAlign: TextAlign.center,
                ),
                const SizedBox(height: 2),
                Text(
                  achievement['description'] as String,
                  style: const TextStyle(fontSize: 10),
                  textAlign: TextAlign.center,
                  maxLines: 2,
                  overflow: TextOverflow.ellipsis,
                ),
                if (!isUnlocked) ...[
                  const SizedBox(height: 4),
                  LinearProgressIndicator(
                    value: progress,
                    backgroundColor: Colors.grey[300],
                    valueColor: const AlwaysStoppedAnimation<Color>(
                      Colors.amber,
                    ),
                  ),
                ],
              ],
            ),
          ),
        );
      },
    );
  }

  Widget _buildMonthlyTrends(Map<String, int> trends) {
    if (trends.isEmpty) {
      return const Card(
        child: Padding(
          padding: EdgeInsets.all(16),
          child: Text('No trend data available'),
        ),
      );
    }

    final sortedEntries = trends.entries.toList()
      ..sort((a, b) => a.key.compareTo(b.key));

    return Card(
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          children: sortedEntries.map((entry) {
            final monthYear = entry.key;
            final starCount = entry.value;

            return Padding(
              padding: const EdgeInsets.symmetric(vertical: 4),
              child: Row(
                children: [
                  SizedBox(
                    width: 80,
                    child: Text(
                      monthYear,
                      style: const TextStyle(fontSize: 12),
                    ),
                  ),
                  Expanded(
                    child: LinearProgressIndicator(
                      value:
                          starCount /
                          (trends.values.reduce((a, b) => a > b ? a : b)),
                      backgroundColor: Colors.grey[300],
                      valueColor: const AlwaysStoppedAnimation<Color>(
                        Colors.amber,
                      ),
                    ),
                  ),
                  const SizedBox(width: 8),
                  Text(
                    '$starCount',
                    style: const TextStyle(fontWeight: FontWeight.bold),
                  ),
                ],
              ),
            );
          }).toList(),
        ),
      ),
    );
  }

  Widget _buildAdminStats(List<Map<String, dynamic>> leaderboard) {
    final totalAgents = leaderboard.length;
    final totalStars = leaderboard.fold<int>(
      0,
      (sum, agent) => sum + (agent['totalStars'] as int),
    );
    final avgStars = totalAgents > 0 ? totalStars / totalAgents : 0.0;

    return Row(
      children: [
        Expanded(
          child: _buildStatCard('Total Agents', '$totalAgents', Icons.people),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard('Total Stars', '$totalStars', Icons.star),
        ),
        const SizedBox(width: 16),
        Expanded(
          child: _buildStatCard(
            'Avg Stars',
            avgStars.toStringAsFixed(1),
            Icons.analytics,
          ),
        ),
      ],
    );
  }

  Widget _buildStatCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.blue.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.blue.withValues(alpha: 0.3)),
      ),
      child: Column(
        children: [
          Icon(icon, color: Colors.blue, size: 20),
          const SizedBox(height: 4),
          Text(
            value,
            style: const TextStyle(
              fontWeight: FontWeight.bold,
              color: Colors.blue,
              fontSize: 16,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 10),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Stars'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Star Type Filter
              Consumer(
                builder: (context, ref, child) {
                  final filter = ref.watch(starFilterProvider);
                  return DropdownButtonFormField<String?>(
                    value: filter.type,
                    decoration: const InputDecoration(labelText: 'Star Type'),
                    items: const [
                      DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Types'),
                      ),
                      DropdownMenuItem(
                        value: 'sale',
                        child: Text('Direct Sale'),
                      ),
                      DropdownMenuItem(
                        value: 'upline_bonus',
                        child: Text('Upline Bonus'),
                      ),
                      DropdownMenuItem(
                        value: 'first_sale_bonus',
                        child: Text('First Sale Bonus'),
                      ),
                    ],
                    onChanged: (value) {
                      ref.read(starFilterProvider.notifier).setType(value);
                    },
                  );
                },
              ),

              const SizedBox(height: 16),

              // Star Source Filter
              Consumer(
                builder: (context, ref, child) {
                  final filter = ref.watch(starFilterProvider);
                  return DropdownButtonFormField<String?>(
                    value: filter.source,
                    decoration: const InputDecoration(labelText: 'Star Source'),
                    items: const [
                      DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Sources'),
                      ),
                      DropdownMenuItem(
                        value: 'direct_sale',
                        child: Text('Direct Sale'),
                      ),
                      DropdownMenuItem(
                        value: 'first_downline_sale',
                        child: Text('First Downline Sale'),
                      ),
                    ],
                    onChanged: (value) {
                      ref.read(starFilterProvider.notifier).setSource(value);
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(starFilterProvider.notifier).resetFilters();
              Navigator.of(context).pop();
            },
            child: const Text('Reset'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _showLeaderboard(BuildContext context) {
    final leaderboardAsync = ref.watch(starLeaderboardProvider);

    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 16),

              Text(
                'Star Leaderboard',
                style: Theme.of(
                  context,
                ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
              ),

              const SizedBox(height: 16),

              Expanded(
                child: leaderboardAsync.when(
                  data: (leaderboard) {
                    if (leaderboard.isEmpty) {
                      return const Center(child: Text('No data available'));
                    }

                    return ListView.builder(
                      controller: scrollController,
                      itemCount: leaderboard.length,
                      itemBuilder: (context, index) {
                        final entry = leaderboard[index];
                        final agent = entry['agent'];
                        final rank = index + 1;

                        return ListTile(
                          leading: CircleAvatar(
                            backgroundColor: _getRankColor(rank),
                            child: Text(
                              '$rank',
                              style: const TextStyle(
                                color: Colors.white,
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                          title: Text(agent.name),
                          subtitle: Text('Level ${agent.level}'),
                          trailing: Column(
                            mainAxisAlignment: MainAxisAlignment.center,
                            children: [
                              Row(
                                mainAxisSize: MainAxisSize.min,
                                children: [
                                  const Icon(
                                    Icons.star,
                                    size: 16,
                                    color: Colors.amber,
                                  ),
                                  const SizedBox(width: 4),
                                  Text(
                                    '${agent.totalStars}',
                                    style: const TextStyle(
                                      fontWeight: FontWeight.bold,
                                    ),
                                  ),
                                ],
                              ),
                              Text(
                                '₹${agent.totalCommissions.toStringAsFixed(0)}',
                                style: const TextStyle(fontSize: 12),
                              ),
                            ],
                          ),
                        );
                      },
                    );
                  },
                  loading: () =>
                      const Center(child: CircularProgressIndicator()),
                  error: (error, _) => Center(child: Text('Error: $error')),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Color _getRankColor(int rank) {
    switch (rank) {
      case 1:
        return Colors.amber; // Gold
      case 2:
        return Colors.grey; // Silver
      case 3:
        return Colors.brown; // Bronze
      default:
        return Colors.blue;
    }
  }

  void _showStarDetails(BuildContext context, StarModel star) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Star Details',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildDetailRow('Type', star.typeDescription, Icons.star),
            _buildDetailRow('Source', star.sourceDescription, Icons.source),
            _buildDetailRow('Count', '${star.count}', Icons.numbers),
            if (star.propertyTitle != null)
              _buildDetailRow('Property', star.propertyTitle!, Icons.home_work),
            _buildDetailRow(
              'Earned At',
              _formatDateTime(star.earnedAt),
              Icons.calendar_today,
            ),
            if (star.notes != null && star.notes!.isNotEmpty)
              _buildDetailRow('Notes', star.notes!, Icons.note),
          ],
        ),
      ),
    );
  }

  void _showBonusDetails(BuildContext context, StarBonusModel bonus) {
    showModalBottomSheet(
      context: context,
      builder: (context) => Container(
        padding: const EdgeInsets.all(16),
        child: Column(
          mainAxisSize: MainAxisSize.min,
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Star Bonus Details',
              style: Theme.of(
                context,
              ).textTheme.titleLarge?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            _buildDetailRow(
              'Amount',
              bonus.formattedBonusAmount,
              Icons.currency_rupee,
            ),
            _buildDetailRow('Star Count', '${bonus.starCount}', Icons.star),
            _buildDetailRow('Status', bonus.status.toUpperCase(), Icons.info),
            _buildDetailRow(
              'Eligible At',
              _formatDate(bonus.eligibleAt),
              Icons.calendar_today,
            ),
            if (bonus.awardedAt != null)
              _buildDetailRow(
                'Awarded At',
                _formatDate(bonus.awardedAt!),
                Icons.payment,
              ),
            if (bonus.awardedBy != null)
              _buildDetailRow('Awarded By', bonus.awardedBy!, Icons.person),
            if (bonus.notes != null && bonus.notes!.isNotEmpty)
              _buildDetailRow('Notes', bonus.notes!, Icons.note),
          ],
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.w500)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  String _formatDateTime(DateTime dateTime) {
    return '${dateTime.day}/${dateTime.month}/${dateTime.year} ${dateTime.hour}:${dateTime.minute.toString().padLeft(2, '0')}';
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _awardBonus(StarBonusModel bonus) async {
    final currentUser = ref.read(currentUserProvider);
    if (currentUser == null) return;

    final confirmed = await showDialog<bool>(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Award Star Bonus'),
        content: Text(
          'Award ${bonus.formattedBonusAmount} to ${bonus.agentName}?',
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(false),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(true),
            child: const Text('Award'),
          ),
        ],
      ),
    );

    if (confirmed == true) {
      final result = await StarService.awardStarBonus(bonus.id, currentUser.id);

      if (result.isSuccess) {
        ref.invalidate(allStarBonusesProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Star bonus awarded successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text('Failed to award bonus: ${result.message}'),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}
