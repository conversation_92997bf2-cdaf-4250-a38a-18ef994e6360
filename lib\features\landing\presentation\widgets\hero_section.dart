import 'package:flutter/material.dart';
import 'package:flutter_animate/flutter_animate.dart';
import '../../../../shared/themes/app_theme.dart';

/// Hero section with main value proposition
class HeroSection extends StatelessWidget {
  final VoidCallback? onExploreProperties;
  final VoidCallback? onBecomeAgent;

  const HeroSection({
    super.key,
    this.onExploreProperties,
    this.onBecomeAgent,
  });

  @override
  Widget build(BuildContext context) {
    final screenWidth = MediaQuery.of(context).size.width;
    final isMobile = screenWidth < 768;
    final isTablet = screenWidth < 1024;

    return Container(
      height: isMobile ? 500 : (isTablet ? 550 : 600),
      decoration: BoxDecoration(
        gradient: LinearGradient(
          begin: Alignment.topCenter,
          end: Alignment.bottomCenter,
          colors: [
            AppTheme.backgroundColor,
            AppTheme.backgroundColor.withOpacity(0.8),
            AppTheme.cardColor.withOpacity(0.3),
          ],
        ),
      ),
      child: Stack(
        children: [
          // Background Pattern
          Positioned.fill(
            child: Opacity(
              opacity: 0.1,
              child: Container(
                decoration: const BoxDecoration(
                  image: DecorationImage(
                    image: NetworkImage(
                      'https://images.unsplash.com/photo-1560518883-ce09059eeffa?ixlib=rb-4.0.3&ixid=M3wxMjA3fDB8MHxwaG90by1wYWdlfHx8fGVufDB8fHx8fA%3D%3D&auto=format&fit=crop&w=1000&q=80',
                    ),
                    fit: BoxFit.cover,
                  ),
                ),
              ),
            ),
          ),
          
          // Content
          Center(
            child: Padding(
              padding: EdgeInsets.symmetric(horizontal: isMobile ? 16 : 20),
              child: Column(
                mainAxisAlignment: MainAxisAlignment.center,
                children: [
                  // Main Heading
                  Text(
                    '🏡 Premium Real Estate Network',
                    style: TextStyle(
                      fontSize: isMobile ? 28 : (isTablet ? 36 : 48),
                      fontWeight: FontWeight.bold,
                      color: Colors.white,
                      shadows: [
                        Shadow(
                          offset: const Offset(0, 2),
                          blurRadius: 4,
                          color: Colors.black.withOpacity(0.5),
                        ),
                      ],
                    ),
                    textAlign: TextAlign.center,
                  )
                    .animate()
                    .fadeIn(duration: 800.ms)
                    .slideY(begin: -0.3, end: 0),

                  SizedBox(height: isMobile ? 16 : 20),

                  // Subtitle
                  Text(
                    '"Build Your Wealth Through Smart Property Deals"',
                    style: TextStyle(
                      fontSize: isMobile ? 16 : (isTablet ? 20 : 24),
                      color: Colors.white.withOpacity(0.9),
                      fontStyle: FontStyle.italic,
                    ),
                    textAlign: TextAlign.center,
                  )
                    .animate(delay: 400.ms)
                    .fadeIn(duration: 800.ms)
                    .slideY(begin: 0.3, end: 0),
                  
                  SizedBox(height: isMobile ? 30 : 40),

                  // Action Buttons
                  if (isMobile)
                    // Mobile: Stack buttons vertically
                    Column(
                      children: [
                        SizedBox(
                          width: double.infinity,
                          child: ElevatedButton(
                            onPressed: onExploreProperties,
                            style: ElevatedButton.styleFrom(
                              backgroundColor: AppTheme.primaryColor,
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 14,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'Explore Properties',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                              ),
                            ),
                          ),
                        )
                          .animate(delay: 800.ms)
                          .fadeIn(duration: 600.ms)
                          .scale(begin: const Offset(0.8, 0.8)),

                        const SizedBox(height: 12),

                        SizedBox(
                          width: double.infinity,
                          child: OutlinedButton(
                            onPressed: onBecomeAgent,
                            style: OutlinedButton.styleFrom(
                              side: const BorderSide(
                                color: AppTheme.primaryColor,
                                width: 2,
                              ),
                              padding: const EdgeInsets.symmetric(
                                horizontal: 24,
                                vertical: 14,
                              ),
                              shape: RoundedRectangleBorder(
                                borderRadius: BorderRadius.circular(12),
                              ),
                            ),
                            child: const Text(
                              'Become an Agent',
                              style: TextStyle(
                                fontSize: 16,
                                fontWeight: FontWeight.w600,
                                color: AppTheme.primaryColor,
                              ),
                            ),
                          ),
                        )
                          .animate(delay: 1000.ms)
                          .fadeIn(duration: 600.ms)
                          .scale(begin: const Offset(0.8, 0.8)),
                      ],
                    )
                  else
                    // Desktop/Tablet: Side by side buttons
                    Row(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        ElevatedButton(
                          onPressed: onExploreProperties,
                          style: ElevatedButton.styleFrom(
                            backgroundColor: AppTheme.primaryColor,
                            padding: EdgeInsets.symmetric(
                              horizontal: isTablet ? 24 : 32,
                              vertical: isTablet ? 14 : 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            'Explore Properties',
                            style: TextStyle(
                              fontSize: isTablet ? 16 : 18,
                              fontWeight: FontWeight.w600,
                            ),
                          ),
                        )
                          .animate(delay: 800.ms)
                          .fadeIn(duration: 600.ms)
                          .scale(begin: const Offset(0.8, 0.8)),

                        const SizedBox(width: 20),

                        OutlinedButton(
                          onPressed: onBecomeAgent,
                          style: OutlinedButton.styleFrom(
                            side: const BorderSide(
                              color: AppTheme.primaryColor,
                              width: 2,
                            ),
                            padding: EdgeInsets.symmetric(
                              horizontal: isTablet ? 24 : 32,
                              vertical: isTablet ? 14 : 16,
                            ),
                            shape: RoundedRectangleBorder(
                              borderRadius: BorderRadius.circular(12),
                            ),
                          ),
                          child: Text(
                            'Become an Agent',
                            style: TextStyle(
                              fontSize: isTablet ? 16 : 18,
                              fontWeight: FontWeight.w600,
                              color: AppTheme.primaryColor,
                            ),
                          ),
                        )
                          .animate(delay: 1000.ms)
                          .fadeIn(duration: 600.ms)
                          .scale(begin: const Offset(0.8, 0.8)),
                      ],
                    ),
                  
                  SizedBox(height: isMobile ? 40 : 60),

                  // Stats Row
                  Container(
                    padding: EdgeInsets.all(isMobile ? 16 : 24),
                    decoration: BoxDecoration(
                      color: Colors.black.withOpacity(0.3),
                      borderRadius: BorderRadius.circular(16),
                      border: Border.all(
                        color: AppTheme.primaryColor.withOpacity(0.3),
                      ),
                    ),
                    child: isMobile
                        ? Column(
                            children: [
                              Row(
                                mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                                children: [
                                  _buildStatItem('500+', 'Properties', Icons.home, isMobile),
                                  _buildStatItem('1000+', 'Agents', Icons.people, isMobile),
                                ],
                              ),
                              const SizedBox(height: 20),
                              _buildStatItem('₹50Cr+', 'Sales', Icons.trending_up, isMobile),
                            ],
                          )
                        : Row(
                            mainAxisAlignment: MainAxisAlignment.spaceEvenly,
                            children: [
                              _buildStatItem('500+', 'Properties', Icons.home, isMobile),
                              _buildDivider(),
                              _buildStatItem('1000+', 'Agents', Icons.people, isMobile),
                              _buildDivider(),
                              _buildStatItem('₹50Cr+', 'Sales', Icons.trending_up, isMobile),
                            ],
                          ),
                  )
                    .animate(delay: 1200.ms)
                    .fadeIn(duration: 800.ms)
                    .slideY(begin: 0.3, end: 0),
                ],
              ),
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildStatItem(String number, String label, IconData icon, bool isMobile) {
    return Column(
      children: [
        Icon(
          icon,
          color: AppTheme.primaryColor,
          size: isMobile ? 24 : 32,
        ),
        SizedBox(height: isMobile ? 6 : 8),
        Text(
          number,
          style: TextStyle(
            fontSize: isMobile ? 20 : 28,
            fontWeight: FontWeight.bold,
            color: Colors.white,
          ),
        ),
        Text(
          label,
          style: TextStyle(
            fontSize: isMobile ? 12 : 14,
            color: Colors.white.withOpacity(0.7),
          ),
          textAlign: TextAlign.center,
        ),
      ],
    );
  }

  Widget _buildDivider() {
    return Container(
      height: 60,
      width: 1,
      color: Colors.white.withOpacity(0.3),
    );
  }
}
