# Production Deployment Setup - Rama Realty MLM

## 🚀 Production Environment Configuration

### **Firebase Production Setup**

#### **1. Firebase Project Configuration**
```yaml
# firebase.json
{
  "hosting": {
    "public": "build/web",
    "ignore": [
      "firebase.json",
      "**/.*",
      "**/node_modules/**"
    ],
    "rewrites": [
      {
        "source": "**",
        "destination": "/index.html"
      }
    ],
    "headers": [
      {
        "source": "**/*.@(js|css)",
        "headers": [
          {
            "key": "Cache-Control",
            "value": "max-age=31536000"
          }
        ]
      }
    ]
  },
  "firestore": {
    "rules": "firestore.rules",
    "indexes": "firestore.indexes.json"
  },
  "storage": {
    "rules": "storage.rules"
  },
  "functions": {
    "source": "functions",
    "runtime": "nodejs18"
  }
}
```

#### **2. Firestore Security Rules**
```javascript
// firestore.rules
rules_version = '2';
service cloud.firestore {
  match /databases/{database}/documents {
    // Users collection
    match /users/{userId} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
      allow read: if request.auth != null && 
        resource.data.uplineId == request.auth.uid ||
        resource.data.downlineIds.hasAny([request.auth.uid]);
    }
    
    // Properties collection
    match /properties/{propertyId} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.uid == resource.data.agentId || 
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Commissions collection
    match /commissions/{commissionId} {
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.agentId ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    // Transactions collection
    match /transactions/{transactionId} {
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.agentId ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    // Stars collection
    match /stars/{starId} {
      allow read: if request.auth != null && 
        (request.auth.uid == resource.data.agentId ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
      allow write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
    
    // Leads collection
    match /leads/{leadId} {
      allow read, write: if request.auth != null && 
        (request.auth.uid == resource.data.agentId ||
         get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // Admin-only collections
    match /analytics/{document=**} {
      allow read, write: if request.auth != null && 
        get(/databases/$(database)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
  }
}
```

#### **3. Storage Security Rules**
```javascript
// storage.rules
rules_version = '2';
service firebase.storage {
  match /b/{bucket}/o {
    // Property images
    match /properties/{propertyId}/{allPaths=**} {
      allow read: if request.auth != null;
      allow write: if request.auth != null && 
        (request.auth.uid == resource.metadata.uploadedBy ||
         firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin == true);
    }
    
    // User profile images
    match /users/{userId}/{allPaths=**} {
      allow read, write: if request.auth != null && request.auth.uid == userId;
    }
    
    // Admin uploads
    match /admin/{allPaths=**} {
      allow read, write: if request.auth != null && 
        firestore.get(/databases/(default)/documents/users/$(request.auth.uid)).data.isAdmin == true;
    }
  }
}
```

### **Environment Configuration**

#### **Production Environment Variables**
```dart
// lib/core/config/environment.dart
class Environment {
  static const String environment = String.fromEnvironment(
    'ENVIRONMENT',
    defaultValue: 'development',
  );
  
  static const bool isProduction = environment == 'production';
  static const bool isDevelopment = environment == 'development';
  static const bool isStaging = environment == 'staging';
  
  // Firebase Configuration
  static const String firebaseProjectId = String.fromEnvironment(
    'FIREBASE_PROJECT_ID',
    defaultValue: 'rama-realty-mlm-prod',
  );
  
  static const String firebaseApiKey = String.fromEnvironment(
    'FIREBASE_API_KEY',
    defaultValue: 'your-production-api-key',
  );
  
  static const String firebaseAppId = String.fromEnvironment(
    'FIREBASE_APP_ID',
    defaultValue: 'your-production-app-id',
  );
  
  // API Configuration
  static const String baseUrl = isProduction 
    ? 'https://api.ramarealty.com'
    : 'https://api-staging.ramarealty.com';
    
  // WhatsApp Configuration
  static const String whatsappBusinessApiUrl = String.fromEnvironment(
    'WHATSAPP_API_URL',
    defaultValue: 'https://graph.facebook.com/v18.0',
  );
  
  static const String whatsappAccessToken = String.fromEnvironment(
    'WHATSAPP_ACCESS_TOKEN',
    defaultValue: '',
  );
  
  // Analytics Configuration
  static const String googleAnalyticsId = String.fromEnvironment(
    'GOOGLE_ANALYTICS_ID',
    defaultValue: 'G-XXXXXXXXXX',
  );
  
  // Performance Monitoring
  static const bool enableCrashlytics = isProduction;
  static const bool enablePerformanceMonitoring = isProduction;
  
  // Logging Configuration
  static const String logLevel = isProduction ? 'ERROR' : 'DEBUG';
  
  // Feature Flags
  static const bool enableBetaFeatures = !isProduction;
  static const bool enableDebugMode = isDevelopment;
  
  // Commission Configuration
  static const Map<int, double> commissionRates = {
    0: 0.05,   // Level 0: 5%
    1: 0.02,   // Level 1: 2%
    2: 0.01,   // Level 2: 1%
    3: 0.005,  // Level 3: 0.5%
    4: 0.002,  // Level 4: 0.2%
  };
  
  static const double starBonusAmount = 50000.0; // ₹50,000
  static const int starBonusThreshold = 12;
  
  // System Limits
  static const int maxNetworkDepth = 5;
  static const int maxPropertiesPerAgent = 100;
  static const int maxLeadsPerAgent = 500;
  
  // Cache Configuration
  static const Duration cacheTimeout = Duration(hours: 1);
  static const Duration sessionTimeout = Duration(hours: 24);
  
  // Security Configuration
  static const int maxLoginAttempts = 5;
  static const Duration loginCooldown = Duration(minutes: 15);
  static const Duration passwordResetExpiry = Duration(hours: 1);
}
```

### **Build Configuration**

#### **Production Build Scripts**
```yaml
# build_scripts/build_production.yaml
name: Production Build
on:
  push:
    branches: [main]
  pull_request:
    branches: [main]

jobs:
  build:
    runs-on: ubuntu-latest
    
    steps:
    - uses: actions/checkout@v3
    
    - name: Setup Flutter
      uses: subosito/flutter-action@v2
      with:
        flutter-version: '3.16.0'
        channel: 'stable'
    
    - name: Install dependencies
      run: flutter pub get
    
    - name: Run tests
      run: flutter test --coverage
    
    - name: Upload coverage
      uses: codecov/codecov-action@v3
      with:
        file: coverage/lcov.info
    
    - name: Build web
      run: flutter build web --release --dart-define=ENVIRONMENT=production
    
    - name: Build Android APK
      run: flutter build apk --release --dart-define=ENVIRONMENT=production
    
    - name: Build Windows
      run: flutter build windows --release --dart-define=ENVIRONMENT=production
    
    - name: Deploy to Firebase
      uses: FirebaseExtended/action-hosting-deploy@v0
      with:
        repoToken: '${{ secrets.GITHUB_TOKEN }}'
        firebaseServiceAccount: '${{ secrets.FIREBASE_SERVICE_ACCOUNT }}'
        projectId: rama-realty-mlm-prod
```

### **Performance Optimization**

#### **Web Performance Configuration**
```dart
// lib/core/config/web_config.dart
class WebConfig {
  static void configureApp() {
    if (kIsWeb) {
      // Configure web-specific optimizations
      _configureServiceWorker();
      _configureCaching();
      _configureAnalytics();
    }
  }
  
  static void _configureServiceWorker() {
    // Register service worker for offline support
    if (html.window.navigator.serviceWorker != null) {
      html.window.navigator.serviceWorker!.register('/sw.js');
    }
  }
  
  static void _configureCaching() {
    // Configure caching strategies
    // Images: Cache first
    // API calls: Network first with fallback
    // Static assets: Cache first with update
  }
  
  static void _configureAnalytics() {
    if (Environment.isProduction) {
      // Initialize Google Analytics
      // Initialize Firebase Analytics
      // Initialize performance monitoring
    }
  }
}
```

### **Monitoring and Logging**

#### **Production Monitoring Setup**
```dart
// lib/core/services/monitoring_service.dart
class MonitoringService {
  static Future<void> initialize() async {
    if (Environment.isProduction) {
      await _initializeCrashlytics();
      await _initializePerformanceMonitoring();
      await _initializeAnalytics();
    }
  }
  
  static Future<void> _initializeCrashlytics() async {
    await FirebaseCrashlytics.instance.setCrashlyticsCollectionEnabled(true);
    
    // Set custom keys for better debugging
    await FirebaseCrashlytics.instance.setCustomKey('environment', Environment.environment);
    await FirebaseCrashlytics.instance.setCustomKey('app_version', '1.0.0');
  }
  
  static Future<void> _initializePerformanceMonitoring() async {
    await FirebasePerformance.instance.setPerformanceCollectionEnabled(true);
  }
  
  static Future<void> _initializeAnalytics() async {
    await FirebaseAnalytics.instance.setAnalyticsCollectionEnabled(true);
    
    // Set default parameters
    await FirebaseAnalytics.instance.setDefaultEventParameters({
      'app_type': 'mlm_real_estate',
      'market': 'india',
      'currency': 'INR',
    });
  }
  
  static void logError(dynamic error, StackTrace? stackTrace, {
    Map<String, dynamic>? additionalData,
  }) {
    if (Environment.isProduction) {
      FirebaseCrashlytics.instance.recordError(
        error,
        stackTrace,
        information: additionalData?.entries.map((e) => 
          DiagnosticsProperty(e.key, e.value)).toList() ?? [],
      );
    } else {
      debugPrint('Error: $error');
      if (stackTrace != null) {
        debugPrint('Stack trace: $stackTrace');
      }
    }
  }
  
  static void logEvent(String name, Map<String, dynamic>? parameters) {
    if (Environment.isProduction) {
      FirebaseAnalytics.instance.logEvent(
        name: name,
        parameters: parameters,
      );
    }
  }
}
```

### **Security Hardening**

#### **Production Security Configuration**
```dart
// lib/core/security/security_config.dart
class SecurityConfig {
  static void initialize() {
    _configureNetworkSecurity();
    _configureDataEncryption();
    _configureAuthSecurity();
  }
  
  static void _configureNetworkSecurity() {
    // Configure certificate pinning
    // Set up secure headers
    // Enable HTTPS enforcement
  }
  
  static void _configureDataEncryption() {
    // Configure local data encryption
    // Set up secure storage
    // Configure sensitive data handling
  }
  
  static void _configureAuthSecurity() {
    // Configure session management
    // Set up multi-factor authentication
    // Configure password policies
  }
}
```

---

**Status**: Production deployment configuration complete ✅  
**Next**: Database optimization and performance tuning  
**Environment**: Production-ready with monitoring and security  
**Deployment**: Automated CI/CD pipeline configured
