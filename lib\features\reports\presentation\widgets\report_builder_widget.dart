import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../../commissions/models/commission_enums.dart';
import '../../models/report_models.dart';
import '../../services/report_service.dart';

/// Report builder widget for creating custom reports
class ReportBuilderWidget extends ConsumerStatefulWidget {
  final String? agentId;
  final Function(GeneratedReport)? onReportGenerated;

  const ReportBuilderWidget({super.key, this.agentId, this.onReportGenerated});

  @override
  ConsumerState<ReportBuilderWidget> createState() =>
      _ReportBuilderWidgetState();
}

class _ReportBuilderWidgetState extends ConsumerState<ReportBuilderWidget>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  // Form controllers
  final _nameController = TextEditingController();
  final _descriptionController = TextEditingController();

  // Report configuration
  ReportType _selectedType = ReportType.sales;
  ReportPeriod _selectedPeriod = ReportPeriod.monthly;
  ReportFormat _selectedFormat = ReportFormat.pdf;
  DateTime _startDate = DateTime.now().subtract(const Duration(days: 30));
  DateTime _endDate = DateTime.now();
  List<String> _selectedMetrics = [];
  List<ReportFilter> _filters = [];
  bool _includeCharts = true;
  bool _includeComparisons = false;
  bool _isGenerating = false;

  List<ReportTemplate> _templates = [];

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
    _loadTemplates();
    _updateSelectedMetrics();
  }

  @override
  void dispose() {
    _tabController.dispose();
    _nameController.dispose();
    _descriptionController.dispose();
    super.dispose();
  }

  Future<void> _loadTemplates() async {
    final templates = await ReportService.getReportTemplates();
    setState(() {
      _templates = templates;
    });
  }

  void _updateSelectedMetrics() {
    setState(() {
      _selectedMetrics = _selectedType.availableMetrics.take(3).toList();
    });
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      children: [
        // Header
        _buildHeader(),

        const SizedBox(height: 16),

        // Tab Bar
        _buildTabBar(),

        // Tab Content
        Expanded(
          child: TabBarView(
            controller: _tabController,
            children: [
              _buildConfigurationTab(),
              _buildTemplatesTab(),
              _buildPreviewTab(),
            ],
          ),
        ),

        // Generate Button
        _buildGenerateButton(),
      ],
    );
  }

  /// Build header
  Widget _buildHeader() {
    return Padding(
      padding: const EdgeInsets.symmetric(horizontal: 16),
      child: Row(
        children: [
          Icon(Icons.analytics, color: AppTheme.primaryColor, size: 28),
          const SizedBox(width: 12),
          Text(
            'Report Builder',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 24,
              fontWeight: FontWeight.bold,
            ),
          ),
          const Spacer(),
          if (_isGenerating)
            SizedBox(
              width: 20,
              height: 20,
              child: CircularProgressIndicator(
                strokeWidth: 2,
                valueColor: AlwaysStoppedAnimation<Color>(
                  AppTheme.primaryColor,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build tab bar
  Widget _buildTabBar() {
    return Container(
      margin: const EdgeInsets.symmetric(horizontal: 16),
      decoration: BoxDecoration(
        color: AppTheme.darkCard,
        borderRadius: BorderRadius.circular(12),
      ),
      child: TabBar(
        controller: _tabController,
        indicator: BoxDecoration(
          gradient: AppTheme.primaryGradient,
          borderRadius: BorderRadius.circular(12),
        ),
        indicatorSize: TabBarIndicatorSize.tab,
        dividerColor: Colors.transparent,
        labelColor: Colors.white,
        unselectedLabelColor: AppTheme.darkSecondaryText,
        labelStyle: const TextStyle(fontSize: 14, fontWeight: FontWeight.w600),
        tabs: const [
          Tab(text: 'Configuration'),
          Tab(text: 'Templates'),
          Tab(text: 'Preview'),
        ],
      ),
    );
  }

  /// Build configuration tab
  Widget _buildConfigurationTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Basic Information
          _buildBasicInformation(),

          const SizedBox(height: 24),

          // Report Type Selection
          _buildReportTypeSelection(),

          const SizedBox(height: 24),

          // Period Selection
          _buildPeriodSelection(),

          const SizedBox(height: 24),

          // Metrics Selection
          _buildMetricsSelection(),

          const SizedBox(height: 24),

          // Format and Options
          _buildFormatAndOptions(),
        ],
      ),
    );
  }

  /// Build basic information section
  Widget _buildBasicInformation() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Basic Information',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // Report Name
          TextField(
            controller: _nameController,
            style: TextStyle(color: AppTheme.darkPrimaryText),
            decoration: InputDecoration(
              labelText: 'Report Name',
              labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
              hintText: 'Enter report name',
              hintStyle: TextStyle(color: AppTheme.darkHintText),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppTheme.darkBorder),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppTheme.darkBorder),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppTheme.primaryColor),
              ),
            ),
          ),

          const SizedBox(height: 16),

          // Report Description
          TextField(
            controller: _descriptionController,
            style: TextStyle(color: AppTheme.darkPrimaryText),
            maxLines: 3,
            decoration: InputDecoration(
              labelText: 'Description (Optional)',
              labelStyle: TextStyle(color: AppTheme.darkSecondaryText),
              hintText: 'Enter report description',
              hintStyle: TextStyle(color: AppTheme.darkHintText),
              border: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppTheme.darkBorder),
              ),
              enabledBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppTheme.darkBorder),
              ),
              focusedBorder: OutlineInputBorder(
                borderRadius: BorderRadius.circular(8),
                borderSide: BorderSide(color: AppTheme.primaryColor),
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build report type selection
  Widget _buildReportTypeSelection() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Report Type',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          Wrap(
            spacing: 12,
            runSpacing: 12,
            children: ReportType.values.map((type) {
              final isSelected = _selectedType == type;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedType = type;
                    _updateSelectedMetrics();
                  });
                },
                child: Container(
                  padding: const EdgeInsets.all(16),
                  decoration: BoxDecoration(
                    gradient: isSelected ? AppTheme.primaryGradient : null,
                    color: isSelected ? null : AppTheme.darkSurface,
                    borderRadius: BorderRadius.circular(12),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : AppTheme.darkBorder,
                    ),
                  ),
                  child: Column(
                    children: [
                      Icon(
                        type.icon,
                        color: isSelected ? Colors.white : type.color,
                        size: 32,
                      ),
                      const SizedBox(height: 8),
                      Text(
                        type.displayName,
                        style: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : AppTheme.darkPrimaryText,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                        textAlign: TextAlign.center,
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 12),

          Text(
            _selectedType.description,
            style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 14),
          ),
        ],
      ),
    );
  }

  /// Build period selection
  Widget _buildPeriodSelection() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Report Period',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // Period Chips
          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: ReportPeriod.values.map((period) {
              final isSelected = _selectedPeriod == period;
              return GestureDetector(
                onTap: () {
                  setState(() {
                    _selectedPeriod = period;
                    if (period != ReportPeriod.custom) {
                      final dateRange = period.getDateRange();
                      _startDate = dateRange.start;
                      _endDate = dateRange.end;
                    }
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 16,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: isSelected ? AppTheme.secondaryGradient : null,
                    color: isSelected ? null : AppTheme.darkSurface,
                    borderRadius: BorderRadius.circular(20),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.secondaryColor
                          : AppTheme.darkBorder,
                    ),
                  ),
                  child: Text(
                    period.displayName,
                    style: TextStyle(
                      color: isSelected
                          ? Colors.white
                          : AppTheme.darkPrimaryText,
                      fontSize: 12,
                      fontWeight: FontWeight.w600,
                    ),
                  ),
                ),
              );
            }).toList(),
          ),

          if (_selectedPeriod == ReportPeriod.custom) ...[
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildDatePicker(
                    'Start Date',
                    _startDate,
                    (date) => setState(() => _startDate = date),
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildDatePicker(
                    'End Date',
                    _endDate,
                    (date) => setState(() => _endDate = date),
                  ),
                ),
              ],
            ),
          ],
        ],
      ),
    );
  }

  /// Build date picker
  Widget _buildDatePicker(
    String label,
    DateTime date,
    Function(DateTime) onChanged,
  ) {
    return GestureDetector(
      onTap: () async {
        final selectedDate = await showDatePicker(
          context: context,
          initialDate: date,
          firstDate: DateTime.now().subtract(const Duration(days: 365)),
          lastDate: DateTime.now(),
        );
        if (selectedDate != null) {
          onChanged(selectedDate);
        }
      },
      child: Container(
        padding: const EdgeInsets.all(12),
        decoration: BoxDecoration(
          color: AppTheme.darkSurface,
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: AppTheme.darkBorder),
        ),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              label,
              style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 12),
            ),
            const SizedBox(height: 4),
            Text(
              '${date.day}/${date.month}/${date.year}',
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 14,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }

  /// Build metrics selection
  Widget _buildMetricsSelection() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Metrics to Include',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          Wrap(
            spacing: 8,
            runSpacing: 8,
            children: _selectedType.availableMetrics.map((metric) {
              final isSelected = _selectedMetrics.contains(metric);
              return GestureDetector(
                onTap: () {
                  setState(() {
                    if (isSelected) {
                      _selectedMetrics.remove(metric);
                    } else {
                      _selectedMetrics.add(metric);
                    }
                  });
                },
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 6,
                  ),
                  decoration: BoxDecoration(
                    gradient: isSelected ? AppTheme.primaryGradient : null,
                    color: isSelected ? null : AppTheme.darkSurface,
                    borderRadius: BorderRadius.circular(16),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.primaryColor
                          : AppTheme.darkBorder,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      if (isSelected)
                        const Icon(Icons.check, color: Colors.white, size: 14),
                      if (isSelected) const SizedBox(width: 4),
                      Text(
                        metric.replaceAll('_', ' ').toUpperCase(),
                        style: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : AppTheme.darkPrimaryText,
                          fontSize: 10,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),
        ],
      ),
    );
  }

  /// Build format and options
  Widget _buildFormatAndOptions() {
    return GradientWidgets.gradientCard(
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Text(
            'Format & Options',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 16),

          // Format Selection
          Text(
            'Export Format',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 14,
              fontWeight: FontWeight.w600,
            ),
          ),

          const SizedBox(height: 8),

          Wrap(
            spacing: 8,
            children: ReportFormat.values.map((format) {
              final isSelected = _selectedFormat == format;
              return GestureDetector(
                onTap: () => setState(() => _selectedFormat = format),
                child: Container(
                  padding: const EdgeInsets.symmetric(
                    horizontal: 12,
                    vertical: 8,
                  ),
                  decoration: BoxDecoration(
                    gradient: isSelected ? AppTheme.secondaryGradient : null,
                    color: isSelected ? null : AppTheme.darkSurface,
                    borderRadius: BorderRadius.circular(8),
                    border: Border.all(
                      color: isSelected
                          ? AppTheme.warningColor
                          : AppTheme.darkBorder,
                    ),
                  ),
                  child: Row(
                    mainAxisSize: MainAxisSize.min,
                    children: [
                      Icon(
                        format.icon,
                        color: isSelected
                            ? Colors.white
                            : AppTheme.darkSecondaryText,
                        size: 16,
                      ),
                      const SizedBox(width: 6),
                      Text(
                        format.displayName,
                        style: TextStyle(
                          color: isSelected
                              ? Colors.white
                              : AppTheme.darkPrimaryText,
                          fontSize: 12,
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                    ],
                  ),
                ),
              );
            }).toList(),
          ),

          const SizedBox(height: 16),

          // Options
          _buildOptionToggle(
            'Include Charts',
            'Add visual charts and graphs to the report',
            _includeCharts,
            (value) => setState(() => _includeCharts = value),
          ),

          const SizedBox(height: 12),

          _buildOptionToggle(
            'Include Comparisons',
            'Add period-over-period comparisons',
            _includeComparisons,
            (value) => setState(() => _includeComparisons = value),
          ),
        ],
      ),
    );
  }

  /// Build option toggle
  Widget _buildOptionToggle(
    String title,
    String description,
    bool value,
    Function(bool) onChanged,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface.withValues(alpha: 0.5),
        borderRadius: BorderRadius.circular(8),
      ),
      child: Row(
        children: [
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  title,
                  style: TextStyle(
                    color: AppTheme.darkPrimaryText,
                    fontSize: 14,
                    fontWeight: FontWeight.w600,
                  ),
                ),
                Text(
                  description,
                  style: TextStyle(
                    color: AppTheme.darkSecondaryText,
                    fontSize: 12,
                  ),
                ),
              ],
            ),
          ),
          Switch(
            value: value,
            onChanged: onChanged,
            activeColor: AppTheme.primaryColor,
          ),
        ],
      ),
    );
  }

  /// Build templates tab
  Widget _buildTemplatesTab() {
    return ListView.builder(
      padding: const EdgeInsets.all(16),
      itemCount: _templates.length,
      itemBuilder: (context, index) {
        final template = _templates[index];
        return Container(
          margin: const EdgeInsets.only(bottom: 12),
          decoration: AppTheme.createCardDecoration(),
          child: ListTile(
            leading: Container(
              padding: const EdgeInsets.all(8),
              decoration: BoxDecoration(
                color: template.type.color.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Icon(
                template.type.icon,
                color: template.type.color,
                size: 20,
              ),
            ),
            title: Text(
              template.name,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontWeight: FontWeight.w600,
              ),
            ),
            subtitle: Text(
              template.description,
              style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 12),
            ),
            trailing: GradientWidgets.gradientButton(
              text: 'Use Template',
              onPressed: () => _useTemplate(template),
              padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
            ),
          ),
        );
      },
    );
  }

  /// Build preview tab
  Widget _buildPreviewTab() {
    return SingleChildScrollView(
      padding: const EdgeInsets.all(16),
      child: GradientWidgets.gradientCard(
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Report Preview',
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 18,
                fontWeight: FontWeight.bold,
              ),
            ),

            const SizedBox(height: 16),

            _buildPreviewItem(
              'Report Name',
              _nameController.text.isNotEmpty
                  ? _nameController.text
                  : 'Untitled Report',
            ),
            _buildPreviewItem('Type', _selectedType.displayName),
            _buildPreviewItem('Period', _selectedPeriod.displayName),
            _buildPreviewItem(
              'Date Range',
              '${_startDate.day}/${_startDate.month}/${_startDate.year} - ${_endDate.day}/${_endDate.month}/${_endDate.year}',
            ),
            _buildPreviewItem('Metrics', '${_selectedMetrics.length} selected'),
            _buildPreviewItem('Format', _selectedFormat.displayName),
            _buildPreviewItem('Include Charts', _includeCharts ? 'Yes' : 'No'),
            _buildPreviewItem(
              'Include Comparisons',
              _includeComparisons ? 'Yes' : 'No',
            ),
          ],
        ),
      ),
    );
  }

  /// Build preview item
  Widget _buildPreviewItem(String label, String value) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 8),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          SizedBox(
            width: 120,
            child: Text(
              label,
              style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 12),
            ),
          ),
          Expanded(
            child: Text(
              value,
              style: TextStyle(
                color: AppTheme.darkPrimaryText,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Build generate button
  Widget _buildGenerateButton() {
    return Container(
      padding: const EdgeInsets.all(16),
      child: SizedBox(
        width: double.infinity,
        child: GradientWidgets.gradientButton(
          text: _isGenerating ? 'Generating Report...' : 'Generate Report',
          onPressed: _isGenerating ? () {} : () => _generateReport(),
          icon: _isGenerating ? null : Icons.file_download,
          padding: const EdgeInsets.symmetric(vertical: 16),
        ),
      ),
    );
  }

  /// Use template
  void _useTemplate(ReportTemplate template) {
    setState(() {
      _nameController.text = template.name;
      _descriptionController.text = template.description;
      _selectedType = template.type;
      _selectedMetrics = List.from(template.defaultMetrics);
    });
    _tabController.animateTo(0);
  }

  /// Generate report
  Future<void> _generateReport() async {
    if (_nameController.text.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a report name')),
      );
      return;
    }

    if (_selectedMetrics.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please select at least one metric')),
      );
      return;
    }

    setState(() => _isGenerating = true);

    try {
      final config = ReportConfig(
        id: '',
        name: _nameController.text,
        description: _descriptionController.text,
        type: _selectedType,
        period: _selectedPeriod,
        startDate: _startDate,
        endDate: _endDate,
        metrics: _selectedMetrics,
        format: _selectedFormat,
        includeCharts: _includeCharts,
        includeComparisons: _includeComparisons,
        agentId: widget.agentId,
        createdAt: DateTime.now(),
      );

      final report = await ReportService.generateReport(config);

      widget.onReportGenerated?.call(report);

      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(
            'Report "${report.config.name}" generated successfully!',
          ),
          backgroundColor: AppTheme.successColor,
        ),
      );
    } catch (e) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text('Failed to generate report: $e'),
          backgroundColor: AppTheme.errorColor,
        ),
      );
    } finally {
      setState(() => _isGenerating = false);
    }
  }
}
