import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:cached_network_image/cached_network_image.dart';
import 'package:intl/intl.dart';

import '../../../../core/models/property_model.dart';
import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../models/property_enums.dart';

/// Enhanced property card with modern design and animations
class EnhancedPropertyCard extends ConsumerStatefulWidget {
  final PropertyModel property;
  final VoidCallback? onTap;
  final bool showAdminActions;
  final bool showAgentActions;
  final Function(PropertyModel)? onEdit;
  final Function(PropertyModel)? onDelete;
  final Function(PropertyModel)? onFavorite;
  final Function(PropertyModel)? onShare;

  const EnhancedPropertyCard({
    super.key,
    required this.property,
    this.onTap,
    this.showAdminActions = false,
    this.showAgentActions = true,
    this.onEdit,
    this.onDelete,
    this.onFavorite,
    this.onShare,
  });

  @override
  ConsumerState<EnhancedPropertyCard> createState() =>
      _EnhancedPropertyCardState();
}

class _EnhancedPropertyCardState extends ConsumerState<EnhancedPropertyCard>
    with SingleTickerProviderStateMixin {
  late AnimationController _animationController;
  late Animation<double> _scaleAnimation;
  bool _isFavorited = false;

  @override
  void initState() {
    super.initState();
    _animationController = AnimationController(
      duration: const Duration(milliseconds: 200),
      vsync: this,
    );
    _scaleAnimation = Tween<double>(begin: 1.0, end: 0.95).animate(
      CurvedAnimation(parent: _animationController, curve: Curves.easeInOut),
    );
  }

  @override
  void dispose() {
    _animationController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AnimatedBuilder(
      animation: _scaleAnimation,
      builder: (context, child) {
        return Transform.scale(
          scale: _scaleAnimation.value,
          child: Container(
            margin: const EdgeInsets.symmetric(horizontal: 16, vertical: 8),
            decoration: AppTheme.createCardDecoration(addGlow: true),
            child: Material(
              color: Colors.transparent,
              child: InkWell(
                onTap: widget.onTap,
                onTapDown: (_) => _animationController.forward(),
                onTapUp: (_) => _animationController.reverse(),
                onTapCancel: () => _animationController.reverse(),
                borderRadius: BorderRadius.circular(16),
                child: Column(
                  crossAxisAlignment: CrossAxisAlignment.start,
                  children: [
                    // Property Image with overlay
                    _buildImageSection(),

                    // Property Details
                    _buildDetailsSection(),

                    // Action Buttons
                    if (widget.showAgentActions || widget.showAdminActions)
                      _buildActionSection(),
                  ],
                ),
              ),
            ),
          ),
        );
      },
    );
  }

  /// Build image section with overlay badges
  Widget _buildImageSection() {
    return Container(
      height: 200,
      width: double.infinity,
      child: Stack(
        children: [
          // Main Image
          ClipRRect(
            borderRadius: const BorderRadius.only(
              topLeft: Radius.circular(16),
              topRight: Radius.circular(16),
            ),
            child: widget.property.primaryImageUrl != null
                ? CachedNetworkImage(
                    imageUrl: widget.property.primaryImageUrl!,
                    height: 200,
                    width: double.infinity,
                    fit: BoxFit.cover,
                    placeholder: (context, url) => Container(
                      color: AppTheme.darkSurface,
                      child: Center(
                        child: CircularProgressIndicator(
                          color: AppTheme.primaryColor,
                        ),
                      ),
                    ),
                    errorWidget: (context, url, error) => Container(
                      color: AppTheme.darkSurface,
                      child: Column(
                        mainAxisAlignment: MainAxisAlignment.center,
                        children: [
                          Icon(
                            Icons.home,
                            size: 48,
                            color: AppTheme.darkSecondaryText,
                          ),
                          const SizedBox(height: 8),
                          Text(
                            'No Image',
                            style: TextStyle(
                              color: AppTheme.darkSecondaryText,
                              fontSize: 14,
                            ),
                          ),
                        ],
                      ),
                    ),
                  )
                : Container(
                    color: AppTheme.darkSurface,
                    child: Column(
                      mainAxisAlignment: MainAxisAlignment.center,
                      children: [
                        Icon(
                          PropertyType.values
                              .firstWhere(
                                (t) => t.value == widget.property.type,
                                orElse: () => PropertyType.residential,
                              )
                              .icon,
                          size: 48,
                          color: AppTheme.darkSecondaryText,
                        ),
                        const SizedBox(height: 8),
                        Text(
                          'No Image Available',
                          style: TextStyle(
                            color: AppTheme.darkSecondaryText,
                            fontSize: 14,
                          ),
                        ),
                      ],
                    ),
                  ),
          ),

          // Gradient Overlay
          Container(
            height: 200,
            decoration: BoxDecoration(
              borderRadius: const BorderRadius.only(
                topLeft: Radius.circular(16),
                topRight: Radius.circular(16),
              ),
              gradient: LinearGradient(
                begin: Alignment.topCenter,
                end: Alignment.bottomCenter,
                colors: [
                  Colors.transparent,
                  Colors.black.withValues(alpha: 0.3),
                ],
              ),
            ),
          ),

          // Top Badges
          Positioned(
            top: 12,
            left: 12,
            child: Row(
              children: [
                // Property Type Badge
                _buildBadge(
                  widget.property.type,
                  PropertyType.values
                      .firstWhere(
                        (t) => t.value == widget.property.type,
                        orElse: () => PropertyType.residential,
                      )
                      .color,
                ),
                const SizedBox(width: 8),
                // Featured Badge
                if (widget.property.isFeatured)
                  _buildBadge('Featured', AppTheme.warningColor, Icons.star),
              ],
            ),
          ),

          // Status Badge
          Positioned(top: 12, right: 12, child: _buildStatusBadge()),

          // Favorite Button
          Positioned(bottom: 12, right: 12, child: _buildFavoriteButton()),

          // Price Tag
          Positioned(bottom: 12, left: 12, child: _buildPriceTag()),
        ],
      ),
    );
  }

  /// Build badge widget
  Widget _buildBadge(String text, Color color, [IconData? icon]) {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: color.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          if (icon != null) ...[
            Icon(icon, color: Colors.white, size: 12),
            const SizedBox(width: 4),
          ],
          Text(
            text,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build status badge
  Widget _buildStatusBadge() {
    final status = PropertyStatus.values.firstWhere(
      (s) => s.value == widget.property.status,
      orElse: () => PropertyStatus.forSale,
    );

    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      decoration: BoxDecoration(
        color: status.color,
        borderRadius: BorderRadius.circular(12),
        boxShadow: [
          BoxShadow(
            color: status.color.withValues(alpha: 0.3),
            blurRadius: 4,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Icon(status.icon, color: Colors.white, size: 12),
          const SizedBox(width: 4),
          Text(
            status.displayName,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 10,
              fontWeight: FontWeight.w600,
            ),
          ),
        ],
      ),
    );
  }

  /// Build favorite button
  Widget _buildFavoriteButton() {
    return GestureDetector(
      onTap: () {
        setState(() {
          _isFavorited = !_isFavorited;
        });
        widget.onFavorite?.call(widget.property);
      },
      child: Container(
        padding: const EdgeInsets.all(8),
        decoration: BoxDecoration(
          color: Colors.black.withValues(alpha: 0.5),
          shape: BoxShape.circle,
        ),
        child: Icon(
          _isFavorited ? Icons.favorite : Icons.favorite_border,
          color: _isFavorited ? AppTheme.errorColor : Colors.white,
          size: 20,
        ),
      ),
    );
  }

  /// Build price tag
  Widget _buildPriceTag() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
      decoration: BoxDecoration(
        gradient: AppTheme.primaryGradient,
        borderRadius: BorderRadius.circular(16),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 8,
            offset: const Offset(0, 2),
          ),
        ],
      ),
      child: Text(
        widget.property.formattedPrice,
        style: const TextStyle(
          color: Colors.white,
          fontSize: 16,
          fontWeight: FontWeight.bold,
        ),
      ),
    );
  }

  /// Build details section
  Widget _buildDetailsSection() {
    return Padding(
      padding: const EdgeInsets.all(16),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Title
          Text(
            widget.property.title,
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 18,
              fontWeight: FontWeight.bold,
            ),
            maxLines: 2,
            overflow: TextOverflow.ellipsis,
          ),

          const SizedBox(height: 8),

          // Location
          Row(
            children: [
              Icon(Icons.location_on, color: AppTheme.primaryColor, size: 16),
              const SizedBox(width: 4),
              Expanded(
                child: Text(
                  widget.property.fullAddress,
                  style: TextStyle(
                    color: AppTheme.darkSecondaryText,
                    fontSize: 14,
                  ),
                  maxLines: 1,
                  overflow: TextOverflow.ellipsis,
                ),
              ),
            ],
          ),

          const SizedBox(height: 12),

          // Property specs
          _buildPropertySpecs(),
        ],
      ),
    );
  }

  /// Build property specifications
  Widget _buildPropertySpecs() {
    final specs = <Widget>[];

    if (widget.property.areaSquareFeet != null) {
      specs.add(
        _buildSpecItem(
          Icons.square_foot,
          '${widget.property.areaSquareFeet!.toInt()} sq ft',
        ),
      );
    }

    if (widget.property.bedrooms != null) {
      specs.add(_buildSpecItem(Icons.bed, '${widget.property.bedrooms} BHK'));
    }

    if (widget.property.bathrooms != null) {
      specs.add(
        _buildSpecItem(Icons.bathroom, '${widget.property.bathrooms} Bath'),
      );
    }

    // Add created date
    specs.add(
      _buildSpecItem(
        Icons.calendar_today,
        DateFormat('MMM yyyy').format(widget.property.createdAt),
      ),
    );

    return Wrap(spacing: 16, runSpacing: 8, children: specs);
  }

  /// Build specification item
  Widget _buildSpecItem(IconData icon, String text) {
    return Row(
      mainAxisSize: MainAxisSize.min,
      children: [
        Icon(icon, color: AppTheme.darkSecondaryText, size: 16),
        const SizedBox(width: 4),
        Text(
          text,
          style: TextStyle(
            color: AppTheme.darkSecondaryText,
            fontSize: 12,
            fontWeight: FontWeight.w500,
          ),
        ),
      ],
    );
  }

  /// Build action section
  Widget _buildActionSection() {
    return Container(
      padding: const EdgeInsets.symmetric(horizontal: 16, vertical: 12),
      decoration: BoxDecoration(
        color: AppTheme.darkSurface.withValues(alpha: 0.5),
        borderRadius: const BorderRadius.only(
          bottomLeft: Radius.circular(16),
          bottomRight: Radius.circular(16),
        ),
      ),
      child: Row(
        children: [
          if (widget.showAgentActions) ...[
            _buildActionButton(
              Icons.share,
              'Share',
              AppTheme.primaryColor,
              () => widget.onShare?.call(widget.property),
            ),
            const SizedBox(width: 12),
            _buildActionButton(
              Icons.info_outline,
              'Details',
              AppTheme.secondaryColor,
              widget.onTap,
            ),
          ],

          if (widget.showAdminActions) ...[
            _buildActionButton(
              Icons.edit,
              'Edit',
              AppTheme.warningColor,
              () => widget.onEdit?.call(widget.property),
            ),
            const SizedBox(width: 12),
            _buildActionButton(
              Icons.delete,
              'Delete',
              AppTheme.errorColor,
              () => widget.onDelete?.call(widget.property),
            ),
          ],

          const Spacer(),

          // Agent info if assigned
          if (widget.property.assignedAgentId != null)
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
              decoration: BoxDecoration(
                color: AppTheme.successColor.withValues(alpha: 0.2),
                borderRadius: BorderRadius.circular(8),
              ),
              child: Text(
                'Assigned',
                style: TextStyle(
                  color: AppTheme.successColor,
                  fontSize: 10,
                  fontWeight: FontWeight.w600,
                ),
              ),
            ),
        ],
      ),
    );
  }

  /// Build action button
  Widget _buildActionButton(
    IconData icon,
    String label,
    Color color,
    VoidCallback? onPressed,
  ) {
    return GestureDetector(
      onTap: onPressed,
      child: Container(
        padding: const EdgeInsets.symmetric(horizontal: 12, vertical: 6),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3), width: 1),
        ),
        child: Row(
          mainAxisSize: MainAxisSize.min,
          children: [
            Icon(icon, color: color, size: 14),
            const SizedBox(width: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w600,
              ),
            ),
          ],
        ),
      ),
    );
  }
}
