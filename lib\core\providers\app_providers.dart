import 'package:flutter/foundation.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';

import '../models/user_model.dart';
import '../services/user_service.dart';
import '../../features/dashboard/models/dashboard_models.dart';
import '../../features/dashboard/services/dashboard_service.dart';
import '../models/property_model.dart';
import '../services/property_service.dart';
import '../../features/commissions/models/enhanced_commission_model.dart';
import '../../features/commissions/services/commission_analytics_service.dart';
import '../../features/mlm/models/network_models.dart';
import '../../features/mlm/services/network_service.dart';
import '../../features/reports/models/report_models.dart';
import '../../features/reports/services/report_service.dart';

// ============================================================================
// AUTHENTICATION PROVIDERS
// ============================================================================

/// Firebase Auth User Stream
final firebaseAuthProvider = StreamProvider<User?>((ref) {
  return FirebaseAuth.instance.authStateChanges();
});

/// Current User Provider
final currentUserProvider = FutureProvider<UserModel?>((ref) async {
  // Development mode - return mock user (set to false for production)
  const bool useMockData = false; // Change to false for production
  if (kIsWeb && kDebugMode && useMockData) {
    return UserModel(
      id: 'dev-user-123',
      email: '<EMAIL>',
      name: 'Demo Agent',
      phoneNumber: '+91 **********',
      role: 'agent',
      level: 2,
      uplineId: null,
      downlineIds: ['dev-user-456', 'dev-user-789'],
      totalSales: 2500000.0,
      totalCommissions: 125000.0,
      totalStars: 85,
      totalBonuses: 15000,
      isActive: true,
      profileImageUrl: null,
      createdAt: DateTime.now().subtract(const Duration(days: 30)),
      updatedAt: DateTime.now(),
      additionalInfo: {
        'referralCode': 'DEMO123',
        'tier': 'gold',
        'signupSource': 'demo',
      },
    );
  }

  final firebaseUser = await ref.watch(firebaseAuthProvider.future);

  if (firebaseUser == null) {
    return null;
  }

  // Load user data from Firestore
  try {
    return await _loadUserFromFirestore(firebaseUser.uid);
  } catch (e) {
    return null;
  }
});

/// Authentication State Provider
final authStateProvider = Provider<AsyncValue<UserModel?>>((ref) {
  return ref.watch(currentUserProvider);
});

// ============================================================================
// DASHBOARD PROVIDERS
// ============================================================================

/// Dashboard Data Provider
final dashboardProvider = FutureProvider.family<AgentDashboardData, String>((
  ref,
  agentId,
) async {
  return DashboardService.getAgentDashboardData(agentId);
});

/// Auto-refresh Dashboard Provider (refreshes every 5 minutes)
final autoRefreshDashboardProvider =
    StreamProvider.family<AgentDashboardData, String>((ref, agentId) {
      return Stream.periodic(
        const Duration(minutes: 5),
        (index) => index,
      ).asyncMap((_) => DashboardService.getAgentDashboardData(agentId));
    });

// ============================================================================
// PROPERTIES PROVIDERS
// ============================================================================

/// Properties State Notifier
final propertiesProvider =
    StateNotifierProvider<PropertiesNotifier, PropertiesState>((ref) {
      return PropertiesNotifier();
    });

/// Properties State
class PropertiesState {
  final List<PropertyModel> properties;
  final bool isLoading;
  final String? error;
  final PropertyFilters? filters;

  const PropertiesState({
    this.properties = const [],
    this.isLoading = false,
    this.error,
    this.filters,
  });

  PropertiesState copyWith({
    List<PropertyModel>? properties,
    bool? isLoading,
    String? error,
    PropertyFilters? filters,
  }) {
    return PropertiesState(
      properties: properties ?? this.properties,
      isLoading: isLoading ?? this.isLoading,
      error: error,
      filters: filters ?? this.filters,
    );
  }
}

/// Properties State Notifier
class PropertiesNotifier extends StateNotifier<PropertiesState> {
  PropertiesNotifier() : super(const PropertiesState()) {
    loadProperties();
  }

  Future<void> loadProperties() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final properties = await PropertyService.getProperties();
      state = state.copyWith(properties: properties, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> addProperty(PropertyModel property) async {
    try {
      await PropertyService.createProperty(property);
      await loadProperties(); // Refresh the list
    } catch (e) {
      state = state.copyWith(error: e.toString());
    }
  }

  void updateFilters(PropertyFilters filters) {
    final filteredProperties = _applyFilters(state.properties, filters);
    state = state.copyWith(properties: filteredProperties, filters: filters);
  }

  void clearFilters() {
    state = state.copyWith(filters: null);
    loadProperties();
  }

  List<PropertyModel> _applyFilters(
    List<PropertyModel> properties,
    PropertyFilters filters,
  ) {
    return properties.where((property) {
      // Apply price range filter
      if (filters.priceRange != null) {
        if (property.price < filters.priceRange!.start ||
            property.price > filters.priceRange!.end) {
          return false;
        }
      }

      // Apply property type filter
      if (filters.propertyType != null &&
          property.type != filters.propertyType) {
        return false;
      }

      // Apply location filter
      if (filters.location != null &&
          !property.location.toLowerCase().contains(
            filters.location!.toLowerCase(),
          )) {
        return false;
      }

      // Apply bedrooms filter
      if (filters.bedrooms != null && property.bedrooms != filters.bedrooms) {
        return false;
      }

      return true;
    }).toList();
  }
}

// ============================================================================
// COMMISSION PROVIDERS
// ============================================================================

/// Commission Analytics Provider
final commissionAnalyticsProvider =
    FutureProvider.family<CommissionAnalytics, String>((ref, agentId) async {
      return CommissionAnalyticsService.getAgentAnalytics(agentId);
    });

/// Recent Commissions Provider
final recentCommissionsProvider =
    FutureProvider.family<List<EnhancedCommissionModel>, String>((
      ref,
      agentId,
    ) async {
      // Get recent commissions directly from Firestore
      final snapshot = await FirebaseFirestore.instance
          .collection('commissions')
          .where('agentId', isEqualTo: agentId)
          .orderBy('createdAt', descending: true)
          .limit(10)
          .get();

      return snapshot.docs
          .map((doc) => EnhancedCommissionModel.fromFirestore(doc))
          .toList();
    });

/// Commission Summary Provider
final commissionSummaryProvider =
    FutureProvider.family<CommissionSummary, String>((ref, agentId) async {
      final analytics = await ref.watch(
        commissionAnalyticsProvider(agentId).future,
      );

      return CommissionSummary(
        totalEarned: analytics.totalEarned,
        totalPending: analytics.totalPending,
        totalPaid: analytics.totalPaid,
        growthRate: analytics.growthRate,
        monthlyAverage: analytics.monthlyData.isNotEmpty
            ? analytics.monthlyData
                      .map((m) => m.amount)
                      .reduce((a, b) => a + b) /
                  analytics.monthlyData.length
            : 0.0,
      );
    });

// ============================================================================
// NETWORK PROVIDERS
// ============================================================================

/// Network Tree Provider
final networkProvider = FutureProvider.family<NetworkNode?, String>((
  ref,
  agentId,
) async {
  return NetworkService.buildNetworkTree(agentId);
});

/// Network Statistics Provider
final networkStatsProvider = FutureProvider.family<NetworkStatistics, String>((
  ref,
  agentId,
) async {
  return NetworkService.getNetworkStatistics(agentId);
});

/// Network Members Provider
final networkMembersProvider = FutureProvider.family<List<NetworkNode>, String>(
  (ref, agentId) async {
    final networkTree = await NetworkService.buildNetworkTree(agentId);
    if (networkTree == null) return [];

    // Flatten the tree to get all network members
    final members = <NetworkNode>[];
    void collectMembers(NetworkNode node) {
      members.add(node);
      for (final child in node.children) {
        collectMembers(child);
      }
    }

    collectMembers(networkTree);

    return members;
  },
);

// ============================================================================
// REPORTS PROVIDERS
// ============================================================================

/// Reports State Notifier
final reportsProvider = StateNotifierProvider<ReportsNotifier, ReportsState>((
  ref,
) {
  return ReportsNotifier();
});

/// Reports State
class ReportsState {
  final List<GeneratedReport> reports;
  final bool isLoading;
  final bool isGenerating;
  final String? error;

  const ReportsState({
    this.reports = const [],
    this.isLoading = false,
    this.isGenerating = false,
    this.error,
  });

  ReportsState copyWith({
    List<GeneratedReport>? reports,
    bool? isLoading,
    bool? isGenerating,
    String? error,
  }) {
    return ReportsState(
      reports: reports ?? this.reports,
      isLoading: isLoading ?? this.isLoading,
      isGenerating: isGenerating ?? this.isGenerating,
      error: error,
    );
  }
}

/// Reports State Notifier
class ReportsNotifier extends StateNotifier<ReportsState> {
  ReportsNotifier() : super(const ReportsState()) {
    loadReportHistory();
  }

  Future<void> loadReportHistory() async {
    state = state.copyWith(isLoading: true, error: null);

    try {
      final reports = await ReportService.getReportHistory();
      state = state.copyWith(reports: reports, isLoading: false);
    } catch (e) {
      state = state.copyWith(isLoading: false, error: e.toString());
    }
  }

  Future<void> generateReport(ReportConfig config) async {
    state = state.copyWith(isGenerating: true, error: null);

    try {
      final report = await ReportService.generateReport(config);

      // Add to reports list
      final updatedReports = [report, ...state.reports];
      state = state.copyWith(reports: updatedReports, isGenerating: false);
    } catch (e) {
      state = state.copyWith(isGenerating: false, error: e.toString());
    }
  }

  void clearError() {
    state = state.copyWith(error: null);
  }
}

// ============================================================================
// GOALS PROVIDERS
// ============================================================================

/// Goals Provider
final goalsProvider = FutureProvider.family<List<DashboardGoal>, String>((
  ref,
  agentId,
) async {
  final dashboardData = await ref.watch(dashboardProvider(agentId).future);
  return dashboardData.goals;
});

/// Active Goals Provider
final activeGoalsProvider = Provider.family<List<DashboardGoal>, String>((
  ref,
  agentId,
) {
  final goalsAsync = ref.watch(goalsProvider(agentId));

  return goalsAsync.when(
    data: (goals) =>
        goals.where((goal) => goal.status == GoalStatus.active).toList(),
    loading: () => [],
    error: (_, __) => [],
  );
});

// ============================================================================
// HELPER FUNCTIONS
// ============================================================================

/// Load user data from Firestore
Future<UserModel?> _loadUserFromFirestore(String uid) async {
  try {
    final doc = await FirebaseFirestore.instance
        .collection('users')
        .doc(uid)
        .get();

    if (doc.exists && doc.data() != null) {
      return UserModel.fromFirestore(doc);
    }

    // User document doesn't exist - this means they need to complete onboarding
    return null;
  } catch (e) {
    if (kDebugMode) {
      print('Error loading user from Firestore: $e');
    }
    return null;
  }
}

// ============================================================================
// HELPER MODELS
// ============================================================================

/// Property Filters
class PropertyFilters {
  final PriceRange? priceRange;
  final String? propertyType;
  final String? location;
  final int? bedrooms;
  final List<String>? amenities;

  const PropertyFilters({
    this.priceRange,
    this.propertyType,
    this.location,
    this.bedrooms,
    this.amenities,
  });
}

/// Price Range
class PriceRange {
  final double start;
  final double end;

  const PriceRange(this.start, this.end);
}

/// Commission Summary
class CommissionSummary {
  final double totalEarned;
  final double totalPending;
  final double totalPaid;
  final double growthRate;
  final double monthlyAverage;

  const CommissionSummary({
    required this.totalEarned,
    required this.totalPending,
    required this.totalPaid,
    required this.growthRate,
    required this.monthlyAverage,
  });
}

// ============================================================================
// USER DATA PROVIDERS
// ============================================================================

/// Provider for getting user's downlines (direct referrals)
final userDownlinesProvider = FutureProvider.family<List<UserModel>, String>((
  ref,
  userId,
) async {
  return await UserService.getUserDownlines(userId);
});

/// Provider for getting user's upline (referrer)
final userUplineProvider = FutureProvider.family<UserModel?, String>((
  ref,
  uplineId,
) async {
  return await UserService.getUserUpline(uplineId);
});

/// Provider for searching users
final searchUsersProvider = FutureProvider.family<List<UserModel>, String>((
  ref,
  searchTerm,
) async {
  if (searchTerm.isEmpty) return [];
  return await UserService.searchUsers(searchTerm);
});

/// Provider for getting all agents (admin only)
final allAgentsProvider = FutureProvider<List<UserModel>>((ref) async {
  return await UserService.getAllAgents();
});
