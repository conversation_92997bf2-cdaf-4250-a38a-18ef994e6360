import 'package:cloud_firestore/cloud_firestore.dart';
import '../models/user_model.dart';
import '../models/network_node.dart';
import '../constants/app_constants.dart';

/// Service for managing MLM network operations
class MLMService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;

  /// Get user's network tree (downlines)
  static Future<NetworkNode?> getUserNetworkTree(String userId, {int maxDepth = 5}) async {
    try {
      // Get the root user
      final userDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(userId)
          .get();

      if (!userDoc.exists) return null;

      final user = UserModel.fromFirestore(userDoc);
      final stats = await _getUserNetworkStats(userId);

      // Build the tree recursively
      final children = await _buildChildrenNodes(userId, 0, maxDepth);

      return NetworkNode(
        user: user,
        children: children,
        depth: 0,
        joinedAt: user.createdAt,
        stats: stats,
      );
    } catch (e) {
      print('Error getting user network tree: $e');
      return null;
    }
  }

  /// Build children nodes recursively
  static Future<List<NetworkNode>> _buildChildrenNodes(
    String parentId,
    int currentDepth,
    int maxDepth,
  ) async {
    if (currentDepth >= maxDepth) return [];

    try {
      // Get direct downlines
      final downlinesQuery = await _firestore
          .collection(AppConstants.usersCollection)
          .where('uplineId', isEqualTo: parentId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt')
          .get();

      final children = <NetworkNode>[];

      for (final doc in downlinesQuery.docs) {
        final user = UserModel.fromFirestore(doc);
        final stats = await _getUserNetworkStats(user.id);
        
        // Recursively get children
        final grandChildren = await _buildChildrenNodes(
          user.id,
          currentDepth + 1,
          maxDepth,
        );

        final node = NetworkNode(
          user: user,
          children: grandChildren,
          depth: currentDepth + 1,
          joinedAt: user.createdAt,
          stats: stats,
        );

        children.add(node);
      }

      return children;
    } catch (e) {
      print('Error building children nodes: $e');
      return [];
    }
  }

  /// Get user's upline chain
  static Future<List<UserModel>> getUserUplineChain(String userId) async {
    try {
      final uplineChain = <UserModel>[];
      String? currentUserId = userId;

      while (currentUserId != null && uplineChain.length < AppConstants.maxMLMLevels) {
        final userDoc = await _firestore
            .collection(AppConstants.usersCollection)
            .doc(currentUserId)
            .get();

        if (!userDoc.exists) break;

        final user = UserModel.fromFirestore(userDoc);
        if (user.uplineId != null) {
          final uplineDoc = await _firestore
              .collection(AppConstants.usersCollection)
              .doc(user.uplineId!)
              .get();

          if (uplineDoc.exists) {
            final upline = UserModel.fromFirestore(uplineDoc);
            uplineChain.add(upline);
            currentUserId = upline.uplineId;
          } else {
            break;
          }
        } else {
          break;
        }
      }

      return uplineChain;
    } catch (e) {
      print('Error getting upline chain: $e');
      return [];
    }
  }

  /// Get direct downlines for a user
  static Future<List<UserModel>> getDirectDownlines(String userId) async {
    try {
      final query = await _firestore
          .collection(AppConstants.usersCollection)
          .where('uplineId', isEqualTo: userId)
          .where('isActive', isEqualTo: true)
          .orderBy('createdAt')
          .get();

      return query.docs.map((doc) => UserModel.fromFirestore(doc)).toList();
    } catch (e) {
      print('Error getting direct downlines: $e');
      return [];
    }
  }

  /// Get all downlines for a user (up to specified levels)
  static Future<List<UserModel>> getAllDownlines(String userId, {int maxLevels = 5}) async {
    try {
      final allDownlines = <UserModel>[];
      final processedIds = <String>{};

      await _collectDownlines(userId, allDownlines, processedIds, 0, maxLevels);

      return allDownlines;
    } catch (e) {
      print('Error getting all downlines: $e');
      return [];
    }
  }

  /// Recursively collect downlines
  static Future<void> _collectDownlines(
    String userId,
    List<UserModel> allDownlines,
    Set<String> processedIds,
    int currentLevel,
    int maxLevels,
  ) async {
    if (currentLevel >= maxLevels || processedIds.contains(userId)) return;

    processedIds.add(userId);

    final directDownlines = await getDirectDownlines(userId);
    allDownlines.addAll(directDownlines);

    for (final downline in directDownlines) {
      await _collectDownlines(
        downline.id,
        allDownlines,
        processedIds,
        currentLevel + 1,
        maxLevels,
      );
    }
  }

  /// Get network statistics for a user
  static Future<NetworkStats> _getUserNetworkStats(String userId) async {
    try {
      // Get all downlines
      final allDownlines = await getAllDownlines(userId);
      
      // Calculate statistics
      final totalDownlines = allDownlines.length;
      final activeDownlines = allDownlines.where((user) => user.isActive).length;
      
      final totalCommissions = allDownlines.fold<double>(
        0.0,
        (sum, user) => sum + user.totalCommissions,
      );
      
      final totalStars = allDownlines.fold<int>(
        0,
        (sum, user) => sum + user.totalStars,
      );

      // Calculate level counts
      final levelCounts = <int, int>{};
      for (final downline in allDownlines) {
        final level = downline.level;
        levelCounts[level] = (levelCounts[level] ?? 0) + 1;
      }

      // Get last activity (most recent user creation)
      final lastActivity = allDownlines.isNotEmpty
          ? allDownlines
              .map((user) => user.updatedAt)
              .reduce((a, b) => a.isAfter(b) ? a : b)
          : DateTime.now();

      return NetworkStats(
        totalDownlines: totalDownlines,
        activeDownlines: activeDownlines,
        totalCommissions: totalCommissions,
        totalStars: totalStars,
        totalSales: 0, // TODO: Calculate from transactions
        levelCounts: levelCounts,
        lastActivity: lastActivity,
      );
    } catch (e) {
      print('Error getting network stats: $e');
      return NetworkStats(lastActivity: DateTime.now());
    }
  }

  /// Get network performance metrics
  static Future<Map<String, dynamic>> getNetworkPerformance(String userId) async {
    try {
      final networkTree = await getUserNetworkTree(userId);
      if (networkTree == null) return {};

      final stats = networkTree.stats;
      
      return {
        'totalNetworkSize': networkTree.totalNetworkSize,
        'directDownlines': networkTree.directDownlineCount,
        'totalCommissions': stats.totalCommissions,
        'totalStars': stats.totalStars,
        'activityScore': stats.activityScore,
        'levelDistribution': stats.levelCounts,
        'growthRate': 0.0, // TODO: Calculate growth rate
      };
    } catch (e) {
      print('Error getting network performance: $e');
      return {};
    }
  }

  /// Search users in network
  static Future<List<UserModel>> searchNetworkUsers(
    String userId,
    String searchQuery,
  ) async {
    try {
      final allDownlines = await getAllDownlines(userId);
      
      final query = searchQuery.toLowerCase();
      return allDownlines.where((user) {
        return user.name.toLowerCase().contains(query) ||
               user.email.toLowerCase().contains(query) ||
               user.phoneNumber.contains(query);
      }).toList();
    } catch (e) {
      print('Error searching network users: $e');
      return [];
    }
  }

  /// Get top performers in network
  static Future<List<UserModel>> getTopPerformers(
    String userId, {
    int limit = 10,
    String sortBy = 'totalStars', // 'totalStars', 'totalCommissions', 'downlineCount'
  }) async {
    try {
      final allDownlines = await getAllDownlines(userId);
      
      // Sort based on criteria
      allDownlines.sort((a, b) {
        switch (sortBy) {
          case 'totalCommissions':
            return b.totalCommissions.compareTo(a.totalCommissions);
          case 'downlineCount':
            return b.downlineIds.length.compareTo(a.downlineIds.length);
          case 'totalStars':
          default:
            return b.totalStars.compareTo(a.totalStars);
        }
      });

      return allDownlines.take(limit).toList();
    } catch (e) {
      print('Error getting top performers: $e');
      return [];
    }
  }

  /// Get recent network activities
  static Future<List<Map<String, dynamic>>> getRecentActivities(
    String userId, {
    int limit = 20,
  }) async {
    try {
      final activities = <Map<String, dynamic>>[];
      
      // Get recent user registrations in network
      final allDownlines = await getAllDownlines(userId);
      final recentJoins = allDownlines
          .where((user) => DateTime.now().difference(user.createdAt).inDays <= 30)
          .toList();

      recentJoins.sort((a, b) => b.createdAt.compareTo(a.createdAt));

      for (final user in recentJoins.take(limit)) {
        activities.add({
          'type': 'new_member',
          'userId': user.id,
          'userName': user.name,
          'timestamp': user.createdAt,
          'description': '${user.name} joined your network',
        });
      }

      // Sort by timestamp
      activities.sort((a, b) => 
          (b['timestamp'] as DateTime).compareTo(a['timestamp'] as DateTime));

      return activities.take(limit).toList();
    } catch (e) {
      print('Error getting recent activities: $e');
      return [];
    }
  }

  /// Validate MLM relationship
  static Future<bool> validateMLMRelationship(String uplineId, String downlineId) async {
    try {
      // Check if users exist
      final uplineDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(uplineId)
          .get();
      
      final downlineDoc = await _firestore
          .collection(AppConstants.usersCollection)
          .doc(downlineId)
          .get();

      if (!uplineDoc.exists || !downlineDoc.exists) return false;

      // Check if relationship would create a cycle
      final uplineChain = await getUserUplineChain(uplineId);
      return !uplineChain.any((user) => user.id == downlineId);
    } catch (e) {
      print('Error validating MLM relationship: $e');
      return false;
    }
  }
}
