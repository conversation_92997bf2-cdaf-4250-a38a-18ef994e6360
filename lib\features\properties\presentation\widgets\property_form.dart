import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../../../../core/constants/app_constants.dart';
import '../../../../core/models/property_model.dart';
import '../../../../core/services/property_service.dart';
import '../providers/property_providers.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

/// Property form widget for adding/editing properties
class PropertyForm extends ConsumerStatefulWidget {
  final PropertyModel? property; // null for new property
  final Function(PropertyModel)? onSaved;

  const PropertyForm({
    super.key,
    this.property,
    this.onSaved,
  });

  @override
  ConsumerState<PropertyForm> createState() => _PropertyFormState();
}

class _PropertyFormState extends ConsumerState<PropertyForm> {
  final _formKey = GlobalKey<FormState>();
  final _scrollController = ScrollController();

  // Text controllers
  late TextEditingController _titleController;
  late TextEditingController _descriptionController;
  late TextEditingController _priceController;
  late TextEditingController _locationController;
  late TextEditingController _cityController;
  late TextEditingController _stateController;
  late TextEditingController _pincodeController;
  late TextEditingController _areaController;
  late TextEditingController _bedroomsController;
  late TextEditingController _bathroomsController;

  // Selected values
  String _selectedType = 'Residential';
  String _selectedStatus = 'for_sale';
  List<String> _selectedAmenities = [];

  // Indian states for dropdown
  final List<String> _indianStates = [
    'Andhra Pradesh', 'Arunachal Pradesh', 'Assam', 'Bihar', 'Chhattisgarh',
    'Goa', 'Gujarat', 'Haryana', 'Himachal Pradesh', 'Jharkhand', 'Karnataka',
    'Kerala', 'Madhya Pradesh', 'Maharashtra', 'Manipur', 'Meghalaya', 'Mizoram',
    'Nagaland', 'Odisha', 'Punjab', 'Rajasthan', 'Sikkim', 'Tamil Nadu',
    'Telangana', 'Tripura', 'Uttar Pradesh', 'Uttarakhand', 'West Bengal',
    'Delhi', 'Jammu and Kashmir', 'Ladakh', 'Puducherry', 'Chandigarh',
    'Dadra and Nagar Haveli and Daman and Diu', 'Lakshadweep',
    'Andaman and Nicobar Islands'
  ];

  // Common amenities
  final List<String> _commonAmenities = [
    'Parking', 'Swimming Pool', 'Gym', 'Garden', 'Security', 'Elevator',
    'Power Backup', 'Water Supply', 'Club House', 'Children Play Area',
    'Jogging Track', 'CCTV', 'Intercom', 'Maintenance Staff', 'Visitor Parking',
    'Fire Safety', 'Waste Management', 'Rainwater Harvesting', 'Solar Panels',
    'Wi-Fi', 'Cafeteria', 'Library', 'Multipurpose Hall', 'Senior Citizen Area'
  ];

  @override
  void initState() {
    super.initState();
    _initializeControllers();
    _loadPropertyData();
  }

  void _initializeControllers() {
    _titleController = TextEditingController();
    _descriptionController = TextEditingController();
    _priceController = TextEditingController();
    _locationController = TextEditingController();
    _cityController = TextEditingController();
    _stateController = TextEditingController();
    _pincodeController = TextEditingController();
    _areaController = TextEditingController();
    _bedroomsController = TextEditingController();
    _bathroomsController = TextEditingController();
  }

  void _loadPropertyData() {
    if (widget.property != null) {
      final property = widget.property!;
      _titleController.text = property.title;
      _descriptionController.text = property.description;
      _priceController.text = property.price.toString();
      _locationController.text = property.location;
      _cityController.text = property.city;
      _stateController.text = property.state;
      _pincodeController.text = property.pincode;
      _areaController.text = property.areaSquareFeet?.toString() ?? '';
      _bedroomsController.text = property.bedrooms?.toString() ?? '';
      _bathroomsController.text = property.bathrooms?.toString() ?? '';
      _selectedType = property.type;
      _selectedStatus = property.status;
      _selectedAmenities = List.from(property.amenities);
    }
  }

  @override
  void dispose() {
    _titleController.dispose();
    _descriptionController.dispose();
    _priceController.dispose();
    _locationController.dispose();
    _cityController.dispose();
    _stateController.dispose();
    _pincodeController.dispose();
    _areaController.dispose();
    _bedroomsController.dispose();
    _bathroomsController.dispose();
    _scrollController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final formState = ref.watch(propertyFormProvider);

    return Scaffold(
      appBar: AppBar(
        title: Text(widget.property == null ? 'Add Property' : 'Edit Property'),
        actions: [
          TextButton(
            onPressed: formState.isLoading ? null : _saveProperty,
            child: formState.isLoading
                ? const SizedBox(
                    width: 20,
                    height: 20,
                    child: CircularProgressIndicator(strokeWidth: 2),
                  )
                : const Text('Save'),
          ),
        ],
      ),
      body: Form(
        key: _formKey,
        child: Scrollbar(
          controller: _scrollController,
          child: SingleChildScrollView(
            controller: _scrollController,
            padding: const EdgeInsets.all(16),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                // Error message
                if (formState.error != null)
                  Container(
                    padding: const EdgeInsets.all(12),
                    margin: const EdgeInsets.only(bottom: 16),
                    decoration: BoxDecoration(
                      color: Colors.red.withValues(alpha: 0.1),
                      borderRadius: BorderRadius.circular(8),
                      border: Border.all(color: Colors.red.withValues(alpha: 0.3)),
                    ),
                    child: Text(
                      formState.error!,
                      style: const TextStyle(color: Colors.red),
                    ),
                  ),

                // Basic Information
                _buildSectionTitle('Basic Information'),
                _buildTextField(
                  controller: _titleController,
                  label: 'Property Title',
                  hint: 'e.g., 3BHK Apartment in Bandra',
                  validator: (value) => value?.isEmpty == true ? 'Title is required' : null,
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _descriptionController,
                  label: 'Description',
                  hint: 'Describe the property features and highlights',
                  maxLines: 4,
                  validator: (value) => value?.isEmpty == true ? 'Description is required' : null,
                ),

                const SizedBox(height: 24),

                // Property Type and Status
                _buildSectionTitle('Property Details'),
                Row(
                  children: [
                    Expanded(
                      child: _buildDropdown(
                        label: 'Property Type',
                        value: _selectedType,
                        items: AppConstants.propertyTypes,
                        onChanged: (value) => setState(() => _selectedType = value!),
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildDropdown(
                        label: 'Status',
                        value: _selectedStatus,
                        items: const ['for_sale', 'for_rent', 'sold', 'rented'],
                        onChanged: (value) => setState(() => _selectedStatus = value!),
                        displayNames: const {
                          'for_sale': 'For Sale',
                          'for_rent': 'For Rent',
                          'sold': 'Sold',
                          'rented': 'Rented',
                        },
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 16),

                // Price in Indian Rupees
                _buildTextField(
                  controller: _priceController,
                  label: 'Price (₹)',
                  hint: 'Enter price in Indian Rupees',
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Price is required';
                    final price = double.tryParse(value!);
                    if (price == null || price <= 0) return 'Enter valid price';
                    return null;
                  },
                  suffixText: 'INR',
                ),

                const SizedBox(height: 24),

                // Location Information
                _buildSectionTitle('Location'),
                _buildTextField(
                  controller: _locationController,
                  label: 'Area/Locality',
                  hint: 'e.g., Bandra West, Sector 15',
                  validator: (value) => value?.isEmpty == true ? 'Location is required' : null,
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _cityController,
                        label: 'City',
                        hint: 'e.g., Mumbai',
                        validator: (value) => value?.isEmpty == true ? 'City is required' : null,
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildDropdown(
                        label: 'State',
                        value: _stateController.text.isEmpty ? null : _stateController.text,
                        items: _indianStates,
                        onChanged: (value) => _stateController.text = value!,
                        validator: (value) => value?.isEmpty == true ? 'State is required' : null,
                      ),
                    ),
                  ],
                ),
                const SizedBox(height: 16),
                _buildTextField(
                  controller: _pincodeController,
                  label: 'Pincode',
                  hint: '6-digit pincode',
                  keyboardType: TextInputType.number,
                  inputFormatters: [
                    FilteringTextInputFormatter.digitsOnly,
                    LengthLimitingTextInputFormatter(6),
                  ],
                  validator: (value) {
                    if (value?.isEmpty == true) return 'Pincode is required';
                    if (value!.length != 6) return 'Enter valid 6-digit pincode';
                    return null;
                  },
                ),

                const SizedBox(height: 24),

                // Property Specifications
                _buildSectionTitle('Specifications'),
                _buildTextField(
                  controller: _areaController,
                  label: 'Area (sq ft)',
                  hint: 'Built-up area in square feet',
                  keyboardType: TextInputType.number,
                  inputFormatters: [FilteringTextInputFormatter.allow(RegExp(r'[0-9.]'))],
                  suffixText: 'sq ft',
                ),
                const SizedBox(height: 16),
                Row(
                  children: [
                    Expanded(
                      child: _buildTextField(
                        controller: _bedroomsController,
                        label: 'Bedrooms',
                        hint: 'Number of bedrooms',
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      ),
                    ),
                    const SizedBox(width: 16),
                    Expanded(
                      child: _buildTextField(
                        controller: _bathroomsController,
                        label: 'Bathrooms',
                        hint: 'Number of bathrooms',
                        keyboardType: TextInputType.number,
                        inputFormatters: [FilteringTextInputFormatter.digitsOnly],
                      ),
                    ),
                  ],
                ),

                const SizedBox(height: 24),

                // Amenities
                _buildSectionTitle('Amenities'),
                _buildAmenitiesSelector(),

                const SizedBox(height: 32),
              ],
            ),
          ),
        ),
      ),
    );
  }

  Widget _buildSectionTitle(String title) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 16),
      child: Text(
        title,
        style: Theme.of(context).textTheme.titleMedium?.copyWith(
          fontWeight: FontWeight.bold,
          color: Theme.of(context).primaryColor,
        ),
      ),
    );
  }

  Widget _buildTextField({
    required TextEditingController controller,
    required String label,
    String? hint,
    int maxLines = 1,
    TextInputType? keyboardType,
    List<TextInputFormatter>? inputFormatters,
    String? Function(String?)? validator,
    String? suffixText,
  }) {
    return TextFormField(
      controller: controller,
      decoration: InputDecoration(
        labelText: label,
        hintText: hint,
        suffixText: suffixText,
        border: const OutlineInputBorder(),
      ),
      maxLines: maxLines,
      keyboardType: keyboardType,
      inputFormatters: inputFormatters,
      validator: validator,
    );
  }

  Widget _buildDropdown({
    required String label,
    required String? value,
    required List<String> items,
    required Function(String?) onChanged,
    Map<String, String>? displayNames,
    String? Function(String?)? validator,
  }) {
    return DropdownButtonFormField<String>(
      value: value,
      decoration: InputDecoration(
        labelText: label,
        border: const OutlineInputBorder(),
      ),
      items: items.map((item) => DropdownMenuItem(
        value: item,
        child: Text(displayNames?[item] ?? item),
      )).toList(),
      onChanged: onChanged,
      validator: validator,
    );
  }

  Widget _buildAmenitiesSelector() {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Select amenities available:',
          style: Theme.of(context).textTheme.bodyMedium,
        ),
        const SizedBox(height: 8),
        Wrap(
          spacing: 8,
          runSpacing: 4,
          children: _commonAmenities.map((amenity) {
            final isSelected = _selectedAmenities.contains(amenity);
            return FilterChip(
              label: Text(amenity),
              selected: isSelected,
              onSelected: (selected) {
                setState(() {
                  if (selected) {
                    _selectedAmenities.add(amenity);
                  } else {
                    _selectedAmenities.remove(amenity);
                  }
                });
              },
            );
          }).toList(),
        ),
      ],
    );
  }

  Future<void> _saveProperty() async {
    if (!_formKey.currentState!.validate()) return;

    final currentUser = ref.read(currentUserProvider);
    if (currentUser == null) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('User not authenticated')),
      );
      return;
    }

    ref.read(propertyFormProvider.notifier).setLoading(true);

    try {
      final property = PropertyModel(
        id: widget.property?.id ?? '',
        title: _titleController.text.trim(),
        description: _descriptionController.text.trim(),
        type: _selectedType,
        status: _selectedStatus,
        price: double.parse(_priceController.text),
        currency: 'INR',
        location: _locationController.text.trim(),
        city: _cityController.text.trim(),
        state: _stateController.text.trim(),
        pincode: _pincodeController.text.trim(),
        areaSquareFeet: _areaController.text.isNotEmpty 
            ? double.tryParse(_areaController.text) 
            : null,
        bedrooms: _bedroomsController.text.isNotEmpty 
            ? int.tryParse(_bedroomsController.text) 
            : null,
        bathrooms: _bathroomsController.text.isNotEmpty 
            ? int.tryParse(_bathroomsController.text) 
            : null,
        amenities: _selectedAmenities,
        imageUrls: widget.property?.imageUrls ?? [],
        ownerId: currentUser.id,
        assignedAgentId: widget.property?.assignedAgentId,
        isApproved: widget.property?.isApproved ?? false,
        isFeatured: widget.property?.isFeatured ?? false,
        createdAt: widget.property?.createdAt ?? DateTime.now(),
        updatedAt: DateTime.now(),
      );

      final result = widget.property == null
          ? await PropertyService.createProperty(property)
          : await PropertyService.updateProperty(property);

      if (result.isSuccess) {
        ref.read(propertyFormProvider.notifier).setLoading(false);
        
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(widget.property == null 
                  ? 'Property added successfully' 
                  : 'Property updated successfully'),
            ),
          );
          
          widget.onSaved?.call(result.property!);
          Navigator.of(context).pop(result.property);
        }
      } else {
        ref.read(propertyFormProvider.notifier).setError(result.message);
      }
    } catch (e) {
      ref.read(propertyFormProvider.notifier).setError('Failed to save property: $e');
    }
  }
}
