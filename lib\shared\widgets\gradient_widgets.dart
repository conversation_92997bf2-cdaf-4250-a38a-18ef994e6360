import 'package:flutter/material.dart';
import '../themes/app_theme.dart';

/// Collection of beautiful gradient widgets for the MLM app
class GradientWidgets {
  
  /// Gradient Card with glow effect
  static Widget gradientCard({
    required Widget child,
    EdgeInsetsGeometry? padding,
    EdgeInsetsGeometry? margin,
    double borderRadius = 16,
    bool addGlow = true,
    LinearGradient? gradient,
  }) {
    return Container(
      margin: margin,
      decoration: AppTheme.createGradientDecoration(
        gradient: gradient ?? AppTheme.cardGradient,
        borderRadius: borderRadius,
        borderColor: AppTheme.darkBorder.withValues(alpha: 0.3),
        borderWidth: 1,
      ),
      child: Container(
        padding: padding ?? const EdgeInsets.all(20),
        child: child,
      ),
    );
  }

  /// Gradient Button with animation
  static Widget gradientButton({
    required String text,
    required VoidCallback onPressed,
    LinearGradient? gradient,
    double borderRadius = 16,
    EdgeInsetsGeometry? padding,
    TextStyle? textStyle,
    IconData? icon,
  }) {
    return Container(
      decoration: AppTheme.createGradientDecoration(
        gradient: gradient ?? AppTheme.primaryGradient,
        borderRadius: borderRadius,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(borderRadius),
          child: Container(
            padding: padding ?? const EdgeInsets.symmetric(horizontal: 32, vertical: 16),
            child: Row(
              mainAxisSize: MainAxisSize.min,
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                if (icon != null) ...[
                  Icon(icon, color: Colors.white, size: 20),
                  const SizedBox(width: 8),
                ],
                Text(
                  text,
                  style: textStyle ?? const TextStyle(
                    color: Colors.white,
                    fontSize: 16,
                    fontWeight: FontWeight.w600,
                    letterSpacing: 0.5,
                  ),
                ),
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Animated Stats Card
  static Widget statsCard({
    required String title,
    required String value,
    required IconData icon,
    Color? iconColor,
    String? subtitle,
    VoidCallback? onTap,
  }) {
    return Container(
      decoration: AppTheme.createCardDecoration(addGlow: true),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onTap,
          borderRadius: BorderRadius.circular(16),
          child: Padding(
            padding: const EdgeInsets.all(20),
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Row(
                  children: [
                    Container(
                      padding: const EdgeInsets.all(12),
                      decoration: BoxDecoration(
                        gradient: AppTheme.primaryGradient,
                        borderRadius: BorderRadius.circular(12),
                      ),
                      child: Icon(
                        icon,
                        color: Colors.white,
                        size: 24,
                      ),
                    ),
                    const Spacer(),
                    if (onTap != null)
                      Icon(
                        Icons.arrow_forward_ios,
                        color: AppTheme.darkSecondaryText,
                        size: 16,
                      ),
                  ],
                ),
                const SizedBox(height: 16),
                Text(
                  title,
                  style: const TextStyle(
                    color: AppTheme.darkSecondaryText,
                    fontSize: 14,
                    fontWeight: FontWeight.w500,
                  ),
                ),
                const SizedBox(height: 4),
                Text(
                  value,
                  style: const TextStyle(
                    color: AppTheme.darkPrimaryText,
                    fontSize: 24,
                    fontWeight: FontWeight.w700,
                  ),
                ),
                if (subtitle != null) ...[
                  const SizedBox(height: 4),
                  Text(
                    subtitle,
                    style: TextStyle(
                      color: AppTheme.primaryColor.withValues(alpha: 0.8),
                      fontSize: 12,
                      fontWeight: FontWeight.w500,
                    ),
                  ),
                ],
              ],
            ),
          ),
        ),
      ),
    );
  }

  /// Gradient App Bar
  static PreferredSizeWidget gradientAppBar({
    required String title,
    List<Widget>? actions,
    Widget? leading,
    bool centerTitle = true,
    LinearGradient? gradient,
  }) {
    return PreferredSize(
      preferredSize: const Size.fromHeight(kToolbarHeight),
      child: Container(
        decoration: BoxDecoration(
          gradient: gradient ?? AppTheme.primaryGradient,
        ),
        child: AppBar(
          title: Text(
            title,
            style: const TextStyle(
              color: Colors.white,
              fontSize: 20,
              fontWeight: FontWeight.w600,
            ),
          ),
          centerTitle: centerTitle,
          backgroundColor: Colors.transparent,
          elevation: 0,
          leading: leading,
          actions: actions,
          iconTheme: const IconThemeData(color: Colors.white),
        ),
      ),
    );
  }

  /// Shimmer Loading Card
  static Widget shimmerCard({
    double height = 120,
    double borderRadius = 16,
  }) {
    return Container(
      height: height,
      decoration: AppTheme.createCardDecoration(),
      child: Container(
        decoration: BoxDecoration(
          borderRadius: BorderRadius.circular(borderRadius),
          gradient: LinearGradient(
            colors: [
              AppTheme.darkCard,
              AppTheme.darkCard.withValues(alpha: 0.5),
              AppTheme.darkCard,
            ],
            stops: const [0.0, 0.5, 1.0],
            begin: Alignment.topLeft,
            end: Alignment.bottomRight,
          ),
        ),
      ),
    );
  }

  /// Progress Bar with Gradient
  static Widget gradientProgressBar({
    required double progress,
    double height = 8,
    double borderRadius = 4,
    LinearGradient? gradient,
    Color? backgroundColor,
  }) {
    return Container(
      height: height,
      decoration: BoxDecoration(
        color: backgroundColor ?? AppTheme.darkBorder,
        borderRadius: BorderRadius.circular(borderRadius),
      ),
      child: FractionallySizedBox(
        alignment: Alignment.centerLeft,
        widthFactor: progress.clamp(0.0, 1.0),
        child: Container(
          decoration: BoxDecoration(
            gradient: gradient ?? AppTheme.primaryGradient,
            borderRadius: BorderRadius.circular(borderRadius),
          ),
        ),
      ),
    );
  }

  /// Floating Action Button with Gradient
  static Widget gradientFAB({
    required VoidCallback onPressed,
    required IconData icon,
    LinearGradient? gradient,
    double size = 56,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: AppTheme.createGradientDecoration(
        gradient: gradient ?? AppTheme.primaryGradient,
        borderRadius: size / 2,
      ),
      child: Material(
        color: Colors.transparent,
        child: InkWell(
          onTap: onPressed,
          borderRadius: BorderRadius.circular(size / 2),
          child: Icon(
            icon,
            color: Colors.white,
            size: 24,
          ),
        ),
      ),
    );
  }

  /// Network Level Badge
  static Widget levelBadge({
    required int level,
    double size = 40,
  }) {
    return Container(
      width: size,
      height: size,
      decoration: AppTheme.createGradientDecoration(
        gradient: level > 5 ? AppTheme.secondaryGradient : AppTheme.primaryGradient,
        borderRadius: size / 2,
      ),
      child: Center(
        child: Text(
          'L$level',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 12,
            fontWeight: FontWeight.w700,
          ),
        ),
      ),
    );
  }
}
