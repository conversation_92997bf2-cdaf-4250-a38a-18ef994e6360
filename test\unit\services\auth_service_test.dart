import 'package:flutter_test/flutter_test.dart';
import 'package:mockito/mockito.dart';
import 'package:mockito/annotations.dart';
import 'package:firebase_auth/firebase_auth.dart';
import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:rama_realty_mlm/core/services/auth_service.dart';
import 'package:rama_realty_mlm/core/models/user_model.dart';

// Generate mocks
@GenerateMocks([
  FirebaseAuth,
  User,
  UserCredential,
  FirebaseFirestore,
  CollectionReference,
  DocumentReference,
  DocumentSnapshot,
])
import 'auth_service_test.mocks.dart';

void main() {
  group('AuthService Tests', () {
    late MockFirebaseAuth mockAuth;
    late MockFirebaseFirestore mockFirestore;
    late MockUser mockUser;
    late MockUserCredential mockUserCredential;
    late MockCollectionReference mockCollection;
    late MockDocumentReference mockDocument;
    late MockDocumentSnapshot mockSnapshot;

    setUp(() {
      mockAuth = MockFirebaseAuth();
      mockFirestore = MockFirebaseFirestore();
      mockUser = MockUser();
      mockUserCredential = MockUserCredential();
      mockCollection = MockCollectionReference();
      mockDocument = MockDocumentReference();
      mockSnapshot = MockDocumentSnapshot();
    });

    group('User Registration', () {
      test('should register user successfully with valid data', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';
        const name = 'Test User';
        const phoneNumber = '+************';
        const referralCode = 'REF123';

        when(mockAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockUserCredential);

        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test-uid');
        when(mockUser.email).thenReturn(email);

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc('test-uid')).thenReturn(mockDocument);
        when(mockDocument.set(any)).thenAnswer((_) async => {});

        // Mock upline user lookup
        when(mockCollection.where('referralCode', isEqualTo: referralCode))
            .thenReturn(mockCollection);
        when(mockCollection.limit(1)).thenReturn(mockCollection);
        when(mockCollection.get()).thenAnswer((_) async {
          final mockQuerySnapshot = MockQuerySnapshot();
          when(mockQuerySnapshot.docs).thenReturn([mockSnapshot]);
          when(mockSnapshot.id).thenReturn('upline-uid');
          when(mockSnapshot.data()).thenReturn({
            'id': 'upline-uid',
            'name': 'Upline User',
            'email': '<EMAIL>',
            'phoneNumber': '+************',
            'role': 'agent',
            'level': 1,
            'uplineId': null,
            'downlineIds': [],
            'totalStars': 5,
            'totalCommissions': 10000.0,
            'isActive': true,
            'isAdmin': false,
            'referralCode': referralCode,
            'createdAt': Timestamp.now(),
            'updatedAt': Timestamp.now(),
          });
          return mockQuerySnapshot;
        });

        // Mock email verification
        when(mockUser.sendEmailVerification()).thenAnswer((_) async => {});

        // Act
        final result = await AuthService.registerWithEmailAndPassword(
          email: email,
          password: password,
          name: name,
          phoneNumber: phoneNumber,
          referralCode: referralCode,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.user?.email, email);
        expect(result.user?.name, name);
        expect(result.user?.phoneNumber, phoneNumber);
        expect(result.user?.level, 2); // Should be upline level + 1

        verify(mockAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).called(1);
        verify(mockUser.sendEmailVerification()).called(1);
      });

      test('should fail registration with invalid email', () async {
        // Arrange
        const email = 'invalid-email';
        const password = 'password123';

        when(mockAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).thenThrow(FirebaseAuthException(
          code: 'invalid-email',
          message: 'The email address is not valid.',
        ));

        // Act
        final result = await AuthService.registerWithEmailAndPassword(
          email: email,
          password: password,
          name: 'Test User',
          phoneNumber: '+************',
        );

        // Assert
        expect(result.isSuccess, false);
        expect(result.message, 'The email address is not valid.');
      });

      test('should fail registration with weak password', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = '123';

        when(mockAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).thenThrow(FirebaseAuthException(
          code: 'weak-password',
          message: 'The password provided is too weak.',
        ));

        // Act
        final result = await AuthService.registerWithEmailAndPassword(
          email: email,
          password: password,
          name: 'Test User',
          phoneNumber: '+************',
        );

        // Assert
        expect(result.isSuccess, false);
        expect(result.message, 'The password provided is too weak.');
      });

      test('should fail registration with existing email', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(mockAuth.createUserWithEmailAndPassword(
          email: email,
          password: password,
        )).thenThrow(FirebaseAuthException(
          code: 'email-already-in-use',
          message: 'An account already exists for this email.',
        ));

        // Act
        final result = await AuthService.registerWithEmailAndPassword(
          email: email,
          password: password,
          name: 'Test User',
          phoneNumber: '+************',
        );

        // Assert
        expect(result.isSuccess, false);
        expect(result.message, 'An account already exists for this email.');
      });
    });

    group('User Sign In', () {
      test('should sign in successfully with valid credentials', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(mockAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockUserCredential);

        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('test-uid');

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc('test-uid')).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async {
          when(mockSnapshot.exists).thenReturn(true);
          when(mockSnapshot.data()).thenReturn({
            'id': 'test-uid',
            'name': 'Test User',
            'email': email,
            'phoneNumber': '+************',
            'role': 'agent',
            'level': 0,
            'uplineId': null,
            'downlineIds': [],
            'totalStars': 0,
            'totalCommissions': 0.0,
            'isActive': true,
            'isAdmin': false,
            'referralCode': 'TEST123',
            'createdAt': Timestamp.now(),
            'updatedAt': Timestamp.now(),
          });
          return mockSnapshot;
        });

        // Act
        final result = await AuthService.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result.isSuccess, true);
        expect(result.user?.email, email);
        expect(result.user?.isActive, true);

        verify(mockAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).called(1);
      });

      test('should fail sign in with wrong password', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'wrongpassword';

        when(mockAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).thenThrow(FirebaseAuthException(
          code: 'wrong-password',
          message: 'Wrong password provided.',
        ));

        // Act
        final result = await AuthService.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result.isSuccess, false);
        expect(result.message, 'Wrong password provided.');
      });

      test('should fail sign in with non-existent user', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(mockAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).thenThrow(FirebaseAuthException(
          code: 'user-not-found',
          message: 'No user found for this email.',
        ));

        // Act
        final result = await AuthService.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result.isSuccess, false);
        expect(result.message, 'No user found for this email.');
      });

      test('should fail sign in for deactivated user', () async {
        // Arrange
        const email = '<EMAIL>';
        const password = 'password123';

        when(mockAuth.signInWithEmailAndPassword(
          email: email,
          password: password,
        )).thenAnswer((_) async => mockUserCredential);

        when(mockUserCredential.user).thenReturn(mockUser);
        when(mockUser.uid).thenReturn('deactivated-uid');

        when(mockFirestore.collection('users')).thenReturn(mockCollection);
        when(mockCollection.doc('deactivated-uid')).thenReturn(mockDocument);
        when(mockDocument.get()).thenAnswer((_) async {
          when(mockSnapshot.exists).thenReturn(true);
          when(mockSnapshot.data()).thenReturn({
            'id': 'deactivated-uid',
            'name': 'Deactivated User',
            'email': email,
            'phoneNumber': '+************',
            'role': 'agent',
            'level': 0,
            'uplineId': null,
            'downlineIds': [],
            'totalStars': 0,
            'totalCommissions': 0.0,
            'isActive': false, // Deactivated user
            'isAdmin': false,
            'referralCode': 'DEACT123',
            'createdAt': Timestamp.now(),
            'updatedAt': Timestamp.now(),
          });
          return mockSnapshot;
        });

        when(mockAuth.signOut()).thenAnswer((_) async => {});

        // Act
        final result = await AuthService.signInWithEmailAndPassword(
          email: email,
          password: password,
        );

        // Assert
        expect(result.isSuccess, false);
        expect(result.message, 'Account is deactivated. Contact admin.');
        verify(mockAuth.signOut()).called(1);
      });
    });

    group('Referral Code Generation', () {
      test('should generate referral code from user ID', () {
        // Arrange
        const userId = 'abcdefgh12345678';

        // Act
        final referralCode = AuthService.generateReferralCode(userId);

        // Assert
        expect(referralCode, 'ABCDEFGH');
        expect(referralCode.length, 8);
      });
    });
  });
}

// Mock QuerySnapshot class
class MockQuerySnapshot extends Mock implements QuerySnapshot<Map<String, dynamic>> {}
