import 'package:flutter/foundation.dart';
import '../domain/admin_auth_state.dart';

/// Admin authentication service with separate credentials
class AdminAuthService {
  static const Map<String, Map<String, dynamic>> _adminCredentials = {
    '<EMAIL>': {
      'password': 'SuperAdmin@2024',
      'user': {
        'id': 'super_admin_001',
        'email': '<EMAIL>',
        'name': 'Super Administrator',
        'role': 'super_admin',
        'permissions': ['all'],
        'lastLogin': '',
      }
    },
    '<EMAIL>': {
      'password': 'Admin@2024',
      'user': {
        'id': 'admin_001',
        'email': '<EMAIL>',
        'name': 'System Administrator',
        'role': 'admin',
        'permissions': [
          'user_management',
          'property_management',
          'transaction_management',
          'commission_management',
          'analytics_view',
          'system_settings'
        ],
        'lastLogin': '',
      }
    },
    '<EMAIL>': {
      'password': 'Manager@2024',
      'user': {
        'id': 'manager_001',
        'email': '<EMAIL>',
        'name': 'Operations Manager',
        'role': 'manager',
        'permissions': [
          'user_management',
          'property_management',
          'transaction_management',
          'analytics_view'
        ],
        'lastLogin': '',
      }
    },
  };

  /// Authenticate admin user
  static Future<AdminAuthResult> authenticateAdmin({
    required String email,
    required String password,
  }) async {
    try {
      // Simulate network delay
      await Future.delayed(const Duration(milliseconds: 1500));

      if (kDebugMode) {
        print('Admin login attempt: $email');
      }

      // Check credentials
      final adminData = _adminCredentials[email.toLowerCase()];
      if (adminData == null) {
        if (kDebugMode) {
          print('Admin not found: $email');
        }
        return AdminAuthResult.failure('Invalid admin credentials');
      }

      if (adminData['password'] != password) {
        if (kDebugMode) {
          print('Invalid password for admin: $email');
        }
        return AdminAuthResult.failure('Invalid admin credentials');
      }

      // Create admin user
      final userData = Map<String, dynamic>.from(adminData['user']);
      userData['lastLogin'] = DateTime.now().toIso8601String();
      
      final adminUser = AdminUser.fromMap(userData);

      if (kDebugMode) {
        print('Admin login successful: ${adminUser.name} (${adminUser.role})');
      }

      return AdminAuthResult.success(adminUser);
    } catch (e) {
      if (kDebugMode) {
        print('Admin authentication error: $e');
      }
      return AdminAuthResult.failure('Authentication failed: $e');
    }
  }

  /// Validate admin session
  static Future<bool> validateAdminSession(String adminId) async {
    // In a real app, this would validate with backend
    return _adminCredentials.values
        .any((data) => data['user']['id'] == adminId);
  }

  /// Get admin permissions
  static List<String> getAdminPermissions(String email) {
    final adminData = _adminCredentials[email.toLowerCase()];
    if (adminData == null) return [];
    
    return List<String>.from(adminData['user']['permissions'] ?? []);
  }

  /// Check if email is admin
  static bool isAdminEmail(String email) {
    return _adminCredentials.containsKey(email.toLowerCase());
  }

  /// Get all admin roles for display
  static List<Map<String, dynamic>> getAdminRoles() {
    return [
      {
        'role': 'super_admin',
        'name': 'Super Administrator',
        'description': 'Full system access and control',
        'permissions': ['all']
      },
      {
        'role': 'admin',
        'name': 'System Administrator',
        'description': 'Manage users, properties, and transactions',
        'permissions': [
          'user_management',
          'property_management',
          'transaction_management',
          'commission_management',
          'analytics_view',
          'system_settings'
        ]
      },
      {
        'role': 'manager',
        'name': 'Operations Manager',
        'description': 'Manage daily operations and view analytics',
        'permissions': [
          'user_management',
          'property_management',
          'transaction_management',
          'analytics_view'
        ]
      },
    ];
  }
}

/// Admin authentication result
class AdminAuthResult {
  final bool isSuccess;
  final AdminUser? admin;
  final String? message;

  const AdminAuthResult._({
    required this.isSuccess,
    this.admin,
    this.message,
  });

  factory AdminAuthResult.success(AdminUser admin) {
    return AdminAuthResult._(
      isSuccess: true,
      admin: admin,
    );
  }

  factory AdminAuthResult.failure(String message) {
    return AdminAuthResult._(
      isSuccess: false,
      message: message,
    );
  }
}
