import 'dart:io';
import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:image_picker/image_picker.dart';

import '../../../../shared/themes/app_theme.dart';
import '../../../../shared/widgets/gradient_widgets.dart';
import '../../models/onboarding_data.dart';
import '../../providers/onboarding_provider.dart';

/// Profile image upload step in onboarding
class ProfileImageStep extends ConsumerStatefulWidget {
  const ProfileImageStep({super.key});

  @override
  ConsumerState<ProfileImageStep> createState() => _ProfileImageStepState();
}

class _ProfileImageStepState extends ConsumerState<ProfileImageStep> {
  final ImagePicker _imagePicker = ImagePicker();

  @override
  Widget build(BuildContext context) {
    final onboardingState = ref.watch(onboardingProvider);
    final data = onboardingState.data;

    return SingleChildScrollView(
      padding: const EdgeInsets.all(24),
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.center,
        children: [
          // Header
          Text(
            'Profile Picture',
            style: TextStyle(
              color: AppTheme.darkPrimaryText,
              fontSize: 28,
              fontWeight: FontWeight.bold,
            ),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 8),
          Text(
            'Add a profile picture to personalize your account (optional)',
            style: TextStyle(color: AppTheme.darkSecondaryText, fontSize: 16),
            textAlign: TextAlign.center,
          ),
          const SizedBox(height: 48),

          // Profile Image Preview
          _buildProfileImagePreview(data),

          const SizedBox(height: 32),

          // Action Buttons
          _buildActionButtons(),

          const SizedBox(height: 32),

          // Skip Option
          _buildSkipOption(),

          const SizedBox(height: 32),

          // Info Card
          _buildInfoCard(),
        ],
      ),
    );
  }

  /// Build profile image preview
  Widget _buildProfileImagePreview(OnboardingData data) {
    return Container(
      width: 160,
      height: 160,
      decoration: BoxDecoration(
        shape: BoxShape.circle,
        gradient: data.profileImage != null ? null : AppTheme.primaryGradient,
        border: Border.all(color: AppTheme.primaryColor, width: 4),
        boxShadow: [
          BoxShadow(
            color: AppTheme.primaryColor.withValues(alpha: 0.3),
            blurRadius: 20,
            offset: const Offset(0, 8),
          ),
        ],
      ),
      child: data.profileImage != null
          ? ClipOval(
              child: Image.file(
                data.profileImage!,
                width: 160,
                height: 160,
                fit: BoxFit.cover,
              ),
            )
          : Icon(Icons.person, size: 80, color: Colors.white),
    );
  }

  /// Build action buttons
  Widget _buildActionButtons() {
    return Row(
      children: [
        // Camera Button
        Expanded(
          child: GradientWidgets.gradientButton(
            text: 'Camera',
            onPressed: () => _pickImage(ImageSource.camera),
            icon: Icons.camera_alt,
            gradient: AppTheme.primaryGradient,
          ),
        ),
        const SizedBox(width: 16),
        // Gallery Button
        Expanded(
          child: GradientWidgets.gradientButton(
            text: 'Gallery',
            onPressed: () => _pickImage(ImageSource.gallery),
            icon: Icons.photo_library,
            gradient: AppTheme.secondaryGradient,
          ),
        ),
      ],
    );
  }

  /// Build skip option
  Widget _buildSkipOption() {
    return TextButton(
      onPressed: () {
        ref.read(onboardingProvider.notifier).skipProfileImage();
      },
      child: Row(
        mainAxisSize: MainAxisSize.min,
        children: [
          Text(
            'Skip for now',
            style: TextStyle(
              color: AppTheme.darkSecondaryText,
              fontSize: 16,
              fontWeight: FontWeight.w500,
            ),
          ),
          const SizedBox(width: 8),
          Icon(
            Icons.arrow_forward,
            color: AppTheme.darkSecondaryText,
            size: 20,
          ),
        ],
      ),
    );
  }

  /// Build info card
  Widget _buildInfoCard() {
    return GradientWidgets.gradientCard(
      gradient: AppTheme.cardGradient,
      child: Column(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Row(
            children: [
              Container(
                padding: const EdgeInsets.all(8),
                decoration: BoxDecoration(
                  gradient: AppTheme.primaryGradient,
                  borderRadius: BorderRadius.circular(8),
                ),
                child: Icon(
                  Icons.tips_and_updates,
                  color: Colors.white,
                  size: 20,
                ),
              ),
              const SizedBox(width: 12),
              Text(
                'Profile Picture Tips',
                style: TextStyle(
                  color: AppTheme.darkPrimaryText,
                  fontSize: 18,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ],
          ),
          const SizedBox(height: 16),
          _buildTipItem('📸 Use a clear, well-lit photo of your face'),
          _buildTipItem('👔 Professional appearance is recommended'),
          _buildTipItem('📱 You can always change it later from settings'),
          _buildTipItem(
            '🔒 Your photo is secure and only visible to your network',
          ),
        ],
      ),
    );
  }

  /// Build tip item
  Widget _buildTipItem(String text) {
    return Padding(
      padding: const EdgeInsets.only(bottom: 12),
      child: Row(
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          Container(
            width: 6,
            height: 6,
            margin: const EdgeInsets.only(top: 8, right: 12),
            decoration: BoxDecoration(
              color: AppTheme.primaryColor,
              shape: BoxShape.circle,
            ),
          ),
          Expanded(
            child: Text(
              text,
              style: TextStyle(
                color: AppTheme.darkSecondaryText,
                fontSize: 14,
                height: 1.4,
              ),
            ),
          ),
        ],
      ),
    );
  }

  /// Pick image from camera or gallery
  Future<void> _pickImage(ImageSource source) async {
    try {
      final XFile? pickedFile = await _imagePicker.pickImage(
        source: source,
        maxWidth: 512,
        maxHeight: 512,
        imageQuality: 80,
      );

      if (pickedFile != null) {
        final File imageFile = File(pickedFile.path);
        ref.read(onboardingProvider.notifier).updateProfileImage(imageFile);
      }
    } catch (e) {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          SnackBar(
            content: Text('Failed to pick image: ${e.toString()}'),
            backgroundColor: AppTheme.errorColor,
          ),
        );
      }
    }
  }
}
