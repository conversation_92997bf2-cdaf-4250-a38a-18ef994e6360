import 'package:cloud_firestore/cloud_firestore.dart';

/// Commission model for tracking MLM commissions
class CommissionModel {
  final String id;
  final String transactionId;
  final String agentId;
  final String agentName;
  final double amount; // Commission amount in INR
  final int level; // MLM level (0 = direct sale, 1-4 = upline levels)
  final double rate; // Commission rate percentage
  final String status; // pending, paid, cancelled
  final DateTime? paidAt;
  final DateTime createdAt;
  final String? paymentMethod;
  final String? paymentReference;
  final String? notes;

  const CommissionModel({
    required this.id,
    required this.transactionId,
    required this.agentId,
    required this.agentName,
    required this.amount,
    required this.level,
    required this.rate,
    this.status = 'pending',
    this.paidAt,
    required this.createdAt,
    this.paymentMethod,
    this.paymentReference,
    this.notes,
  });

  /// Create CommissionModel from Firestore document
  factory CommissionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return CommissionModel(
      id: doc.id,
      transactionId: data['transactionId'] ?? '',
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      amount: (data['amount'] ?? 0.0).toDouble(),
      level: data['level'] ?? 0,
      rate: (data['rate'] ?? 0.0).toDouble(),
      status: data['status'] ?? 'pending',
      paidAt: data['paidAt'] != null ? (data['paidAt'] as Timestamp).toDate() : null,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      paymentMethod: data['paymentMethod'],
      paymentReference: data['paymentReference'],
      notes: data['notes'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'transactionId': transactionId,
      'agentId': agentId,
      'agentName': agentName,
      'amount': amount,
      'level': level,
      'rate': rate,
      'status': status,
      'paidAt': paidAt != null ? Timestamp.fromDate(paidAt!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'paymentMethod': paymentMethod,
      'paymentReference': paymentReference,
      'notes': notes,
    };
  }

  /// Create a copy with updated fields
  CommissionModel copyWith({
    String? id,
    String? transactionId,
    String? agentId,
    String? agentName,
    double? amount,
    int? level,
    double? rate,
    String? status,
    DateTime? paidAt,
    DateTime? createdAt,
    String? paymentMethod,
    String? paymentReference,
    String? notes,
  }) {
    return CommissionModel(
      id: id ?? this.id,
      transactionId: transactionId ?? this.transactionId,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      amount: amount ?? this.amount,
      level: level ?? this.level,
      rate: rate ?? this.rate,
      status: status ?? this.status,
      paidAt: paidAt ?? this.paidAt,
      createdAt: createdAt ?? this.createdAt,
      paymentMethod: paymentMethod ?? this.paymentMethod,
      paymentReference: paymentReference ?? this.paymentReference,
      notes: notes ?? this.notes,
    );
  }

  /// Get formatted commission amount in Indian Rupees
  String get formattedAmount {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(2)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(2)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(2)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }

  /// Get commission level description
  String get levelDescription {
    switch (level) {
      case 0: return 'Direct Sale';
      case 1: return 'Level 1 (Upline)';
      case 2: return 'Level 2 (Upline)';
      case 3: return 'Level 3 (Upline)';
      case 4: return 'Level 4 (Upline)';
      default: return 'Level $level';
    }
  }

  /// Check if commission is paid
  bool get isPaid => status == 'paid';

  /// Check if commission is pending
  bool get isPending => status == 'pending';

  /// Check if commission is cancelled
  bool get isCancelled => status == 'cancelled';

  @override
  String toString() {
    return 'CommissionModel(id: $id, agent: $agentName, amount: $formattedAmount, level: $level, status: $status)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is CommissionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}

/// Transaction model for property sales
class TransactionModel {
  final String id;
  final String propertyId;
  final String propertyTitle;
  final String agentId; // Selling agent
  final String agentName;
  final String? buyerName;
  final String? buyerContact;
  final String type; // sale, rental
  final double propertyAmount; // Final agreed amount in INR
  final double commissionAmount; // Total commission entered by admin
  final double commissionRate; // Commission rate as percentage of property amount
  final String status; // pending, completed, cancelled
  final DateTime? completedAt;
  final DateTime createdAt;
  final String? notes;
  final Map<String, dynamic>? additionalDetails;

  const TransactionModel({
    required this.id,
    required this.propertyId,
    required this.propertyTitle,
    required this.agentId,
    required this.agentName,
    this.buyerName,
    this.buyerContact,
    required this.type,
    required this.propertyAmount,
    required this.commissionAmount,
    required this.commissionRate,
    this.status = 'pending',
    this.completedAt,
    required this.createdAt,
    this.notes,
    this.additionalDetails,
  });

  /// Create TransactionModel from Firestore document
  factory TransactionModel.fromFirestore(DocumentSnapshot doc) {
    final data = doc.data() as Map<String, dynamic>;
    
    return TransactionModel(
      id: doc.id,
      propertyId: data['propertyId'] ?? '',
      propertyTitle: data['propertyTitle'] ?? '',
      agentId: data['agentId'] ?? '',
      agentName: data['agentName'] ?? '',
      buyerName: data['buyerName'],
      buyerContact: data['buyerContact'],
      type: data['type'] ?? 'sale',
      propertyAmount: (data['propertyAmount'] ?? 0.0).toDouble(),
      commissionAmount: (data['commissionAmount'] ?? 0.0).toDouble(),
      commissionRate: (data['commissionRate'] ?? 0.0).toDouble(),
      status: data['status'] ?? 'pending',
      completedAt: data['completedAt'] != null ? (data['completedAt'] as Timestamp).toDate() : null,
      createdAt: (data['createdAt'] as Timestamp).toDate(),
      notes: data['notes'],
      additionalDetails: data['additionalDetails'],
    );
  }

  /// Convert to Firestore document
  Map<String, dynamic> toFirestore() {
    return {
      'propertyId': propertyId,
      'propertyTitle': propertyTitle,
      'agentId': agentId,
      'agentName': agentName,
      'buyerName': buyerName,
      'buyerContact': buyerContact,
      'type': type,
      'propertyAmount': propertyAmount,
      'commissionAmount': commissionAmount,
      'commissionRate': commissionRate,
      'status': status,
      'completedAt': completedAt != null ? Timestamp.fromDate(completedAt!) : null,
      'createdAt': Timestamp.fromDate(createdAt),
      'notes': notes,
      'additionalDetails': additionalDetails,
    };
  }

  /// Create a copy with updated fields
  TransactionModel copyWith({
    String? id,
    String? propertyId,
    String? propertyTitle,
    String? agentId,
    String? agentName,
    String? buyerName,
    String? buyerContact,
    String? type,
    double? propertyAmount,
    double? commissionAmount,
    double? commissionRate,
    String? status,
    DateTime? completedAt,
    DateTime? createdAt,
    String? notes,
    Map<String, dynamic>? additionalDetails,
  }) {
    return TransactionModel(
      id: id ?? this.id,
      propertyId: propertyId ?? this.propertyId,
      propertyTitle: propertyTitle ?? this.propertyTitle,
      agentId: agentId ?? this.agentId,
      agentName: agentName ?? this.agentName,
      buyerName: buyerName ?? this.buyerName,
      buyerContact: buyerContact ?? this.buyerContact,
      type: type ?? this.type,
      propertyAmount: propertyAmount ?? this.propertyAmount,
      commissionAmount: commissionAmount ?? this.commissionAmount,
      commissionRate: commissionRate ?? this.commissionRate,
      status: status ?? this.status,
      completedAt: completedAt ?? this.completedAt,
      createdAt: createdAt ?? this.createdAt,
      notes: notes ?? this.notes,
      additionalDetails: additionalDetails ?? this.additionalDetails,
    );
  }

  /// Get formatted property amount
  String get formattedPropertyAmount {
    if (propertyAmount >= 10000000) {
      return '₹${(propertyAmount / 10000000).toStringAsFixed(2)} Cr';
    } else if (propertyAmount >= 100000) {
      return '₹${(propertyAmount / 100000).toStringAsFixed(2)} L';
    } else {
      return '₹${propertyAmount.toStringAsFixed(0)}';
    }
  }

  /// Get formatted commission amount
  String get formattedCommissionAmount {
    if (commissionAmount >= 10000000) {
      return '₹${(commissionAmount / 10000000).toStringAsFixed(2)} Cr';
    } else if (commissionAmount >= 100000) {
      return '₹${(commissionAmount / 100000).toStringAsFixed(2)} L';
    } else if (commissionAmount >= 1000) {
      return '₹${(commissionAmount / 1000).toStringAsFixed(2)} K';
    } else {
      return '₹${commissionAmount.toStringAsFixed(0)}';
    }
  }

  /// Check if transaction is completed
  bool get isCompleted => status == 'completed';

  /// Check if transaction is pending
  bool get isPending => status == 'pending';

  /// Check if transaction is cancelled
  bool get isCancelled => status == 'cancelled';

  @override
  String toString() {
    return 'TransactionModel(id: $id, property: $propertyTitle, agent: $agentName, amount: $formattedPropertyAmount, commission: $formattedCommissionAmount)';
  }

  @override
  bool operator ==(Object other) {
    if (identical(this, other)) return true;
    return other is TransactionModel && other.id == id;
  }

  @override
  int get hashCode => id.hashCode;
}
