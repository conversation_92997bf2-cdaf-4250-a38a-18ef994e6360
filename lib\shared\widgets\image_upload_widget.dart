import 'dart:typed_data';
import 'package:flutter/material.dart';
import 'package:flutter/foundation.dart';
import 'package:file_picker/file_picker.dart';
import 'package:cached_network_image/cached_network_image.dart';
import '../../services/cloudinary_service.dart';

/// Widget for uploading images to Cloudinary
class ImageUploadWidget extends StatefulWidget {
  final String? currentImageUrl;
  final Function(String imageUrl)? onImageUploaded;
  final Function()? onImageRemoved;
  final double width;
  final double height;
  final String? placeholder;
  final bool allowRemove;
  final String? folder;

  const ImageUploadWidget({
    super.key,
    this.currentImageUrl,
    this.onImageUploaded,
    this.onImageRemoved,
    this.width = 200,
    this.height = 150,
    this.placeholder = 'Tap to upload image',
    this.allowRemove = true,
    this.folder,
  });

  @override
  State<ImageUploadWidget> createState() => _ImageUploadWidgetState();
}

class _ImageUploadWidgetState extends State<ImageUploadWidget> {
  bool _isUploading = false;

  @override
  Widget build(BuildContext context) {
    return Container(
      width: widget.width,
      height: widget.height,
      decoration: BoxDecoration(
        borderRadius: BorderRadius.circular(12),
        border: Border.all(
          color: Colors.grey[600]!,
          style: BorderStyle.solid,
          width: 1,
        ),
      ),
      child: Stack(
        children: [
          // Image or placeholder
          Positioned.fill(
            child: ClipRRect(
              borderRadius: BorderRadius.circular(11),
              child: widget.currentImageUrl != null
                  ? CachedNetworkImage(
                      imageUrl: CloudinaryService.getMediumUrl(
                        widget.currentImageUrl!,
                      ),
                      fit: BoxFit.cover,
                      placeholder: (context, url) => Container(
                        color: Colors.grey[800],
                        child: const Center(
                          child: CircularProgressIndicator(
                            strokeWidth: 2,
                            valueColor: AlwaysStoppedAnimation<Color>(
                              Colors.orange,
                            ),
                          ),
                        ),
                      ),
                      errorWidget: (context, url, error) => Container(
                        color: Colors.grey[800],
                        child: const Icon(
                          Icons.broken_image,
                          color: Colors.grey,
                          size: 32,
                        ),
                      ),
                    )
                  : GestureDetector(
                      onTap: _isUploading ? null : _pickAndUploadImage,
                      child: Container(
                        color: Colors.grey[800],
                        child: Center(
                          child: _isUploading
                              ? const Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    CircularProgressIndicator(
                                      strokeWidth: 2,
                                      valueColor: AlwaysStoppedAnimation<Color>(
                                        Colors.orange,
                                      ),
                                    ),
                                    SizedBox(height: 8),
                                    Text(
                                      'Uploading...',
                                      style: TextStyle(color: Colors.grey),
                                    ),
                                  ],
                                )
                              : Column(
                                  mainAxisAlignment: MainAxisAlignment.center,
                                  children: [
                                    const Icon(
                                      Icons.cloud_upload,
                                      color: Colors.grey,
                                      size: 32,
                                    ),
                                    const SizedBox(height: 8),
                                    Text(
                                      widget.placeholder!,
                                      style: const TextStyle(
                                        color: Colors.grey,
                                      ),
                                      textAlign: TextAlign.center,
                                    ),
                                  ],
                                ),
                        ),
                      ),
                    ),
            ),
          ),

          // Action buttons
          if (widget.currentImageUrl != null)
            Positioned(
              top: 8,
              right: 8,
              child: Row(
                mainAxisSize: MainAxisSize.min,
                children: [
                  // Replace button
                  GestureDetector(
                    onTap: _isUploading ? null : _pickAndUploadImage,
                    child: Container(
                      padding: const EdgeInsets.all(6),
                      decoration: const BoxDecoration(
                        color: Color(0xFFFF6B35),
                        shape: BoxShape.circle,
                      ),
                      child: _isUploading
                          ? const SizedBox(
                              width: 16,
                              height: 16,
                              child: CircularProgressIndicator(
                                strokeWidth: 2,
                                valueColor: AlwaysStoppedAnimation<Color>(
                                  Colors.white,
                                ),
                              ),
                            )
                          : const Icon(
                              Icons.edit,
                              color: Colors.white,
                              size: 16,
                            ),
                    ),
                  ),

                  // Remove button
                  if (widget.allowRemove) ...[
                    const SizedBox(width: 8),
                    GestureDetector(
                      onTap: _removeImage,
                      child: Container(
                        padding: const EdgeInsets.all(6),
                        decoration: const BoxDecoration(
                          color: Colors.red,
                          shape: BoxShape.circle,
                        ),
                        child: const Icon(
                          Icons.delete,
                          color: Colors.white,
                          size: 16,
                        ),
                      ),
                    ),
                  ],
                ],
              ),
            ),
        ],
      ),
    );
  }

  Future<void> _pickAndUploadImage() async {
    if (!CloudinaryService.isConfigured) {
      _showError(
        'Cloudinary service is not configured. Please check your credentials.',
      );
      return;
    }

    try {
      if (kDebugMode) {
        print('🔄 ImageUpload: Starting image selection...');
      }

      // Pick image file with better web support
      FilePickerResult? result;

      try {
        result = await FilePicker.platform.pickFiles(
          type: FileType.image,
          allowMultiple: false,
          withData: true,
        );
      } catch (e) {
        if (kDebugMode) {
          print('❌ ImageUpload: FilePicker error: $e');
        }
        // Fallback for web - show a more user-friendly message
        _showError(
          'Image selection not supported in this browser. Please try a different browser or use the mobile app.',
        );
        return;
      }

      if (result == null || result.files.isEmpty) {
        if (kDebugMode) {
          print('📝 ImageUpload: No file selected');
        }
        return;
      }

      final file = result.files.first;
      if (kDebugMode) {
        print('📄 ImageUpload: File selected: ${file.name}');
        print('📊 ImageUpload: File size: ${file.size} bytes');
      }

      if (file.bytes == null) {
        _showError('Could not read file data');
        return;
      }

      // Check file size (max 10MB for free tier)
      if (file.size > 10 * 1024 * 1024) {
        _showError('Image too large. Please select an image under 10MB.');
        return;
      }

      setState(() => _isUploading = true);

      // Upload to Cloudinary
      final imageUrl = await CloudinaryService.uploadImage(
        imageBytes: file.bytes!,
        fileName: file.name,
        folder: widget.folder,
        tags: {'app': 'rama_realty_mlm'},
      );

      if (imageUrl != null) {
        widget.onImageUploaded?.call(imageUrl);
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Image uploaded successfully!'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        _showError('Failed to upload image. Please try again.');
      }
    } catch (e) {
      _showError('Error uploading image: $e');
    } finally {
      if (mounted) {
        setState(() => _isUploading = false);
      }
    }
  }

  void _removeImage() {
    widget.onImageRemoved?.call();
  }

  void _showError(String message) {
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(content: Text(message), backgroundColor: Colors.red),
      );
    }
  }
}

/// Widget for multiple image uploads
class MultiImageUploadWidget extends StatefulWidget {
  final List<String> currentImageUrls;
  final Function(List<String> imageUrls)? onImagesChanged;
  final int maxImages;
  final String? folder;

  const MultiImageUploadWidget({
    super.key,
    required this.currentImageUrls,
    this.onImagesChanged,
    this.maxImages = 5,
    this.folder,
  });

  @override
  State<MultiImageUploadWidget> createState() => _MultiImageUploadWidgetState();
}

class _MultiImageUploadWidgetState extends State<MultiImageUploadWidget> {
  late List<String> _imageUrls;

  @override
  void initState() {
    super.initState();
    _imageUrls = List.from(widget.currentImageUrls);
  }

  @override
  Widget build(BuildContext context) {
    return Column(
      crossAxisAlignment: CrossAxisAlignment.start,
      children: [
        Text(
          'Images (${_imageUrls.length}/${widget.maxImages})',
          style: const TextStyle(
            color: Colors.white,
            fontSize: 16,
            fontWeight: FontWeight.w500,
          ),
        ),
        const SizedBox(height: 12),

        // Image grid
        GridView.builder(
          shrinkWrap: true,
          physics: const NeverScrollableScrollPhysics(),
          gridDelegate: const SliverGridDelegateWithFixedCrossAxisCount(
            crossAxisCount: 3,
            crossAxisSpacing: 12,
            mainAxisSpacing: 12,
            childAspectRatio: 1,
          ),
          itemCount:
              _imageUrls.length +
              (_imageUrls.length < widget.maxImages ? 1 : 0),
          itemBuilder: (context, index) {
            if (index < _imageUrls.length) {
              // Existing image
              return ImageUploadWidget(
                currentImageUrl: _imageUrls[index],
                width: 100,
                height: 100,
                onImageUploaded: (newUrl) {
                  setState(() {
                    _imageUrls[index] = newUrl;
                  });
                  widget.onImagesChanged?.call(_imageUrls);
                },
                onImageRemoved: () {
                  setState(() {
                    _imageUrls.removeAt(index);
                  });
                  widget.onImagesChanged?.call(_imageUrls);
                },
                folder: widget.folder,
              );
            } else {
              // Add new image button
              return ImageUploadWidget(
                width: 100,
                height: 100,
                placeholder: 'Add Image',
                allowRemove: false,
                onImageUploaded: (newUrl) {
                  setState(() {
                    _imageUrls.add(newUrl);
                  });
                  widget.onImagesChanged?.call(_imageUrls);
                },
                folder: widget.folder,
              );
            }
          },
        ),
      ],
    );
  }
}
