import 'package:cloud_firestore/cloud_firestore.dart';
import 'package:firebase_storage/firebase_storage.dart';
import 'dart:io';
import '../models/property_model.dart';
import '../constants/app_constants.dart';

/// Service for managing property operations
class PropertyService {
  static final FirebaseFirestore _firestore = FirebaseFirestore.instance;
  static final FirebaseStorage _storage = FirebaseStorage.instance;

  /// Get all properties with optional filters
  static Future<List<PropertyModel>> getProperties({
    String? type,
    String? status,
    String? city,
    String? state,
    double? minPrice,
    double? maxPrice,
    bool? isApproved,
    bool? isFeatured,
    String? assignedAgentId,
    int limit = 50,
  }) async {
    try {
      Query query = _firestore.collection(AppConstants.propertiesCollection);

      // Apply filters
      if (type != null && type.isNotEmpty) {
        query = query.where('type', isEqualTo: type);
      }
      if (status != null && status.isNotEmpty) {
        query = query.where('status', isEqualTo: status);
      }
      if (city != null && city.isNotEmpty) {
        query = query.where('city', isEqualTo: city);
      }
      if (state != null && state.isNotEmpty) {
        query = query.where('state', isEqualTo: state);
      }
      if (isApproved != null) {
        query = query.where('isApproved', isEqualTo: isApproved);
      }
      if (isFeatured != null) {
        query = query.where('isFeatured', isEqualTo: isFeatured);
      }
      if (assignedAgentId != null && assignedAgentId.isNotEmpty) {
        query = query.where('assignedAgentId', isEqualTo: assignedAgentId);
      }

      // Price range filtering (done client-side for complex range queries)
      query = query.orderBy('createdAt', descending: true).limit(limit);

      final querySnapshot = await query.get();
      var properties = querySnapshot.docs
          .map((doc) => PropertyModel.fromFirestore(doc))
          .toList();

      // Apply price filters client-side
      if (minPrice != null) {
        properties = properties.where((p) => p.price >= minPrice).toList();
      }
      if (maxPrice != null) {
        properties = properties.where((p) => p.price <= maxPrice).toList();
      }

      return properties;
    } catch (e) {
      print('Error getting properties: $e');
      return [];
    }
  }

  /// Get property by ID
  static Future<PropertyModel?> getPropertyById(String propertyId) async {
    try {
      final doc = await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyId)
          .get();

      if (doc.exists) {
        return PropertyModel.fromFirestore(doc);
      }
      return null;
    } catch (e) {
      print('Error getting property by ID: $e');
      return null;
    }
  }

  /// Create new property (Admin only)
  static Future<PropertyResult> createProperty(PropertyModel property) async {
    try {
      final docRef = await _firestore
          .collection(AppConstants.propertiesCollection)
          .add(property.toFirestore());

      final createdProperty = property.copyWith(id: docRef.id);
      
      // Update with the generated ID
      await docRef.update({'id': docRef.id});

      return PropertyResult.success(createdProperty);
    } catch (e) {
      return PropertyResult.failure('Failed to create property: ${e.toString()}');
    }
  }

  /// Update property (Admin only)
  static Future<PropertyResult> updateProperty(PropertyModel property) async {
    try {
      await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(property.id)
          .update(property.copyWith(updatedAt: DateTime.now()).toFirestore());

      return PropertyResult.success(property);
    } catch (e) {
      return PropertyResult.failure('Failed to update property: ${e.toString()}');
    }
  }

  /// Delete property (Admin only)
  static Future<PropertyResult> deleteProperty(String propertyId) async {
    try {
      // Get property to delete associated images
      final property = await getPropertyById(propertyId);
      
      // Delete property document
      await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyId)
          .delete();

      // Delete associated images from storage
      if (property != null && property.imageUrls.isNotEmpty) {
        await _deletePropertyImages(property.imageUrls);
      }

      return PropertyResult.success(null);
    } catch (e) {
      return PropertyResult.failure('Failed to delete property: ${e.toString()}');
    }
  }

  /// Upload property images
  static Future<List<String>> uploadPropertyImages(
    String propertyId,
    List<File> imageFiles,
  ) async {
    try {
      final uploadedUrls = <String>[];

      for (int i = 0; i < imageFiles.length; i++) {
        final file = imageFiles[i];
        final fileName = 'property_${propertyId}_image_${i}_${DateTime.now().millisecondsSinceEpoch}.jpg';
        final ref = _storage.ref().child('${AppConstants.propertyImagesPath}/$fileName');

        final uploadTask = await ref.putFile(file);
        final downloadUrl = await uploadTask.ref.getDownloadURL();
        uploadedUrls.add(downloadUrl);
      }

      return uploadedUrls;
    } catch (e) {
      print('Error uploading property images: $e');
      return [];
    }
  }

  /// Delete property images from storage
  static Future<void> _deletePropertyImages(List<String> imageUrls) async {
    try {
      for (final url in imageUrls) {
        final ref = _storage.refFromURL(url);
        await ref.delete();
      }
    } catch (e) {
      print('Error deleting property images: $e');
    }
  }

  /// Enhanced search properties with advanced algorithms
  static Future<List<PropertyModel>> searchProperties(String searchQuery) async {
    try {
      // Get all approved properties for search
      final properties = await getProperties(isApproved: true, limit: 1000);

      if (searchQuery.trim().isEmpty) return properties;

      final query = searchQuery.toLowerCase().trim();

      // Apply different search strategies
      final results = <PropertyModel>[];
      final scores = <PropertyModel, double>{};

      for (final property in properties) {
        final score = _calculateSearchScore(property, query);
        if (score > 0) {
          results.add(property);
          scores[property] = score;
        }
      }

      // Sort by relevance score
      results.sort((a, b) => scores[b]!.compareTo(scores[a]!));

      return results;
    } catch (e) {
      print('Error searching properties: $e');
      return [];
    }
  }

  /// Calculate search relevance score
  static double _calculateSearchScore(PropertyModel property, String query) {
    double score = 0.0;

    // Exact title match gets highest score
    if (property.title.toLowerCase() == query) {
      score += 100.0;
    } else if (property.title.toLowerCase().contains(query)) {
      score += 50.0;
    }

    // City and location matches
    if (property.city.toLowerCase() == query) {
      score += 80.0;
    } else if (property.city.toLowerCase().contains(query)) {
      score += 40.0;
    }

    if (property.location.toLowerCase().contains(query)) {
      score += 30.0;
    }

    // Type matches
    if (property.type.toLowerCase() == query) {
      score += 70.0;
    } else if (property.type.toLowerCase().contains(query)) {
      score += 35.0;
    }

    // State matches
    if (property.state.toLowerCase().contains(query)) {
      score += 20.0;
    }

    // Description matches (lower priority)
    if (property.description.toLowerCase().contains(query)) {
      score += 10.0;
    }

    // Amenities matches
    for (final amenity in property.amenities) {
      if (amenity.toLowerCase().contains(query)) {
        score += 15.0;
      }
    }

    // Price-based scoring
    score += _calculatePriceScore(property.price, query);

    // Area-based scoring
    if (property.areaSquareFeet != null) {
      score += _calculateAreaScore(property.areaSquareFeet!, query);
    }

    // Room-based scoring
    score += _calculateRoomScore(property, query);

    // Featured properties get bonus
    if (property.isFeatured) {
      score += 5.0;
    }

    return score;
  }

  /// Calculate price-based search score
  static double _calculatePriceScore(double price, String query) {
    double score = 0.0;

    // Check for price-related keywords
    if (query.contains('cheap') || query.contains('affordable')) {
      if (price < 5000000) score += 20.0; // Under 50 lakh
    }

    if (query.contains('luxury') || query.contains('premium')) {
      if (price > 50000000) score += 20.0; // Above 5 crore
    }

    // Check for specific price mentions
    final priceInLakhs = price / 100000;
    final priceInCrores = price / 10000000;

    if (query.contains('lakh')) {
      final numbers = RegExp(r'\d+').allMatches(query);
      for (final match in numbers) {
        final number = double.tryParse(match.group(0)!);
        if (number != null && (priceInLakhs - number).abs() < 10) {
          score += 25.0;
        }
      }
    }

    if (query.contains('crore')) {
      final numbers = RegExp(r'\d+').allMatches(query);
      for (final match in numbers) {
        final number = double.tryParse(match.group(0)!);
        if (number != null && (priceInCrores - number).abs() < 1) {
          score += 25.0;
        }
      }
    }

    return score;
  }

  /// Calculate area-based search score
  static double _calculateAreaScore(double area, String query) {
    if (query.contains('sqft') || query.contains('square')) {
      final numbers = RegExp(r'\d+').allMatches(query);
      for (final match in numbers) {
        final number = double.tryParse(match.group(0)!);
        if (number != null && (area - number).abs() < 200) {
          return 15.0;
        }
      }
    }
    return 0.0;
  }

  /// Calculate room-based search score
  static double _calculateRoomScore(PropertyModel property, String query) {
    double score = 0.0;

    // BHK patterns
    final bhkPattern = RegExp(r'(\d+)\s*bhk');
    final bhkMatch = bhkPattern.firstMatch(query);
    if (bhkMatch != null && property.bedrooms != null) {
      final bhk = int.tryParse(bhkMatch.group(1)!);
      if (bhk != null && property.bedrooms == bhk) {
        score += 30.0;
      }
    }

    // Bedroom mentions
    if (query.contains('bedroom') && property.bedrooms != null) {
      final numbers = RegExp(r'\d+').allMatches(query);
      for (final match in numbers) {
        final number = int.tryParse(match.group(0)!);
        if (number != null && property.bedrooms == number) {
          score += 20.0;
        }
      }
    }

    // Bathroom mentions
    if (query.contains('bathroom') && property.bathrooms != null) {
      final numbers = RegExp(r'\d+').allMatches(query);
      for (final match in numbers) {
        final number = int.tryParse(match.group(0)!);
        if (number != null && property.bathrooms == number) {
          score += 15.0;
        }
      }
    }

    return score;
  }

  /// Get properties by agent
  static Future<List<PropertyModel>> getPropertiesByAgent(String agentId) async {
    try {
      return await getProperties(assignedAgentId: agentId, isApproved: true);
    } catch (e) {
      print('Error getting properties by agent: $e');
      return [];
    }
  }

  /// Approve property (Admin only)
  static Future<PropertyResult> approveProperty(String propertyId) async {
    try {
      await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyId)
          .update({
        'isApproved': true,
        'updatedAt': Timestamp.now(),
      });

      return PropertyResult.success(null);
    } catch (e) {
      return PropertyResult.failure('Failed to approve property: ${e.toString()}');
    }
  }

  /// Reject property (Admin only)
  static Future<PropertyResult> rejectProperty(String propertyId) async {
    try {
      await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyId)
          .update({
        'isApproved': false,
        'updatedAt': Timestamp.now(),
      });

      return PropertyResult.success(null);
    } catch (e) {
      return PropertyResult.failure('Failed to reject property: ${e.toString()}');
    }
  }

  /// Toggle featured status (Admin only)
  static Future<PropertyResult> toggleFeaturedStatus(String propertyId, bool isFeatured) async {
    try {
      await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyId)
          .update({
        'isFeatured': isFeatured,
        'updatedAt': Timestamp.now(),
      });

      return PropertyResult.success(null);
    } catch (e) {
      return PropertyResult.failure('Failed to update featured status: ${e.toString()}');
    }
  }

  /// Assign property to agent (Admin only)
  static Future<PropertyResult> assignPropertyToAgent(String propertyId, String agentId) async {
    try {
      await _firestore
          .collection(AppConstants.propertiesCollection)
          .doc(propertyId)
          .update({
        'assignedAgentId': agentId,
        'updatedAt': Timestamp.now(),
      });

      return PropertyResult.success(null);
    } catch (e) {
      return PropertyResult.failure('Failed to assign property: ${e.toString()}');
    }
  }

  /// Get property statistics
  static Future<Map<String, dynamic>> getPropertyStatistics() async {
    try {
      final allProperties = await getProperties(limit: 1000);
      
      final stats = {
        'totalProperties': allProperties.length,
        'approvedProperties': allProperties.where((p) => p.isApproved).length,
        'pendingApproval': allProperties.where((p) => !p.isApproved).length,
        'featuredProperties': allProperties.where((p) => p.isFeatured).length,
        'forSale': allProperties.where((p) => p.status == 'for_sale').length,
        'forRent': allProperties.where((p) => p.status == 'for_rent').length,
        'sold': allProperties.where((p) => p.status == 'sold').length,
        'rented': allProperties.where((p) => p.status == 'rented').length,
        'totalValue': allProperties.fold<double>(0, (sum, p) => sum + p.price),
        'averagePrice': allProperties.isNotEmpty 
            ? allProperties.fold<double>(0, (sum, p) => sum + p.price) / allProperties.length 
            : 0,
        'typeDistribution': _getTypeDistribution(allProperties),
        'cityDistribution': _getCityDistribution(allProperties),
      };

      return stats;
    } catch (e) {
      print('Error getting property statistics: $e');
      return {};
    }
  }

  /// Get type distribution
  static Map<String, int> _getTypeDistribution(List<PropertyModel> properties) {
    final distribution = <String, int>{};
    for (final property in properties) {
      distribution[property.type] = (distribution[property.type] ?? 0) + 1;
    }
    return distribution;
  }

  /// Get city distribution
  static Map<String, int> _getCityDistribution(List<PropertyModel> properties) {
    final distribution = <String, int>{};
    for (final property in properties) {
      distribution[property.city] = (distribution[property.city] ?? 0) + 1;
    }
    return distribution;
  }

  /// Format Indian Rupee amount
  static String formatIndianRupees(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(2)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(2)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(2)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }
}

/// Property operation result wrapper
class PropertyResult {
  final bool isSuccess;
  final PropertyModel? property;
  final String? message;

  PropertyResult._(this.isSuccess, this.property, this.message);

  factory PropertyResult.success(PropertyModel? property, {String? message}) {
    return PropertyResult._(true, property, message);
  }

  factory PropertyResult.failure(String message) {
    return PropertyResult._(false, null, message);
  }
}
