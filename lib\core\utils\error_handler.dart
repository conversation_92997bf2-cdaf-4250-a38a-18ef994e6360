import 'dart:developer' as developer;
import 'package:firebase_auth/firebase_auth.dart';

/// Error severity levels
enum ErrorSeverity { info, warning, error, critical }

/// Application error model
class AppError {
  final String message;
  final String? errorCode;
  final ErrorSeverity severity;
  final bool isUserFriendly;
  final StackTrace? stackTrace;
  final Map<String, dynamic>? metadata;

  const AppError({
    required this.message,
    this.errorCode,
    required this.severity,
    required this.isUserFriendly,
    this.stackTrace,
    this.metadata,
  });

  @override
  String toString() {
    return 'AppError(message: $message, code: $errorCode, severity: $severity)';
  }
}

/// Centralized error handling utility
class ErrorHandler {
  /// Handle Firebase Authentication errors
  static AppError handleFirebaseAuthError(FirebaseAuthException error) {
    switch (error.code) {
      case 'invalid-email':
        return const AppError(
          message: 'Please enter a valid email address.',
          errorCode: 'invalid-email',
          severity: ErrorSeverity.warning,
          isUserFriendly: true,
        );

      case 'weak-password':
        return const AppError(
          message: 'Password must be at least 6 characters long.',
          errorCode: 'weak-password',
          severity: ErrorSeverity.warning,
          isUserFriendly: true,
        );

      case 'email-already-in-use':
        return const AppError(
          message:
              'An account with this email already exists. Please sign in instead.',
          errorCode: 'email-already-in-use',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
        );

      case 'user-not-found':
        return const AppError(
          message:
              'No account found with this email. Please check your email or create a new account.',
          errorCode: 'user-not-found',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
        );

      case 'wrong-password':
        return const AppError(
          message: 'Incorrect password. Please try again.',
          errorCode: 'wrong-password',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
        );

      case 'too-many-requests':
        return const AppError(
          message: 'Too many failed attempts. Please try again later.',
          errorCode: 'too-many-requests',
          severity: ErrorSeverity.critical,
          isUserFriendly: true,
        );

      case 'network-request-failed':
        return const AppError(
          message:
              'Network error. Please check your internet connection and try again.',
          errorCode: 'network-request-failed',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
        );

      case 'user-disabled':
        return const AppError(
          message: 'This account has been disabled. Please contact support.',
          errorCode: 'user-disabled',
          severity: ErrorSeverity.critical,
          isUserFriendly: true,
        );

      case 'operation-not-allowed':
        return const AppError(
          message: 'This operation is not allowed. Please contact support.',
          errorCode: 'operation-not-allowed',
          severity: ErrorSeverity.critical,
          isUserFriendly: true,
        );

      default:
        return AppError(
          message: 'An unexpected error occurred. Please try again.',
          errorCode: error.code,
          severity: ErrorSeverity.error,
          isUserFriendly: true,
          metadata: {'originalMessage': error.message},
        );
    }
  }

  /// Handle Firestore errors
  static AppError handleFirestoreError(FirebaseException error) {
    switch (error.code) {
      case 'permission-denied':
        return const AppError(
          message: 'You do not have permission to perform this action.',
          errorCode: 'permission-denied',
          severity: ErrorSeverity.critical,
          isUserFriendly: true,
        );

      case 'not-found':
        return const AppError(
          message: 'The requested data was not found.',
          errorCode: 'not-found',
          severity: ErrorSeverity.warning,
          isUserFriendly: true,
        );

      case 'unavailable':
        return const AppError(
          message:
              'Service is temporarily unavailable. Please try again later.',
          errorCode: 'unavailable',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
        );

      case 'resource-exhausted':
        return const AppError(
          message: 'Service quota exceeded. Please try again later.',
          errorCode: 'resource-exhausted',
          severity: ErrorSeverity.critical,
          isUserFriendly: true,
        );

      case 'deadline-exceeded':
        return const AppError(
          message: 'Request timeout. Please try again.',
          errorCode: 'deadline-exceeded',
          severity: ErrorSeverity.error,
          isUserFriendly: true,
        );

      case 'cancelled':
        return const AppError(
          message: 'Operation was cancelled.',
          errorCode: 'cancelled',
          severity: ErrorSeverity.warning,
          isUserFriendly: true,
        );

      default:
        return AppError(
          message: 'A database error occurred. Please try again.',
          errorCode: error.code,
          severity: ErrorSeverity.error,
          isUserFriendly: true,
          metadata: {'originalMessage': error.message},
        );
    }
  }

  /// Handle network errors
  static AppError handleNetworkError(Exception? error) {
    final errorMessage = error?.toString().toLowerCase() ?? '';

    if (errorMessage.contains('timeout')) {
      return const AppError(
        message:
            'Connection timeout. Please check your internet connection and try again.',
        errorCode: 'network-timeout',
        severity: ErrorSeverity.error,
        isUserFriendly: true,
      );
    }

    if (errorMessage.contains('no internet') ||
        errorMessage.contains('network')) {
      return const AppError(
        message: 'No internet connection. Please check your network settings.',
        errorCode: 'no-internet',
        severity: ErrorSeverity.error,
        isUserFriendly: true,
      );
    }

    if (errorMessage.contains('server') || errorMessage.contains('500')) {
      return const AppError(
        message: 'Server error. Please try again later.',
        errorCode: 'server-error',
        severity: ErrorSeverity.error,
        isUserFriendly: true,
      );
    }

    return AppError(
      message: 'Network error occurred. Please try again.',
      errorCode: 'network-error',
      severity: ErrorSeverity.error,
      isUserFriendly: true,
      metadata: {'originalError': error?.toString()},
    );
  }

  /// Handle generic errors
  static AppError handleGenericError(dynamic error) {
    return AppError(
      message: 'An unexpected error occurred. Please try again.',
      errorCode: 'generic-error',
      severity: ErrorSeverity.error,
      isUserFriendly: true,
      metadata: {'originalError': error?.toString()},
    );
  }

  /// Validate email address
  static String? validateEmail(String? email) {
    if (email == null || email.trim().isEmpty) {
      return 'Email is required';
    }

    final emailRegex = RegExp(
      r'^[a-zA-Z0-9._%+-]+@[a-zA-Z0-9.-]+\.[a-zA-Z]{2,}$',
    );
    if (!emailRegex.hasMatch(email.trim())) {
      return 'Please enter a valid email address';
    }

    return null;
  }

  /// Validate Indian phone number
  static String? validatePhoneNumber(String? phone) {
    if (phone == null || phone.trim().isEmpty) {
      return 'Phone number is required';
    }

    // Remove spaces and special characters
    final cleanPhone = phone.replaceAll(RegExp(r'[\s\-\(\)]'), '');

    // Indian phone number patterns
    final patterns = [
      RegExp(
        r'^\+91[6-9]\d{9}$',
      ), // +91 followed by 10 digits starting with 6-9
      RegExp(r'^91[6-9]\d{9}$'), // 91 followed by 10 digits starting with 6-9
      RegExp(r'^[6-9]\d{9}$'), // 10 digits starting with 6-9
    ];

    final isValid = patterns.any((pattern) => pattern.hasMatch(cleanPhone));

    if (!isValid) {
      if (cleanPhone.length != 10 &&
          !cleanPhone.startsWith('+91') &&
          !cleanPhone.startsWith('91')) {
        return 'Please enter a valid phone number';
      }
      return 'Please enter a valid Indian phone number';
    }

    return null;
  }

  /// Validate password
  static String? validatePassword(String? password) {
    if (password == null || password.isEmpty) {
      return 'Password is required';
    }

    if (password.length < 6) {
      return 'Password must be at least 6 characters long';
    }

    return null;
  }

  /// Validate name
  static String? validateName(String? name) {
    if (name == null || name.trim().isEmpty) {
      return 'Name is required';
    }

    return null;
  }

  /// Validate amount
  static String? validateAmount(String? amount) {
    if (amount == null || amount.trim().isEmpty) {
      return 'Amount is required';
    }

    final numericAmount = double.tryParse(amount.trim());
    if (numericAmount == null) {
      return 'Please enter a valid amount';
    }

    if (numericAmount < 0) {
      return 'Amount must be positive';
    }

    return null;
  }

  /// Validate referral code
  static String? validateReferralCode(String? code) {
    if (code == null || code.trim().isEmpty) {
      return null; // Referral code is optional
    }

    final trimmedCode = code.trim();

    if (trimmedCode.length != 8) {
      return 'Referral code must be 8 characters long';
    }

    if (!RegExp(r'^[A-Z0-9]+$').hasMatch(trimmedCode)) {
      return 'Referral code must be uppercase letters and numbers';
    }

    return null;
  }

  /// Log error with appropriate severity
  static void logError(AppError error) {
    final logMessage = formatErrorForLogging(error);

    switch (error.severity) {
      case ErrorSeverity.info:
        developer.log(logMessage, level: 800, name: 'INFO');
        break;
      case ErrorSeverity.warning:
        developer.log(logMessage, level: 900, name: 'WARNING');
        break;
      case ErrorSeverity.error:
        developer.log(logMessage, level: 1000, name: 'ERROR');
        break;
      case ErrorSeverity.critical:
        developer.log(logMessage, level: 1200, name: 'CRITICAL');
        break;
    }
  }

  /// Format error for logging
  static String formatErrorForLogging(AppError error) {
    final buffer = StringBuffer();
    buffer.writeln('${error.severity.name.toUpperCase()}: ${error.message}');

    if (error.errorCode != null) {
      buffer.writeln('Code: ${error.errorCode}');
    }

    if (error.metadata != null) {
      buffer.writeln('Metadata: ${error.metadata}');
    }

    if (error.stackTrace != null) {
      buffer.writeln('Stack Trace: ${error.stackTrace}');
    }

    return buffer.toString();
  }

  /// Get recovery actions for error
  static List<String> getRecoveryActions(AppError error) {
    switch (error.errorCode) {
      case 'network-error':
      case 'network-timeout':
      case 'no-internet':
        return [
          'Check internet connection',
          'Try again',
          'Switch to mobile data',
        ];

      case 'auth-failed':
      case 'wrong-password':
      case 'user-not-found':
        return ['Check credentials', 'Reset password', 'Create new account'];

      case 'permission-denied':
        return [
          'Contact administrator',
          'Check account permissions',
          'Sign out and sign in again',
        ];

      case 'server-error':
      case 'unavailable':
        return ['Try again later', 'Contact support', 'Check service status'];

      default:
        return ['Try again', 'Restart app', 'Contact support'];
    }
  }

  /// Check if error is recoverable
  static bool isRecoverable(AppError error) {
    switch (error.severity) {
      case ErrorSeverity.info:
      case ErrorSeverity.warning:
        return true;
      case ErrorSeverity.error:
        return error.errorCode != 'permission-denied';
      case ErrorSeverity.critical:
        return false;
    }
  }

  /// Get user-friendly error message
  static String getUserFriendlyMessage(dynamic error) {
    if (error is AppError) {
      return error.message;
    }

    if (error is FirebaseAuthException) {
      return handleFirebaseAuthError(error).message;
    }

    if (error is FirebaseException) {
      return handleFirestoreError(error).message;
    }

    if (error is Exception) {
      return handleNetworkError(error).message;
    }

    return handleGenericError(error).message;
  }
}
