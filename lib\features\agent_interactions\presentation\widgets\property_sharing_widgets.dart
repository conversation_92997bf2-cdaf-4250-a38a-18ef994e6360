import 'package:flutter/material.dart';
import 'package:flutter/services.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import 'package:url_launcher/url_launcher.dart';
import 'package:share_plus/share_plus.dart';
import '../../../../core/models/property_model.dart';
import '../../../../core/models/agent_favorite_model.dart';
import '../../../../core/services/lead_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

/// Property sharing bottom sheet
class PropertySharingSheet extends ConsumerStatefulWidget {
  final PropertyModel property;

  const PropertySharingSheet({
    super.key,
    required this.property,
  });

  @override
  ConsumerState<PropertySharingSheet> createState() => _PropertySharingSheetState();
}

class _PropertySharingSheetState extends ConsumerState<PropertySharingSheet> {
  final TextEditingController _phoneController = TextEditingController();
  final TextEditingController _emailController = TextEditingController();
  final TextEditingController _messageController = TextEditingController();

  @override
  void initState() {
    super.initState();
    _messageController.text = _getDefaultMessage();
  }

  @override
  void dispose() {
    _phoneController.dispose();
    _emailController.dispose();
    _messageController.dispose();
    super.dispose();
  }

  String _getDefaultMessage() {
    return '''Hi! I'd like to share this amazing property with you:

${widget.property.title}
📍 ${widget.property.location}, ${widget.property.city}
💰 ${widget.property.formattedPrice}
🏠 ${widget.property.type}

${widget.property.description}

Interested? Let me know!''';
  }

  @override
  Widget build(BuildContext context) {
    return Container(
      padding: const EdgeInsets.all(16),
      child: Column(
        mainAxisSize: MainAxisSize.min,
        crossAxisAlignment: CrossAxisAlignment.start,
        children: [
          // Header
          Row(
            children: [
              Text(
                'Share Property',
                style: Theme.of(context).textTheme.titleLarge?.copyWith(
                  fontWeight: FontWeight.bold,
                ),
              ),
              const Spacer(),
              IconButton(
                onPressed: () => Navigator.of(context).pop(),
                icon: const Icon(Icons.close),
              ),
            ],
          ),

          const SizedBox(height: 16),

          // Property preview
          Container(
            padding: const EdgeInsets.all(12),
            decoration: BoxDecoration(
              color: Colors.grey[100],
              borderRadius: BorderRadius.circular(8),
            ),
            child: Row(
              children: [
                Container(
                  width: 60,
                  height: 60,
                  decoration: BoxDecoration(
                    color: Colors.grey[300],
                    borderRadius: BorderRadius.circular(8),
                  ),
                  child: widget.property.imageUrls.isNotEmpty
                      ? ClipRRect(
                          borderRadius: BorderRadius.circular(8),
                          child: Image.network(
                            widget.property.imageUrls.first,
                            fit: BoxFit.cover,
                            errorBuilder: (context, error, stackTrace) =>
                                const Icon(Icons.home, color: Colors.grey),
                          ),
                        )
                      : const Icon(Icons.home, color: Colors.grey),
                ),
                const SizedBox(width: 12),
                Expanded(
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      Text(
                        widget.property.title,
                        style: const TextStyle(fontWeight: FontWeight.bold),
                        maxLines: 1,
                        overflow: TextOverflow.ellipsis,
                      ),
                      Text(
                        widget.property.formattedPrice,
                        style: TextStyle(
                          color: Colors.green[700],
                          fontWeight: FontWeight.w600,
                        ),
                      ),
                      Text(
                        '${widget.property.city}, ${widget.property.state}',
                        style: TextStyle(
                          color: Colors.grey[600],
                          fontSize: 12,
                        ),
                      ),
                    ],
                  ),
                ),
              ],
            ),
          ),

          const SizedBox(height: 24),

          // Quick sharing options
          Text(
            'Quick Share',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 12),

          Row(
            children: [
              Expanded(
                child: _buildQuickShareButton(
                  'WhatsApp',
                  Icons.chat,
                  Colors.green,
                  () => _showWhatsAppDialog(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickShareButton(
                  'Email',
                  Icons.email,
                  Colors.blue,
                  () => _showEmailDialog(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickShareButton(
                  'Copy Link',
                  Icons.link,
                  Colors.orange,
                  () => _copyPropertyLink(),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: _buildQuickShareButton(
                  'More',
                  Icons.share,
                  Colors.purple,
                  () => _shareViaSystem(),
                ),
              ),
            ],
          ),

          const SizedBox(height: 24),

          // Custom message
          Text(
            'Custom Message',
            style: Theme.of(context).textTheme.titleMedium?.copyWith(
              fontWeight: FontWeight.bold,
            ),
          ),

          const SizedBox(height: 12),

          TextField(
            controller: _messageController,
            decoration: const InputDecoration(
              border: OutlineInputBorder(),
              hintText: 'Customize your message...',
            ),
            maxLines: 4,
          ),

          const SizedBox(height: 16),

          // Action buttons
          Row(
            children: [
              Expanded(
                child: OutlinedButton(
                  onPressed: () => _addToFavorites(),
                  child: const Text('Add to Favorites'),
                ),
              ),
              const SizedBox(width: 12),
              Expanded(
                child: ElevatedButton(
                  onPressed: () => _createLead(),
                  child: const Text('Create Lead'),
                ),
              ),
            ],
          ),
        ],
      ),
    );
  }

  Widget _buildQuickShareButton(String label, IconData icon, Color color, VoidCallback onTap) {
    return InkWell(
      onTap: onTap,
      borderRadius: BorderRadius.circular(8),
      child: Container(
        padding: const EdgeInsets.symmetric(vertical: 12),
        decoration: BoxDecoration(
          color: color.withValues(alpha: 0.1),
          borderRadius: BorderRadius.circular(8),
          border: Border.all(color: color.withValues(alpha: 0.3)),
        ),
        child: Column(
          children: [
            Icon(icon, color: color, size: 24),
            const SizedBox(height: 4),
            Text(
              label,
              style: TextStyle(
                color: color,
                fontSize: 12,
                fontWeight: FontWeight.w500,
              ),
            ),
          ],
        ),
      ),
    );
  }

  void _showWhatsAppDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share via WhatsApp'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _phoneController,
              decoration: const InputDecoration(
                labelText: 'Phone Number',
                hintText: '+91XXXXXXXXXX',
                prefixIcon: Icon(Icons.phone),
              ),
              keyboardType: TextInputType.phone,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _shareViaWhatsApp(_phoneController.text);
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  void _showEmailDialog() {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Share via Email'),
        content: Column(
          mainAxisSize: MainAxisSize.min,
          children: [
            TextField(
              controller: _emailController,
              decoration: const InputDecoration(
                labelText: 'Email Address',
                hintText: '<EMAIL>',
                prefixIcon: Icon(Icons.email),
              ),
              keyboardType: TextInputType.emailAddress,
            ),
          ],
        ),
        actions: [
          TextButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Cancel'),
          ),
          ElevatedButton(
            onPressed: () {
              Navigator.of(context).pop();
              _shareViaEmail(_emailController.text);
            },
            child: const Text('Share'),
          ),
        ],
      ),
    );
  }

  Future<void> _shareViaWhatsApp(String phoneNumber) async {
    if (phoneNumber.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter a phone number')),
      );
      return;
    }

    final message = _messageController.text;
    final uri = Uri.parse('https://wa.me/$phoneNumber?text=${Uri.encodeComponent(message)}');
    
    if (await canLaunchUrl(uri)) {
      await launchUrl(uri, mode: LaunchMode.externalApplication);
      await _recordSharing('whatsapp', phoneNumber);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not open WhatsApp')),
        );
      }
    }
  }

  Future<void> _shareViaEmail(String email) async {
    if (email.isEmpty) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(content: Text('Please enter an email address')),
      );
      return;
    }

    final subject = 'Property: ${widget.property.title}';
    final body = _messageController.text;
    final uri = Uri(
      scheme: 'mailto',
      path: email,
      query: 'subject=${Uri.encodeComponent(subject)}&body=${Uri.encodeComponent(body)}',
    );

    if (await canLaunchUrl(uri)) {
      await launchUrl(uri);
      await _recordSharing('email', email);
    } else {
      if (mounted) {
        ScaffoldMessenger.of(context).showSnackBar(
          const SnackBar(content: Text('Could not open email app')),
        );
      }
    }
  }

  Future<void> _copyPropertyLink() async {
    final link = 'https://ramarealty.com/property/${widget.property.id}';
    await Clipboard.setData(ClipboardData(text: link));
    await _recordSharing('direct_link', null);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        const SnackBar(
          content: Text('Property link copied to clipboard!'),
          backgroundColor: Colors.green,
        ),
      );
    }
  }

  Future<void> _shareViaSystem() async {
    final message = _messageController.text;
    await Share.share(message, subject: 'Property: ${widget.property.title}');
    await _recordSharing('system_share', null);
  }

  Future<void> _recordSharing(String method, String? recipient) async {
    final currentUser = ref.read(currentUserProvider);
    if (currentUser == null) return;

    final sharing = PropertySharingModel(
      id: '',
      propertyId: widget.property.id,
      agentId: currentUser.id,
      shareMethod: method,
      recipientInfo: recipient,
      sharedAt: DateTime.now(),
    );

    await LeadService.recordPropertySharing(sharing);
  }

  Future<void> _addToFavorites() async {
    final currentUser = ref.read(currentUserProvider);
    if (currentUser == null) return;

    final favorite = AgentFavoriteModel(
      id: '',
      agentId: currentUser.id,
      agentName: currentUser.name,
      propertyId: widget.property.id,
      propertyTitle: widget.property.title,
      propertyType: widget.property.type,
      propertyLocation: '${widget.property.city}, ${widget.property.state}',
      propertyPrice: widget.property.price,
      createdAt: DateTime.now(),
      updatedAt: DateTime.now(),
    );

    final result = await LeadService.addToFavorites(favorite);
    
    if (mounted) {
      ScaffoldMessenger.of(context).showSnackBar(
        SnackBar(
          content: Text(result.isSuccess 
              ? 'Added to favorites!' 
              : result.message),
          backgroundColor: result.isSuccess ? Colors.green : Colors.red,
        ),
      );
    }
  }

  void _createLead() {
    // TODO: Navigate to lead creation form
    ScaffoldMessenger.of(context).showSnackBar(
      const SnackBar(content: Text('Lead creation form coming soon!')),
    );
  }
}
