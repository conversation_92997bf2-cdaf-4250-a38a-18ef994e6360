import 'package:flutter/material.dart';
import 'package:flutter_riverpod/flutter_riverpod.dart';
import '../providers/commission_providers.dart';
import '../widgets/commission_card.dart';
import '../widgets/transaction_form.dart';
import '../../../../core/models/commission_model.dart';
import '../../../../core/services/commission_service.dart';
import '../../../auth/presentation/providers/auth_providers.dart';

class CommissionsPage extends ConsumerStatefulWidget {
  const CommissionsPage({super.key});

  @override
  ConsumerState<CommissionsPage> createState() => _CommissionsPageState();
}

class _CommissionsPageState extends ConsumerState<CommissionsPage>
    with SingleTickerProviderStateMixin {
  late TabController _tabController;

  @override
  void initState() {
    super.initState();
    _tabController = TabController(length: 3, vsync: this);
  }

  @override
  void dispose() {
    _tabController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    final currentUser = ref.watch(currentUserProvider);
    final isAdmin = currentUser?.isAdmin ?? false;

    return Scaffold(
      appBar: AppBar(
        title: const Text('Commissions'),
        actions: [
          IconButton(
            icon: const Icon(Icons.filter_list),
            onPressed: () => _showFilterDialog(context),
          ),
          if (isAdmin)
            IconButton(
              icon: const Icon(Icons.add),
              onPressed: () => _createTransaction(context),
            ),
        ],
        bottom: TabBar(
          controller: _tabController,
          tabs: [
            const Tab(icon: Icon(Icons.currency_rupee), text: 'My Commissions'),
            if (isAdmin)
              const Tab(
                icon: Icon(Icons.admin_panel_settings),
                text: 'All Commissions',
              ),
            if (isAdmin)
              const Tab(icon: Icon(Icons.receipt_long), text: 'Transactions'),
          ],
        ),
      ),
      body: TabBarView(
        controller: _tabController,
        children: [
          // My Commissions Tab
          _buildMyCommissionsTab(),

          // All Commissions Tab (Admin only)
          if (isAdmin) _buildAllCommissionsTab(),

          // Transactions Tab (Admin only)
          if (isAdmin) _buildTransactionsTab(),
        ],
      ),
      floatingActionButton: isAdmin
          ? FloatingActionButton(
              onPressed: () => _createTransaction(context),
              tooltip: 'Create Transaction',
              child: const Icon(Icons.add),
            )
          : null,
    );
  }

  Widget _buildMyCommissionsTab() {
    final commissionsAsync = ref.watch(filteredCommissionsProvider);

    return commissionsAsync.when(
      data: (commissions) {
        if (commissions.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.currency_rupee, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No commissions yet'),
                Text('Commissions will appear here after sales'),
              ],
            ),
          );
        }

        return Column(
          children: [
            // Commission Summary
            CommissionSummaryCard(commissions: commissions),

            // Commission List
            Expanded(
              child: RefreshIndicator(
                onRefresh: () async {
                  ref.invalidate(filteredCommissionsProvider);
                },
                child: ListView.builder(
                  padding: const EdgeInsets.symmetric(vertical: 8),
                  itemCount: commissions.length,
                  itemBuilder: (context, index) {
                    final commission = commissions[index];
                    return CommissionCard(
                      commission: commission,
                      onTap: () => _showCommissionDetails(context, commission),
                    );
                  },
                ),
              ),
            ),
          ],
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, stack) => Center(
        child: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            const Icon(Icons.error, size: 64, color: Colors.red),
            const SizedBox(height: 16),
            Text('Error loading commissions: $error'),
            const SizedBox(height: 16),
            ElevatedButton(
              onPressed: () => ref.refresh(filteredCommissionsProvider),
              child: const Text('Retry'),
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildAllCommissionsTab() {
    final commissionsAsync = ref.watch(allCommissionsProvider);
    final statsAsync = ref.watch(commissionStatsProvider);

    return Column(
      children: [
        // Statistics Card
        statsAsync.when(
          data: (stats) => _buildStatsCard(stats),
          loading: () => const LinearProgressIndicator(),
          error: (_, __) => const SizedBox.shrink(),
        ),

        // Commissions List
        Expanded(
          child: commissionsAsync.when(
            data: (commissions) {
              if (commissions.isEmpty) {
                return const Center(
                  child: Column(
                    mainAxisAlignment: MainAxisAlignment.center,
                    children: [
                      Icon(Icons.inventory, size: 64, color: Colors.grey),
                      SizedBox(height: 16),
                      Text('No commissions to manage'),
                    ],
                  ),
                );
              }

              return ListView.builder(
                padding: const EdgeInsets.all(8),
                itemCount: commissions.length,
                itemBuilder: (context, index) {
                  final commission = commissions[index];
                  return CommissionCard(
                    commission: commission,
                    showAdminActions: true,
                    onTap: () => _showCommissionDetails(context, commission),
                    onMarkAsPaid: (commission) =>
                        _markCommissionAsPaid(commission),
                  );
                },
              );
            },
            loading: () => const Center(child: CircularProgressIndicator()),
            error: (error, _) => Center(child: Text('Error: $error')),
          ),
        ),
      ],
    );
  }

  Widget _buildTransactionsTab() {
    final transactionsAsync = ref.watch(allTransactionsProvider);

    return transactionsAsync.when(
      data: (transactions) {
        if (transactions.isEmpty) {
          return const Center(
            child: Column(
              mainAxisAlignment: MainAxisAlignment.center,
              children: [
                Icon(Icons.receipt_long, size: 64, color: Colors.grey),
                SizedBox(height: 16),
                Text('No transactions yet'),
                Text('Create your first transaction to get started'),
              ],
            ),
          );
        }

        return ListView.builder(
          padding: const EdgeInsets.all(8),
          itemCount: transactions.length,
          itemBuilder: (context, index) {
            final transaction = transactions[index];
            return _buildTransactionCard(transaction);
          },
        );
      },
      loading: () => const Center(child: CircularProgressIndicator()),
      error: (error, _) => Center(child: Text('Error: $error')),
    );
  }

  Widget _buildStatsCard(Map<String, dynamic> stats) {
    return Card(
      margin: const EdgeInsets.all(16),
      child: Padding(
        padding: const EdgeInsets.all(16),
        child: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text(
              'Commission Statistics',
              style: Theme.of(
                context,
              ).textTheme.titleMedium?.copyWith(fontWeight: FontWeight.bold),
            ),
            const SizedBox(height: 16),
            GridView.count(
              shrinkWrap: true,
              physics: const NeverScrollableScrollPhysics(),
              crossAxisCount: 2,
              crossAxisSpacing: 16,
              mainAxisSpacing: 16,
              childAspectRatio: 2,
              children: [
                _buildStatItem(
                  'Total Commissions',
                  _formatAmount(stats['totalCommissions']?.toDouble() ?? 0),
                  Icons.currency_rupee,
                  Colors.blue,
                ),
                _buildStatItem(
                  'Paid Commissions',
                  _formatAmount(stats['paidCommissions']?.toDouble() ?? 0),
                  Icons.check_circle,
                  Colors.green,
                ),
                _buildStatItem(
                  'Pending Commissions',
                  _formatAmount(stats['pendingCommissions']?.toDouble() ?? 0),
                  Icons.pending,
                  Colors.orange,
                ),
                _buildStatItem(
                  'Total Sales',
                  _formatAmount(stats['totalSales']?.toDouble() ?? 0),
                  Icons.trending_up,
                  Colors.purple,
                ),
              ],
            ),
            const SizedBox(height: 16),
            Row(
              children: [
                Expanded(
                  child: _buildInfoCard(
                    'Average Commission',
                    _formatAmount(stats['averageCommission']?.toDouble() ?? 0),
                    Icons.analytics,
                  ),
                ),
                const SizedBox(width: 16),
                Expanded(
                  child: _buildInfoCard(
                    'Commission Rate',
                    '${(stats['commissionRate']?.toDouble() ?? 0).toStringAsFixed(2)}%',
                    Icons.percent,
                  ),
                ),
              ],
            ),
          ],
        ),
      ),
    );
  }

  Widget _buildStatItem(
    String label,
    String value,
    IconData icon,
    Color color,
  ) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: color.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: color.withValues(alpha: 0.3)),
      ),
      child: Column(
        mainAxisAlignment: MainAxisAlignment.center,
        children: [
          Icon(icon, color: color, size: 24),
          const SizedBox(height: 8),
          Text(
            value,
            style: TextStyle(
              fontWeight: FontWeight.bold,
              color: color,
              fontSize: 16,
            ),
          ),
          Text(
            label,
            style: const TextStyle(fontSize: 12),
            textAlign: TextAlign.center,
          ),
        ],
      ),
    );
  }

  Widget _buildInfoCard(String label, String value, IconData icon) {
    return Container(
      padding: const EdgeInsets.all(12),
      decoration: BoxDecoration(
        color: Colors.grey.withValues(alpha: 0.1),
        borderRadius: BorderRadius.circular(8),
        border: Border.all(color: Colors.grey.withValues(alpha: 0.3)),
      ),
      child: Row(
        children: [
          Icon(icon, color: Colors.grey[600], size: 20),
          const SizedBox(width: 8),
          Expanded(
            child: Column(
              crossAxisAlignment: CrossAxisAlignment.start,
              children: [
                Text(
                  label,
                  style: const TextStyle(fontSize: 12, color: Colors.grey),
                ),
                Text(
                  value,
                  style: const TextStyle(
                    fontWeight: FontWeight.bold,
                    fontSize: 14,
                  ),
                ),
              ],
            ),
          ),
        ],
      ),
    );
  }

  Widget _buildTransactionCard(TransactionModel transaction) {
    return Card(
      margin: const EdgeInsets.symmetric(horizontal: 8, vertical: 4),
      child: ListTile(
        leading: CircleAvatar(
          backgroundColor: _getTransactionStatusColor(
            transaction.status,
          ).withValues(alpha: 0.2),
          child: Icon(
            _getTransactionIcon(transaction.type),
            color: _getTransactionStatusColor(transaction.status),
          ),
        ),
        title: Text(
          transaction.propertyTitle,
          style: const TextStyle(fontWeight: FontWeight.w500),
          maxLines: 1,
          overflow: TextOverflow.ellipsis,
        ),
        subtitle: Column(
          crossAxisAlignment: CrossAxisAlignment.start,
          children: [
            Text('Agent: ${transaction.agentName}'),
            Text('Sale: ${transaction.formattedPropertyAmount}'),
            Text('Commission: ${transaction.formattedCommissionAmount}'),
          ],
        ),
        trailing: Column(
          mainAxisAlignment: MainAxisAlignment.center,
          children: [
            Container(
              padding: const EdgeInsets.symmetric(horizontal: 6, vertical: 2),
              decoration: BoxDecoration(
                color: _getTransactionStatusColor(
                  transaction.status,
                ).withValues(alpha: 0.1),
                borderRadius: BorderRadius.circular(8),
                border: Border.all(
                  color: _getTransactionStatusColor(transaction.status),
                ),
              ),
              child: Text(
                transaction.status.toUpperCase(),
                style: TextStyle(
                  color: _getTransactionStatusColor(transaction.status),
                  fontSize: 10,
                  fontWeight: FontWeight.bold,
                ),
              ),
            ),
            const SizedBox(height: 2),
            Text(
              '${transaction.commissionRate.toStringAsFixed(1)}%',
              style: const TextStyle(fontSize: 10),
            ),
          ],
        ),
        onTap: () => _showTransactionDetails(context, transaction),
      ),
    );
  }

  Color _getTransactionStatusColor(String status) {
    switch (status) {
      case 'completed':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  IconData _getTransactionIcon(String type) {
    switch (type) {
      case 'sale':
        return Icons.sell;
      case 'rental':
        return Icons.home;
      default:
        return Icons.receipt;
    }
  }

  String _formatAmount(double amount) {
    if (amount >= 10000000) {
      return '₹${(amount / 10000000).toStringAsFixed(2)} Cr';
    } else if (amount >= 100000) {
      return '₹${(amount / 100000).toStringAsFixed(2)} L';
    } else if (amount >= 1000) {
      return '₹${(amount / 1000).toStringAsFixed(2)} K';
    } else {
      return '₹${amount.toStringAsFixed(0)}';
    }
  }

  void _showFilterDialog(BuildContext context) {
    showDialog(
      context: context,
      builder: (context) => AlertDialog(
        title: const Text('Filter Commissions'),
        content: SingleChildScrollView(
          child: Column(
            mainAxisSize: MainAxisSize.min,
            children: [
              // Status Filter
              Consumer(
                builder: (context, ref, child) {
                  final filter = ref.watch(commissionFilterProvider);
                  return DropdownButtonFormField<String?>(
                    value: filter.status,
                    decoration: const InputDecoration(labelText: 'Status'),
                    items: const [
                      DropdownMenuItem<String?>(
                        value: null,
                        child: Text('All Status'),
                      ),
                      DropdownMenuItem(
                        value: 'pending',
                        child: Text('Pending'),
                      ),
                      DropdownMenuItem(value: 'paid', child: Text('Paid')),
                      DropdownMenuItem(
                        value: 'cancelled',
                        child: Text('Cancelled'),
                      ),
                    ],
                    onChanged: (value) {
                      ref
                          .read(commissionFilterProvider.notifier)
                          .setStatus(value);
                    },
                  );
                },
              ),

              const SizedBox(height: 16),

              // Level Filter
              Consumer(
                builder: (context, ref, child) {
                  final filter = ref.watch(commissionFilterProvider);
                  return DropdownButtonFormField<int?>(
                    value: filter.level,
                    decoration: const InputDecoration(labelText: 'MLM Level'),
                    items: const [
                      DropdownMenuItem<int?>(
                        value: null,
                        child: Text('All Levels'),
                      ),
                      DropdownMenuItem(value: 0, child: Text('Direct Sale')),
                      DropdownMenuItem(value: 1, child: Text('Level 1')),
                      DropdownMenuItem(value: 2, child: Text('Level 2')),
                      DropdownMenuItem(value: 3, child: Text('Level 3')),
                      DropdownMenuItem(value: 4, child: Text('Level 4')),
                    ],
                    onChanged: (value) {
                      ref
                          .read(commissionFilterProvider.notifier)
                          .setLevel(value);
                    },
                  );
                },
              ),
            ],
          ),
        ),
        actions: [
          TextButton(
            onPressed: () {
              ref.read(commissionFilterProvider.notifier).resetFilters();
              Navigator.of(context).pop();
            },
            child: const Text('Reset'),
          ),
          ElevatedButton(
            onPressed: () => Navigator.of(context).pop(),
            child: const Text('Apply'),
          ),
        ],
      ),
    );
  }

  void _createTransaction(BuildContext context) {
    Navigator.of(context).push(
      MaterialPageRoute(
        builder: (context) => TransactionForm(
          onTransactionCreated: (transactionId) {
            ref.invalidate(allCommissionsProvider);
            ref.invalidate(allTransactionsProvider);
            ref.invalidate(filteredCommissionsProvider);
            ref.invalidate(commissionStatsProvider);
          },
        ),
      ),
    );
  }

  void _showCommissionDetails(
    BuildContext context,
    CommissionModel commission,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.6,
        maxChildSize: 0.9,
        minChildSize: 0.4,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 16),

              // Commission details
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Amount and Status
                      Row(
                        children: [
                          Expanded(
                            child: Text(
                              commission.formattedAmount,
                              style: Theme.of(context).textTheme.headlineSmall
                                  ?.copyWith(
                                    fontWeight: FontWeight.bold,
                                    color: Theme.of(context).primaryColor,
                                  ),
                            ),
                          ),
                          Container(
                            padding: const EdgeInsets.symmetric(
                              horizontal: 12,
                              vertical: 6,
                            ),
                            decoration: BoxDecoration(
                              color: _getCommissionStatusColor(
                                commission.status,
                              ).withValues(alpha: 0.1),
                              borderRadius: BorderRadius.circular(12),
                              border: Border.all(
                                color: _getCommissionStatusColor(
                                  commission.status,
                                ),
                              ),
                            ),
                            child: Text(
                              commission.status.toUpperCase(),
                              style: TextStyle(
                                color: _getCommissionStatusColor(
                                  commission.status,
                                ),
                                fontWeight: FontWeight.bold,
                              ),
                            ),
                          ),
                        ],
                      ),

                      const SizedBox(height: 16),

                      // Details
                      _buildDetailRow(
                        'Agent',
                        commission.agentName,
                        Icons.person,
                      ),
                      _buildDetailRow(
                        'Level',
                        commission.levelDescription,
                        Icons.layers,
                      ),
                      _buildDetailRow(
                        'Rate',
                        '${(commission.rate * 100).toStringAsFixed(1)}%',
                        Icons.percent,
                      ),
                      _buildDetailRow(
                        'Created',
                        _formatDate(commission.createdAt),
                        Icons.calendar_today,
                      ),

                      if (commission.isPaid && commission.paidAt != null)
                        _buildDetailRow(
                          'Paid Date',
                          _formatDate(commission.paidAt!),
                          Icons.payment,
                        ),

                      if (commission.paymentMethod != null)
                        _buildDetailRow(
                          'Payment Method',
                          commission.paymentMethod!,
                          Icons.account_balance_wallet,
                        ),

                      if (commission.paymentReference != null)
                        _buildDetailRow(
                          'Payment Reference',
                          commission.paymentReference!,
                          Icons.receipt,
                        ),

                      if (commission.notes != null &&
                          commission.notes!.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Notes',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(commission.notes!),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  void _showTransactionDetails(
    BuildContext context,
    TransactionModel transaction,
  ) {
    showModalBottomSheet(
      context: context,
      isScrollControlled: true,
      builder: (context) => DraggableScrollableSheet(
        initialChildSize: 0.7,
        maxChildSize: 0.9,
        minChildSize: 0.5,
        builder: (context, scrollController) => Container(
          padding: const EdgeInsets.all(16),
          child: Column(
            children: [
              // Handle
              Container(
                width: 40,
                height: 4,
                decoration: BoxDecoration(
                  color: Colors.grey[300],
                  borderRadius: BorderRadius.circular(2),
                ),
              ),

              const SizedBox(height: 16),

              // Transaction details
              Expanded(
                child: SingleChildScrollView(
                  controller: scrollController,
                  child: Column(
                    crossAxisAlignment: CrossAxisAlignment.start,
                    children: [
                      // Title and Status
                      Text(
                        transaction.propertyTitle,
                        style: Theme.of(context).textTheme.headlineSmall
                            ?.copyWith(fontWeight: FontWeight.bold),
                      ),

                      const SizedBox(height: 16),

                      // Financial Details
                      _buildDetailRow(
                        'Property Amount',
                        transaction.formattedPropertyAmount,
                        Icons.home,
                      ),
                      _buildDetailRow(
                        'Commission Amount',
                        transaction.formattedCommissionAmount,
                        Icons.currency_rupee,
                      ),
                      _buildDetailRow(
                        'Commission Rate',
                        '${transaction.commissionRate.toStringAsFixed(2)}%',
                        Icons.percent,
                      ),
                      _buildDetailRow(
                        'Transaction Type',
                        transaction.type.toUpperCase(),
                        Icons.category,
                      ),
                      _buildDetailRow(
                        'Status',
                        transaction.status.toUpperCase(),
                        Icons.info,
                      ),

                      // Agent Details
                      _buildDetailRow(
                        'Selling Agent',
                        transaction.agentName,
                        Icons.person,
                      ),

                      // Buyer Details
                      if (transaction.buyerName != null)
                        _buildDetailRow(
                          'Buyer Name',
                          transaction.buyerName!,
                          Icons.person_outline,
                        ),

                      if (transaction.buyerContact != null)
                        _buildDetailRow(
                          'Buyer Contact',
                          transaction.buyerContact!,
                          Icons.phone,
                        ),

                      // Dates
                      _buildDetailRow(
                        'Created',
                        _formatDate(transaction.createdAt),
                        Icons.calendar_today,
                      ),

                      if (transaction.completedAt != null)
                        _buildDetailRow(
                          'Completed',
                          _formatDate(transaction.completedAt!),
                          Icons.check_circle,
                        ),

                      // Notes
                      if (transaction.notes != null &&
                          transaction.notes!.isNotEmpty) ...[
                        const SizedBox(height: 16),
                        Text(
                          'Notes',
                          style: Theme.of(context).textTheme.titleMedium
                              ?.copyWith(fontWeight: FontWeight.bold),
                        ),
                        const SizedBox(height: 8),
                        Text(transaction.notes!),
                      ],
                    ],
                  ),
                ),
              ),
            ],
          ),
        ),
      ),
    );
  }

  Widget _buildDetailRow(String label, String value, IconData icon) {
    return Padding(
      padding: const EdgeInsets.symmetric(vertical: 4),
      child: Row(
        children: [
          Icon(icon, size: 20, color: Colors.grey[600]),
          const SizedBox(width: 8),
          Text('$label: ', style: const TextStyle(fontWeight: FontWeight.w500)),
          Expanded(child: Text(value)),
        ],
      ),
    );
  }

  Color _getCommissionStatusColor(String status) {
    switch (status) {
      case 'paid':
        return Colors.green;
      case 'pending':
        return Colors.orange;
      case 'cancelled':
        return Colors.red;
      default:
        return Colors.grey;
    }
  }

  String _formatDate(DateTime date) {
    return '${date.day}/${date.month}/${date.year}';
  }

  Future<void> _markCommissionAsPaid(CommissionModel commission) async {
    final result = await showDialog<Map<String, String>>(
      context: context,
      builder: (context) => _PaymentDialog(),
    );

    if (result != null) {
      final commissionResult = await CommissionService.markCommissionAsPaid(
        commission.id,
        result['method']!,
        result['reference']!,
      );

      if (commissionResult.isSuccess) {
        ref.invalidate(allCommissionsProvider);
        ref.invalidate(filteredCommissionsProvider);
        ref.invalidate(commissionStatsProvider);

        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            const SnackBar(
              content: Text('Commission marked as paid successfully'),
              backgroundColor: Colors.green,
            ),
          );
        }
      } else {
        if (mounted) {
          ScaffoldMessenger.of(context).showSnackBar(
            SnackBar(
              content: Text(
                'Failed to mark commission as paid: ${commissionResult.message}',
              ),
              backgroundColor: Colors.red,
            ),
          );
        }
      }
    }
  }
}

class _PaymentDialog extends StatefulWidget {
  @override
  State<_PaymentDialog> createState() => _PaymentDialogState();
}

class _PaymentDialogState extends State<_PaymentDialog> {
  final _methodController = TextEditingController();
  final _referenceController = TextEditingController();
  String _selectedMethod = 'Bank Transfer';

  final List<String> _paymentMethods = [
    'Bank Transfer',
    'UPI',
    'Cash',
    'Cheque',
    'Digital Wallet',
  ];

  @override
  void dispose() {
    _methodController.dispose();
    _referenceController.dispose();
    super.dispose();
  }

  @override
  Widget build(BuildContext context) {
    return AlertDialog(
      title: const Text('Mark Commission as Paid'),
      content: Column(
        mainAxisSize: MainAxisSize.min,
        children: [
          DropdownButtonFormField<String>(
            value: _selectedMethod,
            decoration: const InputDecoration(
              labelText: 'Payment Method',
              border: OutlineInputBorder(),
            ),
            items: _paymentMethods
                .map(
                  (method) =>
                      DropdownMenuItem(value: method, child: Text(method)),
                )
                .toList(),
            onChanged: (value) {
              setState(() {
                _selectedMethod = value!;
              });
            },
          ),
          const SizedBox(height: 16),
          TextFormField(
            controller: _referenceController,
            decoration: const InputDecoration(
              labelText: 'Payment Reference/Transaction ID',
              border: OutlineInputBorder(),
            ),
          ),
        ],
      ),
      actions: [
        TextButton(
          onPressed: () => Navigator.of(context).pop(),
          child: const Text('Cancel'),
        ),
        ElevatedButton(
          onPressed: () {
            if (_referenceController.text.isNotEmpty) {
              Navigator.of(context).pop({
                'method': _selectedMethod,
                'reference': _referenceController.text,
              });
            }
          },
          child: const Text('Mark as Paid'),
        ),
      ],
    );
  }
}
